import { useController } from '@mdtBsComm/hooks/use-controller';
import { AppController } from '../../app/AppController';
import { OperationLog, OperationLogController, OperationLogModel } from '../../pages/operation-log';

const OperationLogView = () => {
  const [controller] = useController(() => {
    const ctrl = new OperationLogController(AppController.getInstance(), OperationLogModel);
    return [ctrl, null];
  });

  return <OperationLog controller={controller} />;
};

export default OperationLogView;
