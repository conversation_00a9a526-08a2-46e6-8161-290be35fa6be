import loadable from '@loadable/component';
import { IRoute } from '@mdtProComm/controllers/BaseRouterController';
import { DatlasRouterController } from '@mdtProMicroModules/datlas/comm/DatlasRouterController';
import { RoutePathEnum } from './constants';
import { MyDataPermissionEnum } from './UserPermissionController';

const Datasource = loadable(() => import('../routers/datasource'));
const OtherAppData = loadable(() => import('../routers/other-app-data'));
const PersonalData = loadable(() => import('../routers/personal-data'));
const SelfAppData = loadable(() => import('../routers/self-app-data'));
const PkgDetail = loadable(() => import('../routers/pkg-detail'));

export const allAuthRoutes: IRoute[] = [
  {
    path: RoutePathEnum.PERSONAL_DATA,
    View: PersonalData,
    permissionKey: MyDataPermissionEnum.MENU_PERSONALDATA,
    withGlobalSearch: true,
  },
  {
    path: RoutePathEnum.SELF_APP_DATA,
    View: SelfAppData,
    permissionKey: MyDataPermissionEnum.MENU_INSTITUTION,
    withGlobalSearch: true,
  },
  {
    path: RoutePathEnum.OTHER_APP_DATA,
    View: OtherAppData,
    permissionKey: MyDataPermissionEnum.MENU_INSTITUTION,
    withGlobalSearch: true,
  },
  {
    path: RoutePathEnum.DATASOURCE,
    View: Datasource,
    permissionKey: MyDataPermissionEnum.MENU_DATASETS, // 权限key
    // headerLess: true, // 不展示header
    // sideMenuLess: true, // 不展示sideMenu
  },
  {
    path: RoutePathEnum.PKG_DETAIL,
    View: PkgDetail,
    permissionKey: true,
    headerLess: true, // 不展示header
    sideMenuLess: true, // 不展示sideMenu
  },
];

class RouterController extends DatlasRouterController {
  public getAllAuthRoutes() {
    return allAuthRoutes;
  }

  public goToDatasource() {
    this.gotoPath(RoutePathEnum.DATASOURCE);
  }

  public goToOtherAppData() {
    this.gotoPath(RoutePathEnum.OTHER_APP_DATA);
  }

  public goToPersonalData() {
    this.gotoPath(RoutePathEnum.PERSONAL_DATA);
  }

  public goToSelfAppData() {
    this.gotoPath(RoutePathEnum.SELF_APP_DATA);
  }
}

export { RouterController };
