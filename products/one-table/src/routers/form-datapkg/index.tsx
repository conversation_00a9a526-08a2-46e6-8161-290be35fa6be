import { useController } from '@mdtBsComm/hooks/use-controller';
import {
  OneTableFormDatapkg,
  OneTableFormDatapkgController,
  OneTableFormDatapkgModelBff,
} from '@mdtProMicroModules/pages/one-table-form-datapkg';

const FormDatapkgView = () => {
  const [controller] = useController(() => {
    return [
      new OneTableFormDatapkgController({
        Model: OneTableFormDatapkgModelBff,
      }),
      null,
    ];
  }, []);

  return <OneTableFormDatapkg controller={controller} />;
};

export default FormDatapkgView;
