import _ from 'lodash';
import { Observable } from 'rxjs';
// import { screen } from '@testing-library/react';
import { IBusinessResult } from '@mdtProComm/interfaces';
import { DialogModifyFormInstitutionController, IControllerOptions } from './DialogModifyFormInstitutionController';
const controllers: DialogModifyFormInstitutionController<any>[] = [];
let controller: DialogModifyFormInstitutionController<any>;

describe('DialogModifyFormInstitutionControllerTest', () => {
  describe('测试默认状态下行为', () => {
    const options: IControllerOptions<any> = {
      modifyDataFunc: (params: any, originalData?: any) => {
        console.log('originalData: ', originalData);
        console.log('params: ', params);
        return new Promise<IBusinessResult<void>>((resolve) => {
          resolve({ success: true });
        });
      },
      queryNameFunc: (id: string) => {
        console.log('id: ', id);
        return new Observable<string>((observable) => {
          observable.next(id);
        });
      },
      selfAppId: 0,
    };
    beforeAll(() => {
      controller = new DialogModifyFormInstitutionController(options);
      controller.getModalCompController().getModalOptions();
      controller.getModalCompController().openModal();
      controller.clickOkBtn();
      controller.getFormCompController();
      controller.openModal();
      controllers.push(controller);
    });
    test('应该被定义', () => {
      expect(controller).toBeDefined();
    });
  });
});

afterAll(() => {
  _.forEach(controllers, (it) => {
    it.destroy();
  });
  controllers.length = 0;
});
