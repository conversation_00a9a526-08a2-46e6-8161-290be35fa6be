import { BehaviorSubject } from 'rxjs';
import { ILabelValue } from '@mdtBsComm/interfaces';
import { TableTaggroupController } from '@mdtProMicroModules/containers/table-taggroup';
import { TableThemeController } from '@mdtProMicroModules/containers/table-theme';
import { AppController } from '../../app/AppController';
import i18n from '../../languages';
import { IMetaDataTemplateManageModel } from './MetaDataTemplateManageModel';

export const THEME_TAG_GROUP_ID = 'theme_tag_group_id';
export enum TagsPage {
  THEME = 'theme',
  TAGGROUP = 'taggroup',
}

class MetaDataTemplateManageController {
  private themesController?: TableThemeController;
  private taggroupController?: TableTaggroupController;
  private activePage$: BehaviorSubject<string>;
  private menus: ILabelValue[] = [];

  public constructor(app: AppController, Model: IMetaDataTemplateManageModel, activePage: TagsPage = TagsPage.THEME) {
    const subModel = Model.getSubModels();
    if (activePage === TagsPage.THEME) {
      this.menus.push({ label: i18n.chain.proMicroModules.theme.name, value: TagsPage.THEME });
      this.themesController = new TableThemeController(subModel.themesModel);
    } else {
      this.menus.push({ label: i18n.chain.proMicroModules.taggroup.name, value: TagsPage.TAGGROUP });
      this.taggroupController = new TableTaggroupController(subModel.taggroupModel);
    }
    this.activePage$ = new BehaviorSubject<string>(activePage);
  }

  public destroy() {
    this.activePage$.complete();
    this.themesController?.destroy();
    this.themesController = undefined;
    this.taggroupController?.destroy();
    this.taggroupController = undefined;
  }

  public getThemesController() {
    return this.themesController!;
  }

  public getTaggroupController() {
    return this.taggroupController!;
  }

  public getActivePage$() {
    return this.activePage$;
  }

  public changeActivePage = (p: string) => {
    this.activePage$.next(p);
  };
}

export { MetaDataTemplateManageController };
