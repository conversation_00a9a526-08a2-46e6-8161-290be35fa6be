# MDT Product Creator (MDT 产品创建器)

这是一个 VSCode 扩展，用于在 MDT 前端 monorepo 项目中创建新的产品或模块。

## 功能

- 创建新的产品/模块脚手架，包含必要的目录结构和基础文件
- 自动更新 datlas 相关配置文件
- 自动添加常量枚举值（ModuleIdEnum, ProductPrefixEnum）
- 自动更新 AppHeader 相关配置
- 自动添加产品/模块跳转方法
- 自动更新权限定义
- 统一的产品/模块创建流程，减少手动操作错误

### 最佳实践

1. 命名规范：

   - 产品/模块 ID 使用短横线命名法，例如：`data-market`、`resource-share`
   - ID 应简洁明了，表达产品/模块的主要功能

2. 代码组织：

   - 创建产品/模块后，保持生成的目录结构
   - 遵循项目的代码规范和架构设计

3. 权限管理：

   - 创建完产品/模块后，及时配置权限定义
   - 与后端团队协调，确保权限系统正确配置

4. 测试验证：
   - 创建完成后，及时启动项目验证基本功能
   - 检查导航、路由和页面加载是否正常工作

## 创建产品/模块流程

1. 在命令面板中选择 `MDT: 创建新产品` 命令
2. 选择创建类型：产品或模块
3. 输入产品/模块 ID（如 `data-analysis`，需使用短横线命名法）
4. 输入中文名称（如 "数据分析"）
5. 输入产品/模块描述
6. 等待创建完成

## 开发使用方式

### 方式一：直接在 Monorepo 中使用

如果您已经克隆了 MDT 前端 monorepo 项目，插件已包含在 `.vscode-extension` 目录中，无需从插件市场下载：

1. 打开终端，进入项目根目录
2. 执行以下命令编译插件：
   ```bash
   cd .vscode-extension/mdt-product-creator
   npm install
   npm run compile
   ```
3. 重启 VSCode 或重新加载窗口
4. 打开命令面板 (Ctrl+Shift+P / Cmd+Shift+P)
5. 输入并选择 `MDT: 创建新产品` 命令
6. 按照提示操作，完成产品/模块创建

### 方式二：从 VSCode 插件市场安装

1. 打开 VSCode
2. 点击左侧扩展图标或按 Ctrl+Shift+X (Mac 上为 Cmd+Shift+X)
3. 在搜索框中输入 "MDT Product Creator"
4. 找到插件后点击 "安装" 按钮
5. 安装完成后，打开命令面板 (Ctrl+Shift+P / Cmd+Shift+P)
6. 输入并选择 `MDT: 创建新产品` 命令
7. 按照提示操作，完成产品/模块创建

### 使用前验证

安装完成后，验证插件是否正确加载：

1. 打开 VSCode 命令面板 (Ctrl+Shift+P / Cmd+Shift+P)
2. 输入 "MDT"
3. 应该能看到 `MDT: 创建新产品` 命令

如果没有看到此命令，请检查：

- 插件是否编译成功（检查 `.vscode-extension/mdt-product-creator/out` 目录是否存在）
- VSCode 是否识别到插件（在 VSCode 中按 F1 打开命令面板，输入 "Developer: Show Running Extensions"，查看是否有 mdt-product-creator）

## 尚未完成的功能

以下功能尚未在插件中实现，需要手动完成：

1. **产品/模块的权限定义**：需要在 `shares/product-comm/src/constants/permission.ts` 中添加权限枚举值
2. **菜单的权限定义**：需要在 `UserPermissionController.ts` 中完善 `getMenuPermission` 方法
3. **BFF 注册**：需要在 `services` 目录下创建相应的 API 服务，并在 BFF 层注册路由和服务
