import {
  BaseBffService,
  batchCreateAlbum,
  deleteAlbum,
  IBatchCreateAlbumOptions,
  IDeleteAlbumOptions,
  IQueryAlbumPkgsOptions,
  IQueryAlbumsOptions,
  IQueryAlbumTagsOptions,
  IUpdateAlbumOptions,
  queryAlbumPkgsFirstPage,
  queryAlbumPkgsNextPage,
  queryAlbums,
  queryAlbumTags,
  updateAlbum,
} from '@mdtBsBffServices/services';

export class DMAlbumBffService extends BaseBffService {
  public queryAlbums(options: IQueryAlbumsOptions) {
    return queryAlbums(this.productName, options);
  }

  public batchCreateAlbum(options: IBatchCreateAlbumOptions) {
    return batchCreateAlbum(this.productName, options);
  }

  public updateAlbum(options: IUpdateAlbumOptions) {
    return updateAlbum(this.productName, options);
  }

  public deleteAlbum(options: IDeleteAlbumOptions) {
    return deleteAlbum(this.productName, options);
  }

  public queryAlbumPkgsFirstPage(options: IQueryAlbumPkgsOptions) {
    return queryAlbumPkgsFirstPage(this.productName, options);
  }

  public queryAlbumPkgsNextPage(options: IQueryAlbumPkgsOptions) {
    return queryAlbumPkgsNextPage(this.productName, options);
  }

  public queryAlbumTags(options: IQueryAlbumTagsOptions) {
    return queryAlbumTags(this.productName, options);
  }
}
