import { FC, memo } from 'react';
import { useParams } from 'react-router-dom';
import { useCreation } from 'ahooks';
import { Empty } from '@mdtBsComm/components/empty';
import { useController } from '@mdtBsComm/hooks/use-controller';
import { getModel } from '../../_util/modelUtil';
import { useAppContext } from '../../app/appContext';
import i18n from '../../languages';
import {
  AlbumPkgSearch,
  AlbumPkgSearchController,
  AlbumPkgSearchModel,
  AlbumPkgSearchModelBff,
} from '../../pages/album-pkg-search';

const AlbumPkgSearchInner: FC<{ albumId: string; name: string }> = memo(({ albumId, name }) => {
  const [controller] = useController(() => {
    const ctrl = new AlbumPkgSearchController({
      title: name,
      albumId,
      Model: getModel(AlbumPkgSearchModel, AlbumPkgSearchModelBff),
    });
    return [ctrl, null];
  });

  return <AlbumPkgSearch controller={controller} />;
});

const AlbumPkgSearchView = () => {
  const { appController } = useAppContext();
  const { id } = useParams<Record<string, string>>();

  const name = useCreation(() => {
    // TODO 应该收敛到页面级别的
    return appController.getAlbumController().getAlbumName(id);
  }, [id]);

  if (name) {
    return <AlbumPkgSearchInner key={id} albumId={id} name={name} />;
  }

  return <Empty description={i18n.chain.datamarket.themeEmptyTip} />;
};

export default AlbumPkgSearchView;
