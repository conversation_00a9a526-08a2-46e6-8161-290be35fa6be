import { FC } from 'react';
import { useObservableState } from '@mdtBsComm/hooks/use-observable-state';
import { IconButton } from '@mdtDesign/button';
import { CollapseTaggroupsList } from '@mdtProMicroModules/containers/collapse-taggroups-list';
import { TableCardDatapkgSearch } from '@mdtProMicroModules/containers/table-card-datapkg-search';
import { DrawerDatapkgDetailPreview } from '@mdtProMicroModules/pages/datapkg-detail-preview';
import { AlbumPkgSearchController } from './AlbumPkgSearchController';
import './index.less';

interface IProps {
  controller: AlbumPkgSearchController;
  open?: boolean;
}

// 子组件--是否展示右侧标签============================================================================
const TagSideWrap: FC<IProps> = ({ controller }) => {
  const open = useObservableState(() => controller.getOpenTagSide$());
  const [cls, icon] = open ? ['close-btn', 'chevron-right'] : ['open-btn', 'chevron-left'];
  return (
    <div className="right-content">
      <div className={cls} onClick={() => controller.changeOpenTagSide(!open)}>
        <IconButton type="only-icon" icon={icon} />
      </div>
      {open ? <CollapseTaggroupsList controller={controller.getTagController()} /> : null}
    </div>
  );
};

// 主题库数据包检索页面================================================================================
const AlbumPkgSearch: FC<IProps> = ({ controller }) => {
  return (
    <div className="album-pkg-search">
      <TableCardDatapkgSearch controller={controller.getTableCardController()} />
      <TagSideWrap controller={controller} />
      <DrawerDatapkgDetailPreview controller={controller.getDrawerController()} />
    </div>
  );
};

export { AlbumPkgSearch };
