import _ from 'lodash';
import { FC } from 'react';
import { useObservableState } from '@mdtBsComm/hooks/use-observable-state';
import Button from '@mdtDesign/button';
import { TableCurdWithSimpleSearch } from '@mdtProMicroModules/containers/table-curd-with-simple-search';
import { DrawerDatapkgDetailFactory } from '@mdtProMicroModules/pages/datapkg-detail-factory';
import i18n from '../../languages';
import { useQualityContext, useQualityProvider } from './qualityContext';
import QualityController from './QualityController';
import { IMenuInfo } from './TableQualityListModel';
import './index.less';

const Tag: FC<{ tag: string }> = ({ tag }) => {
  return <div className="table-quality-list_title-tag">{tag}</div>;
};

const getSelectClassName = (select: boolean) => (select ? 'table-quality-list_title-select' : '');

export const HeadTitle = () => {
  const { qualityController: controller } = useQualityContext();
  const menuInfo = useObservableState(() => controller.getMenuInfo());
  const selectMenuKey = useObservableState(() => controller.getSelectMenuKey());

  const onMenuItemClick = (item: IMenuInfo) => {
    controller.onMenuItemClick(item);
  };

  return (
    <div className="table-quality-list_title">
      {_.map(menuInfo, (item) => {
        const isSelect = selectMenuKey === item.key;
        return (
          <span key={item.key} className={getSelectClassName(isSelect)} onClick={() => onMenuItemClick(item)}>
            {item.title}
            <Tag tag={item.itemNum.toString()} />
          </span>
        );
      })}
    </div>
  );
};

export const RefreshBtn = () => {
  const { qualityController: controller } = useQualityContext();

  return (
    <Button
      leftIcon="refresh"
      type="assist-bg"
      className="table-quality-list_refresh-btn"
      onClick={() => controller.onRefreshBtnClick()}
    >
      {i18n.chain.dataFactory.monitor.refresh}
    </Button>
  );
};

interface IProps {
  controller: QualityController;
}
const QualytiControl: FC<IProps> = ({ controller }) => {
  const Provider = useQualityProvider();
  const val = { qualityController: controller };

  return (
    <>
      <Provider value={val}>
        <div className="quality-control-list">
          <TableCurdWithSimpleSearch controller={controller} />
        </div>
      </Provider>
      <DrawerDatapkgDetailFactory controller={controller.getDrawerDetailController()} />
    </>
  );
};

export default QualytiControl;
