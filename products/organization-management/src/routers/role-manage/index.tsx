import { useController } from '@mdtBsComm/hooks/use-controller';
import { AppController } from '../../app/AppController';
import { RoleManage, RoleManageController } from '../../pages/role-manage';
import { RoleManageModel } from '../../pages/role-manage/RoleManageModel';

const RoleManageView = () => {
  const [controller] = useController(() => {
    const ctrl = new RoleManageController(AppController.getInstance(), RoleManageModel);
    return [ctrl, null];
  });

  return <RoleManage controller={controller} />;
};

export default RoleManageView;
