import _ from 'lodash';
import { INode } from './BuildBpmnUtil';
import { ParseBpmn } from './ParseBpmn';

interface ITaskSpec {
  bpmn_id: string;
  bpmn_name: string;
  description: string;
  inputs: string[];
  outputs: string[];
  name: string;
  typename: string;
  default_task_spec?: string;
  event_definition?: { typename: string };
}

export class ParseBpmnProcessSpec extends ParseBpmn<ITaskSpec> {
  protected getNodes(et: any) {
    return et['task_specs'] as Record<string, ITaskSpec>;
  }

  // 解析Start节点
  protected parseStartNode(
    taskSpecs: Record<string, ITaskSpec>,
    removeIds: string[],
    existsMap: Record<string, boolean>,
  ) {
    const start = taskSpecs['Start'];
    const startNodeIds = start.outputs;
    return _.map(startNodeIds, (id) => {
      // 统计可视化节点，用于连线判断
      existsMap[id] = true;
      removeIds.push(id);
      const node = this.transformSpecToNode(taskSpecs[id]);
      node.inputs = [];
      return node;
    });
  }

  // 解析Boundary节点
  protected parseBoundaryNodes(
    taskSpecs: Record<string, ITaskSpec>,
    removeIds: string[],
    existsMap: Record<string, boolean>,
  ) {
    const boundaryNodes: INode[] = [];
    const boundarys = _.filter(
      _.groupBy(_.keys(taskSpecs), (it) => {
        return _.split(it, '.')[0];
      }),
      (it) => it.length > 1,
    );
    _.forEach(boundarys, (boundary) => {
      const splitNodeId = _.find(boundary, (it) => taskSpecs[it].typename === 'BoundaryEventSplit');
      if (!splitNodeId) return;
      const activeNodeId = _.split(splitNodeId, '.')[0];
      const spliteNode = taskSpecs[splitNodeId];
      const activeNode = taskSpecs[activeNodeId];
      const children = _.reject(spliteNode.outputs, (it) => it === activeNodeId);
      // 统计可视化节点，用于连线判断
      existsMap[activeNodeId] = true;
      const childrenNodes = _.map(children, (child) => {
        existsMap[child] = true;
        return this.transformSpecToNode(taskSpecs[child]);
      });
      const boundaryNode = this.transformSpecToNode(activeNode);
      boundaryNode.children = children;
      boundaryNode.childrenNodes = childrenNodes;
      boundaryNode.inputs = spliteNode.inputs;
      boundaryNode.outputs = _.reject(activeNode.outputs, (it) => taskSpecs[it].typename === 'BoundaryEventJoin');
      removeIds.push(...boundary, ...children);
      boundaryNodes.push(boundaryNode);
    });
    return boundaryNodes;
  }

  // 解析End节点
  protected parseEndNode(
    taskSpecs: Record<string, ITaskSpec>,
    removeIds: string[],
    existsMap: Record<string, boolean>,
  ) {
    const end = taskSpecs['End'];
    const endNodeIds = _.flatMap(end.inputs, (it) => {
      removeIds.push(it);
      return taskSpecs[it].inputs;
    });
    return _.map(endNodeIds, (id) => {
      // 统计可视化节点，用于连线判断
      existsMap[id] = true;
      removeIds.push(id);
      const node = this.transformSpecToNode(taskSpecs[id]);
      node.outputs = [];
      return node;
    });
  }

  // 解析剩余节点
  protected parseLeftNodes(
    taskSpecs: Record<string, ITaskSpec>,
    removeIds: string[],
    existsMap: Record<string, boolean>,
  ) {
    const leftNodes: Record<string, ITaskSpec> = _.omit(taskSpecs, removeIds);
    return _.map(leftNodes, (it) => {
      // 统计可视化节点，用于连线判断
      existsMap[it.bpmn_id] = true;
      return this.transformSpecToNode(it);
    });
  }

  private transformSpecToNode(taskSpec: ITaskSpec): INode {
    return {
      id: taskSpec.bpmn_id,
      label: taskSpec.bpmn_name || '',
      inputs: taskSpec.inputs,
      outputs: taskSpec.outputs,
      typename: taskSpec.typename,
      defaultTaskSpec: taskSpec.default_task_spec,
      eventDefinition: taskSpec.event_definition,
    };
  }
}
