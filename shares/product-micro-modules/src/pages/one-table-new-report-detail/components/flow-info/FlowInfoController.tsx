import _ from 'lodash';
import { FC, ReactNode } from 'react';
import { Divider } from '@metroDesign/divider';
import { Space } from '@metroDesign/space';
import { Typography } from '@metroDesign/typography';
import { BehaviorSubject, combineLatest, from } from 'rxjs';
import { IFloworkTask, IWorkflow } from '@mdtApis/interfaces';
import { formateDateByUnix } from '@mdtBsComm/utils/dayUtil';
import { getWorkflowAsync, queryFloworkTasksAsync, queryWorkflowTasksAsync } from '@mdtBsServices/flowork';
import { DbColumnTypeEnum } from '@mdtProComm/constants';
import { TransformIdToName, TransformUserIdToOrgName } from '../../../../components/transform-id-to-name';
import type { IOneTableNewOperatorDataComm } from '../../../../interfaces';
import i18n from '../../../../languages';
import {
  getSelectedCurrentLevelUsers,
  getSelectedLowerLevelOrgUsers,
  getWorkflowIsRuning,
} from '../../../../utils/oneTableNewUtil';

enum TaskStatusEnum {
  SUBMIT = 'submit',
  UNSUBMIT = 'unsubmit',
  APPROVE = 'approve',
  REJECT = 'reject',
  APPROVED = 'approved',
  REJECTD = 'rejectd',
  SEND_DOWN = 'send_down',
  TRANSFER = 'transfer',
  REINFORCE = 'reinforce',
}

export interface IFlowData {
  title: { name: string; createTime: string };
  description: ReactNode;
}

const RenderSignleUser: FC<{ id: number }> = ({ id }) => {
  return (
    <Typography.Text strong>
      <TransformIdToName id={id} type={DbColumnTypeEnum.USER_ID} />
      (<TransformUserIdToOrgName placement="right" id={id} />)
    </Typography.Text>
  );
};

const RenderListUser: FC<{ ids: number[] }> = ({ ids }) => {
  // 最多只展示50个
  const eles = _.flatMap(_.slice(_.uniq(ids), 0, 50), (id, index) => [
    <RenderSignleUser key={id} id={id} />,
    <Divider key={index} type="vertical" />,
  ]);
  eles.pop();
  return (
    <Space direction="horizontal" size={6} wrap>
      {eles}
    </Space>
  );
};

interface IDescriptionItem {
  label?: string;
  children: ReactNode;
}

const RenderDescription: FC<{ items: IDescriptionItem[] }> = ({ items }) => {
  const itemEls = _.map(items, ({ label, children }, index) => {
    return label ? (
      <Space key={index} size={8} direction="horizontal" style={{ alignItems: 'flex-start' }}>
        <div style={{ width: 'max-content' }}>{`${label}:`}</div>
        <div>{children}</div>
      </Space>
    ) : (
      <div key={index}>{children}</div>
    );
  });
  return (
    <Space direction="vertical" size={6}>
      {itemEls}
    </Space>
  );
};

interface ITaskDataItem extends IFloworkTask {
  taskUserId?: number;
}

interface IControllerOptions {
  itemData: IOneTableNewOperatorDataComm;
}

class FlowInfoController {
  private flowData$ = new BehaviorSubject<IFlowData[]>([]);
  private itemData: IOneTableNewOperatorDataComm;

  public constructor(options: IControllerOptions) {
    this.itemData = options.itemData;
  }

  public getFlowData$() {
    return this.flowData$;
  }

  public setFlowData(flowData: IFlowData[]) {
    this.flowData$.next(flowData);
  }

  public async loadData() {
    const { assignWfId } = this.itemData;
    combineLatest([
      from(getWorkflowAsync(assignWfId, { params: { with_form_data: true } })),
      from(
        // 先只展示最近的100条记录
        queryWorkflowTasksAsync(assignWfId, {
          params: {
            with_data: true,
            page_size: 100,
            orderby: '-update_time',
            executor_is_me: false,
            assign_me: false,
            status: 'completed',
            // fetch_total_count: true,
          },
        }),
      ),
      from(
        queryFloworkTasksAsync({
          params: {
            parent_workflow_id: assignWfId,
            assign_me: false,
            // fetch_total_count: true,
            executor_is_me: false,
            with_data: true,
            page_size: 100,
            status: 'completed',
            name: 'ApprovalDesk',
          },
        }),
      ),
    ]).subscribe(([rootResp, taskResp, task2Resp]) => {
      const t2 = _.map(task2Resp.data, (tk) => {
        const td = tk.data!;
        return {
          data: { ...td, command: `${td.approval_command}d` },
          executor: tk.executor,
          taskUserId: td.primary_assignee,
          update_time: tk.update_time,
        };
      });
      const taskData = _.sortBy([...(taskResp.data || []), ...t2], 'update_time');
      this.transfromToList(rootResp.data, taskData as IFloworkTask[]);
    });
  }

  public destroy() {
    this.flowData$.complete();
    this.flowData$.next([]);
  }

  private getRenderItem(titleName: string, createTime: number, items: IDescriptionItem[]): IFlowData {
    return {
      title: { name: titleName, createTime: formateDateByUnix(createTime, 2) },
      description: <RenderDescription items={items} />,
    };
  }

  private getFirstItem(rootData: IWorkflow) {
    const descriptionItems: IDescriptionItem[] = [];
    let titleName = '';
    if (this.itemData.isFormManageLevelUser) {
      titleName = i18n.chain.proMicroModules.oneTable.firstDispatch3;
      descriptionItems.push({
        label: i18n.chain.flowStatusLabel.startUser,
        children: <RenderSignleUser id={rootData.initiator} />,
      });
    } else {
      titleName = i18n.chain.proMicroModules.oneTable.firstDispatch2;
      const [mainUser, ...refources] = rootData.form_data!.input_assignees as number[];
      descriptionItems.push(
        {
          label: i18n.chain.flowStatusLabel.issuedUser,
          children: <RenderSignleUser id={rootData.initiator} />,
        },
        {
          label: i18n.chain.flowStatusLabel.mainUser,
          children: <RenderSignleUser id={mainUser} />,
        },
      );
      refources.length &&
        descriptionItems.push({
          label: i18n.chain.flowStatusLabel.refourceUser,
          children: <RenderListUser ids={refources} />,
        });
    }
    return this.getRenderItem(titleName, rootData.create_time, descriptionItems);
  }

  private getLastItem(rootData: IWorkflow) {
    if (getWorkflowIsRuning(rootData.status)) return;
    const descriptionItems: IDescriptionItem[] = [
      {
        label: i18n.chain.flowStatusLabel.endUser,
        children: <RenderSignleUser id={rootData.initiator} />,
      },
    ];
    return this.getRenderItem(i18n.chain.proMicroModules.oneTable.endFill, rootData.update_time, descriptionItems);
  }

  // 下发
  private getSendDownItem(item: ITaskDataItem, users?: number[][]) {
    if (!item.executor || !users) return;
    const signles: number[] = [];
    const list: number[][] = [];
    _.forEach(users, (us) => {
      _.size(us) === 1 ? signles.push(us[0]) : list.push(us);
    });
    const descriptionItems: IDescriptionItem[] = [];
    if (signles.length) {
      descriptionItems.push(
        {
          label: i18n.chain.flowStatusLabel.mainUser2,
          children: <RenderListUser ids={signles} />,
        },
        { children: <Divider /> },
      );
    }
    _.forEach(list, (ls) => {
      const [mainUser, ...refources] = ls;
      descriptionItems.push(
        {
          label: i18n.chain.flowStatusLabel.mainUser3,
          children: <RenderSignleUser id={mainUser} />,
        },
        {
          label: i18n.chain.flowStatusLabel.refourceUser,
          children: <RenderListUser ids={refources} />,
        },
        { children: <Divider /> },
      );
    });
    descriptionItems.pop();
    return this.getRenderItem(i18n.chain.proMicroModules.oneTable.btnAddDispatch2, item.update_time, descriptionItems);
  }

  // 协同
  private getReinforceItem(item: ITaskDataItem, users?: number[], clearExisting?: boolean) {
    if (!item.executor || !users) return;
    const [titleName, label] = clearExisting
      ? [i18n.chain.flowStatusLabel.editCollaborate, i18n.chain.flowStatusLabel.afterEdit]
      : [i18n.chain.proMicroModules.oneTable.btnAddCollector, i18n.chain.flowStatusLabel.newAdd];
    const descriptionItems: IDescriptionItem[] = [{ label: label, children: <RenderListUser ids={users} /> }];
    return this.getRenderItem(titleName, item.update_time, descriptionItems);
  }

  // 提交
  private getSubmitItem(item: ITaskDataItem) {
    if (!item.executor) return;
    const [titleName, label] = this.itemData.isNeedApproval
      ? [i18n.chain.flowStatusLabel.submitData, i18n.chain.flowStatusLabel.submintUser]
      : [i18n.chain.flowStatusLabel.submitData2, i18n.chain.flowStatusLabel.submintUser2];
    const descriptionItems: IDescriptionItem[] = [{ label: label, children: <RenderSignleUser id={item.executor} /> }];
    return this.getRenderItem(titleName, item.update_time, descriptionItems);
  }

  // 撤回提交
  private getUnsubmitItem(item: ITaskDataItem) {
    if (!item.executor) return;
    const descriptionItems: IDescriptionItem[] = [
      { label: i18n.chain.flowStatusLabel.unsubmitUser, children: <RenderSignleUser id={item.executor} /> },
    ];
    return this.getRenderItem(i18n.chain.flowStatusLabel.unsubmit, item.update_time, descriptionItems);
  }

  // 转交
  private getTransferItem(item: ITaskDataItem, user?: number) {
    if (!item.executor || !user) return;
    const descriptionItems: IDescriptionItem[] = [
      {
        children: (
          <Space size={6}>
            <RenderSignleUser id={item.executor} />
            <span>{i18n.chain.proMicroModules.oneTable.btnForword3}</span>
            <RenderSignleUser id={user} />
          </Space>
        ),
      },
    ];
    return this.getRenderItem(i18n.chain.proMicroModules.oneTable.btnForword3, item.update_time, descriptionItems);
  }

  // 提交被审核驳回
  private getRejectItem(item: ITaskDataItem, reason: string) {
    if (!item.executor) return;
    const descriptionItems: IDescriptionItem[] = [
      {
        label: i18n.chain.flowStatusLabel.approveUser,
        children: <RenderSignleUser id={item.executor} />,
      },
      {
        label: i18n.chain.flowStatusLabel.reason,
        children: <Typography.Text>{reason}</Typography.Text>,
      },
    ];
    return this.getRenderItem(i18n.chain.flowStatusLabel.reject, item.update_time, descriptionItems);
  }

  // 审核提交：驳回
  private getRejectdItem(item: ITaskDataItem, reason: string) {
    if (!item.executor || !item.taskUserId) return;
    const descriptionItems: IDescriptionItem[] = [
      {
        label: i18n.chain.flowStatusLabel.approveUser,
        children: <RenderSignleUser id={item.executor} />,
      },
      {
        label: i18n.chain.flowStatusLabel.reason,
        children: <Typography.Text>{reason}</Typography.Text>,
      },
      {
        label: i18n.chain.flowStatusLabel.taskUser,
        children: <RenderSignleUser id={item.taskUserId} />,
      },
    ];
    return this.getRenderItem(i18n.chain.flowStatusLabel.rejectd, item.update_time, descriptionItems);
  }

  // 提交被审核通过
  private getApproveItem(item: ITaskDataItem) {
    if (!item.executor) return;
    const descriptionItems: IDescriptionItem[] = [
      {
        label: i18n.chain.flowStatusLabel.approveUser,
        children: <RenderSignleUser id={item.executor} />,
      },
    ];
    return this.getRenderItem(i18n.chain.flowStatusLabel.approve, item.update_time, descriptionItems);
  }

  // 审核提交：通过
  private getApprovedItem(item: ITaskDataItem) {
    if (!item.executor || !item.taskUserId) return;
    const descriptionItems: IDescriptionItem[] = [
      {
        label: i18n.chain.flowStatusLabel.approveUser,
        children: <RenderSignleUser id={item.executor} />,
      },
      {
        label: i18n.chain.flowStatusLabel.taskUser,
        children: <RenderSignleUser id={item.taskUserId} />,
      },
    ];
    return this.getRenderItem(i18n.chain.flowStatusLabel.approved, item.update_time, descriptionItems);
  }

  private transfromToList(rootData?: IWorkflow, taskDatas?: ITaskDataItem[]) {
    if (!rootData || !taskDatas) {
      this.flowData$.next([]);
      return;
    }
    const firstItem = this.getFirstItem(rootData);
    const lastItem = this.getLastItem(rootData);
    const taskItems = taskDatas.map((item) => {
      const taskData = item.data!;
      const command = (taskData.command || taskData.approval_command) as TaskStatusEnum;

      switch (command) {
        case TaskStatusEnum.SEND_DOWN:
          return this.getSendDownItem(item, getSelectedLowerLevelOrgUsers(taskData));
        case TaskStatusEnum.REINFORCE:
          return this.getReinforceItem(item, getSelectedCurrentLevelUsers(taskData), taskData.clear_existing);
        case TaskStatusEnum.SUBMIT:
          return this.getSubmitItem(item);
        case TaskStatusEnum.UNSUBMIT:
          return this.getUnsubmitItem(item);
        case TaskStatusEnum.TRANSFER:
          return this.getTransferItem(item, _.flatten(getSelectedCurrentLevelUsers(taskData) as number[])[0]);
        case TaskStatusEnum.REJECT:
          return this.getRejectItem(item, taskData.approval_comment);
        case TaskStatusEnum.REJECTD:
          return this.getRejectdItem(item, taskData.approval_comment);
        case TaskStatusEnum.APPROVE:
          return this.getApproveItem(item);
        case TaskStatusEnum.APPROVED:
          return this.getApprovedItem(item);
        default:
          return null;
      }
    });
    const items = [firstItem, ...taskItems, lastItem].filter(Boolean);
    this.flowData$.next(items as IFlowData[]);
  }
}
export { FlowInfoController };
