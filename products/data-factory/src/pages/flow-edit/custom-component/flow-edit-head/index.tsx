// import _ from 'lodash';
import { useObservableState } from '@mdtBsComm/hooks/use-observable-state';
import { Button } from '@mdtDesign/button';
import { Dropmenu } from '@mdtDesign/dropdown';
import { Input } from '@mdtDesign/input';
import i18n from '../../../../languages';
import { useFlowEditContext } from '../../flowEditContext';
import etlIcon from './etl.svg';
import './index.less';

enum SettingEnum {
  RUN_SAMPLES_SETTING = 'run-samples-setting',
}

const EditHead = () => {
  const { flowEditController: controller } = useFlowEditContext();
  const flowMeta = useObservableState(() => controller.getFlowMeta());
  // const isRunFlow = !_.size(flowMeta.nodes);

  return (
    <div className="headContainer">
      <div className="leftBox">
        <img src={etlIcon} alt="" />
        <Input
          value={flowMeta.title}
          placeholder={i18n.chain.dataFactory.flow.noName}
          className="nameInput"
          onChange={(e) => controller.saveFlowTitle(e.target.value)}
        />
        <Button
          type="primary"
          // disabled={isRunFlow}
          onlyIcon="play"
          className="runBtn"
          onClick={() => controller.saveAndRunFlow()}
        />
        {/* <Button type="assist-bg" onlyIcon="minimize" />
        <Button type="assist-bg" onlyIcon="minimize" className="toolBtn" /> */}
      </div>
      <div className="rightBox">
        <Dropmenu
          noSelected
          renderDropNode={() => <Button onlyIcon="setting" />}
          onClickMenu={({ key }) => {
            if (key === SettingEnum.RUN_SAMPLES_SETTING) {
              controller.drawerGlobalOpenModal();
            }
          }}
          menus={[{ title: i18n.chain.dataFactory.flow.runSamplesSetting, key: SettingEnum.RUN_SAMPLES_SETTING }]}
        />
        <div className="splitLine" />
        <Button type="assist" onClick={() => controller.saveFlow()}>
          {i18n.chain.comButton.save}
        </Button>
        <div className="splitLine" />
        <Button type="assist" status="danger" className="giveUpBtn" onClick={() => controller.closeFlowEditDrawer()}>
          {i18n.chain.dataFactory.flow.giveUp}
        </Button>
        <Button type="assist" status="success" onClick={() => controller.finishFlow()}>
          {i18n.chain.dataFactory.flow.complete}
        </Button>
      </div>
    </div>
  );
};

export default EditHead;
