import _ from 'lodash';
import { from } from 'rxjs';
import { map } from 'rxjs/operators';
import { IAuditQuery, IAuditResponse } from '@mdtApis/interfaces';
import { DATE_FORMATTER_1, formateDateWithoutMillisecond } from '@mdtBsComm/utils/dayUtil';
import { queryLogsAsync, queryLogsTotalAsync } from '@mdtBsServices/audit';
import { UserModel } from '@mdtProMicroModules/models/UserModel';
import { ILogs } from './OperationLogController';

class OperationLogModel extends UserModel {
  public static queryLogsFirst = (params?: IAuditQuery) => {
    return from(queryLogsTotalAsync(params)).pipe(
      map((resp) => {
        const data = this.translateLogs(resp.data?.dataResult);
        return [resp.data?.total_count || 0, data] as [number, ILogs[]];
      }),
    );
  };

  public static queryLogsNext = (params?: IAuditQuery) => {
    return from(queryLogsAsync(params)).pipe(
      map((resp) => {
        return this.translateLogs(resp.data?.dataResult);
      }),
    );
  };

  private static translateLogs(data?: IAuditResponse[]): ILogs[] {
    return _.map(data, (item) => ({
      id: item.id,
      eventTime: formateDateWithoutMillisecond(item.event_time, DATE_FORMATTER_1),
      operationResult: item.operation_result,
      operationFrom: item.operation_from,
      resourceName: item.resource_name,
      resourceType: item.resource_type,
      appId: item.app_id,
      userId: item.user_id,
      operation: item.operation,
    }));
  }
}

export { OperationLogModel };
export type IOperationLogModel = typeof OperationLogModel;
