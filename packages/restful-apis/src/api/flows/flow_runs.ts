import { FLOW_RUNS_URL } from '../../config';
import {
  IApiResponse,
  IFlowRun,
  IFlowRunResult,
  IFlowsRunsQuery,
  IRequestPromise,
  IRequestRequestConfig,
  ITemporaryExecuteFlow,
  ITemporaryExecuteFlowResult,
} from '../../interfaces';
import request from '../../request';

// 查询全部运行记录
export const queryFlowsRuns = (config?: IRequestRequestConfig<IFlowsRunsQuery>) => {
  return request({
    ...(config || {}),
    method: 'get',
    url: FLOW_RUNS_URL,
  }) as IRequestPromise<IApiResponse<IFlowRun[]>>;
};

// 查询某个运行记录
export const getFlowRun = (flowRunId: string, config?: IRequestRequestConfig) => {
  return request({
    ...(config || {}),
    method: 'get',
    url: `${FLOW_RUNS_URL}/${flowRunId}`,
  }) as IRequestPromise<IApiResponse<IFlowRun>>;
};

// 执行某个运行记录的动作
export const runFlowRunAction = (flowRunId: string, action: 'cancel', config?: IRequestRequestConfig) => {
  return request({
    ...(config || {}),
    method: 'post',
    url: `${FLOW_RUNS_URL}/${flowRunId}/actions/${action}`,
  }) as IRequestPromise<IApiResponse<IFlowRun>>;
};

// 查询某个运行记录结果
export const getFlowRunResult = (flowRunId: string, config?: IRequestRequestConfig) => {
  return request({
    ...(config || {}),
    method: 'get',
    url: `${FLOW_RUNS_URL}/${flowRunId}/result`,
  }) as IRequestPromise<IApiResponse<IFlowRunResult>>;
};

// 临时执行一次FLOW
export const temporaryExecuteFlow = (data: ITemporaryExecuteFlow, config?: IRequestRequestConfig) => {
  return request({
    ...(config || {}),
    method: 'post',
    url: FLOW_RUNS_URL,
    data,
  }) as IRequestPromise<IApiResponse<ITemporaryExecuteFlowResult>>;
};
