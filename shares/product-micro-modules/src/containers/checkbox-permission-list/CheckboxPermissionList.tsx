import { FC } from 'react';
import { LeftAnchor } from './components/left-anchor';
import { RightPermissions } from './components/right-permissions';
import { CheckboxPermissionListController } from './CheckboxPermissionListController';
import { useCheckboxPermissionListContextProvider } from './checkPermissionListContext';
import './index.less';

// 权限==========================================================================================
interface IProps {
  controller: CheckboxPermissionListController;
}

const CONTAINER_ID = 'permission-inner';

const CheckboxPermissionList: FC<IProps> = ({ controller }) => {
  const Provider = useCheckboxPermissionListContextProvider();
  const value = { checkboxPermissionListController: controller };
  return (
    <Provider value={value}>
      <div className="module_checkbox-permission-list" id={CONTAINER_ID}>
        <LeftAnchor containerId={CONTAINER_ID} />
        <RightPermissions />
      </div>
    </Provider>
  );
};

export { CheckboxPermissionList };
