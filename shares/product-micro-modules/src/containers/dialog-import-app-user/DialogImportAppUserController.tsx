import { FC } from 'react';
import { ModalWithBtnsCompDialogController } from '@mdtBsComponents/modal-with-btns-comp-dialog';
import { DropzoneProps } from '@mdtDesign/dropzone';
import i18n from '../../languages';
import { DialogImportAppUserInner } from './DialogImportAppUserInner';

export interface IControllerOptions {
  uploadProps?: DropzoneProps;
  downloadUrl: string;
}
class DialogImportAppUserController extends ModalWithBtnsCompDialogController {
  private uploadProps?: DropzoneProps;
  private downloadUrl: string;
  public constructor(options: IControllerOptions) {
    super({
      modalCompOptions: {
        modalOptions: () => this.initModalOptions(),
      },
      InnerView: DialogImportAppUserInner as FC,
    });
    this.downloadUrl = options.downloadUrl;
    this.uploadProps = options.uploadProps;
  }

  public destroy() {
    super.destroy();
    this.uploadProps = null!;
    this.downloadUrl = '';
  }

  public getUploadProps() {
    return this.uploadProps;
  }

  public downloadTemplate = () => {
    window.location.href = this.downloadUrl;
  };

  private initModalOptions() {
    return {
      title: i18n.chain.proMicroModules.orgList.importUser,
      width: 480,
      footer: () => null,
    };
  }
}

export { DialogImportAppUserController };
