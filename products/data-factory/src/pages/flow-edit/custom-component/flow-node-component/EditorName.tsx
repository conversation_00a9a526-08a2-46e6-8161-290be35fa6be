import { ChangeEvent, FC, useEffect, useRef } from 'react';
import { BehaviorSubject } from 'rxjs';
import Play2 from '@mdtDesign/icons/play-2';
import Input from '@mdtDesign/input';
import { TooltipText } from '@mdtDesign/tooltip';
import { RunNodeActionEnum } from '../../_util/enum';
import { IRunNodeData } from '../../_util/interfaces';

interface IProps {
  id: string;
  title: string;
  className: string;
  editingTitle?: boolean;
  isRunNode$?: BehaviorSubject<boolean | IRunNodeData>;
  enableRun?: boolean;
}

const EditorName: FC<IProps> = (props) => {
  const { id, title, className, enableRun, isRunNode$, editingTitle } = props;
  const ref = useRef<any>(null);

  const notifyLabel = (newLabel: string) => {
    isRunNode$?.next({
      id,
      action: RunNodeActionEnum.UPDATA_LABEL,
      data: { label: newLabel || title, editingTitle: false },
    });
  };

  const onBlur = (e: ChangeEvent<HTMLInputElement>) => {
    notifyLabel(e.target.value.trim());
  };

  const onPressEnter = (e: KeyboardEvent) => {
    e.preventDefault();
    e.stopPropagation();
    notifyLabel(ref.current.input.value.trim());
  };

  useEffect(() => {
    editingTitle && ref.current?.focus();
  }, [editingTitle]);

  const rn =
    enableRun && isRunNode$ ? (
      <div className="node-play-icon" onClick={() => isRunNode$.next(true)}>
        <Play2 />
      </div>
    ) : null;

  const child = editingTitle ? (
    <Input
      size="compact"
      ref={ref}
      style={{ marginLeft: '-12px' }}
      defaultValue={title}
      onBlur={onBlur}
      onPressEnter={onPressEnter}
    />
  ) : (
    <>
      <div className="node-label">
        <TooltipText text={title} />
      </div>
      {rn}
    </>
  );

  return <div className={`node-container ${className}`}>{child}</div>;
};

export { EditorName };
