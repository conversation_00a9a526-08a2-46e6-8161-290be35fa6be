import { FC, Suspense } from 'react';
import { Spin } from '@metroDesign/spin';
import { AmisRender } from '@mdtProMicroModules/amis-expand-prod/AmisRender';
import { DatlasAppController } from '@mdtProMicroModules/datlas/app/DatlasAppController';
import '@mdtProMicroModules/amis-expand-prod/importStyle';
import i18n from '../languages';

// 骨架屏体验更优
const Fallback = () => (
  <div style={{ width: '100%', height: '100%' }}>
    <Spin fillParent tip={i18n.chain.comWelcome} />
  </div>
);

const AppPageRender: FC<{ item: any }> = ({ item }) => {
  if (item.View) {
    return (
      <Suspense fallback={<Fallback />}>
        <item.View />
      </Suspense>
    );
  }

  if (item.amisjson) {
    // 按需提供环境变量
    return <AmisRender schema={item.amisjson as any} />;
  }

  if (item.innerurl) {
    const url = DatlasAppController.getInstance()!.getProductAuthUrl(item.innerurl, item.params);
    return <iframe frameBorder={0} src={url} width="100%" height="100%" sandbox={undefined} />;
  }

  if (item.url) {
    return <iframe frameBorder={0} src={item.url} width="100%" height="100%" sandbox={undefined} />;
  }

  return <div>not support(only support View,amisjson,url)</div>;
};

export default AppPageRender;
