import { FC } from 'react';
import { FormField } from '@mdtBsComm/components/form';
import { FormComp } from '@mdtBsComponents/form-comp';
import Input from '@mdtDesign/input';
import Select from '@mdtDesign/select';
import i18n from '../../languages';
import { DialogGeneratePkgFormController, FormDataAttrs } from './DialogGeneratePkgFormController';
import './index.less';

interface IProps {
  controller?: DialogGeneratePkgFormController;
}
// 编辑数据库连接信息表单================================================================================
export const DialogGeneratePkgFormInner: FC<IProps> = (props: IProps) => {
  const controller = props.controller!;

  return (
    <div className="module_dialog-generate-pkg-form">
      <FormComp controller={controller.getFormCompController()}>
        <FormField
          name={FormDataAttrs.NAME}
          label={i18n.chain.proMicroModules.datapkg.datapkgName}
          required={true}
          rules={[{ required: true, message: i18n.chain.proMicroModules.datapkg.datapkgNameEmptyError }]}
        >
          <Input placeholder={i18n.chain.comPlaceholder.input} allowClear={false} block />
        </FormField>
        <FormField
          name={FormDataAttrs.TYPE}
          label={i18n.chain.proMicroModules.dataSearch.dataType}
          required={true}
          rules={[{ required: true, message: i18n.chain.proMicroModules.dataSearch.dataTypeEmptyError }]}
        >
          <Select options={controller.getGeometryOptions()} block />
        </FormField>
      </FormComp>
    </div>
  );
};
