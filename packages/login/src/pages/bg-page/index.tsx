import { FC } from 'react';
import { GlobalContext } from '../../containers/global-context';
import { LanguageSetting } from '../../containers/language-setting';
import { LoginLogo } from '../../containers/login-logo';
import { LoginStyle } from '../../containers/login-style';
import { Panel } from '../../containers/panel';

interface IProps {
  className?: string;
  didMount?: Function;
}

const Content: FC<IProps> = ({ className, children }) => {
  return (
    <>
      <LoginStyle className={className}>{children}</LoginStyle>
      <LoginLogo />
      <LanguageSetting />
    </>
  );
};

const BgPage: FC<IProps> = ({ className, didMount, children }) => {
  const cls = ['mdt-login-page'];
  className && cls.push(className);
  return (
    <Panel didMount={didMount}>
      <GlobalContext>
        <Content className={className}>{children}</Content>
      </GlobalContext>
    </Panel>
  );
};

BgPage.displayName = 'BgPage';
export { BgPage };
