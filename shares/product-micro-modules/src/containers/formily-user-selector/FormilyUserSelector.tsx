import _ from 'lodash';
import { useEffect, useRef } from 'react';
import { connect } from '@formily/react';
import { useCreation, useSetState } from 'ahooks';
import { skip } from 'rxjs/operators';
import { DatlasAppController } from '../../datlas/app/DatlasAppController';
import { ITransferPanelControllerOptions, TransferPanel, TransferPanelController } from '../transfer-panel';
import { ISelectorItem, IUserLazySelectorModel, UserLazySelectorModel } from '../user-lazy-selector';

export type IFormilyUserSelectorProps = ITransferPanelControllerOptions & {
  value?: ISelectorItem[];
  onChange?: (value: ISelectorItem[]) => void;
  getUserSelectorModel?: () => IUserLazySelectorModel;
  disabled?: boolean;
  className?: string;
  uniqueType?: string;
};

let defaultUserSelectorMode: IUserLazySelectorModel;

export const FormilyUserSelector = connect((props: IFormilyUserSelectorProps) => {
  const getDefaultModel = () => {
    if (defaultUserSelectorMode) return defaultUserSelectorMode;
    defaultUserSelectorMode = new UserLazySelectorModel({
      appId: DatlasAppController.getInstance().getAppId(),
      isDataAuthFromUser: true,
    });
    return defaultUserSelectorMode;
  };

  const {
    value,
    onChange,
    getUserSelectorModel = getDefaultModel,
    disabled = false,
    userSelectorModel,
    className,
    ...rest
  } = props;

  const onChangeRef = useRef<any>();
  onChangeRef.current = onChange;
  const [state, setState] = useSetState<any>(value);
  const [changedCount, setChangedCount] = useSetState<any>(0);

  const controller = useCreation(() => {
    const controller = new TransferPanelController({
      data: value,
      userSelectorModel: userSelectorModel || getUserSelectorModel(),
      ...rest,
    });

    controller
      .getSelectedItems$()
      .pipe(skip(1))
      .subscribe((values) => {
        setState(values);
        onChangeRef.current?.(values);
      });
    return controller;
  }, [rest.uniqueType]);

  useEffect(() => {
    if (!_.isEqual(state, value)) {
      controller.getSelectedItems$().next(value || []);
      setChangedCount(changedCount + 1);
    }
  }, [value, controller]);

  return useCreation(() => {
    return <TransferPanel controller={controller} disabled={disabled} className={className} />;
  }, [controller]);
});
