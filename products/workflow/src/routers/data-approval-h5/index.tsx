import { useController } from '@mdtBsComm/hooks/use-controller';
import { WorkflowDataApprovalModel } from '@mdtProMicroModules/pages/workflow-data-approval/WorkflowDataApprovalModel';
import { WorkflowDataApprovalModelBff } from '@mdtProMicroModules/pages/workflow-data-approval/WorkflowDataApprovalModelBff';
import {
  WorkflowDataApprovalH5,
  WorkflowDataApprovalH5Controller,
} from '@mdtProMicroModules/pages/workflow-data-approval-h5';
import { getModel } from '../../_util/modelUtil';

const DataApprovalViewH5 = () => {
  const [controller] = useController(() => {
    const ctrl = new WorkflowDataApprovalH5Controller({
      Model: getModel(WorkflowDataApprovalModel, WorkflowDataApprovalModelBff),
    });
    return [ctrl, null];
  });

  return <WorkflowDataApprovalH5 controller={controller} />;
};

export default DataApprovalViewH5;
