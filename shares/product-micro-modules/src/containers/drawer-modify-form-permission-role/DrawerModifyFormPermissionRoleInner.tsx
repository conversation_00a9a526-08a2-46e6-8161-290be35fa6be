import { FormField } from '@mdtBsComm/components/form';
import Input, { Textarea } from '@mdtDesign/input';
import i18n from '../../languages';
import { FormDataAttrs } from './DrawerModifyFormPermissionRoleController';

// 编辑角色的表单组件==================================================================================
export function DrawerModifyFormPermissionRoleInner() {
  return (
    <>
      <FormField
        name={FormDataAttrs.NAME}
        label={i18n.chain.proMicroModules.role.roleName}
        required={true}
        direction="column"
        rules={[{ required: true, message: i18n.chain.proMicroModules.role.roleNameEmptyError }]}
      >
        <Input allowClear={false} block />
      </FormField>
      <FormField name={FormDataAttrs.DESCRIPTION} label={i18n.chain.proMicroModules.role.roleDesc} direction="column">
        <Textarea autoSize={{ minRows: 6, maxRows: 24 }} block />
      </FormField>
    </>
  );
}
