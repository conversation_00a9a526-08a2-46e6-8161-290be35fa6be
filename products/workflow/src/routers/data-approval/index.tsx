import { useController } from '@mdtBsComm/hooks/use-controller';
import {
  WorkflowDataApproval,
  WorkflowDataApprovalController,
  WorkflowDataApprovalModel,
} from '@mdtProMicroModules/pages/workflow-data-approval';
import { WorkflowDataApprovalModelBff } from '@mdtProMicroModules/pages/workflow-data-approval/WorkflowDataApprovalModelBff';
import { getModel } from '../../_util/modelUtil';

const DataApprovalView = () => {
  const [controller] = useController(() => {
    const ctrl = new WorkflowDataApprovalController({
      Model: getModel(WorkflowDataApprovalModel, WorkflowDataApprovalModelBff),
    });
    return [ctrl, null];
  });

  return <WorkflowDataApproval controller={controller} />;
};

export default DataApprovalView;
