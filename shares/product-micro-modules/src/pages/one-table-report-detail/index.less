.one-table-report-detail-page {
  height: 100%;

  .metro-tabs-tabpane {
    width: 100%;
    height: 100%;
  }

  .metro-tabs-content-holder {
    height: 100%;
  }

  .page_datapkg-data-preview_table {
    .com_right-click-folder-menu_overlay {
      height: calc(100% - 40px);
    }

    .metro-pagination {
      margin-top: 6px;
      text-align: right;
    }
  }
}

.one-table-report-detail-h5-page {
  .page_datapkg-data-preview_tool-bar {
    display: none;
  }

  .metro-tabs-nav-list {
    justify-content: space-around;
    width: 100%;
  }

  .dmc-one-table-flow-block .accept-order-btns-block {
    top: auto;
    right: 0;
    bottom: 0;
    left: 0;
    display: flex;
    padding: 16px;
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
    background: var(--metro-bg-0);
  }

  .dmc-one-table-flow-block .accept-order-btns-block .metro-btn {
    flex: 1;
  }

  .dmc-one-table-data-block .data-btns-wrap {
    top: auto;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1;
    display: flex;
    margin-bottom: constant(safe-area-inset-bottom);
    margin-bottom: env(safe-area-inset-bottom);
    padding: 16px;
    background: var(--metro-bg-0);

    .metro-space-item {
      flex: 1;
    }

    .metro-btn {
      width: 100%;
    }
  }

  .rdg-cell-selected {
    box-shadow: none;
  }

  .dmc-one-table-data-block {
    height: 90%;
  }
}
