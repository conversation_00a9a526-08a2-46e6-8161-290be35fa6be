import { HelpOutlined } from '@metro/icons';
import { drawerApi } from '@metroDesign/drawer';
import { Modal } from '@metroDesign/modal';
import { getQlang } from '@sql-generator/sql-builder-mdt';
import { getRunQlang } from '@sql-generator/sql-editor-mdt';
import { SqlBuilder } from '@mdtProMicroModules/components/sql-builder';
import { SqlEditor } from '@mdtProMicroModules/components/sql-editor';
import i18n from '../../../languages';
import { IFlowNodeParamsFromQLangRaw } from './interfaces';

export const openLowCodeQeEditor = (
  data: IFlowNodeParamsFromQLangRaw,
  sureConfirm: (config: Record<string, any>) => void,
) => {
  const openLowCodeDrawer = () => {
    let storeRef: any = null;
    const onConfirm = (onClose?: () => void) => {
      const config = storeRef?.getConfig();
      onClose?.();
      config && sureConfirm({ _fparams_: config, query: getQlang(config) });
    };

    drawerApi.open({
      placement: 'right',
      width: '100vw',
      maskClosable: false,
      destroyOnClose: true,
      title: i18n.chain.dataFactory.selectWriteLowCode,
      onConfirm: onConfirm,
      closable: false,
      operatorsTarget: 'extra',
      push: false,
      keyboard: false,
      children: () => {
        return (
          <SqlBuilder
            config={data._fparams_ as any}
            ref={(ref) => {
              storeRef = ref;
            }}
          />
        );
      },
    });
  };

  const openHightCodeDrawer = () => {
    drawerApi.open({
      placement: 'right',
      width: '100vw',
      maskClosable: false,
      destroyOnClose: true,
      closable: false,
      footer: null,
      push: false,
      keyboard: false,
      bodyStyle: { padding: 0 },
      children: (onClose) => {
        return (
          <SqlEditor
            onClose={onClose}
            data={{ sql: data.query }}
            onConfirm={(val) => {
              onClose();
              sureConfirm({ query: getRunQlang(val.sql) });
            }}
          />
        );
      },
    });
  };

  if (data.query && data._fparams_) {
    // 编辑器编辑
    return openLowCodeDrawer();
  }

  if (data.query) {
    return openHightCodeDrawer();
  }

  Modal.confirm({
    title: i18n.chain.dataFactory.selectWriteQeType,
    icon: <HelpOutlined />,
    okText: i18n.chain.dataFactory.selectWriteLowCode,
    cancelText: i18n.chain.dataFactory.selectWriteHightCode,
    onCancel: openHightCodeDrawer,
    onOk: openLowCodeDrawer,
  });
};
