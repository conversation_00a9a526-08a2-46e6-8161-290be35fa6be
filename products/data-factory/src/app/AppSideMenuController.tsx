import { DataNode } from '@mdtBsComm/components/side-menu';
import { CheckBox, FormatAlignLeft2 } from '@mdtDesign/icons';
import { DatlasAppSideMenuController } from '@mdtProMicroModules/datlas/app-side-menu';
import { RoutePathEnum } from '../_util/constants';
import i18n from '../languages';

class AppSideMenuController extends DatlasAppSideMenuController {
  public async initMenus() {
    const app = this.app!;
    const psc = app.getUserPermissionController();
    const menuPs = psc.getMenuPermission();

    const menus: DataNode[] = [];
    if (menuPs.enableEtl) {
      menus.push(
        {
          key: RoutePathEnum.MONITOR,
          title: i18n.chain.dataFactory.menu.monitor,
          className: 'top-level',
          icon: <CheckBox size={16} />,
        },
        {
          key: RoutePathEnum.FLOWS,
          title: i18n.chain.dataFactory.menu.etl,
          className: 'top-level',
          icon: <FormatAlignLeft2 size={16} />,
        },
      );
    }
    this.changeMenuData(menus);
  }
}

export { AppSideMenuController };
