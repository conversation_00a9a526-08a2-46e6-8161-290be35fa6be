import { BaseUserPermissionController } from '@mdtProComm/controllers/BaseUserPermissionController';

// 权限key列表
export enum DataFactoryPermissionEnum {
  // ----------------菜单权限----------------
  // 数据搜索
  MENU_ETL = 'low_code_etl',

  // ----------------操作权限----------------
  // 质量监控
}

class UserPermissionController extends BaseUserPermissionController {
  // 菜单
  public getMenuPermission() {
    return {
      enableEtl: this.checkUserPermission(DataFactoryPermissionEnum.MENU_ETL),
    };
  }
}

export { UserPermissionController };
