body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', "'Roboto'", "'Oxygen'", "'Ubuntu'", '"Cantarell"',
    'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New', monospace;
}

#root,
body,
html {
  width: 100%;
  height: 100%;
}

*,
::after,
::before {
  box-sizing: border-box;
  outline: none;
}

.mdt-login-collector-layout {
  display: flex;
  align-items: center;
  height: 100%;
}

.mdt-login-title {
  margin-bottom: 8px;
  color: var(--mdt-login-text-color);
  font-weight: 500;
  font-size: 24px;
  line-height: 34px;
  white-space: nowrap;
}

.mdt-login-title-container {
  justify-content: unset;
}

.mdt-login-desc {
  color: var(--mdt-login-text-dim-color);
  font-weight: 500;
  font-size: 14px;
  line-height: 20px;
}

// 监听档位
@media screen and (max-width: 1080px) {
  .mdt-login-collector-tabs-nav-autohide .mdt-login-tabs-nav-wechat,
  .mdt-login-collector-tabs-nav-autohide .mdt-login-tabs-nav-dingtalk,
  .mdt-login-collector-tabs-nav-autohide .mdt-login-tabs-nav-zzding {
    display: none;
  }
}
