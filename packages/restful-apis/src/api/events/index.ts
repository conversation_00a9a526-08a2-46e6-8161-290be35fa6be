import { EVENTS_URL } from '../../config';
import {
  IApiResponse,
  IEventsSubscriptionsParams,
  IEventsSubscriptionsPatchParams,
  IEventsSubscriptionsQuery,
  IEventsSubscriptionsResponse,
  IRequestPromise,
  IRequestRequestConfig,
} from '../../interfaces';
import request from '../../request';

export const queryEventsSubscriptions = (
  query: IEventsSubscriptionsQuery,
  config?: IRequestRequestConfig<IEventsSubscriptionsQuery>,
) => {
  return request({
    ...(config || {}),
    method: 'get',
    url: `${EVENTS_URL}/subscriptions`,
    params: { ...config?.params, ...query },
  }) as IRequestPromise<IApiResponse<IEventsSubscriptionsResponse[]>>;
};

export const postEventsSubscriptions = (
  data: IEventsSubscriptionsParams,
  config?: IRequestRequestConfig<IEventsSubscriptionsParams>,
) => {
  return request({
    ...(config || {}),
    method: 'post',
    url: `${EVENTS_URL}/subscriptions`,
    data,
  }) as IRequestPromise<IApiResponse<IEventsSubscriptionsResponse[]>>;
};

export const patchEventsSubscriptions = (
  eventId: string,
  data?: IEventsSubscriptionsPatchParams,
  config?: IRequestRequestConfig<IEventsSubscriptionsPatchParams>,
) => {
  return request({
    ...(config || {}),
    method: 'patch',
    url: `${EVENTS_URL}/subscriptions/${eventId}`,
    data,
  }) as IRequestPromise<IApiResponse<IEventsSubscriptionsResponse[]>>;
};

export const deleteEventsSubscriptions = (
  eventId: string,
  config?: IRequestRequestConfig<IEventsSubscriptionsPatchParams>,
) => {
  return request({
    ...(config || {}),
    method: 'delete',
    url: `${EVENTS_URL}/subscriptions/${eventId}`,
  }) as IRequestPromise<IApiResponse<IEventsSubscriptionsResponse[]>>;
};
