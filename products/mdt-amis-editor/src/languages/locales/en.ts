import { en as microEn } from '@mdtProMicroModules/languages';
import { Locale } from './cn';

const en: Locale = {
  ...microEn,
  edit: 'Edit',
  preview: 'Preview',
  fullEdit: 'Fullscreen',
  editorTitle: 'JSON Visual Editor',
  qlang: 'QLang',
  lowcodeQlang: 'Low-code QLang',
  datapkg: 'Data Package',
  custom: 'Custom',
  requestType: 'Request Type',
  requestPlaceholder: 'Please select a type',
  queryData: 'Query Data',
  addData: 'Add Data',
  updateData: 'Update Data',
  deleteData: 'Delete Data',
  selectDataPkg: 'Select Data Package',
  actionSettings: 'Action Settings',
  selectAction: 'Please select an action',
  qeEditor: 'Qe Low-code Editor',
  qeBuilder: 'Qe Builder',
  full: 'Fullscreen',
  lowcodeSql: 'Low-code Sql',
  previewEdit: 'Preview Mode Editing',
  sqlDep: 'Sql Dependency',
  requestAdaptation: 'Request Adaptation',
  apiSettingTitle: 'Api Quick Settings',
  formError: 'Please check if the form is filled out correctly',
  commonSetting: 'Common Settings',
  advanceSetting: 'Advanced Settings',
  noDepText: 'No dependencies',
  cnName: '简体中文',
  enName: 'English',
  userInfo: 'User Information',
  userId: 'User ID',
  uuid: 'UUID',
  userName: 'Username',
  email: 'Email',
  phone: 'Phone',
  expireTime: 'User Expire Time',
  appInfo: 'App Information',
  appId: 'App ID',
  appName: 'App Name',
  appExpireTime: 'App Expire Time',
  browserVar: 'Browser Variables',
  jsonComp: {
    commonLayout: 'Common Layout',
    leftRightEqually: 'Left-Right Equally',
    leftRightEquallyDesc: 'Common layout: Left-right equally layout (based on CSS Flex layout container).',
    stickyContainer: 'Sticky Container',
    stickyDescription: 'Common layout: Sticky container (based on CSS Flex layout container).',
    stickyTopContainer: 'Sticky Top Container',
    stickyTopDescription: 'Common layout: Sticky top container (based on CSS Flex layout container).',
    floatingContainer: 'Floating Container',
    floatingDescription: 'Common layout: Floating container (based on CSS Flex layout container).',
    scrollXContainer: 'Scroll X Container',
    scrollXDescription: 'Scroll X Container: Based on CSS Flex layout container.',
    scrollYContainer: 'Scroll Y Container',
    scrollYDescription: 'Scroll Y Container: Based on CSS Flex layout container.',
    layout1_1_1_v2: 'Top Middle Bottom',
    layout1_1_1_v2Description: 'Common layout: Top Middle Bottom layout (based on CSS Flex layout container).',
    layout1_1_1: 'Three Columns Evenly',
    layout1_1_1Description: 'Common layout: Three columns evenly layout (based on CSS Flex layout container).',
    layout1_1_v2: 'Top and Bottom Layout',
    layout1_1_v2Description: 'Common layout: Top and bottom layout (based on CSS Flex layout container).',
    layout1_2_3: '1:2:3 Three Columns',
    layout1_2_3Description: 'Common layout: 1:2:3 Three columns layout (based on CSS Flex layout container).',
    layout1_2_v2: 'One Drag Two',
    layout1_2_v2Description: 'Common layout: One drag two layout (based on CSS Flex layout container).',
    layout1_2_v3: 'Left One Right Two',
    layout1_2_v3Description: 'Common layout: Left one right two layout (based on CSS Flex layout container).',
    layout1_2_v4: 'Classic Layout',
    layout1_2_v4Description: 'Common layout: Classic layout (based on CSS Flex layout container).',
    layout1_2: '1:2 Layout',
    layout1_2Description: 'Common layout: 1:2 layout (based on CSS Flex layout container).',
    layout1_3: '1:3 Layout',
    layout1_3Description: 'Common layout: 1:3 layout (based on CSS Flex layout container).',
    layout2_1_v2: 'Two Drag One',
    layout2_1_v2Description: 'Common layout: Two drag one layout (based on CSS Flex layout container).',
    layout2_1_v3: 'Left Two Right One',
    layout2_1_v3Description: 'Common layout: Left two right one layout (based on CSS Flex layout container).',
    flexContainer: 'Flex Container',
    flexContainerDescription:
      'Flex Container is a layout effect based on CSS Flex, which is more controllable for the position of child nodes than Grid and HBox, and easier to use than using CSS classes.',
    flexContainerAttributeTitle: 'Attributes',
    flexContainerBasicTitle: 'Basic',
    flexContainerAdvancedTitle: 'Advanced',
    flexContainerAppearanceTitle: 'Appearance',
    flexContainerBaseChildren: 'Children Nodes Collection',
    flexContainerShortcutLayoutSetting: 'Shortcut Layout Setting',
    flexContainerInnerAlignmentSetting: 'Inner Alignment Setting',
    flexContainerRowGap: 'Row Gap',
    flexContainerColumnGap: 'Column Gap',
    topInsertLayoutContainer: 'Insert Layout Container Above',
    bottomInsertLayoutContainer: 'Insert Layout Container Below',
    addFlexColumnElement: 'Add Flex Column Element',
    insertBelowNewRow: 'Insert New Row Below',
    insertAboveNewRow: 'Insert New Row Above',
    heightSetting: 'Height Setting',
    widthSetting: 'Width Setting',
    elasticHeight: 'Elastic Height',
    elasticWidth: 'Elastic Width',
    fixedHeight: 'Fixed Height',
    fixedWidth: 'Fixed Width',
    firstCol: 'First Column',
    secondCol: 'Second Column',
    thirdCol: 'Third Column',
    background: 'Background',
    up: 'Up',
    down: 'Down',
    left: 'Left',
    right: 'Right',
    insertColContainer: 'Insert Column Container',
    layout_fixed: 'Fixed Container',
    layout_fixedDescription: 'Fixed Container: Special layout container based on CSS Fixed.',
    layout_fixed_top: 'Sticky Container',
    layout_fixed_topDescription: 'Sticky Container: Can be set to stick to the top or bottom.',
    mdtUpload: 'MDT Upload File',
    mdtUploadDesc: 'Used for uploading and displaying MDT files',
    mdtComponent: 'MDT Component',
    valChange: 'Value Change',
    valChangeDesc: 'Triggered when the value of the uploaded file changes (will also trigger on upload failure)',
    data: 'Data',
    dataTitle: 'Uploaded File',
    removeFile: 'Remove File',
    removeFileDesc: 'Triggered when removing a file',
    removeFileDesc2: 'File removed',
    uploadSuccessDesc: 'Triggered when the file upload is successful',
    uploadSuccessDesc2: 'Response data returned after a successful remote upload request',
    uploadFailDesc: 'Triggered when the file upload fails',
    uploadFailDesc2: 'Error message returned after a failed remote upload request',
    clearData: 'Clear Data',
    clearDataDesc: 'Clear selected files',
    setValue: 'Set Value',
    setValueDesc: 'Trigger component data update',
    basic: 'Basic',
    limitSize: 'Limit Single File Size',
    compressImage: 'Enable Image Compression',
    limitSizeMsg: 'Error message for exceeding size limit',
    caremaText: 'Mobile Camera',
    watermarkLabel1: 'Add Username and ID',
    wartermarkLabel2: 'Date',
    minCount: 'Minimum Upload Count',
    maxCount: 'Maximum Upload Count',
    fileType: 'File Type',
    fileTypeDesc: 'Please enter the file suffixes, separate multiple types with <code>,</code>',
    style: 'Appearance',
    styleDesc: 'Description',
    styleDesc2: 'Select Button',
    styleDesc3: 'Upload Button',
    event: 'Event',
    uploadSuccess: 'Upload Success',
    uploadFail: 'Upload Fail',
    property: 'Property',
    uniqueKey: 'Unique Key',
    columnSelectPlaceholder: 'Default: Use "id" as the unique key',
    columnSelectTip:
      'By default, the unique key will be automatically filled based on the selected package. Once confirmed, the unique key will no longer be automatically filled. To re-fetch the unique key when switching packages, please clear the current value and re-select the package.',
  },
};

export default en;
