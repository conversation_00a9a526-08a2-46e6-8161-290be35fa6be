import { createLocales } from '@designable/core';

export const Upload = {
  'zh-CN': {
    title: '上传',
    settings: {
      'x-component-props': {
        listType: {
          title: '上传类型',
          dataSource: ['拖拽', '按钮'],
        },
        btnTitle: '按钮文字',
        accept: '可接受类型',
        multiple: '多选模式',
        maxSize: '最大尺寸',
        minSize: '最小尺寸',
        tip: '上传文案',
        desc: '描述文案',
        textContent: '点击或拖拽文件到这个区域上传',
      },
    },
  },
  'en-US': {
    title: 'Upload',
    settings: {
      'x-component-props': {
        listType: {
          title: 'upload type',
          dataSource: ['drag', 'button'],
        },
        btnTitle: 'button title',
        accept: 'Accept',
        multiple: 'Multiple',
        maxSize: 'Max Size',
        minSize: 'Min Size',
        method: 'Method',
        tip: 'tip',
        desc: 'description',
        textContent: 'Click or drag file to this area to upload',
      },
    },
  },
};

export const UploadDragger = createLocales(Upload, {
  'zh-CN': {
    title: '拖拽上传',
    settings: {
      'x-component-props': {},
    },
  },
  'en-US': {
    title: 'UploadDragger',
    settings: {
      'x-component-props': {},
    },
  },
});
