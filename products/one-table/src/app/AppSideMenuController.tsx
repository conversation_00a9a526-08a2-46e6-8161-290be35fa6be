import _ from 'lodash';
import { OneTableRoutePathEnum } from '@mdtProComm/constants/route';
import { DatlasAppSideMenuController } from '@mdtProMicroModules/datlas/app-side-menu';
import { AppController } from './AppController';

class AppSideMenuController extends DatlasAppSideMenuController {
  public async initMenus() {
    const { items, openKeys } = this.getMenuConfig();
    const permissionMenus = this.getPermissionMenu(items);

    this.changeMenuData(permissionMenus);
    this.changeOpenKeys(openKeys);
  }

  private getMenuConfig() {
    return (this.app as AppController).getAppMenuConfig();
  }

  private getPermissionMenu(menuItems: any[] = []) {
    const { enableFormManagement, enableMyData, enableGrantedForm } =
      this.app!.getUserPermissionController().getMenuPermission();
    const menuPermissionMap = {
      [OneTableRoutePathEnum.FORM_MANAGEMENT]: enableFormManagement,
      [OneTableRoutePathEnum.NEW_GRANTED_ORG_FORM]: enableGrantedForm,
      [OneTableRoutePathEnum.NEW_GRANTED_FORM]: enableGrantedForm,
      '/inner-md': enableMyData,
    };
    return _.filter(menuItems, (menu) => !(menu.key in menuPermissionMap) || menuPermissionMap[menu.key]);
  }
}

export { AppSideMenuController };
