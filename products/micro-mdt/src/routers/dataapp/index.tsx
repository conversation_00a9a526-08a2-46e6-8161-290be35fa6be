import { useEffect } from 'react';
import { ModuleIdEnum } from '@mdtProComm/constants';
import { useSubModule } from '../../_util/useSubModule';
import { useAppContext } from '../../app/appContext';

const DataAppView = () => {
  const { appController } = useAppContext();

  useEffect(() => {
    appController.jumpToProductDataMap(undefined, { replace: true });
  }, [appController]);

  return null;
};

const DataApp = () => {
  const subModule = useSubModule(ModuleIdEnum.DATA_APP_IFRAME);

  return subModule || <DataAppView />;
};

export default DataApp;
