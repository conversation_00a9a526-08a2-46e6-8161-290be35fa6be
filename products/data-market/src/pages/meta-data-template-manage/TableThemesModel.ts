import _ from 'lodash';
import { forkJoin, from, Observable, of } from 'rxjs';
import { map } from 'rxjs/operators';
import { IBusinessResult } from '@mdtBsComm/interfaces';
import { deleteAlbumAsync, patchAlbumAsync, postAlbumAsync, statsAlbumsAsync } from '@mdtBsServices/albums';
import { IAlbum } from '@mdtBsServices/interfaces';
import { IFormData } from '@mdtProMicroModules/containers/dialog-modify-form-tag';
import { ITableData } from '@mdtProMicroModules/containers/table-theme/TableThemeModel';
import { AppController } from '../../app/AppController';

export class TableThemesModel {
  public static queryPermissions() {
    const app = AppController.getInstance();
    const ps = app.getUserPermissionController().getThemeManagePermission();
    if (app.getPreferencesController().getHasSetPkgPref()) {
      return {
        ...ps,
        createProps: {
          disabled: true,
        },
      };
    }
    return ps;
  }

  public static queryThemeList(): Observable<ITableData[]> {
    // TODO 应该根据偏好管理主题库
    const albumC = AppController.getInstance().getAlbumController()!;
    const aids = albumC.getPrefAlbumIds();
    return aids.length
      ? from(statsAlbumsAsync({ album_ids: albumC.getPrefAlbumIds() })).pipe(
          map((resp) => {
            if (!resp.success) return [];
            const idCountMap: Record<string, number> = {};
            _.forEach(resp.data, (it) => {
              idCountMap[it.album_id] = it.datapkg_count;
            });
            return _.map(albumC.getPrefAlbums(), (item) => this.transformToTableData(item, idCountMap));
          }),
        )
      : of([]);
  }

  public static createThemes(createData: IFormData): Observable<IBusinessResult<ITableData[]>> {
    const allRequest = _.map(createData.name, (tag) => postAlbumAsync({ name: tag } as any));
    return forkJoin(...allRequest).pipe(
      map((allResp) => {
        const result = _.reduce(
          allResp,
          (prev, curr) => {
            if (curr.success) {
              prev.push(this.transformToTableData(curr.data!, {}));
            }
            return prev;
          },
          [] as ITableData[],
        );

        return {
          success: true,
          result,
        };
      }),
    );
  }

  public static updateTheme(
    updateData: IFormData,
    originalUpdateData: ITableData,
  ): Observable<IBusinessResult<ITableData>> {
    const name = updateData.name as string;
    return from(patchAlbumAsync(originalUpdateData.id, { name })).pipe(
      map((resp) => ({
        success: resp.success,
        result: resp.success ? { ...originalUpdateData, name: resp.data!.name } : undefined,
      })),
    );
  }

  public static deleteTheme(deleteData: ITableData): Observable<IBusinessResult<ITableData>> {
    return from(deleteAlbumAsync(deleteData.id)).pipe(
      map((resp) => ({
        success: resp.success,
        result: resp.success ? deleteData : undefined,
      })),
    );
  }

  private static transformToTableData(item: IAlbum, idCountMap: Record<string, number>): ITableData {
    return {
      id: item.id,
      name: item.name,
      pkgStats: `${idCountMap[item.id] || 0}`,
    };
  }
}
