import loadable from '@loadable/component';
import { PermissionEnum } from '@mdtProComm/constants';
import { IRoute } from '@mdtProComm/controllers/BaseRouterController';
import { DatlasRouterController } from '@mdtProMicroModules/datlas/comm/DatlasRouterController';
import { RoutePathEnum } from './constants';

const Home = loadable(() => import('../routers/home'));

export const allAuthRoutes: IRoute[] = [
  {
    path: RoutePathEnum.ROOT,
    View: Home,
    permissionKey: PermissionEnum.MODULE_RS,
    sideMenuLess: true,
  },
];

class RouterController extends DatlasRouterController {
  public getAllAuthRoutes() {
    return allAuthRoutes;
  }
}

export { RouterController };
