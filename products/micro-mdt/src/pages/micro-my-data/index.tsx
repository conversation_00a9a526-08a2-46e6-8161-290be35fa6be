import { FC, memo } from 'react';
import { ModuleIdEnum } from '@mdtProComm/constants';
import { getMicroAppProps, getSubAppName } from '../../_util/util';
import { MICRO_MY_DATA } from '../../config';

const MicroMyData: FC<{ product: string }> = memo(({ product }) => {
  const appName = getSubAppName(product, ModuleIdEnum.MY_DATA);
  const props = getMicroAppProps(appName, MICRO_MY_DATA);
  return <micro-app {...props} />;
});

export { MicroMyData };
