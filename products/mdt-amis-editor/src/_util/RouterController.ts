import loadable from '@loadable/component';
import { IRoute } from '@mdtProComm/controllers/BaseRouterController';
import { DatlasRouterController } from '@mdtProMicroModules/datlas/comm/DatlasRouterController';
import { RoutePathEnum } from './constants';

const DrawerEditor = loadable(() => import('../routers/DrawerEditor'));
const IframeEditor = loadable(() => import('../routers/IframeEditor'));
const Editor = loadable(() => import('../routers/Editor'));
const IframeAmisRender = loadable(() => import('../routers/IframeAmisRender'));

export const allAuthRoutes: IRoute[] = [
  {
    path: RoutePathEnum.ROOT,
    View: Editor,
    permissionKey: true,
    headerLess: true,
    sideMenuLess: true,
  },
  {
    path: RoutePathEnum.DRAWER_EDITOR,
    View: DrawerEditor,
    permissionKey: true,
    headerLess: true,
    sideMenuLess: true,
  },
  {
    path: RoutePathEnum.IFRAME_EDITOR,
    View: IframeEditor,
    permissionKey: true,
    headerLess: true,
    sideMenuLess: true,
  },
  {
    path: RoutePathEnum.IFRAME_AMIS_RENDER,
    View: IframeAmisRender,
    permissionKey: true,
    headerLess: true,
    sideMenuLess: true,
  },
];

class RouterController extends DatlasRouterController {
  public getAllAuthRoutes() {
    return allAuthRoutes;
  }
}

export { RouterController };
