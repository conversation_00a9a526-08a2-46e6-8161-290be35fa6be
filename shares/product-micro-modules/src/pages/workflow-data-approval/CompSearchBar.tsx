import { FC, useRef, useState } from 'react';
import ChevronsDown from '@metro/icons/dist/esm/react/ChevronsDown';
import ChevronsUp from '@metro/icons/dist/esm/react/ChevronsUp';
import Search from '@metro/icons/dist/esm/react/Search';
import { Button } from '@metroDesign/button';
import { Input } from '@metroDesign/input';
import { Select } from '@metroDesign/select';
import { useAsyncEffect, useCreation } from 'ahooks';
import { BehaviorSubject } from 'rxjs';
import { debounceTime, skip } from 'rxjs/operators';
import { transformDateToUnix } from '@mdtBsComm/utils/dayUtil';
import { DebounceTimeEnum } from '@mdtProComm/constants';
import { FormView } from '../../components/form-view';
import { WORKFLOW_INFO } from '../../datlas/datlasConfig';
import i18n from '../../languages';
import {
  HighSearchGroup,
  IHighSearchData,
  ISearchKeyData,
  ISpecOption,
} from '../workflow-application-list/CompSearchBar';
import { ApprovalStatusEnum, statusOptions, WorkflowDataApprovalController } from './WorkflowDataApprovalController';
import '../workflow-application-list/compSearchBarStyle.less';
import './compSearchBarStyle.less';

interface ICompProps<T> {
  value?: T;
  onChange: (val: T) => void;
  specOptions?: ISpecOption[];
  controller?: WorkflowDataApprovalController;
  [key: string]: any;
}

interface IFilterData {
  applyDate?: [string, string];
  specId?: string;
  status?: ApprovalStatusEnum;
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const FilterGroup: FC<ICompProps<IFilterData>> = ({ onChange, specOptions }) => {
  const properties = {
    applyDate: {
      type: 'string',
      title: i18n.chain.proMicroModules.workflow.createTime,
      'x-decorator': 'FormItem',
      'x-component': 'DatePicker.RangePicker',
      'x-component-props': {
        format: 'YYYY-MM-DD',
      },
    },
    specId: {
      type: 'string',
      'x-decorator': 'FormItem',
      'x-component': 'Select',
      'x-hidden': WORKFLOW_INFO.hiddenSelectSpec,
      'x-component-props': {
        options: specOptions,
        placeholder: i18n.chain.proMicroModules.workflow.byWorkflow,
        // mode: 'multiple',
        allowClear: true,
      },
    },
    status: {
      type: 'string',
      'x-decorator': 'FormItem',
      'x-component': 'Select',
      'x-component-props': {
        placeholder: i18n.chain.proMicroModules.workflow.byStatus,
        options: statusOptions,
        allowClear: true,
      },
    },
  };
  const schema = {
    schema: { properties },
  };

  return (
    <div className="filter-wrap">
      <FormView
        formilySchema={schema}
        onChange={(val) => {
          onChange(val);
        }}
      />
    </div>
  );
};

// ----- 搜索 -----------
interface ISearchData {
  wfOrTaskName?: string;
  type?: SearchTypeEnum;
}

enum SearchTypeEnum {
  WORKFLOW = 'workflow',
  TASK = 'task',
}

const searchTypeOptions = [
  {
    label: i18n.chain.proMicroModules.workflow.byInstance,
    value: SearchTypeEnum.WORKFLOW,
  },
  {
    label: i18n.chain.proMicroModules.workflow.byTask,
    value: SearchTypeEnum.TASK,
  },
];

const SearchGroup: FC<ICompProps<ISearchData>> = ({ value = { type: SearchTypeEnum.WORKFLOW }, onChange }) => {
  const [searchVal, setSearchVal] = useState<ISearchData>(value);

  const val$ = useCreation(() => {
    const innerVal$ = new BehaviorSubject(value);
    innerVal$.pipe(skip(1), debounceTime(DebounceTimeEnum.MAX)).subscribe((v) => {
      onChange(v || {});
    });
    return innerVal$;
  }, []);

  const handleChange = (val: string, attr: string) => {
    const nVal = {
      ...val$.getValue(),
      [attr]: val,
    };
    val$.next(nVal);
    setSearchVal(nVal);
  };

  return (
    <>
      <Select
        onChange={(val) => handleChange(val, 'type')}
        value={searchVal.type}
        options={searchTypeOptions}
        allowClear
        width={120}
      />
      <Input
        className="wf-name"
        value={searchVal.wfOrTaskName}
        onChange={(e) => handleChange(e.target.value, 'wfOrTaskName')}
        placeholder={
          searchVal.type === SearchTypeEnum.WORKFLOW
            ? i18n.chain.proMicroModules.workflow.searchPlaceholder
            : i18n.chain.proMicroModules.workflow.searchTaskPlaceholder
        }
        prefix={<Search />}
      />
    </>
  );
};

// ----- 入口 -----------
export interface ISearchBarData {
  status?: ApprovalStatusEnum;
  createTimeMin?: number;
  createTimeMax?: number;
  specId?: string;
  taskName?: string;
  wfName?: string;
  searchKeyDataList?: ISearchKeyData[];
}

interface ISearchBarInnerData {
  filterVal?: IFilterData;
  searchVal?: ISearchData;
  highSearchVal?: IHighSearchData;
}

export const CompSearchBar: FC<ICompProps<ISearchBarData>> = ({ controller, onChange }) => {
  const [specOptions, setSpecOptions] = useState<ISpecOption[]>([]);
  const [showHighSearch, setShowHighSearch] = useState<boolean>(false);
  const showHighSearchRef = useRef<boolean>(showHighSearch);

  const val$ = useCreation(() => {
    const innerVal$ = new BehaviorSubject<ISearchBarInnerData>({});
    innerVal$.pipe(skip(1)).subscribe((val) => {
      const { filterVal, searchVal, highSearchVal } = val;
      const date = filterVal?.applyDate || [];
      let createTimeMin;
      if (date[0]) {
        const d = new Date(date[0]).setHours(0);
        createTimeMin = transformDateToUnix(d);
      }
      let createTimeMax;
      if (date[1]) {
        const d = new Date(date[1]).setHours(24);
        createTimeMax = transformDateToUnix(d);
      }

      const searchData: Record<string, any> = {};
      if (searchVal?.wfOrTaskName) {
        const key = searchVal?.type === SearchTypeEnum.WORKFLOW ? 'wfName' : 'taskName';
        searchData[key] = searchVal?.wfOrTaskName;
      }

      onChange({
        createTimeMin,
        createTimeMax,
        status: filterVal?.status,
        specId: showHighSearchRef.current ? highSearchVal?.specId || filterVal?.specId : filterVal?.specId,
        ...searchData,
        searchKeyDataList: showHighSearchRef.current ? highSearchVal?.searchKeyDataList : undefined,
      });
    });
    return innerVal$;
  }, []);

  useAsyncEffect(async () => {
    const data = await controller!.querySpecList();
    setSpecOptions(data);
  }, []);

  const toggleShowHighSearch = () => {
    showHighSearchRef.current = !showHighSearch;
    setShowHighSearch(!showHighSearch);
  };

  const buttonIcon = showHighSearch ? <ChevronsUp /> : <ChevronsDown />;

  const filterContainer = useCreation(() => {
    const handleFilterChange = (val: IFilterData) => {
      val$.next({ ...val$.getValue(), filterVal: val });
    };
    return <FilterGroup onChange={handleFilterChange} specOptions={specOptions} />;
  }, [specOptions]);

  const searchContainer = useCreation(() => {
    const handleSearchChange = (val: ISearchData) => {
      val$.next({ ...val$.getValue(), searchVal: val });
    };
    return <SearchGroup onChange={handleSearchChange} />;
  }, []);

  const highSearchContainer = useCreation(() => {
    const handleHighSearchChange = (val: IHighSearchData) => {
      val$.next({ ...val$.getValue(), highSearchVal: val });
    };
    return <HighSearchGroup onChange={handleHighSearchChange} specOptions={specOptions} />;
  }, [specOptions]);

  const moreFilterBtnEle = WORKFLOW_INFO.hiddenHightSearch ? null : (
    <Button onClick={toggleShowHighSearch} icon={buttonIcon} iconDirection="right">
      {i18n.chain.proMicroModules.workflow.moreFilterBtnLabel}
    </Button>
  );

  return (
    <div className="search-bar-wrap approval-search-bar-wrap">
      <div className="filter-search-wrap">
        {filterContainer}
        <div className="search-input-wrap">
          {searchContainer}
          {moreFilterBtnEle}
        </div>
      </div>
      <div style={{ display: showHighSearch ? 'block' : 'none' }}>{highSearchContainer}</div>
    </div>
  );
};
