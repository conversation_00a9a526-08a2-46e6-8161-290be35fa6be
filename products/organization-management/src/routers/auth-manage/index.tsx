import { useController } from '@mdtBsComm/hooks/use-controller';
import { UserModel } from '@mdtProMicroModules/models/UserModel';
import { AppController } from '../../app/AppController';
import { AuthManage, AuthManageController, AuthMangeModel } from '../../pages/auth-manage';

const AuthManageView = () => {
  const [controller] = useController(() => {
    const ctrl = new AuthManageController(AppController.getInstance(), AuthMangeModel, UserModel);
    return [ctrl, null];
  });

  return <AuthManage controller={controller} />;
};

export default AuthManageView;
