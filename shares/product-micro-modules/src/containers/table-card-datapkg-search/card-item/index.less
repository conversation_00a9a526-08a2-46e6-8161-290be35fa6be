.module_table-card-datapkg-search {
  .pkg-search_card-item {
    flex: auto;
    min-width: 0;
    height: 127px;
    padding: 14px 16px 12px;
    background: var(--dmc-primary-panel-3);
    border: 1px solid var(--dmc-split-page-color);
    border-radius: 10px;
    cursor: pointer;

    &:hover {
      background: var(--dmc-page-300-color);
    }
  }

  .pkg-search_card-item-selected {
    border: 2px solid var(--dmc-blue-500-color);
  }

  .pkg-search_item-name-wrap {
    display: flex;
    align-items: center;
    width: 100%;
    margin-bottom: 16px;
    overflow: hidden;

    .dmc-icon {
      width: 18px;
      min-width: 18px;
      height: 18px;
      margin-right: 6px;
    }
  }

  .pkg-search_item-name {
    flex-grow: 1;
    color: var(--dmc-text-8);
    font-weight: 500;
    font-size: 14px;
    line-height: 20px;
  }

  .pkg-search_item-app-wrap {
    display: flex;
    margin-bottom: 6px;

    .dmc-icon {
      width: 14px;
      min-width: 14px;
      height: 14px;
      margin-right: 4px;
    }
  }

  .pkg-search_item-app {
    display: flex;
    flex: 1;
    min-width: 0;
    color: var(--dmc-text-color);
  }

  .pkg-search_app-name {
    color: var(--dmc-text-7);
    font-weight: normal;
    font-size: 12px;
    line-height: 17px;
  }

  .pkg-search_permissions-wrap {
    display: flex;
    margin-bottom: 8px;
    color: var(--dmc-text-7);
    font-weight: normal;
    font-size: 12px;
    line-height: 16px;

    .dmc-icon {
      width: 12px;
      min-width: 12px;
      height: 12px;
      margin-right: 2px;
      color: var(--dmc-green-600-color);
    }
  }

  .pkg-search_permissions {
    display: flex;
    align-items: center;
    margin-right: 6px;
  }

  .pkg-search_update-time-wrap {
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: var(--dmc-text-3);
    font-weight: normal;
    font-size: 12px;
    line-height: 17px;

    .dmc-icon {
      width: 14px;
      min-width: 14px;
      height: 14px;
      margin-right: 4px;
    }

    .pkg-search_count {
      display: flex;
      align-items: center;
    }
  }
}
