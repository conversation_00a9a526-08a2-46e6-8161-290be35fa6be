import { NsGraph, uuidv4 } from '@antv/xflow';
import i18n from '../../../languages';
import { COMMON_GROUPS, COMMON_PORT_CONFIG, PORT_CONNECTED } from './constants';
import { GraphPortGroupEnum, JunctionNameEnum, NodeGroupEnum, NodeTypeEnum } from './enum';
import { IGraphNodePort } from './interfaces';

/**
 * key: group NodeGroupEnum
 * nodeType: NodeTypeEnum
 */
export const getPortsConfig = (key: string, nodeType?: NodeTypeEnum): NsGraph.INodePortMeta | undefined => {
  if (key === '') return;
  // 默认均会含有一个输出桩
  let portsItems: IGraphNodePort[] = [
    {
      id: uuidv4(),
      type: NsGraph.AnchorType.OUTPUT,
      group: GraphPortGroupEnum.OUT,
      tooltip: i18n.chain.dataFactory.flow.outputPile,
      ...COMMON_PORT_CONFIG,
    },
  ];
  // 按照后端group进行选择
  switch (key) {
    // 输入源节点 只有一个输出 不做处理
    case NodeGroupEnum.EXTRACTOR:
      // 如果是run_flow节点，有输入和输出节点
      if (nodeType === NodeTypeEnum.RUN_FLOW) {
        portsItems.push({
          id: uuidv4(),
          type: NsGraph.AnchorType.INPUT,
          group: GraphPortGroupEnum.IN,
          portPosition: JunctionNameEnum.DEFAULT_JUNCTION_NAME,
          index: 0,
          tooltip: i18n.chain.dataFactory.flow.inputPile,
          [PORT_CONNECTED]: false,
          ...COMMON_PORT_CONFIG,
        });
      }
      break;
    // 输出源节点 有输入 有输出
    case NodeGroupEnum.LOADER:
      portsItems.push({
        id: uuidv4(),
        type: NsGraph.AnchorType.INPUT,
        group: GraphPortGroupEnum.IN,
        portPosition: JunctionNameEnum.DEFAULT_JUNCTION_NAME,
        index: 0,
        tooltip: i18n.chain.dataFactory.flow.inputPile,
        [PORT_CONNECTED]: false,
        ...COMMON_PORT_CONFIG,
      });
      break;
    // 转换节点 根据不同节点类型 有不同的输入 输出仅一个
    default:
      // JOIN 双输入桩
      if (nodeType === NodeTypeEnum.JOIN || nodeType === NodeTypeEnum.SJOIN) {
        portsItems.push({
          id: uuidv4(),
          type: NsGraph.AnchorType.INPUT,
          group: GraphPortGroupEnum.IN,
          index: 0,
          portPosition: JunctionNameEnum.DEFAULT_JUNCTION_NAME,
          tooltip: `${i18n.chain.dataFactory.flow.inputPile}1`,
          [PORT_CONNECTED]: false,
          ...COMMON_PORT_CONFIG,
        });
        portsItems.push({
          id: uuidv4(),
          type: NsGraph.AnchorType.INPUT,
          group: GraphPortGroupEnum.IN,
          index: 1,
          portPosition: JunctionNameEnum.JOIN_JUNCTION_NAME,
          tooltip: `${i18n.chain.dataFactory.flow.inputPile}2`,
          [PORT_CONNECTED]: false,
          ...COMMON_PORT_CONFIG,
        });
      }
      // UPSERT_DATAFRAME 和APPEND 双输入桩
      else if (
        nodeType === NodeTypeEnum.UPSERT_DATAFRAME ||
        nodeType === NodeTypeEnum.APPEND ||
        nodeType === NodeTypeEnum.PIPE
      ) {
        portsItems.push({
          id: uuidv4(),
          type: NsGraph.AnchorType.INPUT,
          group: GraphPortGroupEnum.IN,
          index: 0,
          portPosition: JunctionNameEnum.DEFAULT_JUNCTION_NAME,
          tooltip: `${i18n.chain.dataFactory.flow.inputPile}1`,
          [PORT_CONNECTED]: false,
          ...COMMON_PORT_CONFIG,
        });
        portsItems.push({
          id: uuidv4(),
          type: NsGraph.AnchorType.INPUT,
          group: GraphPortGroupEnum.IN,
          index: 1,
          portPosition:
            nodeType === NodeTypeEnum.UPSERT_DATAFRAME
              ? JunctionNameEnum.UPSERT_JUNCTION_NAME
              : JunctionNameEnum.APPEND_JUNCTION_NAME,
          tooltip: `${i18n.chain.dataFactory.flow.inputPile}2`,
          [PORT_CONNECTED]: false,
          ...COMMON_PORT_CONFIG,
        });
      }
      // 普通转换节点 一个输入一个输出
      else {
        portsItems.push({
          id: uuidv4(),
          type: NsGraph.AnchorType.INPUT,
          group: GraphPortGroupEnum.IN,
          tooltip: i18n.chain.dataFactory.flow.inputPile,
          index: 0,
          portPosition: JunctionNameEnum.DEFAULT_JUNCTION_NAME,
          [PORT_CONNECTED]: false,
          ...COMMON_PORT_CONFIG,
        });
      }
      break;
  }
  return {
    ...COMMON_GROUPS,
    items: portsItems,
  };
};
