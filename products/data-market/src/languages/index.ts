import { createI18n, I18n } from '@i18n-chain/core';
import { LanguageEnum } from '@mdtProComm/constants';
import microI18n from '@mdtProMicroModules/languages';
import cn, { Locale } from './locales/cn';
import en from './locales/en';

const i18n = createI18n({ defaultLocale: { key: LanguageEnum.CN, values: cn } });
i18n.define(LanguageEnum.EN, en);

const originalLocale = i18n.locale.bind(i18n);
i18n.locale = async (lang: string) => {
  originalLocale(lang);
  microI18n.locale(lang);
};
export default i18n as unknown as I18n<Locale, Locale>;
