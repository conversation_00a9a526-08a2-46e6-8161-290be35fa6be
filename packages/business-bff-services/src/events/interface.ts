import { IEventsSubscriptionsParams, IEventsSubscriptionsResponse } from '@mdtApis/interfaces';
import { ICancelTokenOption } from '../interfaces';

export interface IEventsSubscriptionsOptions extends ICancelTokenOption {
  data: IEventsSubscriptionsParams;
  respData?: IEventsSubscriptionsResponse;
}

export interface IDeleteEventsSubscriptions extends ICancelTokenOption {
  data: { id: string };
  respData?: IEventsSubscriptionsResponse;
}
