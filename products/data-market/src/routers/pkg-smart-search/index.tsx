import { useController } from '@mdtBsComm/hooks/use-controller';
import {
  DatapkgSmartSearch,
  DatapkgSmartSearchController,
  DatapkgSmartSearchModel,
  DatapkgSmartSearchModelBff,
} from '@mdtProMicroModules/pages/datapkg-smart-search';
import { getModel } from '../../_util/modelUtil';

const PkgSmartSearchView = () => {
  const [controller] = useController(() => {
    const ctrl = new DatapkgSmartSearchController({
      Model: getModel(DatapkgSmartSearchModel, DatapkgSmartSearchModelBff),
    });
    return [ctrl, null];
  });

  return <DatapkgSmartSearch controller={controller} />;
};

export default PkgSmartSearchView;
