import { ENABLE_HASH_ROUTER } from '../config';

export const getSubAppPath = (name: string) => {
  return `/${name}`;
};

// eslint-disable-next-line no-undef
export const getMicroAppProps = (name: string, micro: IMicro) => {
  const baseroute = !ENABLE_HASH_ROUTER && micro.enableHashRouter ? undefined : getSubAppPath(name);

  return {
    class: 'micro-app',
    name,
    baseroute,
    url: `${micro.entry}?t=${new Date().getTime()}`,
  };
};

export const getSubAppName = (productName: string, moduleName?: string) => {
  let paths = moduleName ? [productName, moduleName] : [productName];
  return paths.join('/');
};

export const getMicroName = (name: string): any => {
  return `micro-app-${name}`;
};
