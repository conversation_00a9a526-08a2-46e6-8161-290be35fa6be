import { useController } from '@mdtBsComm/hooks/use-controller';
import { AppController } from '../../app/AppController';
import { DataApproval, DataApprovalController } from '../../pages/data-approval';

const DataApprovalOtherView = () => {
  const [controller] = useController(() => {
    const ctrl = new DataApprovalController(AppController.getInstance(), { isSelfApp: false });
    return [ctrl, null];
  });

  return <DataApproval controller={controller} />;
};

export default DataApprovalOtherView;
