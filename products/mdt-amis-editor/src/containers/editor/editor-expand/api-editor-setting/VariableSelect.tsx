import _ from 'lodash';
import { FC, useRef } from 'react';
import { observer } from '@formily/react';
import { Select } from '@metroDesign/select';
import i18n from '../../../../languages';

export const VariableSelect: FC = observer((props: any) => {
  const { value, onChange } = props;
  const valueRef = useRef(value);
  valueRef.current = value;
  const selectRef = useRef(value);
  selectRef.current = value;

  // TODO 获取amis的内部变量
  const items = Math.random() > 0 ? [] : [{ label: 'label', value: 'value' }];
  const options = _.map(items, (it) => {
    return { label: it.label, value: it.value, type: 'form-item' };
  });

  const handleChange = (v: any, options: any) => {
    selectRef.current = options;
  };

  const handleDeselect = (v: string) => {
    const newValue = _.filter(selectRef.current, (it) => it.value !== v);
    onChange(newValue);
  };

  const handleBlur = () => {
    if (valueRef.current !== selectRef.current) {
      onChange(selectRef.current);
    }
  };

  return (
    <Select
      placeholder={i18n.chain.noDepText}
      mode="multiple"
      maxTagCount="responsive"
      onBlur={handleBlur}
      options={options}
      onDeselect={handleDeselect}
      defaultValue={value || []}
      onChange={handleChange}
    />
  );
});
