import dayjs from 'dayjs';
dayjs.locale('zh-cn');

const cn = {
  // api
  api: {
    0: '网络连接超时',
    400: '当前请求参数错误',
    500: '内部服务器错误',
    502: '错误的网关',
    503: '超载或系统维护',
    504: '网关超时',
  },
  rc: {
    1005: 'SMS服务错误',
    1011: '用户不存在或密码错误',
    1012: '账号已过期',
    1013: '手机号未注册',
    1014: '验证码请求频繁',
    1015: '验证码错误或已过期',
    1016: '微信验证错',
    1017: '绑定微信的vcode错误或已失效',
    1018: '非法微信登录',
    1020: '微信账号重复绑定',
    1026: '验证码发送失败，手机号无效',
    1050: [
      '第{{times}}次登陆失败, 请等待{{timedelta}}再登录',
      {
        times: 0,
        timedelta: (value = 0) => {
          return dayjs.duration(value, 's').humanize();
        },
      },
    ],
    1051: [
      '用户被锁定，需等待{{timedelta}}后再登录',
      {
        timedelta: (value = 0) => {
          return dayjs.duration(value, 's').humanize();
        },
      },
    ],
    1077: '验证码错误',
    3002: '微信api错误',
    3003: '阿里云短信验证码接口已知错误',
    3004: '阿里云短信验证码接口未知错误',
  },
  // languages
  cn: '简体中文',
  en: 'English',
  // tabs
  loginByPhone: '短信登录',
  loginByWechat: '微信登录',
  loginByAccount: '账号登录',
  loginByDingtalk: '钉钉登录',
  loginByDingtalkOAuth: '钉钉授权',
  loginByDatlas: 'Datlas登录',
  // 通用
  buttonLabel: '进入平台',
  buttonOkLabel: '确定',
  buttonCancelLabel: '取消',
  buttonUpdateLabel: '确认修改',
  buttonLoadingLabel: '正在进入...',
  loginSuccess: '登录成功',
  // 标题
  loginTitle: 'Collector Manager',
  // 手机登录相关
  phoneLabel: '手机号',
  phonePlaceholder: '请输入手机号',
  phoneVerifyEmpty: '手机号不能为空',
  phoneVerifyInvalid: ['请输入有效的手机号(示例: {{example}})', { example: '' }],
  captchaLabel: '验证码',
  captchaPlaceholder: '请输入验证码',
  captchaButtonLabel: '获取验证码',
  captchaSendedTip: '验证码已发送',
  captchaVerifyEmpty: '验证码不能为空',
  captchaVerifyInvalid: '请输入6位数字验证码',
  captchaButtonSendedLabel: ['({{gap}}s)重新获取', { gap: 60 }],
  accountVerify: '用户名可以包含中文/字母/数字/下划线',
  passwordVerify: '密码需要包含6位以上的字母+数字',
  // 忘记密码相关
  pfTitle: '找回密码',
  pfDesc: '通过手机号/邮箱验证找回密码',
  accountLabel: '手机号/邮箱',
  accountPlaceholder: '请输入',
  accountVerifyEmpty: '手机号/邮箱不能为空',
  accountVerifyInvalid: '请输入有效的手机号/邮箱',
  accountEmpty: '未找到相关账户',
  newPasswordLabel: '新密码',
  newPasswordError: '至少8个字符，至少包含大、小写字母、数字、特殊字符其中三类',
  passwordConfirmLabel: '确认新密码',
  passwordConfirmError: '两次密码输入不一致',
  passwordChangeSuccess: '密码修改成功',
  // 微信登录相关
  wechatSuccessContent: '扫描成功 请在 微信 中确认登录/绑定',
  wechatFailContent: '已取消此次登陆/绑定 您可再次扫描登录/绑定',
  wechatStatusContent: '使用 微信 扫描二维码登录/绑定',
  // 账号登录相关
  nameLabel: '用户名',
  namePlaceholder: '请输入用户名',
  nameVerifyEmpty: '用户名不能为空',
  passwordLabel: '密码',
  passwordPlaceholder: '请输入密码',
  passwordVerifyEmpty: '密码不能为空',
  passwordForget: '忘记密码',
  accountErrorTip: '密码第三次输入错误',
  accountErrorTipDesc:
    '您如果再次输错密码，账号将被锁定。您可以通过手机号或邮箱自己找回密码；如果您没有关联过手机号或邮箱，请书面告知机构用户管理员，帮助重置密码。',
  // 双因子登录
  doubleCheckTitle: '短信二次验证',
  doubleCheckDesc: '由于管理员设置了登录保护，您需要二次验证，我们已经向您的手机发送了验证码',
  doubleCheckOk: '验证',
  // 协议
  privacyText: '我已阅读',
  privacy1: '脉策服务协议',
  privacySlot: '和',
  privacy2: '隐私策略',
  privacyDesc: ['为更好地保护你的合法权益，请阅读并同意{{str}}', { str: '' }],
  privacyOk: '同意',
  privacyCancel: '不同意',
  // 认证
  verifySuccess: '验证成功, 页面跳转中...',
  verifyChecking: '正在验证，请稍后...',
  verifyFailed: '验证失败，请联系管理员！',
  verifyFailedJumpTip: ['{{count}}秒后将跳转到登录页面。', { count: 0 }],
  verifySlideSuccess: '认证成功!',
  verifyPlaceholder: '向右滑动滑块填充拼图',
  verifyTitle: '请完成安全认证',
  verifyRetryText: '请再试一次',
};

export type Locale = typeof cn;
export default cn;
