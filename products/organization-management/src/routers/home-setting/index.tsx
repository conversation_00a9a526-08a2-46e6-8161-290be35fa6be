import { useController } from '@mdtBsComm/hooks/use-controller';
import { AppController } from '../../app/AppController';
import { HomeSetting, HomeSettingController, HomeSettingModel } from '../../pages/home-setting';

const HomeSettingView = () => {
  const [controller] = useController(() => {
    const ctrl = new HomeSettingController(AppController.getInstance(), HomeSettingModel);
    return [ctrl, null];
  });

  return <HomeSetting controller={controller} />;
};

export default HomeSettingView;
