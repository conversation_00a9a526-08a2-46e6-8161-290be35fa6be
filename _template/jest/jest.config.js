const path = require('path');
const { createJestConfig } = require('@craco/craco');
const { pathsToModuleNameMapper } = require('ts-jest/utils');
const { compilerOptions } = require('../../tsconfig.comm.json');
const prefix = path.resolve(process.cwd(), '../../');

const loadJestConfig = () => {
  const jestConfig = createJestConfig({});
  jestConfig.moduleNameMapper = {
    ...jestConfig.moduleNameMapper,
    ...pathsToModuleNameMapper(compilerOptions.paths, { prefix }),
  };
  jestConfig.transformIgnorePatterns = ['<rootDir>/node_modules/(?!react-data-grid)'];
  jestConfig.setupFilesAfterEnv = [`${prefix}/node_modules/jest-canvas-mock`, `${prefix}/node_modules/@testing-library/jest-dom`];
  return jestConfig;
};

module.exports = { loadJestConfig };
