import { useContext } from 'react';
import { createNameContext } from '@mdtBsComm/utils/contextUtil';
import QualityController from './QualityController';

export interface IContext {
  qualityController: QualityController;
}

const context = createNameContext<IContext>('QualityController');

export const useQualityProvider = () => {
  return context.Provider;
};

export const useQualityContext = () => {
  return useContext(context);
};
