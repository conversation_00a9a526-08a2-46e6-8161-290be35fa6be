import { ChangeEvent, FC, KeyboardEvent, ReactNode, useState } from 'react';

type ReactChangeEvent = (e: ChangeEvent<HTMLInputElement>) => void;
type ReactKeyboardEvent = (e: KeyboardEvent<HTMLInputElement>) => void;

export interface IProps {
  prefix?: ReactNode;
  suffix?: ReactNode;
  type?: string;
  value?: string;
  hasError?: boolean;
  onKeyDown?: ReactKeyboardEvent;
  onPressEnter?: ReactKeyboardEvent;
  onFocus?: ReactChangeEvent;
  onBlur?: ReactChangeEvent;
  onChange?: ReactChangeEvent;
  className?: string;
  placeholder?: string;
}

const Input: FC<IProps> = (props) => {
  const { prefix, suffix, onFocus, onBlur, onPressEnter, onKeyDown, className, hasError, ...reset } = props;
  const [isFocused, setFocused] = useState<boolean>(false);

  const _onKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
    e.key === 'Enter' && onPressEnter?.(e);
    onKeyDown?.(e);
  };

  const _onBlur = (e: ChangeEvent<HTMLInputElement>) => {
    setFocused(false);
    onBlur?.(e);
  };

  const _onFocus = (e: ChangeEvent<HTMLInputElement>) => {
    setFocused(true);
    onFocus?.(e);
  };

  const cls: string[] = ['mdt-login-input-wrap'];
  isFocused && cls.push('focused');
  hasError && cls.push('error-state');
  className && cls.push(className);

  return (
    <div className={cls.join(' ')}>
      {prefix}
      <input {...reset} className="mdt-login-input" onBlur={_onBlur} onFocus={_onFocus} onKeyDown={_onKeyDown} />
      {suffix}
    </div>
  );
};

Input.displayName = 'Input';

export * from './CaptchaInput';
export * from './PhoneInput';
export * from './PasswordInput';
export { Input };
