import { FC, useRef } from 'react';
import { Button } from '@metroDesign/button';
import ReactCron from '@mdtProComm/components/react-cron';
import { language } from './locales';

const pv = { language };

export interface ICronInputProps {
  value: string;
  onChange: (value: string) => void;
  cronProps?: any;
}

export const CronInput: FC<ICronInputProps> = ({ value, onChange, cronProps }) => {
  const cronFnsRef = useRef<any>(null);

  const handleClick = () => {
    let val = cronFnsRef.current.getValue();
    onChange(val);
  };

  return (
    <ReactCron.Provider value={pv}>
      <ReactCron
        footer={[
          <Button key="2" type="primary" onClick={handleClick}>
            {language.okBtnText}
          </Button>,
        ]}
        {...(cronProps || {})}
        value={value}
        getCronFns={(fns: any) => {
          cronFnsRef.current = fns;
        }}
      />
    </ReactCron.Provider>
  );
};
