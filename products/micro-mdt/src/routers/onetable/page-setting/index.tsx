import { useController } from '@mdtBsComm/hooks/use-controller';
import { DatlasAppController } from '@mdtProMicroModules/datlas/app/DatlasAppController';
import { getOneTableH5Menus } from '@mdtProMicroModules/pages-in-micro/one-table/util';
import { Setting, SettingController } from '@mdtProMicroModules/pages-in-micro/shared/pages/setting';

const PageSetting = () => {
  const [controller] = useController(() => {
    const ctrl = new SettingController({
      app: DatlasAppController.getInstance(),
      showBottomMenu: true,
    });
    return [ctrl, null];
  });

  return <Setting controller={controller} menus={getOneTableH5Menus()} />;
};

export default PageSetting;
