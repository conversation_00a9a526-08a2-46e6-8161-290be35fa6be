import { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { getWorkflowSpecAsync } from '@mdtBsServices/flowork';
import { VisualizationFlow } from './VisualizationFlow';
import { VisualizationFlowController } from './VisualizationFlowController';

const VisualizationFlowView = () => {
  const { id } = useParams<Record<string, string>>();
  const [controller, setController] = useState<any>();

  useEffect(() => {
    getWorkflowSpecAsync(id, { params: { with_engine: true, with_xml: true } }).then((res) => {
      setController(new VisualizationFlowController(res.data));
    });
  }, [id]);

  return controller ? <VisualizationFlow controller={controller} /> : null;
};

export default VisualizationFlowView;
