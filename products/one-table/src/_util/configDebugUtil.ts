import _ from 'lodash';
import { deepClean } from '@mdtBsComm/utils/deepCleanUtil';
import { modifyParamsOfUrl } from '@mdtProComm/utils/urlUtil';

const addDebugParams = (url?: string) => (url ? modifyParamsOfUrl(url, 'mdtdebug', '1') : undefined);

export const configDebugUtil = (config: any) => {
  return {
    ..._.cloneDeep(config),
    routerConfig: _.map(config.routerConfig, (it: any) => {
      if (it.url || it.innerurl) {
        return deepClean({
          ...it,
          url: addDebugParams(it.url),
          innerurl: addDebugParams(it.innerurl),
        });
      }
      return it;
    }),
  };
};
