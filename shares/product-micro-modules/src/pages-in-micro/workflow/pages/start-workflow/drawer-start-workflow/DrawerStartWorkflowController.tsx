import { FC } from 'react';
import { AsyncSubject } from 'rxjs';
import {
  IBusinessResult,
  IDrawerProps,
  ModalWithBtnsCompDrawerController,
} from '@mdtBsComponents/modal-with-btns-comp-drawer';
import { IWorkflowFormModel } from '../../../_util/WorkFlowFormModel';
import { StartWorkflow } from '../StartWorkflow';
import { StartWorkflowController } from '../StartWorkflowController';

const DrawerHeader: FC<{ controller?: DrawerStartWorkflowController }> = ({ controller }) => {
  const rest = controller?.getModalRest();
  return <div className="modal-drawer_title">{rest?.title}</div>;
};

interface IDrawerWorkflowFormData {
  title: string;
  wfSpecId: string;
  confirmText?: string;
  needValidate?: boolean;
  onSubmit?: (values: any) => Promise<boolean>;
}

// 发起流程弹窗
export class DrawerStartWorkflowController extends ModalWithBtnsCompDrawerController<IDrawerWorkflowFormData> {
  private innerController?: StartWorkflowController;
  private notify?: AsyncSubject<IBusinessResult<IDrawerWorkflowFormData>>;

  public constructor(Model: IWorkflowFormModel) {
    super({
      modalCompOptions: {
        modalOptions: () => this.initDrawerOptions(),
        InnerView: StartWorkflow as FC,
        innerViewController: () => this.innerController as any,
        beforeOpenFunc: async (rest) => {
          const options = {
            Model,
            ...rest,
          };
          this.innerController = new StartWorkflowController(options as any, () => {
            this.notify?.next({ success: true });
            this.closeModal();
          });
        },
      },
      beforeCancelFunc: async () => {
        this.innerController?.destroy();
        this.innerController = undefined;
        return true;
      },
      uiOptions: { customerDrawerHeader: DrawerHeader as FC },
    });
  }

  public destroy() {
    super.destroy();
    this.innerController?.destroy();
    this.innerController = undefined;
    this.notify?.complete();
    this.notify = undefined;
  }

  public openModal(rest?: IDrawerWorkflowFormData | undefined): AsyncSubject<IBusinessResult<IDrawerWorkflowFormData>> {
    this.notify = super.openModal(rest);
    return this.notify;
  }

  private initDrawerOptions = (): IDrawerProps => {
    return { width: '45vw', wrapperClassName: 'drawer-start-workflow' };
  };
}
