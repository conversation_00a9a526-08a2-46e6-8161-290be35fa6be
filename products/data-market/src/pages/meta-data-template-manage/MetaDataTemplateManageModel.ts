import { getModel } from '../../_util/modelUtil';
import { TableTaggroupModel } from './TableTaggroupModel';
import { TableTaggroupModelBff } from './TableTaggroupModelBff';
import { TableThemesModel } from './TableThemesModel';
import { TableThemesModelBff } from './TableThemesModelBff';

export class MetaDataTemplateManageModel {
  public static getSubModels() {
    return {
      themesModel: getModel(TableThemesModel, TableThemesModelBff),
      taggroupModel: getModel(TableTaggroupModel, TableTaggroupModelBff),
    };
  }
}

export type IMetaDataTemplateManageModel = typeof MetaDataTemplateManageModel;
