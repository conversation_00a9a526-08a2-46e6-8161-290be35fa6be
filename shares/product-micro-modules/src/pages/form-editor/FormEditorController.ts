import _ from 'lodash';
import {
  createDesigner,
  Engine,
  FromNodeEvent,
  ITreeNode,
  KeyCode,
  RemoveNodeEvent,
  Shortcut,
  TreeNode,
} from '@designable/core';
import { IFormilySchema, transformToTreeNode } from '@designable/formily-transformer';
import { transformToSchema } from '@designable/formily-transformer';
import type { ISchema } from '@formily/json-schema';
import { autorun } from '@formily/reactive';
import { BehaviorSubject } from 'rxjs';
import { debounceTime, skip } from 'rxjs/operators';
import type { ILabelValue } from '@mdtBsComm/interfaces';
import { DebounceTimeEnum, OTHER_OPTION_KEY_PREFIX } from '@mdtProComm/constants';
import { isFormHiddenKey } from '@mdtProComm/utils/questionUtil';
import { getAvaliableNodeId, MetroSettingProp } from '@mdtProFormEditor/metro-form-design/shared';
import { getOtherOptionI<PERSON><PERSON><PERSON> } from '../../components/form-view/util';
import i18n from '../../languages';
import type { IOnPageCancelCb, IOnPageSuccessCb } from '../drawer-within-page';
import { DataValue } from './service/data-value';
import { defaultConfig, formDefaultConfig } from './default-config';
import { ICustomerDefaultSettingFunc } from './interface';
import { DataSource, saveSchema } from './service';
import { generateNewQuestionSchema } from './transformer';
import {
  ArrayBaseCompEnum,
  arrayBaseComps,
  ensureSameStyle,
  FORM_NODE_ID,
  getNewFieldSchema,
  getNewFormSchema,
  getNodeAndChildrenIds,
  hasDataSource,
  isArrayBaseComp,
  isCompAddOtherOption,
  mergeNodePropsWithSetting,
  modifySavedFormilySchemaAndSentting,
  NODE_COMP,
  NODE_PROPS_ID,
  sureNodeNameAndOtherNameSame,
  transformAllSettingValuesToSave,
  transformFormilySchemaToSave,
  transformSavedAllSettingValues,
  transformSavedFormilySchema,
} from './util';

export interface IFormEditorControllerProps {
  theme?: string;
  editorSchema?: IFormEditorSchema;
  optionsMap?: Record<string, ILabelValue[]>;
  validate?: (values: IFormEditorSchema) => string | undefined;
  defaultSettingFunc?: ICustomerDefaultSettingFunc;
  onSuccessCb?: IOnPageSuccessCb;
  onCancelCb?: IOnPageCancelCb;
}

export interface IFormEditorSchema {
  formilySchema: IFormilySchema;
  allSettingValues: Record<string, ISchema>;
  dataSource?: DataSource;
}

// 默认值管理器接口
interface IDefaultValueManager {
  getDefaultValue(componentName: string, context?: IDefaultValueContext): any;
  mergeWithExisting(defaultValue: any, existingValue: any): any;
  processSpecialCases(node: ITreeNode, targetNode: TreeNode, defaultValue: any): any;
}

interface IDefaultValueContext {
  node?: ITreeNode;
  targetNode?: TreeNode;
  isClone?: boolean;
  isReplaceType?: boolean;
  preserveProps?: string[];
}

// 默认值管理器实现
class DefaultValueManager implements IDefaultValueManager {
  constructor(
    private defaultConfig: Record<string, any>,
    private customerDefaultSettingFunc?: ICustomerDefaultSettingFunc,
    private controller?: FormEditorController
  ) {}

  getDefaultValue(componentName: string, context?: IDefaultValueContext): any {
    const defaultValue = this.defaultConfig[componentName] || {};

    // 如果是 Form 组件，需要特殊处理
    if (componentName === 'Form') {
      const formDefault = _.cloneDeep(defaultValue);
      if (this.controller) {
        formDefault['x-metro-default-value'].defaultConfig.map.value = this.controller.getDefaultValueFiledMap();
      }
      return formDefault;
    }

    // 如果有自定义默认值函数且有上下文，调用它
    if (context?.node && context?.targetNode && this.customerDefaultSettingFunc) {
      const customDefault = this.customerDefaultSettingFunc(context.node, context.targetNode);
      if (customDefault) {
        return { ...defaultValue, ...customDefault };
      }
    }

    return defaultValue;
  }

  mergeWithExisting(defaultValue: any, existingValue: any): any {
    return { ...defaultValue, ...existingValue };
  }

  processSpecialCases(node: ITreeNode, targetNode: TreeNode, defaultValue: any): any {
    let result = { ...defaultValue };

    // 处理表格列标题
    const noNeedTitle = _.get(targetNode, 'parent.props.x-component') === 'ArrayTable.Column';
    if (noNeedTitle) {
      result.title = '';
    }

    // 处理子节点布局
    if (targetNode.depth > 1 && this.controller) {
      result['x-decorator-props'] = {
        layout: _.get(this.controller.allSettingValues, 'form-node-id.layout'),
        colon: _.get(this.controller.allSettingValues, 'form-node-id.colon'),
      };
    }

    return result;
  }
}

export class FormEditorController {
  public currentNodeData$: BehaviorSubject<any> = new BehaviorSubject(null);
  public onSuccessCb?: IOnPageSuccessCb;
  public onCancelCb?: IOnPageCancelCb;
  private _engine: Engine;
  private _theme: string;
  private _initSchemaUpdate$ = new BehaviorSubject('N');
  private _optionsMap?: Record<string, ILabelValue[]>;
  private allSettingValues: Record<string, ISchema> = {};
  // 移除手动维护的 formilySchema，改为从 engine 实时获取
  // private formilySchema: IFormilySchema = {};
  // 字段节点实例
  private nodeFiledMap: Record<string, any> = {};
  // 默认值管理器
  private defaultValueManager: IDefaultValueManager;
  private nodeSetting$ = new BehaviorSubject<any>(null);
  private dataSource: DataSource;
  private dataValue: DataValue;
  private customerValidate: (values: IFormEditorSchema) => string | undefined;
  private customerDefaultSettingFunc?: ICustomerDefaultSettingFunc;

  public constructor(props?: IFormEditorControllerProps, onSuccessCb?: IOnPageSuccessCb, onCancelCb?: IOnPageCancelCb) {
    const {
      theme = 'light',
      editorSchema,
      optionsMap,
      validate = this.defaultCustomerValidate,
      defaultSettingFunc,
      onSuccessCb: fbOnSuccessCb,
      onCancelCb: fbOnCancelCb,
    } = props || {};
    this.onSuccessCb = onSuccessCb || fbOnSuccessCb;
    this.onCancelCb = onCancelCb || fbOnCancelCb;
    this.customerValidate = validate;
    this.customerDefaultSettingFunc = defaultSettingFunc;
    defaultConfig['Form'] = _.cloneDeep(formDefaultConfig);
    this._theme = theme;
    this._engine = createDesigner({
      shortcuts: [
        new Shortcut({
          codes: [
            [KeyCode.Meta, KeyCode.S],
            [KeyCode.Control, KeyCode.S],
          ],
          handler(ctx) {
            saveSchema(ctx.engine);
          },
        }),
      ],
      rootComponentName: 'Form',
    });
    this.dataSource = new DataSource(this._engine);
    this.dataValue = new DataValue(this._engine);
    // 初始化默认值管理器
    this.defaultValueManager = new DefaultValueManager(defaultConfig, this.customerDefaultSettingFunc, this);
    // 补充默认值
    this._engine.subscribeTo(FromNodeEvent, (event) => {
      this.saveNodeFiledMap(event.data.target);
      this.fillDefaultValue(event.data.source, event.data.target);
    });
    // 删除节点时，需要将删除的节点的设置值也删除
    this._engine.subscribeTo(RemoveNodeEvent, (event) => {
      this.saveNodeFiledMap(event.data.target, true);
      this.removeEditorNode(event.data.target);
    });
    this.listenAddNode();
    this.listenNodeSettingChange();
    if (editorSchema) {
      requestAnimationFrame(() => {
        this.initSchema(editorSchema, optionsMap);
      });
    }
  }

  public destroy() {
    this.currentNodeData$.complete();
    this.currentNodeData$.next(null);
    this.onSuccessCb = undefined;
    this.onCancelCb = undefined;
    this._engine = null!;
    this._initSchemaUpdate$.complete();
    this._optionsMap = undefined;
    this.allSettingValues = {};
    // this.formilySchema = {}; // 已移除
    this.nodeFiledMap = {};
    this.nodeSetting$.complete();
    this.nodeSetting$.next(null);
    this.dataSource.destroy();
    this.dataValue.destroy();
    this.customerValidate = this.defaultCustomerValidate;
  }

  public getDataSource() {
    return this.dataSource;
  }

  public getDataValue() {
    return this.dataValue;
  }

  public get engine() {
    return this._engine;
  }

  public get theme() {
    return this._theme;
  }

  public get initSchemaUpdate$() {
    return this._initSchemaUpdate$;
  }

  public get optionsMap() {
    return this._optionsMap;
  }

  // 从 engine 实时获取 formilySchema，替代手动维护的 formilySchema
  private getCurrentFormilySchema(): IFormilySchema {
    return transformToSchema(this._engine.getCurrentTree());
  }

  // 初始化editor schema
  public initSchema(editorSchema?: IFormEditorSchema, optionsMap?: Record<string, ILabelValue[]>) {
    this._optionsMap = optionsMap;
    let val = editorSchema || { formilySchema: {}, allSettingValues: {} };
    val = modifySavedFormilySchemaAndSentting(val);

    // 转换保存的数据格式为运行时格式
    const transformedFormilySchema = transformSavedFormilySchema(val.formilySchema);
    this.allSettingValues = transformSavedAllSettingValues(val.allSettingValues);

    // 直接设置到 engine，不再手动维护 formilySchema
    this._engine.setCurrentTree(transformToTreeNode(transformedFormilySchema));
    this.initSchemaUpdate$.next('Y');
  }

  // 预览时无需触发外部校验
  public getEditorSchemaToSave(disableCustomerValidate = false): IFormEditorSchema | string {
    const errMsg = this.validate();
    if (errMsg) return errMsg;

    // 从 engine 实时获取 formilySchema，而不是使用手动维护的
    const currentFormilySchema = this.getCurrentFormilySchema();

    const data = {
      formilySchema: transformFormilySchemaToSave(currentFormilySchema, this._engine),
      allSettingValues: transformAllSettingValuesToSave(this.allSettingValues, currentFormilySchema),
    };

    ensureSameStyle(data);

    const customerErrorMsg = !disableCustomerValidate ? this.customerValidate(data) : '';
    if (customerErrorMsg) return customerErrorMsg;

    return data;
  }

  public getAllSettingValues() {
    return this.allSettingValues;
  }

  // 属性修改后刷新node tree
  public refreshNodeTree(node: TreeNode, setting: any) {
    this.nodeSetting$.next({ node, setting });
  }

  // 修改题型后替换node
  public replaceNodeType(node: TreeNode, newComponentName: string) {
    const nodeId = node.id;
    const currentProps = node.props || {};

    // 使用默认值管理器获取默认值
    const context: IDefaultValueContext = {
      node: node as ITreeNode,
      targetNode: node,
      isReplaceType: true,
      preserveProps: ['x-index', NODE_PROPS_ID, 'title', 'name', 'dataSource', 'x-decorator-props']
    };
    const defaultSettingValue = this.defaultValueManager.getDefaultValue(newComponentName, context);

    // 生成新的 node props
    const newProps = {
      ...generateNewQuestionSchema(newComponentName, defaultSettingValue),
      ..._.pick(currentProps, ['x-index', NODE_PROPS_ID, 'title', 'name', 'dataSource', 'x-decorator-props']),
    };

    // 更新 node.props，让 formily engine 自动更新 schema
    node.props = newProps;

    // 保存设置值 - 保留数据源设置
    const oldSettingValueToStore = _.pick(this.allSettingValues[nodeId], [MetroSettingProp.datasource]);
    const mergedSettingValue = this.defaultValueManager.mergeWithExisting(
      {
        ...defaultSettingValue,
        title: currentProps.title,
        name: currentProps.name,
        'x-decorator-props': currentProps['x-decorator-props'],
      },
      oldSettingValueToStore
    );

    this.allSettingValues[node.id] = mergedSettingValue;
    sureNodeNameAndOtherNameSame(this.allSettingValues, nodeId);

    if (hasDataSource(newComponentName, _.get(mergedSettingValue, MetroSettingProp.datasource))) {
      this.dataSource.addNodeDataSource(node.id, mergedSettingValue);
    }
  }

  public initNodeForm(nodeId: string, values: any) {
    const ds = this.dataSource.getData(nodeId);
    const hasDataSource = ds && values?.[MetroSettingProp.datasource];
    const nodeData = hasDataSource ? this.dataSource.addNodeDataSource(nodeId, values, true) : null;
    this.currentNodeData$.next(nodeData);
  }

  public getDefaultValueFiledMap() {
    const defaultValueList: any[] = [];
    const editorSchema = this.getEditorSchemaToSave();
    if (typeof editorSchema !== 'string') {
      const filterKeys = ['form_submitter__BPMN_ROOT_NODE_KEY'];
      _.forEach(_.values(editorSchema.formilySchema.schema?.properties), (item: any) => {
        if (_.includes(filterKeys, item.name)) return;
        defaultValueList.push({ label: item.name, value: item.name, select: true });
      });
    }
    return defaultValueList;
  }

  // 属性修改后或添加新的node后更新editorSchema
  private updateEditorSchema(node: ITreeNode, values: any) {
    const nodeID = node.id!;
    const isForm = node.componentName === 'Form';

    // 只更新 allSettingValues，不再手动维护 formilySchema
    this.allSettingValues[nodeID] = values;
    sureNodeNameAndOtherNameSame(this.allSettingValues, nodeID);

    // 更新 node.props，让 formily engine 自动处理 schema 构建
    if (isForm) {
      // Form 节点的处理
      let horizontalStyle = { labelCol: 24 };
      if (values.layout === 'horizontal') {
        horizontalStyle = { labelCol: 6 };
      }
      node.props = { ...node.props, ...values, ...horizontalStyle };
    } else {
      // 普通字段节点的处理
      node.props = mergeNodePropsWithSetting(node, values);
    }
  }

  private removeEditorNode(nodes: TreeNode | TreeNode[]) {
    // 只需要清理 allSettingValues，formily engine 会自动处理 schema 中的节点删除
    let nodeIdsToDelete: string[] = [];
    if (_.isArray(nodes)) {
      _.forEach(nodes, (n) => {
        nodeIdsToDelete.push(...getNodeAndChildrenIds(n));
      });
    } else {
      nodeIdsToDelete.push(...getNodeAndChildrenIds(nodes as TreeNode));
    }
    _.forEach(nodeIdsToDelete, (id) => {
      delete this.allSettingValues[id];
    });
  }

  // 为node添加默认属性
  private fillDefaultValue(node: ITreeNode, targetNode: TreeNode) {
    const isForm = node.componentName === 'Form';
    if (isForm) {
      this.fillFormDefaultValue(node);
      return;
    }

    this.fillFieldDefaultValue(node, targetNode);
  }

  private fillFormDefaultValue(node: ITreeNode) {
    node.id = FORM_NODE_ID;

    const defaultValue = this.defaultValueManager.getDefaultValue('Form');
    const existingValue = this.allSettingValues[FORM_NODE_ID];
    const mergedValue = this.defaultValueManager.mergeWithExisting(defaultValue, existingValue);

    this.updateEditorSchema(node, mergedValue);
  }

  private fillFieldDefaultValue(node: ITreeNode, targetNode: TreeNode) {
    const nodePropsId = node.props?.[NODE_PROPS_ID];
    const isClone = nodePropsId && node.id !== nodePropsId;
    const compName = node.props?.[NODE_COMP];

    // 获取默认值
    const context: IDefaultValueContext = { node, targetNode, isClone };
    const defaultValue = this.defaultValueManager.getDefaultValue(compName, context);

    // 获取设置值
    const settingValue = isClone
      ? { ...this.allSettingValues[nodePropsId], name: undefined }
      : this.getSettingValue(node, targetNode);

    // 合并默认值和设置值
    let mergedSettingValue = this.defaultValueManager.mergeWithExisting(defaultValue, settingValue);

    // 处理特殊情况
    mergedSettingValue = this.defaultValueManager.processSpecialCases(node, targetNode, mergedSettingValue);

    // 处理克隆的其他选项
    if (isClone && _.startsWith(nodePropsId, OTHER_OPTION_KEY_PREFIX)) {
      const parent = targetNode.parent;
      const nId = getOtherOptionInnerKey(parent.id);
      mergedSettingValue.name = getOtherOptionInnerKey(parent.props!.name);
      targetNode.id = nId;
      node.id = nId;
    } else {
      const isNameUseInputComp = !this.optionsMap?.name;
      isNameUseInputComp && (mergedSettingValue.name = mergedSettingValue.name || getAvaliableNodeId(node.id!));
    }

    this.updateEditorSchema(node, mergedSettingValue);

    // 如果有数据源设置，则创建数据源对象
    if (hasDataSource(compName, _.get(mergedSettingValue, MetroSettingProp.datasource))) {
      this.dataSource.addNodeDataSource(node.id!, mergedSettingValue);
    }
  }

  private getSettingValue = (node: ITreeNode, targetNode: TreeNode) => {
    const parentNode = targetNode.parent;
    const settingValue = this.allSettingValues[node.id!];
    if (!settingValue) {
      const pprops = parentNode.props as Record<string, any>;
      if (isCompAddOtherOption(pprops)) {
        // other选项需要保证和parent一致
        return { title: '', name: pprops.name ? getOtherOptionInnerKey(pprops.name) : node.id };
      }
      return this.customerDefaultSettingFunc?.(node, targetNode);
    }
    return settingValue;
  };

  private getChildrenNodeLayout(node: TreeNode) {
    return node.depth > 1
      ? {
          'x-decorator-props': {
            layout: _.get(this.allSettingValues, 'form-node-id.layout'),
            colon: _.get(this.allSettingValues, 'form-node-id.colon'),
          },
        }
      : {};
  }

  // 存储字段节点实例, 方便后续操作
  private saveNodeFiledMap(node: TreeNode | TreeNode[], remove?: boolean) {
    const nodes = Array.isArray(node) ? node : [node];
    if (remove) {
      for (const d of nodes) {
        delete this.nodeFiledMap[d.id];
      }
    } else {
      for (const d of nodes) {
        this.nodeFiledMap[d.id] = node;
      }
    }
  }

  private listenAddNode() {
    autorun(() => {
      const nodes = this._engine.getCurrentTree()?.children || [];
      const propertyIdIndexMap = _.reduce(
        nodes,
        (map, node, index) => {
          map[node.id] = index;
          return map;
        },
        {} as any,
      );
      if (nodes.length) {
        // 直接更新 node.props 中的 x-index，让 formily engine 自动处理
        _.forEach(nodes, (node, index) => {
          if (node.props) {
            node.props['x-index'] = index;
          }
        });
      }
    });
  }

  private listenNodeSettingChange() {
    this.nodeSetting$.pipe(skip(1), debounceTime(DebounceTimeEnum.NORMAL)).subscribe((val) => {
      if (!val) return;
      const { node, setting } = val;
      this.updateEditorSchema(node, setting);

      // node.props 已在 updateEditorSchema 中更新，无需再次设置
      const isForm = node.componentName === 'Form';
      if (isForm) {
        // 处理 Form 布局变化对子节点的影响
        const newLayout = setting.layout;
        node.eachChildren((subNode: TreeNode) => {
          const decoProps = _.get(subNode, 'props.x-decorator-props');
          if (!decoProps) return;
          if (decoProps.layout === newLayout) return false;
          decoProps.layout = newLayout;
        });
      }
    });
  }

  private validate() {
    const baseErrMsg = this.baseValidate();
    if (baseErrMsg) {
      console.log('base validate error', baseErrMsg, this.getCurrentFormilySchema(), this.allSettingValues);
      return baseErrMsg;
    }

    const arrayBaseErrMsg = this.validateArrayBaseComps();
    if (arrayBaseErrMsg) {
      console.log('array base validate error', arrayBaseErrMsg, this.getCurrentFormilySchema(), this.allSettingValues);
      return arrayBaseErrMsg;
    }
  }

  // 字段校验
  private baseValidate() {
    const prefixOfNoNeedNamesComs = _.map(arrayBaseComps, (comp) => `${comp}.`);
    const currentSchema = this.getCurrentFormilySchema();
    const obj = _.values(currentSchema.schema?.properties);
    const properties = _.filter(
      obj,
      (it: any) => !_.some(prefixOfNoNeedNamesComs, (prefix) => _.startsWith(it[NODE_COMP], prefix)),
    );
    const validFields: string[] = [];
    const repeatedNames: string[] = [];
    const noNameFields: string[] = [];
    const existNameMap: Record<string, true> = {};
    _.forEach(properties, (it: any) => {
      const compName = it[NODE_COMP];
      const name = it.name;
      const title = it.title;
      const nodeId = it[NODE_PROPS_ID];
      if (!compName || isFormHiddenKey(nodeId)) return;
      if (name) {
        validFields.push(name);
        if (existNameMap[name]) {
          repeatedNames.push(title || 'name');
        } else {
          existNameMap[name] = true;
        }
      } else {
        noNameFields.push(title);
      }
    });
    if (!_.isEmpty(noNameFields))
      return `${i18n.chain.proMicroModules.fillDesign.columnFieldEmptyTip} ${_.join(noNameFields, ', ')}`;
    if (!_.isEmpty(repeatedNames))
      return `${i18n.chain.proMicroModules.fillDesign.columnFieldRepeatTip} ${_.join(repeatedNames, ', ')}`;
    if (_.isEmpty(validFields)) return i18n.chain.proMicroModules.fillDesign.columnEmptyTip;
  }

  private validateArrayBaseComps() {
    let result = '';
    const schema = transformToSchema(this.engine.getCurrentTree());
    const list = _.values(schema.schema?.properties);
    const arrayBaseProperties = _.filter(list, isArrayBaseComp);
    _.forEach(arrayBaseProperties, (abItem: any) => {
      const comp = abItem[NODE_COMP];
      const children = _.get(abItem, 'items.properties');
      let hasChildren = true;
      if (comp === ArrayBaseCompEnum.ARRAY_CARDS) {
        hasChildren = _.some(children, (child) => !_.startsWith(child[NODE_COMP], ArrayBaseCompEnum.ARRAY_CARDS));
      } else if (comp === ArrayBaseCompEnum.ARRAY_TABLE) {
        hasChildren = _.some(children, (col) => !!_.size(col.properties));
      }
      if (!hasChildren) {
        result = i18n.chain.proMicroModules.fillDesign.dragFieldToForm(abItem.title || abItem[NODE_PROPS_ID]);
        return false;
      }
    });
    return result;
  }

  private defaultCustomerValidate() {
    return undefined;
  }
}
