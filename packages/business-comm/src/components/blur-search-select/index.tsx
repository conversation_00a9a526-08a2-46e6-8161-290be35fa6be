import _ from 'lodash';
import { FC, isValidElement, ReactNode } from 'react';
import Select, { SelectProps } from '@mdtDesign/select';
import { search as pinyinSearch } from '../../utils/pinyinUtil';

function getLabelText(node: ReactNode): string {
  if (isValidElement(node)) {
    return _.flattenDeep(
      _.map(node.props.children, (child) => {
        if (isValidElement(child)) {
          return getLabelText(child);
        }
        return child;
      }),
    ).join('');
  } else {
    return '';
  }
}

const BlurSearchSelect: FC<SelectProps> = (props) => {
  const labelKey = props.labelKey || 'label';
  return (
    <Select
      showSearch
      {...props}
      customSearchFilter={(value, options) => {
        return _.filter(options, (cell) => {
          if (_.isString(cell[labelKey])) {
            return pinyinSearch(cell[labelKey], value);
          }
          return pinyinSearch(getLabelText(cell[labelKey]), value);
        });
      }}
    />
  );
};

export { BlurSearchSelect };
