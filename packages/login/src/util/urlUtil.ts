/* eslint-disable */

/**
 * 获取url上的参数
 */
export const getParamFromUrl = (name: string, url?: string): string => {
  if (!url) url = window.location.href;
  // name = name.replace(/[[]/, '[').replace(/[\]]/, '\\]');
  const regexS = '[\\?&]' + name + '=([^&#]*)';
  const regex = new RegExp(regexS);
  const results = regex.exec(url);
  return results == null ? '' : decodeURIComponent(results[1]);
};

/**
 * 批量获取url参数
 */
export const getParamsFromUrl = (params: string[], url?: string): string[] => {
  return params.map((param) => getParamFromUrl(param, url));
};

/**
 * 获取url上的全部参数
 */
export const getAllParamsFromUrl = (url?: string): Record<string, string> => {
  if (!url) url = window.location.href;
  const params: Record<string, string> = {};
  const queryString = url.split('?')[1];

  if (!queryString) return params;

  const pairs = queryString.split('&');
  for (const pair of pairs) {
    const [key, value] = pair.split('=');
    if (key) {
      params[key] = value || '';
    }
  }

  return params;
};
