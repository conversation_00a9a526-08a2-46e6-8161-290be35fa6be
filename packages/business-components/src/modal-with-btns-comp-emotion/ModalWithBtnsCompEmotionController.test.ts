import _ from 'lodash';
// import { render } from '@testing-library/react';
import { ModalWithBtnsCompEmotionController } from './ModalWithBtnsCompEmotionController';
const controllers: ModalWithBtnsCompEmotionController[] = [];
let controller: ModalWithBtnsCompEmotionController;

describe('ModalWithBtnsCompDrawerControllerTest', () => {
  describe('测试默认状态下行为', () => {
    beforeAll(() => {
      controller = new ModalWithBtnsCompEmotionController({
        modalCompOptions: { modalOptions: {} },
      });
      controllers.push(controller);
    });

    test('应该被定义', () => {
      expect(controller).toBeDefined();
    });
  });

  describe('model getModalOptionss测试', () => {
    beforeAll(() => {
      controller = new ModalWithBtnsCompEmotionController({
        modalCompOptions: { modalOptions: {} },
      });
      controllers.push(controller);
    });
    test('model getModalOptions测试', () => {
      controller.getModalCompController().getModalOptions();
      expect(controller.getModalCompController().getModalOptions().animation).toEqual('zoom');
      expect(controller.getModalCompController().getModalOptions().maskAnimation).toEqual('fade');
      // expect(controller.getModalCompController().getModalOptions().maskClosable).toEqual()
    });
  });
  describe('model getUiOptions测试', () => {
    beforeAll(() => {
      controller = new ModalWithBtnsCompEmotionController({
        modalCompOptions: { modalOptions: {} },
      });
      controllers.push(controller);
    });
    test('model getUiOptions测试', () => {
      controller.getUiOptions();
      expect(controller.getUiOptions().okBtnProps).toEqual({ type: 'primary', status: 'danger' });
    });
  });
});

afterAll(() => {
  _.forEach(controllers, (it) => {
    it.destroy();
  });
  controllers.length = 0;
});
