import { useContext } from 'react';
import { createNameContext } from '@mdtBsComm/utils/contextUtil';
import { DatapkgShareController } from './DatapkgShareController';

export interface IContext {
  datapkgShareController: DatapkgShareController;
}

const context = createNameContext<IContext>('DatapkgShareController');

export const useDatapkgShareProvider = () => {
  return context.Provider;
};

export const useDatapkgShareContext = () => {
  return useContext(context);
};
