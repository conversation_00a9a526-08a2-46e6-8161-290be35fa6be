import { connect, mapProps, mapReadPretty } from '@formily/react';
import { Radio as MetroRadio } from '@metroDesign/radio';
import { PreviewText } from '../preview-text';
import './styles.less';

export const InternalRadio = connect(
  MetroRadio,
  mapProps({
    value: 'checked',
    onInput: 'onChange',
  }),
);

const Group = connect(
  MetroRadio.Group,
  mapProps({
    dataSource: 'options',
  }),
  mapReadPretty(PreviewText.Select),
);

export const Radio = Object.assign(InternalRadio, {
  // __ANT_RADIO: true,
  Group,
});

export default Radio;
