/* eslint-disable sonarjs/no-duplicate-string */
/* eslint-disable @typescript-eslint/explicit-member-accessibility */
import i18n from '../../../languages'; // 假设i18n.js文件在这个路径
import { FlexPluginBase } from '../FlexPluginBase';

export default class Layout_fixed extends FlexPluginBase {
  name = i18n.chain.jsonComp.floatingContainer;
  isBaseComponent = false;
  pluginIcon = 'layout-fixed-plugin';
  description = i18n.chain.jsonComp.floatingDescription;
  tags = [i18n.chain.jsonComp.commonLayout];
  order = -1;
  scaffold: any = {
    type: 'container',
    size: 'xs',
    body: [],
    style: {
      position: 'fixed',
      inset: 'auto 50px 50px auto',
      zIndex: 10,
      minWidth: '80px',
      minHeight: '80px',
    },
    originPosition: 'right-bottom',
  };
}
