import _ from 'lodash';
import { FC } from 'react';
import { If } from '@mdtBsComm/components/if';
import { useObservableState } from '@mdtBsComm/hooks/use-observable-state';
import Tag from '@mdtDesign/tag';
import { CardCurdWithSimpleSearch } from '../../../../containers/card-curd-with-simple-search';
import i18n from '../../../../languages';
import { BottomMenu } from '../../../shared/components/bottom-menu';
import { getOneTableH5Menus } from '../../util';
import { CardListController, ListStatusEnum } from './CardListController';
import { ICardData } from './CardListModel';
import { CompSearchBar } from './CompSearchBar';
import './index.less';

interface IProps {
  controller: CardListController;
}

export interface IOptionCardProps {
  item: ICardData;
  controller: CardListController;
}

export const DefaultOptionCard: FC<IOptionCardProps> = ({ item, controller }) => {
  const { statusDisplay, tags, startTime, endTime, applyTime } = item;
  const status = statusDisplay && (
    <div className="status-warp">
      <Tag tag={statusDisplay[0]} color={statusDisplay[1] as any} />
    </div>
  );
  const tagList = _.map(tags, (t, index) => <Tag key={index} tag={t} className="report-tag" />);
  const reportTime = startTime && (
    <div className="card-item">
      {i18n.chain.proMicroModules.oneTable.h5.startEndData}：{startTime}
      {i18n.chain.proMicroModules.oneTable.h5.to}
      {endTime}
    </div>
  );
  const applyTimeCont = applyTime && (
    <div className="card-item">
      {i18n.chain.proMicroModules.workflow.h5.applyTime} {item.applyTime}
    </div>
  );
  return (
    <div key={item.id} className="option-card" onClick={() => controller.handleClickOption(item)}>
      <div className="report-name">
        {item.name || '-'}
        {status}
      </div>
      <div className="card-item">
        {i18n.chain.proMicroModules.oneTable.tableColumns.reportTag}：
        {_.isEmpty(tagList) ? i18n.chain.comText.none : tagList}
      </div>
      {reportTime}
      {applyTimeCont}
    </div>
  );
};

const Menus: FC<IProps> = ({ controller }) => {
  const count = useObservableState(controller.getCount$());
  const status = useObservableState(controller.getStatus$());

  const calcCls = (itemStatus: ListStatusEnum) => {
    return `status-item${itemStatus === status ? ' active' : ''}`;
  };

  const options = _.map(controller.getStatusOptions(), (o) => {
    const num = o.value === ListStatusEnum.READY ? count.ready : count.completed;
    return (
      <div className={calcCls(o.value)} onClick={() => controller.changeStatus(o.value)} key={o.value}>
        <div className="status-item-label">
          {o.label}
          <span className="record-val">{num}</span>
        </div>
      </div>
    );
  });

  return <div className="status-wrap">{options}</div>;
};

const Tools: FC<IProps> = ({ controller }) => {
  return (
    <div className="tool-wrap">
      <Menus controller={controller} />
      <If data={!controller.getHideHeaderTool()}>
        <CompSearchBar onChange={controller.changeSearchData} controller={controller} />
      </If>
    </div>
  );
};

export const CardList: FC<IProps> = ({ controller }) => {
  return (
    <div className="one-table-card-list-h5-page">
      {controller.getShowBottomMenu() && <BottomMenu menus={getOneTableH5Menus()} />}
      <Tools controller={controller} />
      <CardCurdWithSimpleSearch controller={controller.getListController()} />
    </div>
  );
};
