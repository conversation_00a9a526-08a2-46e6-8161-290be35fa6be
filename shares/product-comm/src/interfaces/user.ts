import type { Dayjs } from 'dayjs';

// 角色
export interface IRoles {
  label: string;
  value: number;
}

// 组织
export interface IOrgs {
  key: string;
  title: string;
  pid?: string;
  isLeaf?: boolean;
  permission: number[];
}

// 组织树
export interface IOrgTrees extends IOrgs {
  children?: IOrgTrees[];
}

// 用户
export interface IUsers {
  id: number;
  name: string;
  email: string;
  phone: string;
  role: number[];
  roleDisplay: string[];
  disable: boolean;
  disableDisplay: string;
  expireTime: Dayjs;
  expireTimeDisplay: string;
  permission: number[];
  uuid: string;
  organization: number[];
  orgDisplay?: string[];
}
