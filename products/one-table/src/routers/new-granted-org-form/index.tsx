import { useController } from '@mdtBsComm/hooks/use-controller';
import {
  OneTableNewGrantedOrgForm,
  OneTableNewGrantedOrgFormController,
  OneTableNewGrantedOrgFormModel,
} from '@mdtProMicroModules/pages/one-table-new-granted-org-form';

const NewGrantedOrgFormView = () => {
  const [controller] = useController(() => {
    return [
      new OneTableNewGrantedOrgFormController({
        Model: OneTableNewGrantedOrgFormModel,
      }),
      null,
    ];
  }, []);
  return <OneTableNewGrantedOrgForm controller={controller} />;
};

export default NewGrantedOrgFormView;
