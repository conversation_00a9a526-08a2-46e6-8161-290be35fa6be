import { useController } from '@mdtBsComm/hooks/use-controller';
import { AppController } from '../../app/AppController';
import { AppStyle, AppStyleController, AppStyleModel } from '../../pages/app-style';

const AppStyleView = () => {
  const [controller] = useController(() => {
    const ctrl = new AppStyleController(AppController.getInstance(), AppStyleModel);
    return [ctrl, null];
  });

  return <AppStyle controller={controller} />;
};

export default AppStyleView;
