import _ from 'lodash';
import { Field, onFieldReact } from '@formily/core';
import { drawerApi } from '@metroDesign/drawer';
import { IBpmnCustomerNode, IFormSpecValue } from '@mdtBpmnPropertiesPanel/MdtExternalProvider';
import { parseStrToObj } from '@mdtBsComm/utils/stringUtil';
import toastApi from '@mdtDesign/toast';
import { getBoolOptions } from '@mdtProComm/utils/commonUtil';
import { FormView } from '../../../components/form-view';
import i18n from '../../../languages';
import { getGlobalVarsOptions } from '../../../utils/bpmn-xml-util';
import { setFieldVisible } from '../../workflow-property-config';
import WorkflowEditController from '../WorkflowEditController';

export enum FormDefaultValueAttrEnum {
  TYPE = 'type',
  IS_USE_PREV = 'isUsePrev',
  // 旧版本字段, 打开设置页面时需要把旧版本格式转化为新版本格式
  OTHER_NODE_SPECS = 'otherNodeSpecs',
  // 新版本field映射字段
  FIELD_MAP_LIST = 'fieldMapList',
}

export enum OtherNodeSpecsAttrEnum {
  NODE_ID = 'nodeId',
  FIELD_MAP_LIST = 'fieldMapList',
}

export enum FieldMapListAttrEnum {
  RESOURCE_FIELD = 'resourceField',
  SELF_FIELD = 'selfField',
}

export enum DefaultValueTypeEnum {
  NODE = 'node',
  API = 'api',
}
const DefaultValueTypeOptions = () => {
  return [
    {
      label: i18n.chain.proMicroModules.workflow.edit.fromNodeAndVars,
      value: DefaultValueTypeEnum.NODE,
    },
  ];
};

const transformToFrontendData = (value: string, selfOptions: any[]) => {
  const { otherNodeSpecs, fieldMapList, ...rest } = parseStrToObj(value) as any;
  // 转换旧数据结构
  if (otherNodeSpecs) {
    const transformedList = _.flatten(_.map(otherNodeSpecs, 'fieldMapList'));
    return { ...rest, fieldMapList: transformedList };
  }
  // 映射关系设置默认值
  if (!fieldMapList) {
    const defaultList = _.map(selfOptions, (it) => ({
      selfField: it.value,
    }));
    return { ...rest, fieldMapList: defaultList };
  }
  return { ...rest, fieldMapList };
};

const transformToBackendData = (values: any) => {
  const { fieldMapList, ...rest } = values;
  const list = _.filter(fieldMapList, (it) => it.resourceField && it.selfField);
  return JSON.stringify({ ...rest, fieldMapList: list });
};

export const openFormDefaultValueConfig = async (
  node: IBpmnCustomerNode,
  value: IFormSpecValue,
  callback: any,
  controller: WorkflowEditController,
) => {
  if (_.isEmpty(value.spec)) {
    toastApi.warning(i18n.chain.proMicroModules.workflow.edit.tipSetForm);
    return;
  }

  const { xml } = await controller.getEditXml();
  const { groupedOptions } = getGlobalVarsOptions(xml);
  const nodeName = node.name || node.id;
  const resourceOptions = _.filter(groupedOptions, (it) => it.label !== nodeName);
  const selfOptions = _.find(groupedOptions, (it) => it.label === nodeName)!.options;

  const fieldMapListSchema = {
    type: 'array',
    title: i18n.chain.proMicroModules.workflow.edit.fieldMapList,
    'x-decorator-props': {
      colon: false,
      layout: 'vertical',
    },
    'x-decorator': 'FormItem',
    'x-component': 'ArrayTable',
    'x-component-props': { withVerticalBorder: false },
    items: {
      type: 'object',
      properties: {
        column1: {
          type: 'void',
          // eslint-disable-next-line sonarjs/no-duplicate-string
          'x-component': 'ArrayTable.Column',
          'x-component-props': { width: 200, title: i18n.chain.proMicroModules.workflow.edit.nodeAndVarsField },
          properties: {
            resourceField: {
              type: 'string',
              'x-component': 'Select',
              'x-component-props': {
                options: resourceOptions,
                placeholder: i18n.chain.comPlaceholder.select,
                showSearch: true,
                optionFilterProp: 'label',
              },
            },
          },
        },
        column2: {
          type: 'void',
          'x-component': 'ArrayTable.Column',
          'x-component-props': { width: 200, title: i18n.chain.proMicroModules.workflow.edit.currentNodeField },
          properties: {
            selfField: {
              type: 'string',
              'x-component': 'Select',
              'x-component-props': {
                options: selfOptions,
                placeholder: i18n.chain.comPlaceholder.select,
                showSearch: true,
                optionFilterProp: 'label',
              },
            },
          },
        },
        delete: {
          type: 'void',
          'x-component': 'ArrayTable.Column',
          'x-component-props': {
            dataIndex: 'operations',
            width: 5,
            fixed: 'right',
          },
          properties: {
            item: {
              type: 'void',
              'x-component': 'FormItem',
              properties: {
                remove: {
                  type: 'void',
                  'x-component': 'ArrayTable.Remove',
                },
              },
            },
          },
        },
      },
    },
    properties: {
      add: {
        type: 'void',
        title: i18n.chain.comButton.create,
        'x-component': 'ArrayTable.Addition',
      },
    },
  };
  const schema = {
    form: { colon: false, layout: 'vertical' },
    schema: {
      properties: {
        [FormDefaultValueAttrEnum.TYPE]: {
          type: 'string',
          title: i18n.chain.proMicroModules.workflow.edit.defaultType,
          default: DefaultValueTypeEnum.NODE,
          'x-decorator': 'FormItem',
          'x-component': 'Select',
          'x-component-props': {
            options: DefaultValueTypeOptions(),
            showSearch: true,
            optionFilterProp: 'label',
          },
        },
        [FormDefaultValueAttrEnum.IS_USE_PREV]: {
          type: 'boolean',
          title: i18n.chain.proMicroModules.workflow.edit.isUsePrev,
          default: false,
          'x-decorator': 'FormItem',
          'x-decorator-props': {
            style: { marginBottom: 0 },
          },
          'x-component': 'Radio.Group',
          'x-component-props': {
            options: getBoolOptions(),
          },
        },
        [FormDefaultValueAttrEnum.FIELD_MAP_LIST]: fieldMapListSchema,
      },
    },
  };

  const effects = () => {
    onFieldReact(FormDefaultValueAttrEnum.FIELD_MAP_LIST, (field) => {
      const isUsePrev = (field.query(FormDefaultValueAttrEnum.IS_USE_PREV).take() as Field)?.value;
      if (isUsePrev) {
        field.setTitle(i18n.chain.proMicroModules.workflow.edit.defaultValTitleFull);
      } else {
        field.setTitle(i18n.chain.proMicroModules.workflow.edit.defaultValTitle);
      }
    });

    setFieldVisible(FormDefaultValueAttrEnum.IS_USE_PREV, FormDefaultValueAttrEnum.TYPE, DefaultValueTypeEnum.NODE);
    setFieldVisible(
      FormDefaultValueAttrEnum.OTHER_NODE_SPECS,
      FormDefaultValueAttrEnum.TYPE,
      DefaultValueTypeEnum.NODE,
    );
  };

  drawerApi.open({
    title: i18n.chain.proMicroModules.workflow.edit.configFormDefault,
    width: 650,
    okButtonProps: { style: { display: 'none' } },
    cancelButtonProps: { style: { display: 'none' } },
    closable: true,
    destroyOnClose: true,
    children: (onClose) => (
      <FormView
        formilySchema={schema}
        formData={transformToFrontendData(value.defaultValueConfig, selfOptions)}
        onSubmit={async (values: any) => {
          callback(transformToBackendData(values));
          onClose();
          return { success: true };
        }}
        effects={effects}
      />
    ),
  });
};
