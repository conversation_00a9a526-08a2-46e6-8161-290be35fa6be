import { toast<PERSON>pi } from '@metroDesign/toast';
import { BehaviorSubject } from 'rxjs';
import type { IOperatorFilter } from '@mdtApis/interfaces';
import type { IOneTableNewOperatorDataComm } from '../../interfaces';
import i18n from '../../languages';
import { getNeedApproveSubmitDataOperatorFilter } from '../../utils/oneTableNewFilterUtil';
import { DataTableTypeEnum } from '../../utils/oneTableNewUtil';
import { OneTableNewSubmitConfirmDataTableController } from './OneTableNewSubmitConfirmDataTableController';
import { IOneTableNewSubmitConfirmModel } from './OneTableNewSubmitConfirmModel';

export interface IControllerOptions {
  Model: IOneTableNewSubmitConfirmModel;
  itemData: IOneTableNewOperatorDataComm;
  closeModalCallback?: () => void;
  onlySaveCallBack?: () => void;
  submitSuccessCallback?: () => void;
  // 保存按钮
  showSaveBtn?: boolean;
  // 取消按钮
  showCancelBtn?: boolean;
  // 返回按钮
  showBackBtn?: boolean;
  // 提交按钮
  showSubmitBtn?: boolean;
}

export class OneTableNewSubmitConfirmController {
  private Model: IOneTableNewSubmitConfirmModel;
  private dataTableController: OneTableNewSubmitConfirmDataTableController;
  private saving$ = new BehaviorSubject<boolean>(false);
  private closeModalCallback?: () => void;
  private onlySaveCallBack?: () => void;
  private submitSuccessCallback?: () => void;
  private itemData: IOneTableNewOperatorDataComm;
  private showSaveBtn?: boolean;
  private showCancelBtn?: boolean;
  private showBackBtn?: boolean;
  private showSubmitBtn?: boolean;

  public constructor(options: IControllerOptions) {
    this.closeModalCallback = options.closeModalCallback;
    this.onlySaveCallBack = options.onlySaveCallBack;
    this.submitSuccessCallback = options.submitSuccessCallback;
    const { itemData } = options;
    this.Model = options.Model;
    this.itemData = options.itemData;
    this.showSaveBtn = options.showSaveBtn;
    this.showCancelBtn = options.showCancelBtn;
    this.showBackBtn = options.showBackBtn;
    this.showSubmitBtn = options.showSubmitBtn;
    const controller = new OneTableNewSubmitConfirmDataTableController({
      itemData: options.itemData,
      dataTableType: DataTableTypeEnum.SUBMIT,
      filterLinkStatics: !itemData.isNeedApproval,
      dataPreviewOptions: { pkgId: itemData.pkgId, hasDownload: false },
    });
    this.dataTableController = controller;
  }

  public destroy() {
    this.closeModalCallback = undefined;
    this.onlySaveCallBack = undefined;
    this.submitSuccessCallback = undefined;
    this.saving$.complete();
    this.dataTableController.destroy();
  }

  public getIsNeedApproval() {
    return this.itemData.isNeedApproval;
  }

  public getSaving$() {
    return this.saving$;
  }

  public getFooterBtnConfig() {
    return {
      showSaveBtn: this.showSaveBtn,
      showCancelBtn: this.showCancelBtn,
      showBackBtn: this.showBackBtn,
      showSubmitBtn: this.showSubmitBtn,
    };
  }

  public goBack = () => {
    this.closeModalCallback?.();
  };

  public onlySave = () => {
    this.closeModalCallback?.();
    this.onlySaveCallBack?.();
  };

  public submitData = async () => {
    this.saving$.next(true);
    const { itemData } = this;
    const dtc = this.dataTableController;
    let condition = getNeedApproveSubmitDataOperatorFilter(dtc.getUserIds());
    if (!itemData.isNeedApproval) {
      const opt = dtc.getFilterParams().operator_filter;
      if (opt && typeof opt === 'object') {
        condition = opt as IOperatorFilter;
      }
    }
    const resp = await this.Model.submitData(itemData.rootWfId, itemData.assignWfId, itemData.pkgId, condition);
    this.saving$.next(false);
    if (!resp) return;
    toastApi.success(i18n.chain.comTip.submitSuccess);
    this.closeModalCallback?.();
    this.submitSuccessCallback?.();
  };

  public getDataTableController() {
    return this.dataTableController;
  }
}
