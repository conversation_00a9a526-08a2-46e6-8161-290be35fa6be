import _ from 'lodash';
import { from, Observable } from 'rxjs';
import { map, takeWhile } from 'rxjs/operators';
import { getDatapkgSimpleListRespData } from '@mdtBsBffServices/services';
import { IDatapkgsQuery, IRequestCancelToken } from '@mdtProComm/interfaces';
import { IDatapkgItem } from '@mdtProMicroModules/containers/table-card-datapkg-search';
import { DatapkgSearchModel } from '../../_util/DatapkgSearchModel';
import { pkgBffService } from '../../bff-service';
import i18n from '../../languages';

export class DatapkgShareModelBff extends DatapkgSearchModel {
  // 查询第一页数据
  // eslint-disable-next-line max-params
  public static queryFirstPagePkgs(
    data: IDatapkgsQuery,
    params: any,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    cancelToken: IRequestCancelToken,
    appName: string,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    apps?: string,
  ): Observable<[number, IDatapkgItem[]]> {
    return from(
      pkgBffService.queryDatapkgs({
        data,
        params,
        isFirstPage: true,
        cancelToken,
        respData: getDatapkgSimpleListRespData(['app']),
      }),
    ).pipe(
      takeWhile((resp) => !resp.canceled),
      map((resp) => {
        if (!resp.success) return [0, []] as [number, IDatapkgItem[]];
        const localePath = 'geo.';
        const list = _.map(resp.page_data, (item) => ({
          ...item,
          geoTypeDisplay: item.geoTypeDisplay?.includes(localePath)
            ? item.geoTypeDisplay
            : // @ts-ignore
              i18n.chain.geo[item.geoTypeDisplay],
          appName,
        }));
        return [resp.total_count, list] as unknown as [number, IDatapkgItem[]];
      }),
    );
  }

  // 查询下一页数据
  // eslint-disable-next-line max-params
  public static queryNextPageTickets(
    data: IDatapkgsQuery,
    params: any,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    cancelToken: IRequestCancelToken,
    appName: string,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    apps?: string,
  ): Observable<IDatapkgItem[]> {
    return from(
      pkgBffService.queryDatapkgs({ data, params, cancelToken, respData: getDatapkgSimpleListRespData(['app']) }),
    ).pipe(
      takeWhile((resp) => !resp.canceled),
      map((resp) => {
        if (!resp.success) return [];
        const localePath = 'geo.';
        return _.map(resp.page_data, (item) => ({
          ...item,
          geoTypeDisplay: item.geoTypeDisplay?.includes(localePath)
            ? item.geoTypeDisplay
            : // @ts-ignore
              i18n.chain.geo[item.geoTypeDisplay],
          appName,
        })) as any;
      }),
    );
  }
}
