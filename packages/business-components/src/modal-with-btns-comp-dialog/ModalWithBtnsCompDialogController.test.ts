import _ from 'lodash';
import { ModalWithBtnsCompDialogController } from './ModalWithBtnsCompDialogController';

const controllers: ModalWithBtnsCompDialogController[] = [];
let controller: ModalWithBtnsCompDialogController;

describe('ModalWithBtnsCompDialogControllerTest', () => {
  describe('测试默认状态下行为', () => {
    beforeAll(() => {
      controller = new ModalWithBtnsCompDialogController({
        modalCompOptions: { modalOptions: {} },
      });
      controllers.push(controller);
    });

    test('应该被定义', () => {
      expect(controller).toBeDefined();
    });
  });

  describe('model getModalOptionss测试', () => {
    beforeAll(() => {
      controller = new ModalWithBtnsCompDialogController({
        modalCompOptions: { modalOptions: {} },
      });
      controllers.push(controller);
    });
    test('model getModalOptionss测试', () => {
      controller.getModalCompController().getModalOptions();
      expect(controller.getModalCompController().getModalOptions().animation).toEqual('zoom');
      expect(controller.getModalCompController().getModalOptions().maskAnimation).toEqual('fade');
    });
  });

  describe('model getUiOptions测试', () => {
    beforeAll(() => {
      controller = new ModalWithBtnsCompDialogController({
        modalCompOptions: { modalOptions: {} },
      });
      controllers.push(controller);
    });
    test('model getUiOptions测试', () => {
      controller.getUiOptions();
      expect(controller.getUiOptions().okBtnProps).toEqual({ type: 'primary' });
    });
  });
});

afterAll(() => {
  _.forEach(controllers, (it) => {
    it.destroy();
  });
  controllers.length = 0;
});
