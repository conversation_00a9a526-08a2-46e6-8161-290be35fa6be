import React from 'react';
import { createBehavior, createResource } from '@designable/core';
import { DnFC } from '@designable/react';
import { DatePicker as FormilyDatePicker } from '../../../metro-formily/index';
import { QuestionIcon } from '../../icons';
import Icon from '../../icons/Calendar.svg';
import { AllLocales } from '../../locales';
import { AllSchemas } from '../../schemas';
import { createFieldSchema } from '../Field/util';
import { getResidentChildDesignerProps } from '../ResidentInfo';

const FormilyDateRangePicker = FormilyDatePicker.RangePicker;
export const DatePicker: DnFC<React.ComponentProps<typeof FormilyDatePicker>> = FormilyDatePicker;
export const DateRangePicker: DnFC<React.ComponentProps<typeof FormilyDateRangePicker>> = FormilyDateRangePicker;

const COMP_NAME = 'DatePicker';
const COMP_NAME_OTHER = 'DatePicker.RangePicker';

DatePicker.Behavior = createBehavior({
  name: COMP_NAME,
  extends: ['Field'],
  selector: (node) => node.props?.['x-component'] === COMP_NAME,
  designerProps: (node) => ({
    ...getResidentChildDesignerProps(node),
    propsSchema: createFieldSchema(AllSchemas.DatePicker),
  }),
  designerLocales: AllLocales.DatePicker,
});

DatePicker.Resource = createResource({
  icon: <img className="dn-resource-item-icon" src={Icon} alt="" />,
  elements: [
    {
      componentName: 'Field',
      props: {
        type: 'string',
        'x-decorator': 'FormItem',
        'x-component': COMP_NAME,
      },
    },
  ],
});

DateRangePicker.Behavior = createBehavior({
  name: COMP_NAME_OTHER,
  extends: ['Field'],
  selector: (node) => node.props?.['x-component'] === COMP_NAME_OTHER,
  designerProps: {
    propsSchema: createFieldSchema(AllSchemas.DatePicker),
  },
  designerLocales: AllLocales.DatePicker,
});

DateRangePicker.Resource = createResource({
  icon: <QuestionIcon comp="DateRangePicker" />,
  elements: [
    {
      componentName: 'Field',
      props: {
        type: 'string[]',
        'x-decorator': 'FormItem',
        'x-component': COMP_NAME_OTHER,
        'x-component-props': {
          allowClear: true,
          showNow: true,
        },
      },
    },
  ],
});
