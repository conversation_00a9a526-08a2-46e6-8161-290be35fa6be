import type { Socket } from 'socket.io-client';
// @ts-ignore
import io from 'socket.io-client/dist/socket.io.js';

export type { Socket } from 'socket.io-client';

// 默认最大重连尝试次数
export const DEFAULT_RECONNECTION_ATTEMPTS = 10;

export interface ISocketOptions {
  socketUrl: string;
  authorization?: string;
  socketOptions?: any;
  onMaxReconnectionAttempts?: () => void;
  reconnectionAttempts?: number;
}

export class SocketClass {
  public static createSocket(options: ISocketOptions) {
    return new SocketClass(options);
  }

  private socket: Socket;
  private maxReconnectionAttempts: number;
  public constructor(options: ISocketOptions) {
    this.maxReconnectionAttempts = options.reconnectionAttempts ?? DEFAULT_RECONNECTION_ATTEMPTS;

    const ins = io(options.socketUrl, {
      reconnection: true,
      reconnectionAttempts: this.maxReconnectionAttempts,
      transports: ['websocket'],
      autoConnect: false,
      ...(options.socketOptions || {}),
      query: { authorization: options.authorization },
    });
    this.addScoketCommListener(ins, options.onMaxReconnectionAttempts);
    this.socket = ins;
  }

  public getSocketIns() {
    return this.socket;
  }

  private addScoketCommListener = (socket: Socket, onMaxReconnectionAttempts?: () => void) => {
    // 手动跟踪重连次数
    let reconnectAttempts = 0;

    socket.on('connect', () => {
      console.info('socket connect success:', new Date().toLocaleString());
      reconnectAttempts = 0;
    });

    socket.on('connect_error', (error: Error) => {
      if (reconnectAttempts < this.maxReconnectionAttempts) {
        reconnectAttempts++;
      }
      console.error(`socket connect error (${reconnectAttempts}/${this.maxReconnectionAttempts}), retry...:`, error);

      if (reconnectAttempts >= this.maxReconnectionAttempts) {
        socket.io.opts.reconnection = false;
        socket.disconnect();
        onMaxReconnectionAttempts?.();
        console.error('socket reached maximum reconnection attempts');
      }
    });
    // 断线自动重连
    socket.on('disconnect', (reason: string) => {
      if (reason === 'io server disconnect') {
        socket.connect();
      }
    });
    // 监听网络延迟
    socket.on('pong', (latency: number) => {
      latency > 200 && console.warn('The network latency is high: ', `${latency}ms`);
    });
  };
}
