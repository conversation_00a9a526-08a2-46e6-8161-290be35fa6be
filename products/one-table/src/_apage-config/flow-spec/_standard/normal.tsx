import i18n from '../../../languages';
import { getFeatureFlags } from '../comm';

export const flowSpec = {
  formilySchema: {
    form: { layout: 'vertical', colon: false },
    schema: {
      type: 'object',
      properties: {
        flowTitle: {
          type: 'void',
          'x-component': 'DescriptionText',
          'x-component-props': {
            textContent: i18n.chain.proMicroModules.oneTable.fillSetting,
            textMode: 'h3',
            style: { marginBottom: 12 },
          },
        },
        end_date: {
          type: 'string',
          title: i18n.chain.proMicroModules.oneTable.endFillDate,
          'x-decorator': 'FormItem',
          'x-component': 'DatePicker',
        },
        flowTitle2: {
          type: 'void',
          'x-component': 'DescriptionText',
          'x-component-props': {
            textContent: i18n.chain.proMicroModules.oneTable.flowSetting,
            textMode: 'h3',
            style: { borderTop: `1px solid var(--metro-divider-0)`, paddingTop: 16 },
          },
        },
        flowConfig: {
          type: 'void',
          properties: {
            space: {
              type: 'void',
              'x-component': 'Space',
              'x-component-props': { direction: 'vertical', size: 12 },
              properties: {
                // check_data_submitted: {
                //   type: 'boolean',
                //   'x-component': 'Checkbox',
                //   default: true,
                //   'x-component-props': {
                //     children: '是否需要提交/同步数据给上级',
                //     style: { fontWeight: 500 },
                //   },
                // },
                need_approval: {
                  type: 'boolean',
                  'x-component': 'Checkbox',
                  default: true,
                  'x-component-props': { children: i18n.chain.proMicroModules.oneTable.needApproval },
                },
                feature_flags: getFeatureFlags(),
              },
            },
          },
        },
      },
    },
  },
  allSettingValues: {},
};
