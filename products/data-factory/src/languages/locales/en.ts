/* eslint-disable sonarjs/no-duplicate-string */
import { en as microEn } from '@mdtProMicroModules/languages';
import { Locale } from './cn';

const en: Locale = {
  ...microEn,
  dataFactory: {
    lowCodeQe: 'QLang Low-Code Editor',
    selectWriteQeType: 'Please select Qe Writing Method',
    selectWriteLowCode: 'Qe Editor',
    selectWriteHightCode: 'Handwritten Qe',
    menu: {
      etl: 'ETL Jobs',
      monitor: 'Monitor Jobs',
    },
    monitor: {
      allData: 'All Data',
      qualified: 'Qualified',
      unqualified: 'Unqualified',
      notMonitored: 'Not Monitored',
      refresh: 'Refresh',
      searchData: 'Search Data',
      datapkgName: 'Data Package Name',
      dataUpdatetime: 'Data Update Time',
      lastRunTime: 'Last Run Time',
      monitorResult: 'Monitoring Result',
      runError: 'Quality Control Run Error',
      runSuccess: 'Quality Control Run Passed',
      runUnqualified: 'Quality Control Run Failed',
    },
    etl: {
      play: 'Execute',
      download: 'Download',
      delData: 'Delete Data',
      success: 'Success',
      failed: 'Failed',
      running: 'Running',
      jobName: 'Job Name',
      jobAdmin: 'Job Admin',
      lastPlayTime: 'Last Execution Time',
      lastPlayResult: 'Last Execution Result',
      runStartTime: 'Start Time',
      runEndTime: 'End Time',
      useTime: 'Time Used',
      result: 'Result',
      uploadFlow: 'Upload Flow',
      createFlow: 'Create Flow',
      searchFlow: 'Search Flow',
      editFlow: 'Edit Flow',
      jsonInvalid: (msg?: string) => `Invalid JSON format: ${msg}`,
      timingJob: 'Timing Job',
      playLog: 'Execution Log',
      etlEmptyError: 'Please enter ETL name!',
      saveFailed: 'Save failed',
      saveSuccess: 'Save success',
      globalSetting: 'Global Setting',
    },
    flow: {
      play: 'Execute',
      domDataPreview: 'Node Data Preview',
      domDataPreviewDesc: 'The current result is not the complete calculation result',
      clickHere: 'Click here',
      runSamplesSetting: 'Set the number of sample rows for trial run',
      noName: 'Unnamed',
      giveUp: 'Cancel',
      complete: 'Complete',
      domRunError: 'Node execution error!',
      domRunEnd: 'Node execution ends here',
      domNeedParams: 'Warning: There are required parameters that have not been filled in for this node!',
      runSamples: 'Number of sample rows for trial run',
      getKeyInfo: 'Get field information',
      fromDatapkgName: 'Read from Data Package',
      fromDatapkgTip: 'Read data from an existing data package, requires view_detail permission',
      fromDbTableName: 'Read from Database Table (Whole Table)',
      fromDbTableTip: 'Read data from a whole table in a database',
      fromSqlName: 'Read from Database Table (SQL)',
      fromSqlTip: 'Read data from a database using a SQL SELECT statement',
      fromQlangRaw: 'Read data by QLang',
      fromQlangRawTip:
        'Generate the required data by writing QLang, which requires the right to retrieve the data packet (view_detail)',
      fromFileIdName: 'Upload Local File',
      fromFileIdTip: 'Read data from a file on the local computer',
      fromFileName: 'Read from Remote File',
      fromFileTip: 'Read data from a file on the internet',
      fromFSSheet: 'Read from feishu spreadsheet',
      fromFSSheetTip: 'Read data from feishu spreadsheet',
      fromFSTable: 'Read from feishu bitable',
      fromFSTableTip: 'Read data from feishu bitable',
      fromDKSheet: 'Read from DingTalk Spreadsheet',
      fromDKSheetTip: 'Retrieve data from DingTalk spreadsheet',
      dkUnionId: 'DingTalk User ID',
      dkUnionIdTip: 'Represents the unique union id of a user in DingTalk',
      dkWorkbookId: 'ID of the DingTalk Spreadsheet',
      dkWorkbookIdTip: 'Obtainable via Spreadsheet->Document Information->Spreadsheet ID in DingTalk document',
      dkSheetId: 'Specify Worksheet ID',
      dkSheetName: 'Specify Worksheet Name',
      dkSheetIndex: 'Specify Worksheet Index',
      dkSheetIndexTip: 'Default is the first sheet, start counting from 0 when entering',
      dkWithFormat: 'Include Format Information',
      dkWithFormatTip:
        'Whether to include cell format with the spreadsheet data; when set to True, formatted cells are returned with their display value. For example, if a cell is formatted to show percentages and has a value of 1, it will return 100%',
      dkRange: 'Specified data range',
      dkRangeTip:
        'The range of the worksheet to be read, for example, A1:B5. If not specified, the entire table will be read.',
      fromDKProcess: 'Retrieve DingTalk Approval Data',
      fromDKProcessTip: 'Read approval instance data within a certain period of time in DingTalk',
      dkProcessCode: 'Process Form ID',
      dkProcessCodeTip: 'Can be found at the bottom of the basic settings page in the approval form edit view',
      wwApprovalData: 'Extract approval data from WeCom (WeChat Work)',
      wwApprovalDataTip: 'Extract approval data from WeCom (WeChat Work)',
      wwApprovalDataApplicationId: 'Use third-party enterprise application id',
      wwApprovalDataApplicationIdTip:
        'Please contact the developer to obtain this after installing the third-party application',
      wwApprovalDataAuthCorpId: 'Open_id of the third-party application provider',
      wwApprovalDataAuthCorpIdTip:
        'Please contact the developer to obtain this after installing the third-party application',
      wwApprovalDataPermanentCode: 'Enterprise authorization code',
      wwApprovalDataPermanentCodeTip:
        'Please contact the developer to obtain this after installing the third-party application',
      wwApprovalDataStartTime: 'Query start time',
      wwApprovalDataStartTimeTip: 'Initiate a query for the process started after this time',
      wwApprovalDataEndTime: 'Query end time',
      wwApprovalDataEndTimeTip: 'Initiate a query for the process started before this time',
      wwApprovalDataTemplateId: 'Approval process template id',
      wwApprovalDataTemplateIdTip: 'Can be obtained on the approval template editing page',
      dkStartTime: 'Start Time',
      dkStartTimeTip:
        'Represents instances initiated after this time; by default, data after 0:00 of 119 days ago will be fetched',
      dkEndTime: 'End Time',
      dkEndTimeTip: 'Query processes initiated before this time',
      dkStatus: 'Approval Status',
      dkStatusTip: 'If no value is passed, the default is to query all statuses',
      dkProcessNew: 'New',
      dkProcessRunning: 'Running',
      dkProcessTerminated: 'Terminated',
      dkProcessCompleted: 'Completed',
      dkProcessCanceled: 'Canceled',
      fsAppID: 'App ID',
      fsAppIDTip: 'The unique identifier of a Feishu Open Platform application',
      fsAppSecret: 'App Secret',
      fsAppSecretTip: 'The secret key of a Feishu Open Platform application',
      fsSpreadsheetToken: 'Spreadsheet ID',
      fsSpreadsheetTokenTip: 'The unique identifier of a Feishu Spreadsheet',
      fsRange: 'Specified data range',
      fsRangeTip:
        'The range of the worksheet to be read, for example, A1:B5. If not specified, the entire table will be read.',
      fsSheetIndex: 'Specified worksheet index',
      fsSheetIndexTip:
        'The default option is to use the first one, and counting starts from 0 when filling in a specific value.',
      fsSheetName: 'Specified worksheet name',
      fsSheetId: 'Specified worksheet ID',
      fsAppToken: 'Datatable ID',
      fsAppTokenTip: 'The unique identifier of a Feishu Datatable',
      fsColumns: 'Specified columns',
      fsColumnsTip: 'If not specified, all columns will be read',
      fsTableName: 'Specified data table name',
      fsTableId: 'Specified data table ID',
      fsWithRecordId: 'Whether to read row IDs',
      fsWithRecordIdTip: 'The unique row ID generated by Feishu for each row of data',
      fsWithUserId: 'Whether to read user IDs',
      fsWithUserIdTip: 'By default, reads the name of the person.',
      appendName: 'Append Data Vertically',
      appendTip: 'Append one set of data to another set of data',
      copyColumnName: 'Copy Column',
      copyColumnTip: 'Copy specified columns and generate new columns',
      pipeName: 'Python',
      pipeTip: 'Execute Python functions in a fixed format',
      insertSequenceColumnName: 'Add Sequence Column',
      insertSequenceColumnTip:
        'Add a new column and fill it with an incrementing sequence based on a starting value and step size',
      insertConstColumnName: 'Add Constant Column',
      insertConstColumnTip: 'Add a new column and fill it with a constant value',
      joinName: 'Merge Data Horizontally',
      joinTip: 'Merge two sets of data together based on specified columns',
      sjoinName: 'Spatially Join Data',
      sjoinTip: 'Spatially join and tag point data with polygon data',
      pctChangeName: 'Period-over-Period Change',
      pctChangeTip: 'Calculate the ratio/change between the current value and the previous value',
      upsertName: 'Update Data',
      upsertTip:
        'Compare two sets of data based on one or more index columns and update the old data with the new data',
      onlyColumnsName: 'Select Columns',
      onlyColumnsTip: 'Select columns to keep',
      renameName: 'Rename Columns',
      renameTip: 'Rename specified columns',
      replaceName: 'Replace Text',
      replaceTip: 'Replace the content of text (str) fields, supports string replacement and regex replacement',
      typecastName: 'Change Column Type',
      typecastTip: 'Convert columns to specified types',
      nullIfName: 'Replace with Null',
      nullIfTip: 'Replace values with null',
      stripName: 'Strip Values/Spaces',
      stripTip: 'Can remove/replace specified characters or spaces',
      toStripTitle: 'Replacement Target',
      toStripDescription:
        'Specify the target value or character to replace. If not specified, default is to replace all whitespace characters including spaces, /r/n/t, etc.',
      stripColsTitle: 'Specify Stripping Columns',
      stripColsDescription: 'Specify columns that need to be stripped/replaced according to the rules',
      stripDirTitle: 'Strip/Replace Direction',
      stripDirDescription:
        'Choose the processing direction - determines which side of the string or all to remove/replace the specified value or space.',
      stripDirLeft: 'Remove values/spaces from the left side of the string',
      stripDirRight: 'Remove values/spaces from the right side of the string',
      stripDirBoth: 'Remove values/spaces from both ends of the string',
      stripDirLeftAndRight: 'Remove values/spaces from both ends of the string',
      stripDirAll: 'Remove all values/spaces from the string',
      stripDirMiddle: 'Remove values/spaces from the middle of the string',
      valueMappingName: 'Replace with Mapping',
      valueMappingTip: 'Map one column of data to another column based on a preset mapping relationship',
      groupbyName: 'Group and Aggregate',
      groupbyTip: 'Group data based on one column and aggregate other columns',
      dropDuplicatesName: 'Remove Duplicate Rows',
      dropDuplicatesTip: 'Remove rows with duplicate values based on one or more columns',
      dropColumnsName: 'Remove Columns',
      dropColumnsTip: 'Remove a column based on column name',
      sortName: 'Sort',
      sortTip: 'Sort data based on one or more columns',
      filterName: 'Filter Rows',
      filterTip: 'Filter data',
      geocodeName: 'Geocode Addresses',
      geocodeTip:
        'Infer map coordinates based on addresses and generate longitude and latitude (lng and lat); to display on a map, follow with the "Generate Geometry Column Based on Longitude and Latitude" node',
      lnglatToGeometryName: 'Generate Geometry Column Based on Longitude and Latitude',
      lnglatToGeometryTip:
        'Generate a shapely.point type geometry column based on longitude and latitude, can be displayed on a map',
      convertGeometryName: 'Convert Geometry Format',
      convertGeometryTip:
        'Convert geometry from one format to another, currently supports conversion between wkb, wkt, geojson, and shapely',
      executeSqlName: 'Execute SQL Statement',
      executeSqlTip:
        'Execute any SQL statement in a database, does not return data (note the difference with "Read from Database Table (SQL)")',
      runFlowName: 'Run Flow',
      runFlowTip: 'Execute the Flow you have created',
      createTableName: 'Create Database Table',
      createTableTip:
        'Create a data table in the database using the data result. If you want to insert data rows, you need to follow it with the "Update Database Table" node',
      toUpsertDbTableName: 'Update Database Table',
      toUpsertDbTableTip:
        'Update an existing data table in the database using the data result based on a unique index column (insert+update)',
      toMarketName: 'Create Data Package',
      toMarketTip: 'Create a data package using the data result',
      toUpdateDatapkgName: 'Update Data Package',
      toUpdateDatapkgTip:
        'Update a specified data package using the data result. Editing rights (update) for the data package are required',
      aggFuncNameDistinctCount: 'Distinct Count',
      aggFuncNameCount: 'Count',
      aggFuncNameSum: 'Sum',
      aggFuncNameMean: 'Mean',
      aggFuncNameMin: 'Minimum',
      aggFuncNameMax: 'Maximum',
      aggFuncNameStd: 'Standard Deviation',
      aggFuncNameFirst: 'First Value',
      aggFuncNameLast: 'Last Value',
      remoteFileTypeOss: 'Aliyun OSS File Service',
      remoteFileTypeMinio: 'Minio File Service',
      remoteFileTypeFtp: 'FTP File Service',
      remoteFileTypeSftp: 'sFTP File Service',
      remoteFileTypeHttp: 'HTTP Static File Service',
      tableConnectTypeInner: 'Intersection',
      tableConnectTypeOuter: 'Union',
      tableConnectTypeLeft: 'Left Join',
      tableConnectTypeRight: 'Right Join',
      tableActionTypeNew: 'New',
      tableActionTypeReplace: 'Replace',
      tableActionTypeTruncate: 'Truncate',
      commonEq: 'Equal to',
      commonNe: 'Not Equal to',
      commonIn: 'In the Set',
      commonIs: 'Is Null',
      numberGt: 'Greater Than',
      numberGe: 'Greater Than or Equal to',
      numberLt: 'Less Than',
      numberLe: 'Less Than or Equal to',
      numberBetween: 'Between',
      textStartswith: 'Starts With',
      textEndswith: 'Ends With',
      textLike: 'Contains',
      textIlike: 'Contains (Case Insensitive)',
      textContain: 'Text Contains a Substring',
      textMatch: 'Matches Regular Expression',
      filterTextIs: 'Is Null',
      filterTextEq: 'Equal to',
      filterTextStartswith: 'Starts With',
      filterTextEndswith: 'Ends With',
      filterNumberIs: 'Is Null',
      filterNumberEq: 'Equal to',
      filterNumberGt: 'Greater Than',
      filterNumberGe: 'Greater Than or Equal to',
      filterNumberLt: 'Less Than',
      filterNumberLe: 'Less Than or Equal to',
      filterNumberBetween: 'Between',
      convertGeometryFormatWkb: 'WKB or EWKB',
      convertGeometryFormatWkt: 'WKT or EWKT',
      convertGeometryFormatGeojson: 'GeoJSON',
      convertGeometryFormatShapely: 'Shapely',
      dataHandle: 'Handle',
      filterData: 'Filter',
      replaceData: 'Replace',
      addRow: 'Add Col',
      mergedData: 'Merge',
      geoHandler: 'Geo',
      inputSource: 'Input Source',
      outputSource: 'Output Source',
      create: 'Create',
      delDomOrBorder: 'Delete Node or Edge',
      other: 'Other',
      inputPile: 'Input Pile',
      outputPile: 'Input Pile',
      domRequiredParamsEmpty: (label?: string, field?: string) =>
        `The required field 【${field}】 of the node 【${label}】 is empty`,
      flowInvalidDAG: 'The FLOW may not conform to the DAG specification, please check!',
      temporaryFlowLeft: 'Temporary Flow - Left',
      temporaryFlowRight: 'Temporary Flow - Right',
      temporaryFlow: 'Temporary Flow',
      flowRequiredDom: 'The FLOW must contain at least one node!',
      domParamsCompletion: 'Please complete the node parameters first',
      chooseDataSource: 'Choose Data Source',
      pleaseChooseDataSource: 'Please choose a data source',
      datapkg: 'Data Package',
      datapkgUuid: 'UUID of the Data Package',
      searchDatapkg: 'Please search for a data package',
      getGeoCol: 'Get Geospatial Column',
      getGeoRowDesc:
        'Whether to get the geospatial data column: default is not to get; true: get the geospatial data column; false: do not get the geospatial data column',
      specifyDatapkgCol: 'Specify Data Package Column',
      noSpecifySyncAll: 'No specification, load all columns of the data package by default',
      excludeDatapkgCol: 'Exclude Data Package Column',
      excludeDatapkgColDesc: 'Exclude some columns, which is useful when there are many columns in the data package',
      filterDatapkgData: 'Filter Data Package Data',
      personalData: 'Personal Data',
      otherOrgs: 'Other Organizations',
      fileId: 'File ID',
      uploadFile: 'Upload File',
      fileIdDesc: 'The file corresponding to the file_id',
      db: 'Database',
      needOperateDb: 'The database to be operated',
      schemaDesc: 'The name of the schema to which the data table belongs',
      dataDbName: 'Data Table Name',
      dbName: 'Database Name',
      savedDbId: 'Saved Database ID',
      sqlInstruction: 'SQL Statement',
      spreadEdit: 'Expand Editing',
      flowName: 'Flow Name',
      fileToFlowId: 'The flow_id corresponding to the file',
      originFileType: 'Remote File Type',
      fileInclude: 'The file needs to include the suffix',
      bucketName: 'Bucket Name',
      filePath: 'File Path',
      spreadMore: 'Expand More',
      endPoint: 'Endpoint',
      fileEncodeWay: 'File Encoding Method',
      connectTimeout: 'Connection Timeout',
      hostAddress: 'Host Address',
      username: 'Username',
      password: 'Password',
      port: 'Port',
      fileUrlAddress: 'File URL Address',
      order: 'Order',
      orderDesc: 'If the columns are not aligned, whether to sort the columns',
      params: 'Field Name',
      paramsDesc:
        'Specify the columns that need to replace values according to the rules, and replace each string that meets the rules in the specified columns',
      partialReplace: 'Partial Replace',
      dataReplace: 'Replace Data',
      afterReplace: 'Before Replacement',
      replaceWith: 'Replace With',
      renameParams: 'Rename Fields',
      renameParamsDesc: 'Rename data fields',
      paramsMap: 'Field Mapping Relationship',
      originParams: 'Original Field Name',
      newParams: 'New Field Name',
      addSequenceParams: 'Add Sequence Field',
      col: 'Column Name',
      colDesc2: 'Add a serialized value column in the DataFrame',
      type: 'Type',
      value: 'Value',
      insertValue: 'Inserted Value',
      existColSkip: 'Skip if the column exists',
      startValue: 'Start Value',
      endValue: 'End Value',
      intervalLength: 'Interval Length',
      chooseColData: 'Select Column Data',
      chooseColDataDesc: 'All column names that need to be selected',
      copyCol: 'Copy Column',
      copyColDesc: 'Select the columns to be copied and give them new names',
      originCol: 'Original Column Name',
      newCol: 'New Column Name',
      leftDbParamsFilter: 'Left Table Field Filter',
      leftDbParamsFilterDesc: 'Specify the columns used by the left data for join',
      rightDbParamsFilter: 'Right Table Field Filter',
      rightDbParamsFilterDesc: 'Specify the columns used by the right data for join',
      mergedWay: 'Merge Method',
      mergedWayDesc: 'Not filled by default inner',
      essentialColEqual: 'Whether the key columns are the same',
      essentialParams: 'Key Fields',
      essentialParamsDesc:
        'List of column or index level names to join on. These must be found in both DataFrames. If on is None and not merging on indexes, then it defaults to the intersection of the columns in both DataFrames.',
      leftDbEssentialParams: 'Left table key columns',
      leftDbEssentialParamsDesc: 'List of column or index level names to join on for the left DataFrame.',
      rightDbEssentialParams: 'Right table key columns',
      rightDbEssentialParamsDesc: 'List of column or index level names to join on for the right DataFrame.',
      orderDesc2:
        'Sort the join keys in the result DataFrame in lexicographical order. If False, the order of the join keys depends on the join type (how keyword).',
      mergedWayDesc2: 'Defaults to intersection',
      leftConnect: 'Left join',
      rightConnect: 'Right join',
      intersection: 'Intersection',
      connectionDesc:
        'Currently only supports geographic association between a point data and a polygon data, and the output result can only be point data',
      geoConnectWay: 'Geographic association method',
      geoConnectWayDesc: 'Geographic association method, default is intersection',
      intersect: 'Intersection',
      leftDbGeoParams: 'Left table geographic field',
      leftDbGeoParamsDesc: 'The geographic column in the left DataFrame used for geographic association',
      rightDbGeoParams: 'Right table geographic field',
      rightDbGeoParamsDesc: 'The geographic column in the right DataFrame used for geographic association',
      calculateWay: 'Calculation method',
      calculateWayDesc:
        'YoY: compare with the same period in history; MoM: compare with the previous statistical period',
      yoy: 'YoY',
      ring: 'MoM',
      timeParams: 'Time field',
      timeParamsDesc: 'Time field in the data table',
      timeParamsEmpty: 'Please select a time field',
      calculateParams: 'Calculation field',
      calculateParamsDesc: 'Fields to be calculated',
      calculateParamsEmpty: 'Fields to be calculated',
      generateParams: 'Generated field',
      generateParamsDesc: 'The name of the new field generated after calculation is completed',
      generateParamsEmpty: 'Please enter the generated field name',
      choosePolymerizationParams: 'Select aggregation field',
      userCode: 'User code',
      userCodeDesc:
        'The input and output of this node are both a single dataframe. For example, read data package -> Python_Pipe -> update data package. The code needs to be included in a run function, and parameters can be added to the function (the default parameter type is str, and it can be converted as needed).',
      date: 'Date',
      runParamsByUserCode: 'Parameters and values used to execute user code',
      keyParams: 'Key of parameter',
      valueParams: 'Value of parameter',
      addEntry: 'Add entry',
      essentialParamsDesc2: 'Columns used to determine which rows to UPSERT',
      alignRules: 'Alignment rules',
      alignRulesDesc:
        "'left': default; based on df columns 'right': based on new columns 'inner': keep only the columns that exist in both df and new 'outer': keep all columns, and fill in blank positions with null values for columns that only appear in one dataframe.",
      alignRulesDesc2: 'Control how to perform upsert when columns are not aligned',
      defineParamsInfo: 'Field definition information',
      paramsName: 'Field name',
      paramsType: 'Field type',
      colDesc: 'This column needs to obtain field information first',
      operate: 'Operator',
      valueDesc:
        'The parameter corresponding to the operator, the format of the parameter will vary depending on the operator and column type',
      paramsType2: 'Parameter type',
      createDesc: 'Please select the start and end values',
      colOperate: 'Column name operation',
      map: 'Mapping',
      mapData: 'Mapping data',
      mapOriginData: 'Mapping source data',
      mapTargetData: 'Mapping target data',
      emptyFillWith: 'Fill null values with',
      otherFillWith: 'Fill unmatched values with',
      groupParams: 'Grouping field',
      groupParamsDesc: 'One or more column names',
      groupAggregation: 'Grouping aggregation',
      calCol: 'Calculation column',
      calFunc: 'Calculation method',
      returnCol: 'Return column name',
      uniqueColName: 'Column name for deduplication calculation',
      uniqueColNameDesc: 'You can run "Get Field Information" to get the column names of the input table',
      strategy: 'Strategy',
      strategyDesc: 'Strategy for handling duplicate rows',
      saveFirstRepeatCol: 'Keep the first occurrence of duplicate rows',
      saveLastRepeatCol: 'Keep the last occurrence of duplicate rows',
      delAllRepeatCol: 'Delete all duplicate rows',
      delCol: 'Delete column',
      delColDesc: 'You can click to get field information to get the names of all columns',
      byDesc:
        'One or more columns used for sorting. Note the column order. The later column will only be sorted when there is a tie after the previous column is sorted',
      orderAlgorithm: 'Sorting algorithm',
      orderAlgorithmDesc: 'Sorting algorithm, default is quicksort, optional quicksort, mergesort, heapsort',
      orderRules: 'Sorting rules',
      ascending: 'Ascending',
      descending: 'Descending',
      reOrder: 'Reorder',
      reOrderDesc:
        'Default is not to reorder. True needs to be reordered, and the index is reordered from 0-1-2 after sorting',
      afterOrderNullPos: 'Position of null values after sorting',
      afterOrderNullPosDesc:
        'True puts null values at the front after sorting; False puts null values at the end after sorting',
      first: 'Front',
      last: 'Back',
      filterReverse: 'Reverse the filter condition',
      addressCol: 'Address column',
      longCol: 'Longitude column',
      longColDesc: 'The name of the column where the longitude is stored in the result',
      latitudeCol: 'Latitude column',
      latitudeColDesc: 'The name of the column where the latitude is stored in the result',
      coordinate: 'Coordinate system',
      coordinateDesc: 'Default is wgs84',
      skipLatitudeCol: 'Skip rows with existing longitude and latitude',
      scopeWgs: 'World Geodetic System (WGS84)',
      scopeBd: 'Baidu Coordinate System (BD09)',
      scopeGc: 'National Geodetic Coordinate System (GCJ02)',
      scopeShh: 'Shanghai Urban Construction Coordinate System (SHH)',
      longColDesc2: 'The longitude column used to generate geometry, default is lng',
      latitudeColDesc2: 'The latitude column used to generate geometry, default is lat',
      geoCol: 'Geometry column',
      geoColDesc: 'The column where the geometry is stored, default is geometry',
      inputGeoFormat: 'Input geometry format',
      outputGeoFormat: 'Output geometry format',
      inputGeoCol: 'Input geometry column',
      outputGeoCol: 'Output geometry column',
      defaultGeoCol: 'Default geometry column',
      outputGeoColDesc: 'If not specified, it is equal to the input geographic column',
      defaultIdentify: 'Default identification',
      geoFormatEuqal: 'Unified geometry format of input column',
      geoFormatEuqalDesc: 'If the geometry format of the input column is unified, the conversion speed will be faster',
      dropDbcreateAgain: 'Drop table and create again',
      dropDbcreateAgainDesc:
        'If the table to be created already exists, whether to drop the table first and then create it',
      delExistData: 'Delete existing data',
      delExistDataDesc: 'If the table to be created already exists, whether to delete the existing data',
      asyncAddCol: 'Synchronously add columns',
      asyncAddColDesc:
        'If the table to be created already exists, but new columns are added in the new DDL, whether to synchronize these new columns to the data table',
      asyncDelCol: 'Synchronously delete columns',
      asyncDelColDesc:
        'If the table to be created already exists, but some columns are deleted in the new DDL, whether to delete these columns from the data table',
      schemaOperate: 'Schema to operate',
      surfaceName: 'Table name',
      autoCreateId: 'Automatically create index column (id)',
      autoCreateIdDesc: 'Automatically add an auto-incrementing ID column',
      indicateParamsType: 'Indicate the type of this field',
      personreadable: 'Human-readable label or title',
      paramsDesc2: 'Description of the field',
      indicateParamsFormat: 'Indicate the format of this field type',
      noSupport: 'Not supported yet',
      outputSourceDesc:
        'The datasource_id corresponding to the data package (currently defaults to exclusive data source)',
      datapkgName: 'Data package name',
      dataType: 'Data type',
      dataTypeDesc: 'The geometry_type of the data package, which will be automatically determined if not specified',
      datapkgDesc: 'Data package description',
      datapkgDescDesc: 'Data package description, if not provided, it is the same as the data package name',
      idCol: 'ID column',
      idColDesc:
        'The ID column will be used as the unique identification column when updating data, and only fields with no duplicate content can be selected',
      nameCol: 'Name column',
      selectGeoCol: 'Select_geometry column',
      selectGeoColDesc: 'Used for geographic operations',
      displayGeoCol: 'Display_geometry column',
      displayGeoColDesc: 'Used for dual geographic data',
      linkGeometryCol: 'Link_geometry column',
      linkGeometryColDesc: 'Used for point-to-point data',
      addressSearchCol: 'Address search column',
      addressSearchColDesc:
        'The system will use this field as the address to generate latitude and longitude coordinates',
      cityIdDsc: 'City_id corresponding to the data package',
      datapkgTag: 'Data package tag',
      datapkgTagDesc: 'Add multiple tags to the data package',
      multiGeo: 'Use Multi geometry format',
      multiGeoDesc: 'Whether to use the Multi geometry format',
      allowGeoColEmpty: 'Allow geographic column to be completely empty',
      allowGeoColEmptyDesc:
        'Whether to allow the geographic column to be completely empty; by default, there should be no data package with an empty geographic column',
      indexCol: 'Index column',
      indexColDesc:
        'The index column name (usually id) when updating the data table. When executed, the new and old tables will be compared according to the index. Rows that exist in both the new and old tables will be updated, and only rows that exist in the new table will be inserted into the old table. If "Automatically create index column (id)" is selected when creating the database table, "id" needs to be manually filled in here.',
      updateConflictCol: 'Update conflict column',
      updateConflictColDesc:
        'The names of all columns that need to be updated when there is a conflict during data insertion, default is all data columns',
      fragmentSize: 'Fragment size',
      fragmentSizeDesc: 'The size of each database operation fragment, set to 0 means no fragmentation',
      skipConflict: 'Skip conflicts',
      skipConflictDesc: 'If the data already exists in the database, do not update it',
      dataSet: 'Data source',
      dataSetDesc: 'The data source id corresponding to the data package',
      updateDatapkgOperate: 'Update data package operation',
      updateDatapkgOperateDesc: 'Specify how to update the data package (Note: currently only support replace)',
      forceReplace: 'Force replace',
      forceReplaceDesc:
        'When the data package already exists, replace the data package and skip some checks (use with caution)',
      tableAction: 'Base Table Operation',
      tableActionDesc:
        'When updating the data package, be aware that if there are fewer columns than before, do not use replace or clear options to avoid issues.',
      replace: 'Replace',
      outputDatasource: 'Output data source',
      schema: 'Schema name',
    },
  },
};

export default en;
