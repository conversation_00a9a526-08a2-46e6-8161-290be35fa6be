import React from 'react';
import { getQFromUrl } from '@mdtProComm/utils/urlUtil';
import i18n from '../../../languages';
import logo from './logo.png';

export const LogoWidget: React.FC = () => {
  const { enableSchemaSelect } = getQFromUrl();
  const cls = `dm-app-header-title ${enableSchemaSelect ? 'dm-app-header-with-back' : ''}`;

  return (
    <div className={cls}>
      <img className="dm-app-header-logo" src={logo} alt="logo" />
      <span>{i18n.chain.designable.formDesign}</span>
    </div>
  );
};
