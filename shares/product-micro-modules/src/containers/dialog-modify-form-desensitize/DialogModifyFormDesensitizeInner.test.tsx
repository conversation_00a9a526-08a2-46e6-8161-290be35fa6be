import _ from 'lodash';
import { render, screen } from '@testing-library/react';
// import { screen as screenEvent } from 'query-extensions';
import { IBusinessResult } from '@mdtProComm/interfaces';
import {
  DialogModifyFormDesensitizeController,
  IControllerOptions,
  IUiData,
} from './DialogModifyFormDesensitizeController';
import { DialogModifyFormDesensitizeInner } from './DialogModifyFormDesensitizeInner';

let modalUiData: IUiData = {
  columnOptions: [
    { label: '测试', value: '1' },
    { label: '测试', value: '2' },
    { label: '测试', value: '3' },
  ],
  typeOptions: [
    { label: '测试', value: '1' },
    { label: '测试', value: '2' },
    { label: '测试', value: '3' },
  ],
};
const controllers: DialogModifyFormDesensitizeController<any>[] = [];
let controller: DialogModifyFormDesensitizeController<any>;

const options: IControllerOptions<any, any> = {
  modifyDataFunc: (params: any, originalData?: any) => {
    console.log('originalData: ', originalData);
    console.log('params: ', params);
    return new Promise<IBusinessResult<void>>((resolve) => {
      resolve({ success: true });
    });
  },
  loadUiDataFunc: () =>
    new Promise<any>((resolve) => {
      resolve({});
    }),
};

describe('DialogModifyFormDesensitizeInnerTest', () => {
  describe('测试默认状态下行为', () => {
    beforeAll(() => {
      controller = new DialogModifyFormDesensitizeController(options);
      controller.getFormCompController();
      controller.getModalCompController().getModalOptions();
      controller.openModal();
      controller.clickOkBtn();
      controllers.push(controller);
    });

    test('DialogModifyFormDesensitizeController 应该被定义', () => {
      render(<DialogModifyFormDesensitizeInner controller={controller} modalUiData={modalUiData} />);
      expect(screen.getByText('请选择脱敏方式')).toBeVisible();
    });
  });
});

afterAll(() => {
  _.forEach(controllers, (it) => {
    it.destroy();
  });
  controllers.length = 0;
});
