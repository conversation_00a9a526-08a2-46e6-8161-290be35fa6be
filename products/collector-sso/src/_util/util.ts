import { decompressFromEncodedURIComponent } from 'lz-string';
import { getParamFromUrl, getParamsFromUrl } from '@mdtLogin/util/urlUtil';

// 接收参数
enum urlParam {
  openid = 'openid', // 微信
  wxcode = 'wxcode', // 微信
  vcode = 'vcode', // 微信
  qrType = 'qr_type', // 微信
  wechatRedirect = 'wechatRedirect', // 登陆成功跳转地址
  language = 'language', // 语言
  tab = 'tab', // 登录方式
  redirect = 'redirect', // sso回调地址
  force = 'force', // 强制登录
  configPath = 'config_path', // config自定义类型
  type = 'type', // 展示类型
  theme = 'theme', // 主题模式
  selfRedirect = 'self_redirect', // 本页重定向
  q = 'q', // 切换app后的token
  configNamespace = 'config_namespace', // config的命名空间，通过参数匹配，在配置文件中查找
}

// url
export const getLanguageFromUrl = () => {
  return getParamFromUrl(urlParam.language);
};

export const getTabFromUrl = () => {
  return getParamFromUrl(urlParam.tab);
};

export const getRedirectFromUrl = (url?: string) => {
  // sso成功后的跳转地址
  return getParamFromUrl(urlParam.redirect, url) || '';
};

export const getExtraUriFromUrl = () => {
  return getParamFromUrl('extra_uri') || '';
};

export const getSelfRedirectFromUrl = (url?: string) => {
  return getParamFromUrl(urlParam.selfRedirect, url) || '';
};

export const isDialog = () => {
  return getParamFromUrl(urlParam.type) === 'dialog' || undefined;
};

export const getForceFromUrl = () => {
  return getParamFromUrl(urlParam.force);
};

export const getConfigNamesapceFromUrl = () => {
  return getParamFromUrl(urlParam.configNamespace);
};

export const getConfigPathFromUrl = () => {
  return getParamFromUrl(urlParam.configPath);
};

export const getThemeFromUrl = () => {
  return getParamFromUrl(urlParam.theme);
};

export const getVcodeFromUrl = () => {
  return getParamFromUrl(urlParam.vcode);
};

export const getQFromUrl = (url?: string): Record<string, any> => {
  const q = getParamFromUrl(urlParam.q, url);
  return q ? parseStrToObj(decompressFromEncodedURIComponent(q) || '') : {};
};

const WX_VERIFY_KEY = [urlParam.openid, urlParam.wxcode, urlParam.vcode, urlParam.qrType];
export const getWXVerifyFromUrl = (): string[] => {
  return getParamsFromUrl(WX_VERIFY_KEY);
};

const WX_REDIRECT_KEY = [urlParam.vcode, urlParam.wechatRedirect, urlParam.qrType];
export const getWXRedirectFromUrl = (): string[] => {
  return getParamsFromUrl(WX_REDIRECT_KEY);
};

export const parseStrToObj = <T = object>(str: string): T => {
  let val;
  try {
    val = str ? JSON.parse(str) : {};
  } catch (e: any) {
    console.warn(`转换失败，不是合法的数据结构: ${str} -> ${e.message}`);
    val = {};
  }
  return val as T;
};
