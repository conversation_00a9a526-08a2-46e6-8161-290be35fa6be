import _ from 'lodash';
import { Dayjs } from 'dayjs';
import { from } from 'rxjs';
import { map, takeWhile } from 'rxjs/operators';
import { IByRolesPatch, IRequestCancelToken, IRole, IUser } from '@mdtApis/interfaces';
import { createDate, formateDate, isExpirationDateFromNow } from '@mdtBsComm/utils/dayUtil';
import { patchUsersByRoleAsync, queryRolesAsync } from '@mdtBsServices/auth';
import { IRequestSettledResult, IServerResponse } from '@mdtBsServices/interfaces';
import { IPermissionMap } from '@mdtProComm/interfaces';
import { GroupPermissionModel } from '@mdtProMicroModules/models/GroupPermissionModel';
import i18n from '../../languages';

// 通用角色ID
export const COMMON_ROLE_ID = 0;

export interface ITableUserData {
  id: number;
  name: string;
  role: number[];
  expireTime: Dayjs;
  expireTimeDisplay: string;
  permission: number[];
  disable: boolean;
  disableDisplay: string;
  uuid: string;
  organization: number[];
  orgDisplay?: string[];
}

export interface ITableRoleData {
  id: number;
  name: string;
  description?: string;
  permission: number[];
  children?: ITableRoleData[];
  title?: any;
}

export interface IOrgData {
  key: string;
  title: string;
  pid?: string;
  permission: number[];
}

export enum MoreItemsEnum {
  EVICTION = 'eviction',
}

export enum RoleOperateEnum {
  EDIT = 'edit',
  DELETE = 'delete',
}

export interface IMapRespData {
  roleList?: ITableRoleData[];
  permissionOption?: IPermissionMap;
  roleIdNameMap?: Record<number, string>;
  orgIdNameMap?: Record<string, IOrgData>;
  roleIdPermissionMap?: Record<number, number[]>;
}

class RoleUserModel extends GroupPermissionModel {
  // 组织部门的数据翻译函数
  public static translateOriginData = (data: IRole[] = [], rootId = ''): IOrgData[] => {
    return _.map(data, ({ id, name, parent_id, permission }) => {
      const result: IOrgData = {
        permission: permission || [],
        key: _.toString(id),
        title: name,
      };
      if (parent_id?.length) {
        result['pid'] = _.toString(parent_id[0]);
      } else {
        result['pid'] = rootId;
      }
      return result;
    });
  };

  // 用户结构翻译
  public static transformToTableData(
    item: IUser | ITableUserData,
    roleMap: Record<number, string>,
    orgMap: Record<number, IOrgData>,
    orgList?: IOrgData[],
  ): ITableUserData {
    let expireTimeDisplay = i18n.chain.proMicroModules.user.noExpire;
    let et: Dayjs;
    let disable: boolean;
    let permission: number[];
    if ('disable' in item) {
      disable = item.disable;
      et = createDate(item.expireTime);
      permission = item.permission;
    } else {
      disable = !item.enable;
      et = createDate(item.expire_time);
      permission = _.flatMap(item.permission, (p) => p);
    }
    const tip = isExpirationDateFromNow(et, 0) ? '' : i18n.chain.proMicroModules.user.expired;
    expireTimeDisplay = `${formateDate(et)}${tip}`;
    const role = _.filter(item.role, (r) => _.some(_.keys(roleMap), (v) => _.toNumber(v) === r));
    const organization = _.filter(item.role, (r) => _.some(_.keys(orgMap), (v) => _.toNumber(v) === r));
    const user: ITableUserData = {
      id: item.id,
      uuid: item.uuid,
      name: item.name,
      role,
      organization,
      orgDisplay: orgList && _.compact(_.map(organization, (it) => this.getPathById(_.toString(it), orgList))),
      disable,
      disableDisplay: disable ? i18n.chain.orgAdmin.organization.freeze : i18n.chain.orgAdmin.organization.enable,
      expireTime: et,
      expireTimeDisplay,
      permission: permission,
    };
    return user;
  }

  // 角色结构安逸
  public static transformToTableRoleData(item: IRole): ITableRoleData {
    return {
      id: item.id,
      name: item.name,
      description: item.description || '',
      permission: item.permission || [],
    };
  }

  public static async initUserAndMap(
    appId: number,
    roleList: { name: string; id: number }[],
    cancelToken?: IRequestCancelToken,
  ) {
    return from(
      new Promise<IServerResponse<IMapRespData>>((resolve) => {
        const request = async () => {
          const orgCnf = { cancelToken, params: { app_id: appId, role_type: 'organization' } };
          const [orgsResp] = (await Promise.allSettled([queryRolesAsync(orgCnf)])) as [IRequestSettledResult<IRole[]>];
          const roleIdNameMap: Record<number, string> = {};
          const orgIdNameMap: Record<number, IOrgData> = {};
          _.forEach(roleList, (item) => {
            const { id, name } = item;
            roleIdNameMap[id] = name;
          });
          const orgList = this.translateOriginData(orgsResp.value.data);
          _.forEach(orgList, (item: IOrgData) => {
            orgIdNameMap[_.toNumber(item.key)] = item;
          });
          const result: IMapRespData = {
            roleIdNameMap,
            orgIdNameMap,
          };
          resolve({
            ...orgsResp.value,
            data: result,
          });
        };
        request();
      }),
    ).pipe(
      takeWhile((resp) => !resp.canceled),
      map((resp) => {
        return resp.data!;
      }),
    );
  }

  public static patchOrgUsers(appId: number, roleId: number, currentKeys: string[]) {
    const body: IByRolesPatch = {
      new_users: currentKeys,
    };
    return from(patchUsersByRoleAsync({ role_ids: `${roleId}`, app_id: appId }, body)).pipe(
      map((resp) => ({ success: resp.success })),
    );
  }

  public static removeOrgUsers(appId: number, roleId: number, delKey: string) {
    const delUsers = [delKey];
    const body: IByRolesPatch = {};
    if (delUsers) {
      body.del_users = delUsers;
    }
    return from(patchUsersByRoleAsync({ role_ids: `${roleId}`, app_id: appId }, body)).pipe(
      map((resp) => ({ success: resp.success })),
    );
  }

  // 根据部门id拿到所有层级的展示
  private static getPathById = (id: string, arr: IOrgData[]): string => {
    let result = '';
    const mapObj: Record<string, IOrgData> = {};
    _.forEach(arr, ({ key, ...rest }) => {
      mapObj[key] = { key, ...rest };
    });
    const current = mapObj[id];
    if (!current) {
      return '--';
    }
    const loop = (child: IOrgData): void => {
      result = child.title + (result ? '/' : '') + result;
      if (child.pid && mapObj[child.pid]) {
        loop(mapObj[child.pid]);
      }
    };

    loop(current);
    return result;
  };
}

export { RoleUserModel };
export type IRoleUserModel = typeof RoleUserModel;
