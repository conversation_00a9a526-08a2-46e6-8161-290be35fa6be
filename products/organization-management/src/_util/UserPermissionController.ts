import { PermissionEnum } from '@mdtProComm/constants/permission';
import { BaseUserPermissionController } from '@mdtProComm/controllers/BaseUserPermissionController';

class UserPermissionController extends BaseUserPermissionController {
  // 获取菜单权限
  public getMenuPermission() {
    return {
      enableAuthManage: this.checkUserPermission(PermissionEnum.AM_MENU_USER_MANAGE),
      enableRoleUser: this.checkUserPermission(PermissionEnum.AM_MENU_USER_ROLE_MANAGE),
      enableOrganization: this.checkUserPermission(PermissionEnum.AM_MENU_USER_AUTHORITY_MANAGE),
      enableAuth: this.checkUserPermission(PermissionEnum.AM_MENU_AUTHORITY_MANAGE),
      enableDataMarketManage: this.checkUserPermission(PermissionEnum.PRODUCT_MENU_DM),
      enableOneTableManage: this.checkUserPermission(PermissionEnum.PRODUCT_MENU_OT),
      enableDataMapManage: this.checkUserPermission(PermissionEnum.PRODUCT_MENU_DS),
      enablePreferenceManage: this.checkUserPermission(PermissionEnum.AM_MENU_APPSETTING_MANAGE),
      enableAuthorityManage: this.checkUserPermission(PermissionEnum.AM_MENU_AUTHORITY_MANAGE),
      enableLogoStyle: this.checkUserPermission(PermissionEnum.SYS_SETUP_MENU),
      enableCustomLogin: this.checkUserPermission(PermissionEnum.CUSTOM_LOGIN),
      enableHomeSettingManage: this.checkUserPermission(PermissionEnum.USER_EDIT),
      enableMaintenanceStats: this.checkUserPermission(PermissionEnum.MAINTENANCE_STATS),
      enableUserHistoryMenu: this.checkUserPermission(PermissionEnum.USER_HISTORY_MENU),
    };
  }

  // 检查用户管理的增删改权限
  public getUserManagePermission() {
    return {
      enableCreate: this.checkUserPermission(PermissionEnum.USER_ADD),
      enableEdit: this.checkUserPermission(PermissionEnum.USER_EDIT),
      enableDelete: this.checkUserPermission(PermissionEnum.USER_DEL),
      enableResetPwd: this.checkUserPermission(PermissionEnum.RESET_PWD),
    };
  }

  // 检查用户的批量操作权限
  public getUserBatchManagePermission() {
    return {
      enableImport: this.checkUserPermission(PermissionEnum.USER_ADD),
      enableImportOrg: this.checkUserPermission(PermissionEnum.AM_MENU_USER_AUTHORITY_MANAGE),
      enableExport: this.checkUserPermission(PermissionEnum.USER_CONTACT_INFO),
      enableEditTime: this.checkUserPermission(PermissionEnum.USER_EDIT),
    };
  }

  // 检查是否有用户管理权
  public hasUserManagePermission() {
    return this.checkUserAnyPermissions([PermissionEnum.USER_ADD, PermissionEnum.USER_EDIT, PermissionEnum.USER_DEL]);
  }

  // 授权管理权限
  public getAuthManagePermission() {
    return {
      user: this.checkUserPermission(PermissionEnum.USER_EDIT),
      organization: this.checkUserPermission(PermissionEnum.AM_MENU_USER_AUTHORITY_MANAGE),
      role: this.checkUserPermission(PermissionEnum.ROLE),
    };
  }

  // 检查角色管理的增删改权限
  public getRoleManagePermission() {
    return {
      enableCreate: this.checkUserPermission(PermissionEnum.ROLE),
      enableEdit: this.checkUserPermission(PermissionEnum.ROLE),
      enableDelete: this.checkUserPermission(PermissionEnum.ROLE),
    };
  }

  // 检查可见数据设置的改删权限
  public getDataSettingPermission() {
    return {
      enableEdit: this.checkUserPermission(PermissionEnum.UPDATE_APP_PREFERENCE),
      enableDelete: this.checkUserPermission(PermissionEnum.UPDATE_APP_PREFERENCE),
      enableCreate: this.checkUserPermission(PermissionEnum.UPDATE_APP_PREFERENCE),
    };
  }

  // 检查首页设置的编辑权限
  public getHomeSettingPermission() {
    return {
      enableEdit: this.checkUserPermission(PermissionEnum.USER_EDIT),
      enableEditBatch: this.checkUserPermission(PermissionEnum.USER_EDIT),
    };
  }
}

export { UserPermissionController };
