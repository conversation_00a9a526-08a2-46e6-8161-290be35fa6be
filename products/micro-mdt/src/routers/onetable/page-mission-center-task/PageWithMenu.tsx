import { useController } from '@mdtBsComm/hooks/use-controller';
import { OneTableMissionCenterTaskModelBff } from '@mdtProMicroModules/pages/one-table-mission-center-task';
import {
  MissionCenterTask,
  MissionCenterTaskController,
} from '@mdtProMicroModules/pages-in-micro/one-table/pages/mission-center-task';

const PageMissionCenterTask = () => {
  const [controller] = useController(() => {
    const ctrl = new MissionCenterTaskController({
      Model: OneTableMissionCenterTaskModelBff,
      showBottomMenu: true,
    });
    return [ctrl, null];
  });

  return <MissionCenterTask controller={controller} />;
};

export default PageMissionCenterTask;
