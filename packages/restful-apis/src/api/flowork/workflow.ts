import { FLOWORK_URL } from '../../config';
import {
  IApiResponse,
  IFloworkTask,
  IPaginationQuery,
  IRequestPromise,
  IRequestRequestConfig,
  IWorkflow,
  IWorkflowCancelPost,
  IWorkflowCompletePost,
  IWorkflowDelete,
  IWorkflowGenealogy,
  IWorkflowGenealogyBatchItem,
  IWorkflowGenealogyBatchPost,
  IWorkflowGenealogyQuery,
  IWorkflowGet,
  IWorkflowNode,
  IWorkflowNodesQuery,
  IWorkflowSpec,
  IWorkflowSpecGet,
  IWorkflowsQuery,
  IWorkflowsQueryPost,
  IWorkflowTasksQuery,
} from '../../interfaces';
import request from '../../request';

const innerUrl = `${FLOWORK_URL}/workflows`;

// 查询所有的流程实例
export const queryWorkflows = (config?: IRequestRequestConfig<IWorkflowsQuery>) => {
  return request({
    ...(config || {}),
    method: 'get',
    url: `${innerUrl}`,
  }) as IRequestPromise<IApiResponse<IWorkflow[]>>;
};

// 查询单个流程实例
export const getWorkflow = (workflowId: string, config?: IRequestRequestConfig<IWorkflowGet>) => {
  return request({
    ...(config || {}),
    method: 'get',
    url: `${innerUrl}/${workflowId}`,
  }) as IRequestPromise<IApiResponse<IWorkflow>>;
};

// 删除流程
export const deleteWorkflow = (workflowId: string, config?: IRequestRequestConfig<IWorkflowDelete>) => {
  return request({
    ...(config || {}),
    method: 'delete',
    url: `${innerUrl}/${workflowId}`,
  }) as IRequestPromise<IApiResponse<boolean>>;
};

export const postWorkflowComplete = (
  workflowId: string,
  taskIdOrName: string,
  data: IWorkflowCompletePost,
  config?: IRequestRequestConfig,
) => {
  return request({
    ...(config || {}),
    method: 'post',
    data,
    url: `${innerUrl}/${workflowId}/${taskIdOrName}/complete`,
  }) as IRequestPromise<IApiResponse<IWorkflow>>;
};

// 取消流程
export const postWorkflowCancel = (workflowId: string, data?: IWorkflowCancelPost, config?: IRequestRequestConfig) => {
  return request({
    ...(config || {}),
    method: 'post',
    url: `${innerUrl}/${workflowId}/cancel`,
    data,
  }) as IRequestPromise<IApiResponse<IWorkflow>>;
};

export const queryWorkflowNodes = (workflowId: string, config?: IRequestRequestConfig<IWorkflowNodesQuery>) => {
  return request({
    ...(config || {}),
    method: 'get',
    url: `${innerUrl}/${workflowId}/nodes`,
  }) as IRequestPromise<IApiResponse<IWorkflowNode[]>>;
};

// 查询流程的所有任务
export const queryWorkflowTasks = (workflowId: string, config?: IRequestRequestConfig<IWorkflowTasksQuery>) => {
  return request({
    ...(config || {}),
    method: 'get',
    url: `${innerUrl}/${workflowId}/tasks`,
  }) as IRequestPromise<IApiResponse<IFloworkTask[]>>;
};

export const getWorkflowSpecIns = (workflowId: string, config?: IRequestRequestConfig<IWorkflowSpecGet>) => {
  return request({
    ...(config || {}),
    method: 'get',
    url: `${innerUrl}/${workflowId}/workflow_spec`,
  }) as IRequestPromise<IApiResponse<IWorkflowSpec>>;
};

export const queryWorkflowsByPost = (data: IWorkflowsQueryPost, config?: IRequestRequestConfig<IPaginationQuery>) => {
  return request({
    ...(config || {}),
    method: 'post',
    url: `${innerUrl}/query`,
    data,
  }) as IRequestPromise<IApiResponse<IWorkflow[]>>;
};

export const queryWorkflowGenealogy = (workflowId: string, config?: IRequestRequestConfig<IWorkflowGenealogyQuery>) => {
  return request({
    ...(config || {}),
    method: 'get',
    url: `${innerUrl}/${workflowId}/genealogy`,
  }) as IRequestPromise<IApiResponse<IWorkflowGenealogy[]>>;
};

export const batchQueryWorkflowGenealogy = (data: IWorkflowGenealogyBatchPost, config?: IRequestRequestConfig) => {
  return request({
    ...(config || {}),
    method: 'post',
    url: `${innerUrl}/query/genealogy`,
    data,
  }) as IRequestPromise<IApiResponse<IWorkflowGenealogyBatchItem[]>>;
};
