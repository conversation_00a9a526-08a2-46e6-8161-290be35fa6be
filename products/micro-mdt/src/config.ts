import { getDefaultMicroConfig, initCommConfig } from '@mdtProMicroModules/datlas/datlasConfig';

const cfs = initCommConfig('micro-mdt', {
  isDevelop: __IS_DEVELOPMENT,
  developProxyApiUrl: __DEVELOP_PROXY_API_URL,
  developEnvOrigin: __DEVELOP_ENV_ORIGIN,
});
__webpack_public_path__ = cfs.deployPublicPath;

export * from '@mdtProMicroModules/datlas/datlasConfig';

// 微应用配置
export const MICRO_SSO = cfs.microSso || {};
export const MICRO_DATA_FACTORY = cfs.microDataFactory || getDefaultMicroConfig('sub-datafactory');
export const MICRO_DATA_MARKET = cfs.microDataMarket || getDefaultMicroConfig('sub-datamarket');
export const MICRO_MY_DATA = cfs.microMyData || getDefaultMicroConfig('sub-mydata');
export const MICRO_ORGANIZATION_MANAGEMENT = cfs.microOrganizationManagement || getDefaultMicroConfig('sub-orgadmin');
export const MICRO_RESOURCE_SHARE = cfs.microResourceShare || getDefaultMicroConfig('sub-share');
export const MICRO_FORM_DESIGN = cfs.microFormDesign || getDefaultMicroConfig('sub-designable');
export const MICRO_WORKFLOW = cfs.microWorkflow || getDefaultMicroConfig('sub-workflow');
export const MICRO_ONE_TABLE = cfs.microOneTable || getDefaultMicroConfig('sub-onetable');
