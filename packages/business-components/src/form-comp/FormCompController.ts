import _ from 'lodash';
import { FieldData, FormInstance } from 'rc-field-form/es/interface';
import { FormController } from '@mdtBsControllers/form-controller';

class FormCompController<V = any> extends FormController<V> {
  private formIns?: FormInstance<V>;

  public onFieldsChange(changedFields: FieldData[], allFields: FieldData[]) {
    this.changeFormDataError(_.some(allFields, ({ errors }) => _.size(errors)));
  }

  public onValuesChange(changedValues: any, values: V) {
    this.setChangedValues(changedValues);
    this.changeFormData(values);
  }

  public initFormIns(formIns: FormInstance) {
    this.formIns = formIns;
  }

  public async validateFormData() {
    try {
      await this.formIns!.validateFields();
      this.changeFormDataError(false);
    } catch (e) {
      this.changeFormDataError(true);
      console.warn('form valid error: ', e);
    }
  }

  public getFormIns() {
    return this.formIns!;
  }

  public destroy() {
    super.destroy();
    this.formIns = undefined;
  }
}

export { FormCompController };
