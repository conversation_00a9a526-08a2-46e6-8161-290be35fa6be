const fs = require('fs');
const argv = require('yargs').argv;
const { simpleGit } = require('simple-git');
const { join } = require('path');

const rootPath = process.cwd();
const remote = `https://oauth2:<EMAIL>/new-datamap/frontend-config.git`;
const target = join(rootPath, '../repos', 'frontend-config');
const envs = ['dev', 'pri', 'prod', 'staging'];
const configFileMap = {
  dev: 'develop/config.dev.js',
  pri: 'config.js',
  prod: 'develop/config.debug.js',
  staging: 'develop/config.staging.js',
};
const ymlTemp = `variables:
  CONFIG_NAME: config.js
  DINGTALK_WEBHOOK_TOKEN: ''
  MODULE_NAME: ''
  SENTRY_SOURCEMAP_ENABLED: ''
  WWW_DIR: /var/www/\${MODULE_ID}
`;

async function gitOpt(modules) {
  // 如果存在则删除
  if (fs.existsSync(target)) {
    fs.rmdirSync(target, { recursive: true });
  }
  const git = simpleGit();
  // clone
  await git.clone(remote, target);
  const userEmail = await git.getConfig('user.email', 'global');
  const userName = await git.getConfig('user.name', 'global');
  if (!userEmail.value || !userName.value) {
    // 设置基本信息
    await git.addConfig('user.email', '<EMAIL>', false, 'global');
    await git.addConfig('user.name', 'gitlab', false, 'global');
  }
  // 定位到项目
  await git.cwd({ path: target, root: true });
  for (const [moduleId, dirPath] of modules) {
    // 判断moduleId在各个环境存不存在，不存在则创建
    for (const env of envs) {
      // 模块的存储目录
      const mp = join(target, 'config', env, moduleId);
      if (!fs.existsSync(mp)) {
        // 创建目录
        fs.mkdirSync(mp);
        if (env === 'pri') {
          fs.writeFileSync(join(mp, 'README.md'), '', 'utf-8');
        } else {
          // 创建yml
          fs.writeFileSync(join(mp, 'cd-config.yml'), ymlTemp, 'utf-8');
          // 在evn中追加记录
          fs.writeFileSync(join(target, 'env', `env.${env}`), `\n${moduleId}=`, { flag: 'a' });
        }
      }
      // 复制项目下的config文件到config项目中, 需要确保项目中的配置项经过测试
      const projectConfigPath = join(dirPath, moduleId, '.config', configFileMap[env]);
      const configStr = fs.readFileSync(projectConfigPath);
      // 可以做成配置文件
      fs.writeFileSync(join(mp, 'config.js'), configStr, 'utf-8');
    }
  }
  await git.add('./*');
  await git.commit('chore: 🤖 sync config from mdt-frontend');
  await git.push();
}

async function start() {
  const products = join(rootPath, 'products');
  const dirs = fs.readdirSync(products, { withFileTypes: true });
  const modules = dirs.filter((it) => it.isDirectory()).map((it) => [it.name, products]);
  await gitOpt(modules);
}

start('products');
