import { queryMaps } from '@mdtApis/api/maps';
import { getTotalArgs } from './_util/totalUtil';
import { IMap, IMapsQuery, IRequestRequestConfig, IServerPaginationResponse, IServerResponse } from './interfaces';

export const queryMapsPaginationAsync = async (config?: IRequestRequestConfig<IMapsQuery>) => {
  const ncf = { ...(config || {}), needMetadata: true };
  return queryMaps(ncf) as unknown as IServerPaginationResponse<IMap[]>;
};

export const queryMapsPaginationTotalAsync: typeof queryMapsPaginationAsync = (...args) => {
  return queryMapsPaginationAsync(...getTotalArgs(args));
};

export const queryMapsAsync = async (config?: IRequestRequestConfig<IMapsQuery>) => {
  return queryMaps(config) as unknown as IServerResponse<IMap[]>;
};
