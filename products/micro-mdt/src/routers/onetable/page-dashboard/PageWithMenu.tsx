import { useController } from '@mdtBsComm/hooks/use-controller';
import {
  Dashboard,
  DashboardController,
  DashboardModelBff,
} from '@mdtProMicroModules/pages-in-micro/one-table/pages/dashboard';

const PageDashboard = () => {
  const [controller] = useController(() => {
    const ctrl = new DashboardController({
      Model: DashboardModelBff,
      showBottomMenu: true,
    });
    return [ctrl, null];
  });

  return <Dashboard controller={controller} />;
};

export default PageDashboard;
