import React from 'react';
import { useController } from '@mdtBsComm/hooks/use-controller';
import { AppController } from '../app/AppController';
import { Editor, EditorController } from '../containers/editor';

const EdittorView = () => {
  const [controller] = useController(() => {
    const ctrl = new EditorController(AppController.getInstance(), {});
    return [ctrl, null];
  });

  return <Editor controller={controller} />;
};

export default EdittorView;
