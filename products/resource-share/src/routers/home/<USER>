import { useController } from '@mdtBsComm/hooks/use-controller';
import { ResourceShare, ResourceShareController, ResourceShareModel } from '@mdtProMicroModules/pages/resource-share';
import { ResourceShareModelBff } from '@mdtProMicroModules/pages/resource-share/ResourceShareModelBff';
import { getModel } from '../../_util/modelUtil';
import { useAppContext } from '../../app/appContext';

const Home = () => {
  const { appController } = useAppContext();

  const [controller] = useController(() => {
    const hasPermission = appController.getUserPermissionController().getOrgAuthManagePermission();
    const jumpToOrgAuth = hasPermission ? appController.jumpToOrgAuth : undefined;
    const ctrl = new ResourceShareController(
      appController,
      getModel(ResourceShareModel, ResourceShareModelBff),
      jumpToOrgAuth,
    );
    return [ctrl, null];
  });

  return <ResourceShare controller={controller} />;
};

export default Home;
