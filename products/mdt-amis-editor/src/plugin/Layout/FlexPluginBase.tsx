/* eslint-disable sonarjs/no-collapsible-if */
/* eslint-disable @typescript-eslint/member-ordering */
/* eslint-disable sonarjs/cognitive-complexity */
/* eslint-disable complexity */
/* eslint-disable sonarjs/no-duplicate-string */
/* eslint-disable @typescript-eslint/explicit-member-accessibility */
/**
 * @file Flex 常见布局 1:3
 */
import { Button, PlainObject } from 'amis';
import type {
  BaseEventContext,
  BasicToolbarItem,
  EditorNodeType,
  RegionConfig,
  RendererJSONSchemaResolveEventContext,
} from 'amis-editor-core';
import { JSONPipeOut, LayoutBasePlugin, PluginEvent } from 'amis-editor-core';
import { getSchemaTpl } from 'amis-editor-core';
import { Icon } from 'amis-editor-core';
import { JSONPipeIn } from 'amis-editor-core';
import { isAlive } from 'mobx-state-tree';
import i18n from '../../languages';

// 默认的列容器Schema
export const defaultFlexColumnSchema = (title?: string, disableFlexBasis = true) => {
  let style: PlainObject = {
    position: 'static',
    display: 'block',
    flex: '1 1 auto',
    flexGrow: 1,
  };
  if (disableFlexBasis) {
    style.flexBasis = 0;
  }
  return {
    type: 'container',
    body: [],
    size: 'none',
    style,
    wrapperBody: false,
    isFixedHeight: false,
    isFixedWidth: false,
  };
};

const defaultFlexPreviewSchema = (title?: string) => {
  return {
    type: 'tpl',
    tpl: title,
    wrapperComponent: '',
    className: 'bg-light center',
    style: {
      display: 'block',
      flex: '1 1 auto',
      flexBasis: 0,
      textAlign: 'center',
      marginRight: 10,
    },
    inline: false,
  };
};

// 默认的布局容器Schema
const defaultFlexContainerSchema = (flexItemSchema: (title?: string) => any = defaultFlexColumnSchema) => ({
  type: 'flex',
  items: [
    flexItemSchema(i18n.chain.jsonComp.firstCol),
    flexItemSchema(i18n.chain.jsonComp.secondCol),
    flexItemSchema(i18n.chain.jsonComp.thirdCol),
  ],
  style: {
    position: 'relative',
    rowGap: '10px',
    columnGap: '10px',
  },
});

export class FlexPluginBase extends LayoutBasePlugin {
  static id = 'FlexPluginBase';
  rendererName = 'flex';
  $schema = '/schemas/FlexSchema.json';
  disabledRendererPlugin = false;

  name = i18n.chain.jsonComp.flexContainer;
  order = -1200;
  isBaseComponent = true;
  icon = 'fa fa-columns';
  pluginIcon = 'flex-container-plugin';
  description = i18n.chain.jsonComp.flexContainerDescription;
  docLink = '/amis/zh-CN/components/flex';
  tags = [i18n.chain.jsonComp.flexContainer];
  scaffold: any = defaultFlexContainerSchema();
  previewSchema = defaultFlexContainerSchema(defaultFlexPreviewSchema);

  panelTitle = i18n.chain.jsonComp.flexContainer;

  panelJustify = true; // 右侧配置项默认左右展示

  // 设置分栏的默认布局比例
  setFlexLayout = (node: EditorNodeType, value: string) => {
    if (/^[\d:]+$/.test(value) && isAlive(node)) {
      let list = value.trim().split(':');
      let children = node.children || [];

      if (String(node.schema?.style?.flexDirection).includes('column')) {
        list = list.reverse();
        node.updateSchemaStyle({
          flexDirection: 'row',
        });
      }

      // 更新flex布局
      for (let i = 0; i < children.length; i++) {
        let child = children[i];
        child.updateSchemaStyle({
          flexGrow: Number(list[i]),
          width: undefined,
          flexBasis: 0,
          flex: '1 1 auto',
        });
      }

      // 增加或删除列
      if (children.length < list.length) {
        for (let i = 0; i < list.length - children.length; i++) {
          let newColumnSchema = defaultFlexColumnSchema();
          newColumnSchema.style.flexGrow = Number(list[i]);
          this.manager.addElem(newColumnSchema, true, false);
        }
      } else if (children.length > list.length) {
        // 如果删除的列里面存在元素，截断生成新的flex放在组件后面
        const newSchema = JSONPipeIn(JSONPipeOut(node.schema));
        newSchema.items = newSchema.items.slice(list.length);

        node.updateSchema({
          items: node.schema.items.slice(0, list.length),
        });

        if ((newSchema.items as PlainObject[]).some((item) => item.body?.length)) {
          const parent = node.parent;
          this.manager.addChild(parent.id, parent.region, newSchema, parent?.children?.[node.index + 1]?.id);
        }
      }
    }
    return undefined;
  };

  resetFlexBasis = (node: EditorNodeType, flexSetting: PlainObject = {}) => {
    let schema = node.schema;
    if (String(flexSetting.flexDirection).includes('column') && !schema?.style?.height) {
      (node.children || []).forEach((child) => {
        if (!child.schema?.style?.height || /^0/.test(child.schema?.style?.flexBasis)) {
          child.updateSchemaStyle({
            flexBasis: undefined,
          });
        }
      });
    }
  };

  insertItem = (node: EditorNodeType, direction: string) => {
    if (node.info?.plugin !== this) {
      return;
    }
    const store = this.manager.store;
    const newSchema = JSONPipeIn(JSONPipeOut(node.schema));

    const parent = node.parent;
    const nextId = direction === 'upper' ? node.id : parent?.children?.[node.index + 1]?.id;
    const child = this.manager.addChild(parent.id, parent.region, newSchema, nextId);
    if (child) {
      // mobx 修改数据是异步的
      setTimeout(() => {
        store.setActiveId(child.$$id);
      }, 100);
    }
  };

  panelBodyCreator = (context: BaseEventContext) => {
    const curRendererSchema = context?.schema || {};
    const isFlexItem = this.manager?.isFlexItem(context?.id);
    const isFlexColumnItem = this.manager?.isFlexColumnItem(context?.id);
    // 判断是否为吸附容器
    const isSorptionContainer = curRendererSchema?.isSorptionContainer || false;

    const positionTpl = [
      getSchemaTpl('layout:position', {
        visibleOn: '!data.stickyStatus',
      }),
      getSchemaTpl('layout:originPosition'),
      getSchemaTpl('layout:inset', {
        mode: 'vertical',
      }),
    ];
    return [
      getSchemaTpl('tabs', [
        {
          title: i18n.chain.jsonComp.flexContainerAttributeTitle,
          className: 'p-none',
          body: [
            getSchemaTpl('collapseGroup', [
              {
                title: i18n.chain.jsonComp.flexContainerBasicTitle,
                body: [
                  context.node &&
                    getSchemaTpl('layout:flex-layout', {
                      name: 'layout',
                      label: i18n.chain.jsonComp.flexContainerShortcutLayoutSetting,
                      pipeIn: () => {
                        if (isAlive(context.node)) {
                          let children = context.node?.children || [];
                          if (children.every((item) => item.schema?.style?.flex === '1 1 auto')) {
                            return children.map((item) => item.schema?.style?.flexGrow || 1).join(':');
                          }
                        }
                        return undefined;
                      },
                      pipeOut: (value: string) => this.setFlexLayout(context.node, value),
                    }),

                  {
                    type: 'wrapper',
                    size: 'none',
                    className: 'grid grid-cols-2 gap-4 mb-4',
                    body: [
                      {
                        children: (
                          <Button size="sm" onClick={() => this.insertItem(context.node, 'under')}>
                            <Icon className="icon" icon="arrow-to-bottom" />
                            <span>{i18n.chain.jsonComp.insertBelowNewRow}</span>
                          </Button>
                        ),
                      },
                      {
                        children: (
                          <Button size="sm" onClick={() => this.insertItem(context.node, 'upper')}>
                            <Icon className="icon" icon="top-arrow-to-top" />
                            <span>{i18n.chain.jsonComp.insertAboveNewRow}</span>
                          </Button>
                        ),
                      },
                    ],
                  },

                  getSchemaTpl('theme:paddingAndMargin', {
                    name: 'themeCss.baseControlClassName.padding-and-margin:default',
                  }),
                  getSchemaTpl('theme:border', {
                    name: `themeCss.baseControlClassName.border:default`,
                  }),
                  getSchemaTpl('theme:colorPicker', {
                    name: 'themeCss.baseControlClassName.background:default',
                    label: i18n.chain.jsonComp.background,
                    needCustom: true,
                    needGradient: true,
                    needImage: true,
                    labelMode: 'input',
                  }),

                  getSchemaTpl('layout:flex-setting', {
                    label: i18n.chain.jsonComp.flexContainerInnerAlignmentSetting,
                    direction: curRendererSchema.direction,
                    justify: curRendererSchema.justify || 'center',
                    alignItems: curRendererSchema.alignItems,
                    pipeOut: (value: any) => {
                      this.resetFlexBasis(context.node, value);
                      return value;
                    },
                  }),

                  getSchemaTpl('layout:flex-wrap'),

                  getSchemaTpl('layout:flex-basis', {
                    label: i18n.chain.jsonComp.flexContainerRowGap,
                    name: 'style.rowGap',
                  }),
                  getSchemaTpl('layout:flex-basis', {
                    label: i18n.chain.jsonComp.flexContainerColumnGap,
                    name: 'style.columnGap',
                  }),

                  ...(isFlexItem
                    ? [
                        getSchemaTpl('layout:flex', {
                          isFlexColumnItem,
                          label: isFlexColumnItem
                            ? i18n.chain.jsonComp.heightSetting
                            : i18n.chain.jsonComp.widthSetting,
                          visibleOn:
                            'data.style && (data.style.position === "static" || data.style.position === "relative")',
                        }),
                        getSchemaTpl('layout:flex-grow', {
                          visibleOn:
                            'data.style && data.style.flex === "1 1 auto" && (data.style.position === "static" || data.style.position === "relative")',
                        }),
                        getSchemaTpl('layout:flex-basis', {
                          label: isFlexColumnItem
                            ? i18n.chain.jsonComp.elasticHeight
                            : i18n.chain.jsonComp.elasticWidth,
                          visibleOn:
                            'data.style && (data.style.position === "static" || data.style.position === "relative") && data.style.flex === "1 1 auto"',
                        }),
                        getSchemaTpl('layout:flex-basis', {
                          label: isFlexColumnItem ? i18n.chain.jsonComp.fixedHeight : i18n.chain.jsonComp.fixedWidth,
                          visibleOn:
                            'data.style && (data.style.position === "static" || data.style.position === "relative") && data.style.flex === "0 0 150px"',
                        }),
                      ]
                    : []),

                  getSchemaTpl('layout:overflow-x', {
                    visibleOn: `${isFlexItem && !isFlexColumnItem} && data.style.flex === '0 0 150px'`,
                  }),

                  getSchemaTpl('layout:isFixedHeight', {
                    visibleOn: `${!isFlexItem || !isFlexColumnItem}`,
                    onChange: (value: boolean) => {
                      context?.node.setHeightMutable(value);
                    },
                  }),
                  getSchemaTpl('layout:height', {
                    visibleOn: `${!isFlexItem || !isFlexColumnItem}`,
                  }),
                  getSchemaTpl('layout:max-height', {
                    visibleOn: `${!isFlexItem || !isFlexColumnItem}`,
                  }),
                  getSchemaTpl('layout:min-height', {
                    visibleOn: `${!isFlexItem || !isFlexColumnItem}`,
                  }),

                  getSchemaTpl('layout:overflow-y', {
                    visibleOn: `${
                      !isFlexItem || !isFlexColumnItem
                    } && (data.isFixedHeight || data.style && data.style.maxHeight) || (${
                      isFlexItem && isFlexColumnItem
                    } && data.style.flex === '0 0 150px')`,
                  }),

                  getSchemaTpl('layout:isFixedWidth', {
                    visibleOn: `${!isFlexItem || isFlexColumnItem}`,
                    onChange: (value: boolean) => {
                      context?.node.setWidthMutable(value);
                    },
                  }),
                  getSchemaTpl('layout:width', {
                    visibleOn: `${!isFlexItem || isFlexColumnItem}`,
                  }),
                  getSchemaTpl('layout:max-width', {
                    visibleOn: `${!isFlexItem || isFlexColumnItem} || ${isFlexItem} && data.style.flex !== '0 0 150px'`,
                  }),
                  getSchemaTpl('layout:min-width', {
                    visibleOn: `${!isFlexItem || isFlexColumnItem} || ${isFlexItem} && data.style.flex !== '0 0 150px'`,
                  }),

                  getSchemaTpl('layout:overflow-x', {
                    visibleOn: `${
                      !isFlexItem || isFlexColumnItem
                    } && (data.isFixedWidth || data.style && data.style.maxWidth)`,
                  }),

                  !isFlexItem ? getSchemaTpl('layout:margin-center') : null,
                  getSchemaTpl('layout:z-index'),
                  !isSorptionContainer &&
                    getSchemaTpl('layout:sticky', {
                      visibleOn:
                        'data.style && (data.style.position !== "fixed" && data.style.position !== "absolute")',
                    }),
                  getSchemaTpl('layout:stickyPosition'),
                ],
              },
              getSchemaTpl('status'),
              {
                title: i18n.chain.jsonComp.flexContainerAdvancedTitle,
                body: [
                  isSorptionContainer ? getSchemaTpl('layout:sorption') : null,
                  ...(isSorptionContainer ? [] : positionTpl),
                ],
              },
            ]),
          ],
        },
        {
          title: i18n.chain.jsonComp.flexContainerAppearanceTitle,
          className: 'p-none',
          body: getSchemaTpl('collapseGroup', [...getSchemaTpl('theme:common', { exclude: ['layout'] })]),
        },
      ]),
    ];
  };

  regions: Array<RegionConfig> = [
    {
      key: 'items',
      label: i18n.chain.jsonComp.flexContainerBaseChildren,
    },
  ];

  buildEditorToolbar({ id, info, schema, node }: BaseEventContext, toolbars: Array<BasicToolbarItem>) {
    // const store = this.manager.store;
    const parent = node.parent?.schema; // 或者 store.getSchemaParentById(id, true);
    const draggableContainer = this.manager.draggableContainer(id);
    const isFlexItem = this.manager?.isFlexItem(id);
    const isFlexColumnItem = this.manager?.isFlexColumnItem(id);
    const newColumnSchema = isFlexColumnItem ? defaultFlexColumnSchema('', false) : defaultFlexColumnSchema();
    const canAppendSiblings = this.manager?.canAppendSiblings();
    const toolbarsTooltips: any = {};
    toolbars.forEach((toolbar) => {
      if (toolbar.tooltip) {
        toolbarsTooltips[toolbar.tooltip] = 1;
      }
    });

    if (
      parent &&
      (info.renderer?.name === 'flex' || info.renderer?.name === 'container') &&
      !draggableContainer &&
      !schema?.isFreeContainer
    ) {
      // 非特殊布局元素（fixed、absolute）支持前后插入追加布局元素功能icon
      // 备注：如果是列级元素不需要显示
      if (!toolbarsTooltips[i18n.chain.jsonComp.topInsertLayoutContainer] && !isFlexItem && canAppendSiblings) {
        toolbars.push(
          {
            iconSvg: 'add-btn',
            tooltip: i18n.chain.jsonComp.topInsertLayoutContainer,
            level: 'special',
            placement: 'right',
            className: 'ae-InsertBefore is-vertical',
            onClick: () => this.manager.appendSiblingSchema(defaultFlexContainerSchema(), true, true),
          },
          {
            iconSvg: 'add-btn',
            tooltip: i18n.chain.jsonComp.bottomInsertLayoutContainer,
            level: 'special',
            placement: 'right',
            className: 'ae-InsertAfter is-vertical',
            onClick: () => this.manager.appendSiblingSchema(defaultFlexContainerSchema(), false, true),
          },
        );
      }

      // 布局容器 右上角插入子元素
      if (info.renderer?.name === 'flex') {
        if (!toolbarsTooltips[i18n.chain.jsonComp.addFlexColumnElement]) {
          toolbars.push({
            iconSvg: 'add-btn',
            tooltip: i18n.chain.jsonComp.addFlexColumnElement,
            level: 'special',
            placement: 'bottom',
            className: 'ae-AppendChild',
            onClick: () => this.manager.addElem(defaultFlexColumnSchema('', true)),
          });
        }
      }
    }

    if (
      parent &&
      (parent.type === 'flex' || parent.type === 'container') &&
      isFlexItem &&
      !draggableContainer &&
      canAppendSiblings
    ) {
      if (
        !toolbarsTooltips[
          `${isFlexColumnItem ? i18n.chain.jsonComp.up : i18n.chain.jsonComp.left}${
            i18n.chain.jsonComp.insertColContainer
          }`
        ]
      ) {
        // 布局容器的列级元素 增加左右插入icon
        toolbars.push(
          {
            iconSvg: 'add-btn',
            tooltip: `${isFlexColumnItem ? i18n.chain.jsonComp.up : i18n.chain.jsonComp.left}${
              i18n.chain.jsonComp.insertColContainer
            }`,
            level: 'special',
            placement: 'right',
            className: isFlexColumnItem ? 'ae-InsertBefore is-vertical' : 'ae-InsertBefore',
            onClick: () => this.manager.appendSiblingSchema(newColumnSchema, true, true),
          },
          {
            iconSvg: 'add-btn',
            tooltip: `${isFlexColumnItem ? i18n.chain.jsonComp.down : i18n.chain.jsonComp.right}${
              i18n.chain.jsonComp.insertColContainer
            }`,
            level: 'special',
            placement: isFlexColumnItem ? 'right' : 'left',
            className: isFlexColumnItem ? 'ae-InsertAfter is-vertical' : 'ae-InsertAfter',
            onClick: () => this.manager.appendSiblingSchema(newColumnSchema, false, true),
          },
        );
      }
    }
  }

  afterResolveJsonSchema(event: PluginEvent<RendererJSONSchemaResolveEventContext>) {
    const context = event.context;
    const parent = context.node.parent?.host as EditorNodeType;

    if (parent?.info?.plugin === this) {
      event.setData('/schemas/FlexColumn.json');
    }
  }
}
