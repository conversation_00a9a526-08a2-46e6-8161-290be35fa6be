import _ from 'lodash';
import { from, Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { ALBUM_LIST_DATA } from '@mdtBsBffServices/services';
import { IBusinessResult } from '@mdtBsComm/interfaces';
import { IFormData } from '@mdtProMicroModules/containers/dialog-modify-form-tag';
import { ITableData } from '@mdtProMicroModules/containers/table-theme/TableThemeModel';
import { AppController } from '../../app/AppController';
import { albumBffService } from '../../bff-service';

export class TableThemesModelBff {
  public static queryPermissions() {
    const app = AppController.getInstance();
    const ps = app.getUserPermissionController().getThemeManagePermission();
    if (app.getPreferencesController().getHasSetPkgPref()) {
      return {
        ...ps,
        createProps: {
          disabled: true,
        },
      };
    }
    return ps;
  }

  public static queryThemeList(): Observable<ITableData[]> {
    const app = AppController.getInstance();
    const albumC = app.getAlbumController()!;
    const aids = albumC.getPrefAlbumIds();
    return from(
      albumBffService.queryAlbums({
        params: { apps: `${app.getAppId()}` },
        respData: ALBUM_LIST_DATA,
      }),
    ).pipe(
      map((resp) => {
        if (!resp.success) return [];
        const data = _.map(resp.page_data, (item) => ({
          id: item.id,
          name: item.name,
          pkgStats: `${item.pkgStats?.datapkg_count || 0}`,
        }));
        if (app.getPreferencesController().getHasSetPkgPref()) {
          return _.filter(data, (item) => _.includes(aids, item.id));
        }
        return data;
      }),
    );
  }

  public static createThemes(createData: IFormData): Observable<IBusinessResult<ITableData[]>> {
    return from(albumBffService.batchCreateAlbum({ data: _.map(createData.name, (tag) => ({ name: tag })) })).pipe(
      map((resp) => {
        const data = _.map(resp.success ? resp.page_data : resp.extra?.value, (item) => ({
          id: item.id,
          name: item.name,
          pkgStats: '0',
        }));
        return {
          success: resp.success,
          result: data,
        };
      }),
    );
  }

  public static updateTheme(
    updateData: IFormData,
    originalUpdateData: ITableData,
  ): Observable<IBusinessResult<ITableData>> {
    const name = updateData.name as string;
    return from(
      albumBffService.updateAlbum({
        id: originalUpdateData.id,
        data: { name },
      }),
    ).pipe(
      map((resp) => ({
        success: resp.success,
        result: resp.success ? { ...originalUpdateData, name: resp.page_data!.name } : undefined,
      })),
    );
  }

  public static deleteTheme(deleteData: ITableData): Observable<IBusinessResult<ITableData>> {
    return from(albumBffService.deleteAlbum({ id: deleteData.id })).pipe(
      map((resp) => ({
        success: resp.success,
        result: resp.success ? deleteData : undefined,
      })),
    );
  }
}
