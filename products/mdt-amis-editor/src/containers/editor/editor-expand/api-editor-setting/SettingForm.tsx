import _ from 'lodash';
import React, { FC, useMemo } from 'react';
import { createForm } from '@formily/core';
import { observer } from '@formily/react';
import { Button } from '@metroDesign/button';
import { Space } from '@metroDesign/space';
import { toastApi } from '@metroDesign/toast';
import { SchemaField } from '@mdtProFormEditor/metro-form-settings';
import { Form } from '@mdtProFormEditor/metro-formily';
import { RequestTypeEnum } from '@mdtProMicroModules/amis-expand/constants';
import { DatapkgSelectorComp as PkgSelect } from '@mdtProMicroModules/containers/datapkg-selector';
import i18n from '../../../../languages';
import { SettingSchema } from './SettingSchema';
import './index.less';

interface IHeaderProps {
  onClose: () => void;
  onSubmit: () => void;
}
const SettingHeader: FC<IHeaderProps> = ({ onClose, onSubmit }) => {
  return (
    <div className="metro-drawer-header" style={{ height: 49 }}>
      <div className="metro-drawer-header-title">
        <div className="metro-drawer-title">{i18n.chain.apiSettingTitle}</div>
      </div>
      <Space>
        <Button onClick={onClose}>{i18n.chain.comButton.cancel}</Button>
        <Button type="primary" onClick={onSubmit}>
          {i18n.chain.comButton.finish}
        </Button>
      </Space>
    </div>
  );
};

interface IProps {
  payload: {
    data?: any;
    onSubmit: (payload: any) => void;
  };
  onClose: () => void;
}

const settingComponents = {
  PkgSelect,
  ApiSettingSchema: SettingSchema,
};
const schema = {
  properties: {
    setting: {
      type: 'void',
      'x-component': 'ApiSettingSchema',
    },
  },
};

export const SettingsForm: React.FC<IProps> = observer((props) => {
  const {
    payload: { data = {}, onSubmit },
    onClose,
  } = props;

  const form = useMemo(() => {
    const { request_type = RequestTypeEnum.DATAPKG, request_adapt, ...configData } = data.mdtApiProxy || {};
    return createForm({
      values: { request_type, request_adapt, config: { [request_type]: configData } },
    });
  }, [data]);

  const handleSave = async () => {
    try {
      await form.validate();
      const { request_type, request_adapt, config } = form.values;
      const value = _.cloneDeep({ request_type, request_adapt, ...(config[request_type] || {}) });
      onSubmit({ ...data, url: `mdt_api_proxy_${new Date().getTime()}`, method: 'post', mdtApiProxy: value });
      onClose();
    } catch (error) {
      toastApi.error(i18n.chain.formError);
    }
  };

  return (
    <div className="">
      <SettingHeader onClose={onClose} onSubmit={handleSave} />
      <Form
        form={form}
        colon={false}
        labelAlign="left"
        wrapperAlign="right"
        feedbackLayout="none"
        layout="vertical"
        component="div"
        className="metro-form-settings api-editor-form-settings"
      >
        <SchemaField schema={schema} components={settingComponents} />
      </Form>
    </div>
  );
});
