import { from } from 'rxjs';
import { map, takeWhile } from 'rxjs/operators';
import { deleteFileAsync } from '@mdtBsServices/files';
import { putPreferencesAsync } from '@mdtBsServices/preference';
import { getPubfilesByIdAsync, getPubfilesByPublicPathAsync } from '@mdtBsServices/pubfiles';
import { PreferencesEnum } from '@mdtProComm/constants';
import { ICustomLoginPreferences } from './CustomLoginController';

export class CustomLoginModel {
  public static getConfigUrl(fileId: string) {
    return from(getPubfilesByIdAsync(fileId, { redirect: false })).pipe(
      map((resp) => {
        return resp.data?.sign_url.url;
      }),
    );
  }

  public static getConfigUrlWithPath(path: string) {
    return from(getPubfilesByPublicPathAsync(path, { redirect: false })).pipe(
      map((resp) => {
        if (resp.success) {
          const request = new XMLHttpRequest();
          request.open('GET', resp.data?.sign_url.url as string, false); // 设置同步请求
          request.setRequestHeader('Accept', 'application/javascript'); // 添加请求头
          request.send(null);
          return request.status === 200 ? request.responseText : '';
        }
        return '';
      }),
    );
  }

  public static putCustomLogin(value: ICustomLoginPreferences) {
    return from(putPreferencesAsync({ name: PreferencesEnum.CUSTOM_LOGIN }, { value, at: 'app' })).pipe(
      takeWhile((resp) => !resp.canceled),
    );
  }

  public static deleteFile(fileId: string) {
    return from(deleteFileAsync(fileId, { quiet: true })).pipe(takeWhile((resp) => !resp.canceled));
  }
}
export type ICustomLoginModel = typeof CustomLoginModel;
