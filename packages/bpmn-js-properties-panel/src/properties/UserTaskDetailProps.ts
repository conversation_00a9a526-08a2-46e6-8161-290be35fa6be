import { isTextAreaEntryEdited, TextAreaEntry } from '@bpmn-io/properties-panel';
import { useService } from 'bpmn-js-properties-panel';
import { PREFIX } from '../constants';
import { ButtonEntry } from '../entries/ButtonEntry';
import { getRelevantBusinessObject, isBpmnUserTask } from '../utils/ElementUtil';
import { addExtensionElements, getExtensionElement, removeExtensionElements } from '../utils/ExtensionElementsUtil';

const CONFIG_CUSTOM_ELEMENT_KEY = `${PREFIX}:UserTaskDetail`;

export function UserTaskDetailProps(props: any) {
  const { element } = props;

  if (!isBpmnUserTask(element)) {
    return [];
  }

  return [{ id: 'userTaskDetail', component: UserTaskDetail, isEdited: isTextAreaEntryEdited }];
}

function UserTaskDetail(props: any) {
  const { element, id } = props;

  const bpmnFactory = useService('bpmnFactory');
  const commandStack = useService('commandStack');
  const translate = useService('translate');
  const debounce = useService('debounceInput');
  const businessObject = getRelevantBusinessObject(element);
  const mdtExternal = useService('mdtExternalProvider');

  const getValue = () => {
    const extensionElement = getExtensionElement(businessObject, CONFIG_CUSTOM_ELEMENT_KEY);
    return extensionElement?.get('body') || '';
  };

  const setElement = (eleKey: string, value: any) => {
    let ele = getExtensionElement(businessObject, eleKey);
    ele && removeExtensionElements(element, businessObject, ele, commandStack);
    ele = bpmnFactory.create(eleKey, { body: value });
    addExtensionElements(element, businessObject, ele, bpmnFactory, commandStack);
  };

  const setValue = (value: string) => {
    setElement(CONFIG_CUSTOM_ELEMENT_KEY, value);
  };

  const onClick = (e: any) => {
    e.stopPropagation();
    e.preventDefault();
    mdtExternal.handleConfigUserTaskDetail(getValue(), setValue);
  };

  const label = ButtonEntry({
    children: translate('Setting'),
    onClick,
  });

  return TextAreaEntry({ element, id, label, getValue, setValue, debounce, disabled: true });
}
