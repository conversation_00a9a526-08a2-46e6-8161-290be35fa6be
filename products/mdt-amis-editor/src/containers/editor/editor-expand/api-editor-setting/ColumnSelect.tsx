import _ from 'lodash';
import { FC, useEffect, useRef, useState } from 'react';
import { Select } from '@metroDesign/select';
import { Typography } from '@metroDesign/typography';
import { combineLatest } from 'rxjs';
import { DatapkgModel } from '@mdtProComm/models/DatapkgModel';
import { DatapkgModelBff } from '@mdtProComm/models/DatapkgModelBff';
import { ENABLE_BFF } from '@mdtProMicroModules/datlas/datlasConfig';
import i18n from '../../../../languages';

export interface IProps {
  value: string;
  onChange: (value: string) => void;
  pkgId?: string;
}

const DEFAULT_COLUMN = 'id';
const DEFAULT_DATASOURCE = [{ label: DEFAULT_COLUMN, value: DEFAULT_COLUMN }];
export const ColumnSelect: FC<IProps> = ({ value, onChange, pkgId }) => {
  const [dataSource, setDataSource] = useState(DEFAULT_DATASOURCE);
  const onChangeRef = useRef(onChange);
  onChangeRef.current = onChange;
  const Model = ENABLE_BFF ? DatapkgModelBff : DatapkgModel;
  useEffect(() => {
    if (!pkgId) {
      setDataSource(DEFAULT_DATASOURCE);
    } else {
      combineLatest([Model.queryDatapkgDetail(pkgId), Model.queryDatapkgColumns(pkgId)]).subscribe(
        ([detailVal, columnVal]: any) => {
          const { idColumn } = ENABLE_BFF ? detailVal.page_data : detailVal.data;
          const columns = ENABLE_BFF ? columnVal.page_data : columnVal.data;
          const ds = _.map(columns, (item) => ({
            label: item.name,
            value: item.name,
          }));
          setDataSource(ds);
          // 没有value的时候才自动填充
          !value && onChangeRef.current?.(idColumn);
        },
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pkgId]);

  return (
    <>
      <Select
        options={dataSource}
        value={value}
        onChange={onChange}
        allowClear
        placeholder={i18n.chain.jsonComp.columnSelectPlaceholder}
      />
      <Typography.Paragraph type="secondary">{i18n.chain.jsonComp.columnSelectTip}</Typography.Paragraph>
    </>
  );
};
