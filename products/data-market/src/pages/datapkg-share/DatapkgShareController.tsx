import _ from 'lodash';
import { FC } from 'react';
import { Observable, of } from 'rxjs';
import { ILoadDataListRslt, IPaginationParams } from '@mdtBsComponents/data-list-comp-card';
import { ModalWithBtnsCompDrawerController } from '@mdtBsComponents/modal-with-btns-comp-drawer';
import { RequestController } from '@mdtBsControllers/request-controller';
import { IDatapkgsQuery, IRequestCancelToken } from '@mdtBsServices/interfaces';
import { OwnershipEnum } from '@mdtProComm/constants';
import { CardListDatapkgShareController } from '@mdtProMicroModules/containers/card-list-datapkg-share';
import {
  IDatapkgItem,
  TableCardDatapkgSearchController,
} from '@mdtProMicroModules/containers/table-card-datapkg-search';
import {
  TableExternalInstitution,
  TableExternalInstitutionController,
} from '@mdtProMicroModules/containers/table-external-institution';
import { AppController } from '../../app/AppController';
import { ENABLE_BFF } from '../../config';
import i18n from '../../languages';
import { IDatapkgShareModell } from './DatapkgShareModel';
import { ExternalInstitutionModel } from './ExternalInstitutionModel';
import { ExternalInstitutionModelBff } from './ExternalInstitutionModelBff';
import { ShareModel } from './ShareModel';
import { ShareModelBff } from './ShareModelBff';

export interface IControllerOptions {
  title: string;
  Model: IDatapkgShareModell;
  defaultAppId: number;
}

export interface IFilterParams {
  cancelToken: IRequestCancelToken;
  params: IDatapkgsQuery;
}

class DatapkgShareController extends RequestController {
  // 数据包搜索 controller
  private tableCardController: TableCardDatapkgSearchController;
  // 数据包分享列表 controller
  private shareListController: CardListDatapkgShareController;
  // 机构管理弹窗 controller
  private drawerInstitutionController: ModalWithBtnsCompDrawerController<void, void>;
  // 机构管理 controller
  private institutionController?: TableExternalInstitutionController;
  private app?: AppController;
  private Model?: IDatapkgShareModell;
  private appId: number;
  private appName: string;
  private hasEditInstitution = true;

  public constructor(options: IControllerOptions) {
    super();
    this.app = AppController.getInstance() as AppController;
    this.Model = options.Model;
    this.appId = this.app.getAppId();
    // app级别检索的app名称
    this.appName = options.title;

    this.tableCardController = new TableCardDatapkgSearchController({
      dataListControllerOptions: {
        loadDataListFunc: this.queryFirstPageData,
        loadNextPageDataListFunc: this.queryNextPageData,
        getBackendFilterParams: this.getBackendFilterParams,
      },
      headerTitle: options.title,
      clickDatapkgItemFunc: this.clickDatapkgItem,
      enableSelect: true,
    });

    const SModel = ENABLE_BFF ? ShareModelBff : ShareModel;
    this.shareListController = new CardListDatapkgShareController(new SModel(this.appId), {
      deleteOrClearFunc: (id?: string) => this.handleDeleteOrClear(id),
      loadDataListFunc: () => this.loadSelectedPkgs(),
      gotoApprovalFunc: () => this.handleGotoApproval(),
      defaultAppId: options.defaultAppId,
    });

    this.drawerInstitutionController = new ModalWithBtnsCompDrawerController({
      modalCompOptions: {
        modalOptions: {
          width: '500px',
          level: null,
          className: 'drawer-external-institution',
        },
        InnerView: TableExternalInstitution as FC,
        innerViewController: () => this.getInstitutionSetupController(),
      },
      uiOptions: { title: i18n.chain.datamarket.pkg.appSetting, customerHeader: null },
    });

    this.listenSelectedPkgsChange();
    this.init();
  }

  public destroy() {
    super.destroy();
    this.tableCardController.destroy();
    this.shareListController.destroy();
    this.drawerInstitutionController.destroy();
    this.institutionController?.destroy();
    this.institutionController = undefined;
    this.app = undefined;
    this.Model = undefined;
  }

  public getDrawerInstitutionController() {
    return this.drawerInstitutionController;
  }

  public getTableCardController() {
    return this.tableCardController;
  }

  public getShareListController() {
    return this.shareListController;
  }

  public handleSetupInstitution() {
    this.drawerInstitutionController.openModal();
  }

  public hasEditInstitutionPower() {
    return this.hasEditInstitution;
  }

  // 去审批页面
  private handleGotoApproval = () => {
    this.app!.getRouterController().gotoApprovalOther();
  };

  // 选中列表中删除或清空
  private handleDeleteOrClear(id?: string) {
    const list = this.tableCardController.getSelectedDataListValue();
    if (id) {
      this.tableCardController.removeFromSelectedDataList(_.find(list, ['id', id])!);
    } else {
      this.tableCardController.removeFromSelectedDataList(list);
    }
  }

  // 监听数据包选择的变化
  private listenSelectedPkgsChange() {
    this.tableCardController.getSelectedDataList$().subscribe(() => {
      this.shareListController.loadDataList();
    });
  }

  // 加载数据包选中列表
  private loadSelectedPkgs(): Observable<IDatapkgItem[]> {
    return of(this.tableCardController.getSelectedDataListValue());
  }

  private getInstitutionSetupController(): any {
    if (!this.institutionController) {
      const Model = ENABLE_BFF ? ExternalInstitutionModelBff : ExternalInstitutionModel;
      this.institutionController = new TableExternalInstitutionController({
        Model: new Model(this.app!),
        selfAppId: this.app!.getAppId(),
      });
    }
    return this.institutionController;
  }

  private clickDatapkgItem = (item: IDatapkgItem, selected?: boolean) => {
    if (!_.isNil(selected)) {
      selected
        ? this.tableCardController.removeFromSelectedDataList(item)
        : this.tableCardController.addToSelectedDataList(item);
    }
  };

  // 加载首页列表
  private queryFirstPageData = (filterParams: IFilterParams): ILoadDataListRslt<IDatapkgItem> => {
    const params = { apps: this.appId };
    return this.Model!.queryFirstPagePkgs(
      filterParams.params,
      params,
      filterParams.cancelToken,
      this.appName,
      `${this.appId}`,
    );
  };

  // 加载下一页列表
  private queryNextPageData = (params: IPaginationParams) => {
    const filterParams = this.getBackendFilterParams();
    return this.Model!.queryNextPageTickets(
      filterParams.params,
      { ...params, apps: this.appId },
      filterParams.cancelToken,
      this.appName,
      `${this.appId}`,
    );
  };

  private getBackendFilterParams = (): IFilterParams => {
    const tcc = this.tableCardController;
    const params = tcc.defaultGetSearchParams();
    params.need_permissions = true;
    params.need_tags = true;
    params.status = 'approved';
    params.ownership = OwnershipEnum.APP;
    const pas = this.app!.getAlbumController().getPrefAlbumIds();
    this.app!.getPreferencesController().getHasSetPkgPref() && (params.album_id = pas);
    const cancelToken = this.getCancelToken();
    return { cancelToken, params };
  };

  // 初始请求
  private init() {
    // 是否有机构设置权限
    // this.hasEditInstitution = this.app!.getUserPermissionController().hasOptCrossAppManage();
    // 监听toolFilter变化
    const tcc = this.tableCardController;
    tcc.defaultComputeConditionList();
    tcc.listenBackendFilter(tcc.getToolInput$(), tcc.getToolFilter$(), tcc.getToolDateRange$());
    tcc.loadDataList(this.getBackendFilterParams());
  }
}

export { DatapkgShareController };
