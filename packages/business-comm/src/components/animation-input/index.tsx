import { ChangeEvent, FC, useRef, useState } from 'react';
import { IconButton } from '@mdtDesign/button';
import { Input, InputProps, InputRef } from '@mdtDesign/input';
import './index.less';

type IProps = Omit<InputProps, 'prefixIcon' | 'ref'>;

const AnimationInput: FC<IProps> = (props) => {
  const [enable, setEnable] = useState(!!props.value);
  const ref = useRef(null);

  const onClick = () => {
    setEnable(true);
    ref.current && (ref.current as InputRef).focus();
  };

  const onBlur = (e: ChangeEvent<HTMLInputElement>) => {
    if (e.target.value) return;
    setEnable(false);
    props.onBlur?.(e);
  };

  const cls = ['com_animation-input', props.className || '', enable ? 'com_animation-input_open' : ''];

  return (
    <div className={cls.join(' ')}>
      <Input {...props} ref={ref} prefixIcon="search" className="animation-input_input" onBlur={onBlur} />
      <div className="animation-input_icon">
        <IconButton type="only-icon" icon="search" ghost onClick={onClick} />
      </div>
    </div>
  );
};

export { AnimationInput };
