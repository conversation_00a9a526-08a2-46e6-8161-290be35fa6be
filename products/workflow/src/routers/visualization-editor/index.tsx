import { useEffect, useState } from 'react';
import { VisualizationEditor } from './VisualizationEditor';
import { VisualizationEditorController } from './VisualizationEditorController';

const VisualizationEditorView = () => {
  const [controller, setController] = useState<any>();

  useEffect(() => {
    setController(new VisualizationEditorController());
  }, []);

  return controller ? <VisualizationEditor controller={controller} /> : null;
};

export default VisualizationEditorView;
