import { saveAs } from 'file-saver';

export const downloadFromBlob = (bolb: Blob, fileName: string) => {
  saveAs(bolb, fileName);
};

export const downloadFromUrl = (url: string, fileName: string, errorBack?: (ev: ProgressEvent) => void) => {
  const xhr = new XMLHttpRequest();
  xhr.open('GET', url);
  xhr.responseType = 'blob';
  xhr.onload = function () {
    saveAs(xhr.response, fileName);
  };
  xhr.onerror = errorBack || null;
  xhr.send();
};
