export const formatNumberK = (num: number | string): string => {
  const number = Number(num);
  let str = '';
  if (number >= 1e3 && number < 1e4) {
    str = (number / 1e3).toFixed(1) + 'k';
  } else if (number >= 1e4 && number < 1e7) {
    str = (number / 1e4).toFixed(1) + 'w';
  } else if (number >= 1e7) {
    str = (number / 1e7).toFixed(1) + 'kw';
  } else {
    str = `${number}`;
  }
  return str;
};
