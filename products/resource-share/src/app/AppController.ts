import { History } from 'history';
import { initBffCommonService } from '@mdtProComm/bff-services';
import { ModuleIdEnum, OrgAuthModeEnum, ProductPrefixEnum } from '@mdtProComm/constants';
import { DatlasAppController } from '@mdtProMicroModules/datlas/app/DatlasAppController';
import { RouterController } from '../_util/RouterController';
import { UserPermissionController } from '../_util/UserPermissionController';
import i18n from '../languages';
import { AppSideMenuController } from './AppSideMenuController';

/**
 * 使用单例模式
 */
class AppController extends DatlasAppController<RouterController, UserPermissionController, AppSideMenuController> {
  private constructor(history: History) {
    super({ i18n });
    initBffCommonService(ProductPrefixEnum.RESOURCE_SHARE);
    this.routerController = new RouterController(history, this);
  }

  public jumpToOrgAuth() {
    // TODO 测试
    const query = { activeTab: OrgAuthModeEnum.ORGANIZATION };
    this.jumpToOrganizationManagement(query);
  }

  protected async afterAuthSuccess() {
    await super.afterAuthSuccess();
    this.initAppReleation();
  }

  // 构造
  private initAppReleation() {
    // 初始化完必备信息后，构建用户的权限
    const upc = new UserPermissionController(this.getUserPermission()!, this.getPermissionController());
    this.userPermissionController = upc;
    this.appHeaderController = this.initAppHeader({
      defaultProduct: ModuleIdEnum.RESOURCE_SHARE,
      defaultModule: ModuleIdEnum.HOME,
      dynamicOpt: { enableMenuRs: false },
    });
    // 过滤用户没有权限的路由
    this.routerController!.initRoutes();
    this.appSideMenuController = new AppSideMenuController(this);
    this.appSideMenuController.initMenus();
  }
}

export { AppController };
