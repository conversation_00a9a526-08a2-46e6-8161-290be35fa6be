import { FC } from 'react';
import { Spin } from '@metroDesign/spin';
import { useObservableState } from '@mdtBsComm/hooks/use-observable-state';
import { RadioGroup } from '@mdtDesign/radio';
import { OrgAuthModeEnum } from '@mdtProComm/constants';
import { BaseAuthOrgManage } from '@mdtProMicroModules/containers/base-auth-org-manage-controller';
import { DrawerModifyFormPermissionOnly } from '@mdtProMicroModules/containers/drawer-modify-form-permission-only';
import { TableCurdWithSimpleSearch } from '@mdtProMicroModules/containers/table-curd-with-simple-search';
import { DrawerResouceShareOther } from '@mdtProMicroModules/pages/resource-share-other';
import { AuthManageController } from './AuthManageController';
import './index.less';

interface IProps {
  controller: AuthManageController;
}

const TableList: FC<IProps> = ({ controller }) => {
  const mode = useObservableState(controller.getMode$());
  const menu = useObservableState(controller.getMenu$());
  const loading = useObservableState(controller.getInitLoading$());
  const setMode = (val: string) => {
    controller.onChangeMode(val as OrgAuthModeEnum);
  };
  const tableMap: Record<OrgAuthModeEnum, any> = {
    [OrgAuthModeEnum.USER]: <TableCurdWithSimpleSearch controller={controller.getTableUserController()} />,
    [OrgAuthModeEnum.ROLE]: <TableCurdWithSimpleSearch controller={controller.getTableRoleController()} />,
    [OrgAuthModeEnum.ORGANIZATION]: <BaseAuthOrgManage controller={controller.getTableOrgController()} />,
  };

  return (
    <Spin spinning={loading} fillParent>
      <RadioGroup className="table-menu" radioType="nav" defaultValue={mode} options={menu} onChange={setMode} />
      {mode ? tableMap[mode] : null}
    </Spin>
  );
};

const AuthManage: FC<IProps> = ({ controller }) => {
  return (
    <div className="page_auth-manage">
      <TableList controller={controller} />

      <DrawerModifyFormPermissionOnly controller={controller.getUserPermissionController()} />
      <DrawerModifyFormPermissionOnly controller={controller.getRolePermissionController()} />
      <DrawerModifyFormPermissionOnly controller={controller.getOrgPermissionController()} />
      <DrawerResouceShareOther controller={controller.getResouceShareController()} />
    </div>
  );
};

export { AuthManage };
