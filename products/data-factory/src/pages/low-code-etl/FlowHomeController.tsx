import _ from 'lodash';
import { createGraphConfig } from '@antv/xflow';
import Ajv from 'ajv';
import { MenuInfo } from 'rc-menu/es/interface';
import { AsyncSubject, BehaviorSubject, Observable, Subscription } from 'rxjs';
import { DropmenuButton, IMenuItemProps } from '@mdtBsComm/components/dropmenu-button';
import { IBusinessResult } from '@mdtBsComm/interfaces';
import { downloadFromBlob } from '@mdtBsComm/utils/downloadUtil';
import { FolderActionType, ICurdOptions } from '@mdtBsComponents/data-list-comp-table-curd';
import { IDrawerHeaderProps, ModalWithBtnsCompDrawerController } from '@mdtBsComponents/modal-with-btns-comp-drawer';
import { IFlow as IFlowOrigin, IFlowRunPost, IFlowSupportNode } from '@mdtBsServices/interfaces';
import But<PERSON>, { IconButton, LinkButton } from '@mdtDesign/button';
import Tag from '@mdtDesign/tag';
import toastApi from '@mdtDesign/toast';
import { TaskStatusEnum } from '@mdtProComm/constants';
import { FolderOperationCommonController } from '@mdtProMicroModules/containers/folder-operation-common';
import {
  IVirtualizedTableProps,
  TableCurdWithSimpleSearchController,
} from '@mdtProMicroModules/containers/table-curd-with-simple-search';
import { TableTimedTaskController } from '@mdtProMicroModules/containers/table-timed-task';
import { FlowConfigController } from '@mdtProMicroModules/containers/xflow-canvas';
import { type IFolders } from '@mdtProMicroModules/models/FolderModel';
import { decoderFileAsText } from '@mdtProMicroModules/utils/fileUtil';
import { addWaitRunFlowTask } from '@mdtProTasks/util';
import { AppController } from '../../app/AppController';
import i18n from '../../languages';
import { NodeRenderKeyEnum } from '../flow-edit/_util/enum';
import {
  CreateNode,
  ExecuteNode,
  ExtractorNode,
  LoaderNode,
  TransformerNode,
} from '../flow-edit/custom-component/flow-node-component';
import FlowEditController, { IFlowMetaParams } from '../flow-edit/FlowEditController';
import { FlowSchema } from './_util/constants';
import { FlowViewMenuEnum } from './_util/enum';
import { transformFlowToGraphData } from './_util/utils';
import DrawerFlowViewInner from './DrawerInner';
import type { INodeList } from './FlowEditModel';
import {
  FlowListEnum,
  FlowRunListEnum,
  IFlowHomeModel,
  IFlowList as IFlow,
  IFlowRunList as IFlowRun,
} from './FlowHomeModel';

const FOLDER_SPACE = 'flow';

export enum MoreMenuKeyEnum {
  PLAY = 'play',
  DOWNLOAD = 'download',
  DELETE = 'delete',
}

class FlowHomeController extends TableCurdWithSimpleSearchController<IFlow> {
  public cacheNodeList$ = new BehaviorSubject<INodeList>({
    extractor: [],
    transformer: [],
    loader: [],
    create: [],
    execute: [],
  });
  private readonly drawerFlowViewController: ModalWithBtnsCompDrawerController;
  private readonly drawerFlowEditController: FlowEditController;
  private readonly drawerFlowCreateController: FlowEditController;
  private readonly flowRunController: TableCurdWithSimpleSearchController<IFlowRun>;
  private flowConfigController: FlowConfigController;
  private timedTaskController?: TableTimedTaskController;
  private folderOperationCtrl?: FolderOperationCommonController<IFlow>;

  private readonly pageLoading$ = new BehaviorSubject<boolean>(false);
  private readonly currentUseItem$ = new BehaviorSubject<IFlow | undefined>(undefined);
  private readonly selectFlowViewMenu$ = new BehaviorSubject<FlowViewMenuEnum>(FlowViewMenuEnum.FLOW_RUN);

  private app?: AppController;
  private Model?: IFlowHomeModel;
  private flowConfigViewAppSub?: Subscription;
  private flowConfigEditAppSub?: Subscription;
  private nodeList: IFlowSupportNode[] = [];

  public constructor(app: AppController, Model: IFlowHomeModel) {
    super({
      // 主页列表
      dataListCompTableCurdControllerOptions: {
        dataListControllerOptions: {
          loadDataListFunc: (params?: any) => this.queryFlowData(params),
          loadNextPageDataListFunc: (params) => this.queryNextFlowData(params),
          getBackendFilterParams: () => this.getQueryParams(),
          equalItemKey: FlowListEnum.KEY,
        },
        folderControllerOptions: {
          enableFolder: true,
        },
        tableOptions: () => this.initTableProps(),
        curdOptions: () => this.initCurdOptions(),
        clickCreateBtnFunc: () => this.handleCreate(),
        onDragOverFunc: (path: string, items: IFlow[]) => this.folderOperationCtrl?.moveToTarget(path, items),
        folderActionCallbackFunc: (path: string) => this.loadDataList({ path }),
        clickMoreBtnFunc: (key, data) => this.handleMoreBtn(key, data!),
      },
      headerOptions: () => this.loadHeaderOptions(),
    });

    this.app = app;
    this.Model = Model;
    const flowEditModel = this.Model!.getFlowEditModel();
    this.drawerFlowEditController = new FlowEditController(this.app, flowEditModel, this);
    this.drawerFlowCreateController = new FlowEditController(this.app, flowEditModel, this);
    // 抽屉组件
    this.drawerFlowViewController = new ModalWithBtnsCompDrawerController({
      modalCompOptions: {
        modalOptions: { width: '100%', className: 'drawer-flow-view' },
        InnerView: DrawerFlowViewInner,
      },
      // Header内容
      uiOptions: () => this.drawerFlowViewUiOptions(),
      // 确定按钮回调
      clickOkBtnFunc: () => this.flowViewRunFlow(),
      // 关闭drawer
      beforeCancelFunc: () => this.drawerFlowViewCancel(),
    });
    // Xflow
    this.flowConfigController = new FlowConfigController({
      flowCanvasConfig: this.getGraphConfig(),
      endScaleToolbar: true,
      scaleToolbarPosition: { bottom: 12, right: 12 },
    });

    // 执行记录列表
    this.flowRunController = new TableCurdWithSimpleSearchController<IFlowRun>({
      dataListCompTableCurdControllerOptions: {
        dataListControllerOptions: {
          loadDataListFunc: (flowId: string) =>
            this.Model!.getFlowRunList({ flow_id: this.currentUseItem$.getValue()?.key ?? flowId }),
        },
        tableOptions: () => this.initFlowRunTableProps(),
      },
      headerOptions: () => ({ createBtnLabel: '', title: '', hideInput: true }),
    });

    this.folderOperationCtrl = this.getEnableFolder()
      ? new FolderOperationCommonController<IFlow>({
          space: FOLDER_SPACE,
          resouceKey: FlowListEnum.KEY,
          getFolderPathFunc: () => this.getFolderPathValue(),
          transformFolderToVFunc: (item: IFolders) => this.Model!.transformFolderToTableData(item),
          deleteDataModelFunc: (ids: string[]) => this.Model!.deleteBatchFlow(ids.join(',')),
          loadDataListFunc: (params?: any) => this.loadDataList(params),
          addToDataListFunc: (data: IFlow) => this.addDataToList(data),
          deleteDataFromListFunc: (data: IFlow, modifyTotal?: boolean) => this.deleteDataFromList(data, modifyTotal),
        })
      : undefined;

    this.loadDataList();
    this.listenBackendFilter(this.getSingleFilter$());
    this.listenSingleFilterToChangeFolderPath();
    this.Model!.getNodeList().subscribe((data) => {
      this.cacheNodeList$.next(flowEditModel.groupNodeList(data));
      this.nodeList = data;
    });

    // 是否需要根据菜单切换刷新表格数据
    // this.selectFlowViewMenu$.subscribe((key) => {
    //   if (key === FlowViewMenuEnum.FLOW_RUN) {
    //     this.flowRunController?.loadDataList();
    //   } else {
    //     this.timedTaskController?.getTableController().loadDataList();
    //   }
    // });
  }

  public getFolderOperationCtrl() {
    return this.folderOperationCtrl;
  }

  public getFlowConfigController() {
    return this.flowConfigController;
  }

  public getDrawerFlowViewController() {
    return this.drawerFlowViewController;
  }

  public getDrawerFlowEditController() {
    return this.drawerFlowEditController;
  }

  public getDrawerFlowCreateController() {
    return this.drawerFlowCreateController;
  }

  public getFlowRunController() {
    return this.flowRunController;
  }

  // 定时任务Controller
  public getTimedTaskController(): any {
    this.timedTaskController = new TableTimedTaskController(
      this.Model!.getTableTimedTaskModel(this.currentUseItem$.getValue()!.key),
    );
    if (!this.timedTaskController) {
      this.timedTaskController = new TableTimedTaskController(
        this.Model!.getTableTimedTaskModel(this.currentUseItem$.getValue()!.key),
      );
    }
    return this.timedTaskController;
  }

  public getPageLoading() {
    return this.pageLoading$;
  }

  public getCurrentUseItem() {
    return this.currentUseItem$;
  }

  public getSelectFlowViewMenu() {
    return this.selectFlowViewMenu$;
  }

  public changeFlowViewMenu(info: MenuInfo) {
    const key = info.key as FlowViewMenuEnum;
    this.selectFlowViewMenu$.next(key);
  }

  public runFlow(flowId: string, title: string, params: IFlowRunPost, cb?: () => void) {
    this.Model!.runFlow(flowId, params).subscribe((data) => {
      const task = addWaitRunFlowTask(data.task_id, data.flow_id, title);
      // TODO 更新执行结果成功失败
      task.taskResp$.subscribe((taskResp) => {
        const status = taskResp.status;
        if (status === TaskStatusEnum.SUCCESSFUL || status === TaskStatusEnum.FAILED) {
          cb?.();
        }
      });
    });
  }

  public deleteFlow = (flow: IFlow) => {
    const flowId = flow.key;
    const revision = flow.revision;
    this.Model!.deleteFlow(flowId, { revision }).subscribe((data) => {
      data && this.deleteDataFromList(flow);
    });
  };

  public downloadFlow = (flow: IFlowOrigin) => {
    const newFlow = _.pick(flow, ['title', 'description', 'flow_type', 'nodes', 'links', '_fparams_']);
    const blob = new Blob([JSON.stringify(newFlow)], { type: 'text/plain;charset=utf-8' });
    const fileName = `${flow.title}.json`;
    downloadFromBlob(blob, fileName);
  };

  public flowViewEditFlow = () => {
    const useItem = this.currentUseItem$.getValue()!;
    this.editFlow(useItem.flow);
  };

  public drawerFlowEditCancel = () => {
    this.flowConfigEditAppSub?.unsubscribe();
  };

  public destroy() {
    this.drawerFlowViewController.destroy();
    this.drawerFlowEditController.destroy();
    this.drawerFlowCreateController.destroy();
    this.flowRunController.destroy();
    this.flowConfigController.destroy();
    this.timedTaskController?.destroy();
    this.timedTaskController = undefined;
    this.pageLoading$.complete();
    this.currentUseItem$.complete();
    this.currentUseItem$.next(undefined);
    this.selectFlowViewMenu$.complete();
    this.cacheNodeList$.complete();
    this.cacheNodeList$.next(null!);
    this.flowConfigViewAppSub?.unsubscribe();
    this.flowConfigViewAppSub = undefined;
    this.flowConfigEditAppSub?.unsubscribe();
    this.flowConfigEditAppSub = undefined;
    this.app = undefined;
    this.Model = undefined;
    this.nodeList = [];
    this.folderOperationCtrl?.destroy();
  }

  // 渲染表头所需信息
  private initTableProps = (): IVirtualizedTableProps => {
    return {
      columns: [
        {
          code: FlowListEnum.TASK_NAME,
          name: i18n.chain.dataFactory.etl.jobName,
          render: (name: string, data: IFlow) => {
            return name ? (
              <LinkButton disabled={!data.hasRead} onClick={() => this.viewFlowView(data)}>
                {name}
              </LinkButton>
            ) : null;
          },
        },
        { code: FlowListEnum.USER, name: i18n.chain.dataFactory.etl.jobAdmin },
        { code: FlowListEnum.UPDATE_TIME, name: i18n.chain.dataFactory.etl.lastPlayTime },
        {
          code: FlowListEnum.RESULT,
          name: i18n.chain.dataFactory.etl.lastPlayResult,
          align: 'center',
          render: (result: string[]) => <Tag tag={result[0]} color={result[1] as any} />,
        },
      ],
      type: 'page-bg',
      primaryKey: 'id',
      emptyContent: i18n.chain.comNoData,
      withVerticalBorder: false,
    };
  };

  // 渲染表头所需信息
  private initFlowRunTableProps = (): IVirtualizedTableProps => {
    return {
      columns: [
        { code: FlowRunListEnum.START_TIME, name: i18n.chain.dataFactory.etl.runStartTime, width: 200 },
        { code: FlowRunListEnum.END_TIME, name: i18n.chain.dataFactory.etl.runEndTime, width: 200 },
        { code: FlowRunListEnum.CONSUM_TIME, name: i18n.chain.dataFactory.etl.useTime, align: 'center' },
        {
          code: FlowRunListEnum.RESULT,
          name: i18n.chain.dataFactory.etl.result,
          align: 'center',
          render: (result: string[]) => <Tag tag={result[0]} color={result[1] as any} />,
        },
        // {
        //   name: '',
        //   code: FlowRunListEnum.KEY,
        //   width: 160,
        //   align: 'center',
        //   render: (value: string) => {
        //     return value ? (
        //       <div className="home-table-icon">
        //         <IconButton
        //           className="icon"
        //           type="only-icon"
        //           icon="page"
        //           onClick={() => {
        //             alert('查看FLOW日志');
        //           }}
        //         />
        //       </div>
        //     ) : null;
        //   },
        // },
      ],
      type: 'page-bg',
      primaryKey: 'id',
      emptyContent: i18n.chain.comNoData,
      withVerticalBorder: false,
    };
  };

  // 上传flow
  private handleUpload = async (files: File[]) => {
    const str = await decoderFileAsText(files[0]);
    let flow;
    let errorMsg = '';
    try {
      flow = JSON.parse(str);
      const ajv = new Ajv();
      const validate = ajv.compile(FlowSchema);
      !validate(flow) && (errorMsg = JSON.stringify(validate.errors));
    } catch (e: any) {
      errorMsg = e.message;
    }
    if (errorMsg) {
      toastApi.error(i18n.chain.dataFactory.etl.jsonInvalid(errorMsg));
      return;
    }
    this.createFlowByUploda(flow);
  };

  private createFlowByUploda(flow: IFlowOrigin) {
    let graphData =
      (flow._fparams_ as IFlowMetaParams)?.graphData || transformFlowToGraphData(flow, this.nodeList, true);
    const createFlowConfigController = this.drawerFlowCreateController.getFlowConfigController();
    this.drawerFlowCreateController.initEditFlow({ ...flow, _fparams_: { ...flow._fparams_, graphData } as any });
    this.drawerFlowCreateController.openModal();
    createFlowConfigController.setGraphData(graphData);
  }

  private loadHeaderOptions() {
    const menus: IMenuItemProps[] = [
      {
        title: i18n.chain.dataFactory.etl.uploadFlow,
        type: 'upload',
        key: 'upload',
        uploadProps: {
          multiple: false,
          accept: '.json',
          onDropAccepted: this.handleUpload,
        },
      },
    ];
    return {
      createBtnLabel: i18n.chain.dataFactory.etl.createFlow,
      inputPlaceholder: i18n.chain.dataFactory.etl.searchFlow,
      title: 'FLOW',
      hideInput: false,
      renderExtendOthers: () => (
        <DropmenuButton
          menus={menus}
          noSelected={true}
          buttonLabel=""
          destroyPopupOnHide={false}
          buttonProps={{ type: 'primary', style: { marginLeft: 8 }, leftIcon: 'add-2' }}
        >
          <Button className="dm-flow-template_upload" leftIcon="upload" type="primary" />
        </DropmenuButton>
      ),
    };
  }

  private initCurdOptions(): ICurdOptions {
    return {
      enableCreate: true,
      dragCode: 'taskName',
      onFolderCreate: () => this.folderOperationCtrl?.getCreateFolderController().openModal(this.getFolderPathValue()),
      onFolderNavClick: (path: string) => this.loadDataList({ path }),
      otherBtns: (data: IFlow) => {
        return (
          <>
            <IconButton
              className="icon"
              type="only-icon"
              icon="visibility-on"
              onClick={() => this.viewFlowView(data)}
              disabled={!data.hasRead}
            />
            <IconButton
              className="icon edit-icon"
              type="only-icon"
              icon="edit"
              onClick={() => this.editFlow(data.flow)}
              disabled={!data.hasUpdate}
            />
          </>
        );
      },
      moreItems: (item: IFlow) => {
        const menuItems: any[] = [
          {
            title: i18n.chain.comFolder.moveTo,
            key: FolderActionType.MOVE,
            icon: 'move-to',
          },
        ];
        if (item?.hasExecute) {
          menuItems.push({
            title: i18n.chain.dataFactory.etl.play,
            icon: 'play',
            key: MoreMenuKeyEnum.PLAY,
          });
        }
        if (item?.hasRead) {
          menuItems.push({
            title: i18n.chain.dataFactory.etl.download,
            icon: 'download',
            key: MoreMenuKeyEnum.DOWNLOAD,
          });
        }
        if (item?.hasDelete) {
          menuItems.push({
            title: i18n.chain.dataFactory.etl.delData,
            icon: 'delete-2',
            key: MoreMenuKeyEnum.DELETE,
            danger: true,
          });
        }
        return menuItems;
      },
    };
  }

  // Header内容
  private drawerFlowViewUiOptions(): IDrawerHeaderProps<any> {
    const useItem = this.currentUseItem$.getValue();

    return {
      title: useItem?.taskName ?? '',
      okBtnProps: {
        type: 'primary',
        onlyIcon: 'play',
        style: { display: useItem?.hasExecute ? 'flex' : 'none' },
      },
      cancelText: i18n.chain.dataFactory.etl.editFlow,
      cancelBtnProps: {
        type: 'assist-bg',
        className: 'flow-view-cancel-btn',
      },
    };
  }

  private handleCreate() {
    const createFlowConfigController = this.drawerFlowCreateController.getFlowConfigController();
    createFlowConfigController.setGraphData({ nodes: [], edges: [] });
    this.drawerFlowCreateController.initEditFlow({ flow_type: 'dataflow' });
    this.drawerFlowCreateController.openModal();
    return new AsyncSubject<IBusinessResult<IFlow>>();
  }

  private handleMoreBtn(key: string, data: IFlow) {
    if (key === FolderActionType.MOVE) {
      this.folderOperationCtrl?.getTableMoveController().openModal(data);
    } else if (key === FolderActionType.RENAME) {
      this.folderOperationCtrl?.getRenameFolderController().openModal(data);
    } else if (key === FolderActionType.DELETE) {
      this.folderOperationCtrl?.getDeleteFolderController().openModal(data);
    } else if (key === MoreMenuKeyEnum.PLAY) {
      this.runFlow(data.key, data.taskName, { manual: true }, () => {
        this.loadDataList();
      });
    } else if (key === MoreMenuKeyEnum.DOWNLOAD) {
      this.downloadFlow(data.flow);
    } else if (key === MoreMenuKeyEnum.DELETE) {
      this.deleteFlow(data);
    }
    const result = new AsyncSubject<any>();
    result.next({ success: true });
    result.complete();
    return result;
  }

  private queryFlowData = (params?: any): Observable<[number, IFlow[]]> => {
    const data = this.getQueryParams();
    const enableFolder = this.getEnableFolder();
    const searchValue = this.getSingleFilterValue();
    // TODO 搜索状态下去掉文件夹的加载
    if (!enableFolder || searchValue) {
      return this.Model!.getFlowList(data.params);
    }
    const path = params?.path;
    const folderParams: any = { space: FOLDER_SPACE };
    path && (folderParams.path = path);
    const flowParams = path ? { ...data.params, folder: folderParams.path } : { ...data.params, nofolder: true };
    return this.Model!.getFlowFolderList(flowParams, folderParams);
  };

  private queryNextFlowData = (params: any): Observable<IFlow[]> => {
    const data = this.getQueryParams();
    const mergedParams = { ...data.params, ...params };
    if (this.getEnableFolder()) {
      this.getFolderPathValue() ? (mergedParams.folder = this.getFolderPathValue()) : (mergedParams.nofolder = true);
    }
    return this.Model!.getNextFlowList(mergedParams);
  };

  private getQueryParams = () => {
    const filterValue = this.getSingleFilterValue() as string;
    const name = _.size(filterValue) ? filterValue : undefined;
    return { params: { name_like: name } };
  };

  private viewFlowView = (data: IFlow) => {
    const flowConfigController = this.getFlowConfigController();
    this.currentUseItem$.next(data);
    // 获取flow数据（边数据和节点数据）
    const graphData = transformFlowToGraphData(data.flow, this.nodeList);
    this.drawerFlowViewController.openModal();
    this.flowRunController.loadDataList(data.key);
    flowConfigController.setGraphData(graphData);
  };

  private flowViewRunFlow = async () => {
    const useItem = this.currentUseItem$.getValue()!;
    this.runFlow(useItem.key, useItem.taskName, { manual: true }, () => {
      this.flowRunController.loadDataList();
    });
    return { success: false };
  };

  // 关闭抽屉
  private drawerFlowViewCancel = async () => {
    this.flowConfigViewAppSub?.unsubscribe();
    return true;
  };

  private editFlow = (flow: IFlowOrigin) => {
    let graphData =
      (flow._fparams_ as IFlowMetaParams)?.graphData || transformFlowToGraphData(flow, this.nodeList, true);
    const flowMeta = _.omit(flow, ['create_time', 'update_time', 'user_id']);
    this.drawerFlowEditController.initEditFlow({ ...flowMeta, _fparams_: { ...flowMeta._fparams_, graphData } as any });
    this.drawerFlowEditController.openModal();

    const flowConfigController = this.drawerFlowEditController.getFlowConfigController();
    flowConfigController.setGraphData(graphData);
  };

  // 获取画布配置
  private getGraphConfig = () => {
    return createGraphConfig((config) => {
      // TODO 添加预览时的一些限制
      /** 设置画布配置项，会覆盖XFlow默认画布配置项 */
      config.setX6Config({
        grid: {
          size: 20,
          visible: true,
        },
        background: { color: '#f6f7f9' },
        scaling: { min: 0.5, max: 2 },
        /** 画布滚轮缩放 */
        mousewheel: {
          modifiers: ['ctrl', 'meta'],
          enabled: true,
          /** 将鼠标位置作为中心缩放 */
          zoomAtMousePosition: true,
        },
      });

      /** 设置画布需要渲染的React节点、连线上的React内容 */
      config.setNodeRender(NodeRenderKeyEnum.EXTRACTOR_NODE, ExtractorNode);
      config.setNodeRender(NodeRenderKeyEnum.TRANSFORMER_NODE, TransformerNode);
      config.setNodeRender(NodeRenderKeyEnum.LOADER_NODE, LoaderNode);
      config.setNodeRender(NodeRenderKeyEnum.CREATE_NODE, CreateNode);
      config.setNodeRender(NodeRenderKeyEnum.EXECUTE_NODE, ExecuteNode);
    });
  };
}

export default FlowHomeController;
