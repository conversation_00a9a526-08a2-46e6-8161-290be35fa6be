import {
  BaseBffService,
  createArticle,
  deleteArticle,
  ICreateArticleOptions,
  IDeleteArticleOptions,
  IQueryArticleListOptions,
  IUpdateArticleOptions,
  queryArticleList,
  updateArticle,
} from '@mdtBsBffServices/services';

export class DMArticleBffService extends BaseBffService {
  public queryArticleList(options: IQueryArticleListOptions) {
    return queryArticleList(this.productName, options);
  }

  public createArticle(options: ICreateArticleOptions) {
    return createArticle(this.productName, options);
  }

  public updateArticle(options: IUpdateArticleOptions) {
    return updateArticle(this.productName, options);
  }

  public deleteArticle(options: IDeleteArticleOptions) {
    return deleteArticle(this.productName, options);
  }
}
