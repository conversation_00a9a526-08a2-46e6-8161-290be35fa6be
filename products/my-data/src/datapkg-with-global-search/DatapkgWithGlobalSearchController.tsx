import { BehaviorSubject } from 'rxjs';
import { getModel } from '../_util/modelUtil';
import { AppController } from '../app/AppController';
import { GlobalSearchController, GlobalSearchModel, GlobalSearchModelBff } from '../pages/global-search';

export class DatapkgWithGlobalSearchController {
  private showGlobalSearch$ = new BehaviorSubject<boolean>(false);
  private searchVal$ = new BehaviorSubject<string>('');
  private searchConfirmVal$ = new BehaviorSubject<string>('');
  private searchFocus$ = new BehaviorSubject<boolean>(false);
  private globalSearchController: GlobalSearchController;

  public constructor() {
    const app = AppController.getInstance();
    const appIds = app.getSearchDatapkgAppIdsStr();
    this.globalSearchController = new GlobalSearchController({
      app,
      apps: appIds,
      Model: getModel(GlobalSearchModel, GlobalSearchModelBff),
      searchVal$: this.searchConfirmVal$,
      backFunc: () => {
        this.showGlobalSearch$.next(false);
      },
    });
  }

  public destroy() {
    this.showGlobalSearch$.complete();
    this.searchVal$.complete();
    this.searchConfirmVal$.complete();
    this.searchFocus$.complete();
    this.globalSearchController?.destroy();
    this.globalSearchController = null!;
  }

  public getGlobalSearchController() {
    return this.globalSearchController;
  }

  public getShowGlobalSearch$() {
    return this.showGlobalSearch$;
  }

  public getSearchFocus$() {
    return this.searchFocus$;
  }

  public getSearchVal$() {
    return this.searchVal$;
  }

  public changeSearchVal(val: string) {
    this.searchFocus$.next(true);
    this.searchVal$.next(val);
  }

  public changeSearchFocus(val: boolean) {
    this.searchFocus$.next(val);
  }

  public handleSearch() {
    const val = this.searchVal$.getValue();
    if (!val) return; // 没输入不检索
    this.searchFocus$.next(false);
    this.searchConfirmVal$.next(val);
    if (!this.showGlobalSearch$.getValue()) {
      this.showGlobalSearch$.next(true);
    }
  }
}
