/* stylelint-disable scale-unlimited/declaration-strict-value */
/* stylelint-disable no-duplicate-selectors */
@import './grid.less';
@import './animation.less';

@theme: default;

@mdt-prefix: 'mdt';
@form-item-cls: ~'@{mdt-prefix}-formily-item';

@black: #000;
@text-color-secondary: fade(@black, 45%);
@heading-color: fade(@black, 85%);
@error-color: #f5222d;
@warning-color: #faad14;
@success-color: #52c41a;
@border-color-base: hsv(0, 0, 85%);
@form-error-input-bg: var(--metro-secondary-default);
@form-warning-input-bg: var(--metro-secondary-default);
@background-color-light: hsv(0, 0, 98%);

@height-base: 32px;
@form-item-margin-bottom: 24px;
@font-size-base: 14px;
@font-size-sm: 12px;
@height-sm: 24px;
@font-size-lg: @font-size-base + 2px;
@height-lg: 40px;

// 方法
@input-hover-border-color: #1890ff;
.hover(@color: @input-hover-border-color) {
  border-color: @color;
  border-right-width: 1px;
}

@primary-color: #1890ff;
@primary-color-hover: #1890ff;
@outline-fade: 20%;
@primary-color-outline: fade(@primary-color, @outline-fade);
@input-outline-offset: 0 0;
@outline-blur-size: 0;
@outline-width: 2px;
@border-width-base: 1px;
.active(@borderColor: @primary-color; @hoverBorderColor: @primary-color-hover; @outlineColor: @primary-color-outline) {
  & when (@theme = dark) {
    border-color: @borderColor;
  }
  & when (not (@theme = dark) and not (@theme = variable)) {
    border-color: @hoverBorderColor;
  }
  & when not (@theme = variable) {
    /* stylelint-disable-next-line function-no-unknown */
    box-shadow: @input-outline-offset @outline-blur-size @outline-width fade(@borderColor, @outline-fade);
  }
  & when (@theme = variable) {
    border-color: @hoverBorderColor;
    box-shadow: @input-outline-offset @outline-blur-size @outline-width @outlineColor;
  }
  border-right-width: @border-width-base;
  outline: 0;
}

.@{form-item-cls} {
  position: relative;
  display: flex;
  margin-bottom: @form-item-margin-bottom - 2;
  font-size: @font-size-base;

  &-label {
    min-height: @height-base - 2;
    line-height: @height-base;

    label {
      cursor: text;
    }
  }

  textarea.@{mdt-prefix}-input {
    height: auto;
  }

  // input[type=file]
  .@{mdt-prefix}-upload {
    background: transparent;
  }

  .@{mdt-prefix}-upload.@{mdt-prefix}-upload-drag {
    background: @background-color-light;
  }

  input[type='radio'],
  input[type='checkbox'] {
    width: @font-size-base;
    height: @font-size-base;
  }

  // Radios and checkboxes on same line
  .@{mdt-prefix}-radio-inline,
  .@{mdt-prefix}-checkbox-inline {
    display: inline-block;
    margin-left: 8px;
    font-weight: normal;
    vertical-align: middle;
    cursor: pointer;

    &:first-child {
      margin-left: 0;
    }
  }

  .@{mdt-prefix}-checkbox-vertical,
  .@{mdt-prefix}-radio-vertical {
    display: block;
  }

  .@{mdt-prefix}-checkbox-vertical + .@{mdt-prefix}-checkbox-vertical,
  .@{mdt-prefix}-radio-vertical + .@{mdt-prefix}-radio-vertical {
    margin-left: 0;
  }

  .@{mdt-prefix}-input-number {
    width: 100%;

    + .@{mdt-prefix}-form-text {
      margin-left: 8px;
    }

    &-handler-wrap {
      z-index: 2;
    }
  }

  .@{mdt-prefix}-select,
  .@{mdt-prefix}-cascader-picker,
  .@{mdt-prefix}-picker {
    width: 100%;
  }

  // Don't impact select inside input group
  .@{mdt-prefix}-input-group .@{mdt-prefix}-select,
  .@{mdt-prefix}-input-group .@{mdt-prefix}-cascader-picker {
    width: auto;
  }
}

.@{form-item-cls}-label {
  position: relative;
  display: flex;

  &-content {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  &-tooltip {
    cursor: help;

    * {
      cursor: help;
    }

    label {
      border-bottom: 1px dashed currentcolor;
    }
  }
}

.@{form-item-cls}-label {
  color: @heading-color;
}

.@{form-item-cls}-label-align-left {
  > .@{form-item-cls}-label {
    justify-content: flex-start;
  }
}

.@{form-item-cls}-label-align-right {
  > .@{form-item-cls}-label {
    justify-content: flex-end;
  }
}

.@{form-item-cls}-label-wrap {
  .@{form-item-cls}-label {
    label {
      white-space: pre-line;
      word-break: break-all;
    }
  }
}

.@{form-item-cls}-feedback-layout-terse {
  margin-bottom: 8px;

  &.@{form-item-cls}-feedback-has-text:not(.@{form-item-cls}-inset) {
    margin-bottom: 0;
  }
}

.@{form-item-cls}-feedback-layout-loose {
  margin-bottom: 22px;

  &.@{form-item-cls}-feedback-has-text:not(.@{form-item-cls}-inset) {
    margin-bottom: 0;
  }
}

.@{form-item-cls}-feedback-layout-none {
  margin-bottom: 0;

  &.@{form-item-cls}-feedback-has-text:not(.@{form-item-cls}-inset) {
    margin-bottom: 0;
  }
}

.@{form-item-cls}-control {
  flex: 1;
  max-width: 100%;

  .@{form-item-cls}-control-content {
    display: flex;

    .@{form-item-cls}-control-content-component {
      display: flex;
      align-items: center;
      width: 100%;
      min-height: @height-base - 2;
      line-height: @height-base + 2;

      &-has-feedback-icon {
        position: relative;
        display: flex;
        flex: 1;
        align-items: center;
      }
    }

    .@{form-item-cls}-addon-before {
      display: inline-flex;
      flex-shrink: 0;
      align-items: center;
      min-height: @height-base;
      margin-right: 8px;
    }

    .@{form-item-cls}-addon-after {
      display: inline-flex;
      flex-shrink: 0;
      align-items: center;
      min-height: @height-base;
      margin-left: 8px;
    }
  }

  .@{form-item-cls}-help,
  .@{form-item-cls}-extra {
    min-height: 22px;
    color: @text-color-secondary;
    line-height: 22px;
  }
}

.@{form-item-cls}-size-small {
  font-size: @font-size-sm;
  line-height: @height-sm;

  .@{form-item-cls}-label {
    min-height: @height-sm - 2;
    line-height: @height-sm;
  }

  .@{form-item-cls}-control-content {
    .@{form-item-cls}-control-content-component {
      min-height: @height-sm - 2;
      line-height: @height-sm + 2;
    }
  }

  .@{form-item-cls}-help,
  .@{form-item-cls}-extra {
    min-height: @height-sm - 4;
    line-height: @height-sm - 4;
  }

  .@{form-item-cls}-control-content {
    min-height: @height-sm - 2;
  }

  .@{form-item-cls}-label > label {
    height: @height-sm - 2;
  }

  .@{mdt-prefix}-input-affix-wrapper,
  .@{mdt-prefix}-input-number,
  .@{mdt-prefix}-picker {
    padding: 0 11px;

    input {
      height: @height-sm - 2;
      font-size: @font-size-sm;
    }
  }

  .@{mdt-prefix}-cascader-picker {
    height: @height-sm - 2;

    input {
      height: @height-sm - 2;
      padding: 0 7px;
      font-size: @font-size-sm;
    }
  }

  .@{mdt-prefix}-select-single:not(.@{mdt-prefix}-select-customize-input) .@{mdt-prefix}-select-selector {
    height: @height-sm - 2;
    padding: 0 11px;
    font-size: @font-size-sm;
    line-height: @height-sm;

    .@{mdt-prefix}-select-selection-search {
      height: @height-sm;
      line-height: @height-sm - 2;

      &-input {
        height: @height-sm;
        line-height: @height-sm - 2;
      }
    }

    .@{mdt-prefix}-select-selection-placeholder {
      height: @height-sm;
      line-height: @height-sm - 2;
    }

    .@{mdt-prefix}-select-selection-item {
      height: @height-sm;
      line-height: @height-sm - 2;
    }
  }

  .@{mdt-prefix}-select-multiple:not(.@{mdt-prefix}-select-customize-input) .@{mdt-prefix}-select-selector {
    height: @height-sm - 2;
    padding: 0 2px;
    font-size: @font-size-sm;
    line-height: @height-sm;

    &::after {
      height: @height-sm - 8;
      line-height: @height-sm - 8;
    }

    .@{mdt-prefix}-select-selection-search {
      height: @height-sm - 8;
      line-height: @height-sm - 8;
      margin-inline-start: 0;

      &-input {
        height: @height-sm - 12;
        line-height: @height-sm - 12;
      }
    }

    .@{mdt-prefix}-select-selection-placeholder {
      left: 4px;
      height: @height-sm - 8;
      line-height: @height-sm - 8;
    }

    .@{mdt-prefix}-select-selection-overflow-item {
      align-self: flex-start;
    }

    .@{mdt-prefix}-select-selection-item {
      height: @height-sm - 8;
      line-height: @height-sm - 10;
    }
  }

  &.@{form-item-cls}-feedback-layout-terse {
    margin-bottom: 8px;

    &.@{form-item-cls}-feedback-has-text:not(.@{form-item-cls}-inset) {
      margin-bottom: 0;
    }
  }

  &.@{form-item-cls}-feedback-layout-loose {
    margin-bottom: @height-sm - 4;

    &.@{form-item-cls}-feedback-has-text:not(.@{form-item-cls}-inset) {
      margin-bottom: 0;
    }
  }
}

.@{form-item-cls}-size-large {
  font-size: @font-size-lg;
  line-height: @height-lg;

  .@{form-item-cls}-label {
    min-height: @height-lg - 2;
    line-height: @height-lg;
  }

  .@{form-item-cls}-control-content {
    .@{form-item-cls}-control-content-component {
      min-height: @height-lg - 2;
      line-height: @height-lg;
    }
  }

  .@{form-item-cls}-help,
  .@{form-item-cls}-extra {
    min-height: @form-item-margin-bottom;
    line-height: @form-item-margin-bottom;
  }

  .@{form-item-cls}-control-content {
    min-height: @height-lg - 2;
  }

  .@{mdt-prefix}-input {
    font-size: @font-size-lg;
  }

  .@{mdt-prefix}-input-number {
    font-size: @font-size-lg;

    input {
      height: @height-lg - 2;
    }
  }

  .@{mdt-prefix}-input-affix-wrapper,
  .@{mdt-prefix}-picker {
    padding: 0 11px;
    line-height: @height-lg - 2;

    input {
      height: @height-lg - 2;
      font-size: @font-size-lg;
    }
  }

  .@{mdt-prefix}-btn {
    height: @height-lg;
    padding: 0 8px;
  }

  .@{mdt-prefix}-radio-button-wrapper {
    height: @height-lg;
    line-height: @height-lg;
  }

  .@{mdt-prefix}-cascader-picker {
    height: @height-lg - 2;

    input {
      height: @height-lg - 2;
      padding: 0 11px;
      font-size: @font-size-lg;
    }
  }

  .@{mdt-prefix}-select-single:not(.@{mdt-prefix}-select-customize-input) .@{mdt-prefix}-select-selector {
    height: @height-lg;
    padding: 0 11px;
    font-size: @font-size-lg;
    line-height: @height-lg;

    .@{mdt-prefix}-select-selection-search {
      height: @height-lg;
      line-height: @height-lg - 2;

      &-input {
        height: @height-lg;
        line-height: @height-lg - 2;
      }
    }

    .@{mdt-prefix}-select-selection-placeholder {
      height: @height-lg;
      line-height: @height-lg - 2;
    }

    .@{mdt-prefix}-select-selection-item {
      height: @height-lg;
      line-height: @height-lg - 2;
    }
  }

  .@{mdt-prefix}-select-multiple:not(.@{mdt-prefix}-select-customize-input) .@{mdt-prefix}-select-selector {
    height: @height-lg - 2;
    padding: 0 2px;
    font-size: @font-size-lg;
    line-height: @height-lg;

    &::after {
      height: @height-lg - 8;
      line-height: @height-lg - 8;
    }

    .@{mdt-prefix}-select-selection-search {
      height: @height-lg - 8;
      line-height: @height-lg - 8;

      &-input {
        height: @height-lg - 12;
        line-height: @height-lg - 12;
      }
    }

    .@{mdt-prefix}-select-selection-placeholder {
      height: @height-lg - 8;
      line-height: @height-lg - 8;
    }

    .@{mdt-prefix}-select-selection-overflow-item {
      align-self: flex-start;
    }

    .@{mdt-prefix}-select-selection-item {
      height: @height-lg - 8;
      line-height: @height-lg - 10;
    }
  }

  &.@{form-item-cls}-feedback-layout-terse {
    margin-bottom: 8px;

    &.@{form-item-cls}-feedback-has-text:not(.@{form-item-cls}-inset) {
      margin-bottom: 0;
    }
  }

  &.@{form-item-cls}-feedback-layout-loose {
    margin-bottom: @form-item-margin-bottom;

    &.@{form-item-cls}-feedback-has-text:not(.@{form-item-cls}-inset) {
      margin-bottom: 0;
    }
  }
}

.@{form-item-cls} {
  &-layout-vertical {
    display: block;

    .@{form-item-cls}-label {
      min-height: @height-base - 10;
      line-height: 1.5715;
    }
  }
}

.@{form-item-cls}-feedback-layout-popover {
  margin-bottom: 8px;
}

.@{form-item-cls}-label-tooltip-icon {
  display: flex;
  align-items: center;
  max-height: @height-base;
  margin-left: 4px;
  color: #00000073;

  span {
    display: inline-flex;
  }
}

.@{form-item-cls}-control-align-left {
  .@{form-item-cls}-control-content {
    justify-content: flex-start;
  }
}

.@{form-item-cls}-control-align-right {
  .@{form-item-cls}-control-content {
    justify-content: flex-end;
  }
}

.@{form-item-cls}-control-wrap {
  .@{form-item-cls}-control {
    white-space: pre-line;
    word-break: break-all;
  }
}

.@{form-item-cls}-asterisk {
  display: inline-block;
  margin-right: 4px;
  color: @error-color;
  font-family: SimSun, sans-serif;
}

.@{form-item-cls}-colon {
  margin-right: 8px;
  margin-left: 2px;
}

.@{form-item-cls}-help,
.@{form-item-cls}-extra {
  clear: both;
  min-height: @form-item-margin-bottom - 2;
  padding-top: 0;
  color: rgb(0 0 0 / 45%);
  transition: color 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
}

.@{form-item-cls}-fullness {
  > .@{form-item-cls}-control {
    > .@{form-item-cls}-control-content {
      > .@{form-item-cls}-control-content-component {
        > *:first-child {
          width: 100%;
        }
      }
    }
  }
}

.@{form-item-cls}-control-content-component-has-feedback-icon {
  padding-right: 8px;
  border: 1px solid @border-color-base;
  border-radius: 2px;
  outline: none;
  transition: all 0.3s;
  touch-action: manipulation;

  .@{mdt-prefix}-input-number,
  .@{mdt-prefix}-picker,
  .@{mdt-prefix}-cascader-picker:focus .@{mdt-prefix}-cascader-input,
  .@{mdt-prefix}-select:not(.@{mdt-prefix}-select-customize-input) .@{mdt-prefix}-select-selector,
  .@{mdt-prefix}-input-affix-wrapper,
  .@{mdt-prefix}-input {
    border: none !important;
    box-shadow: none !important;
  }
}

.@{form-item-cls}-bordered-none {
  .@{mdt-prefix}-input-number,
  .@{mdt-prefix}-input-affix-wrapper,
  .@{mdt-prefix}-picker,
  .@{mdt-prefix}-cascader-picker:focus .@{mdt-prefix}-cascader-input,
  .@{mdt-prefix}-select:not(.@{mdt-prefix}-select-customize-input) .@{mdt-prefix}-select-selector,
  .@{mdt-prefix}-input {
    border: none !important;
    box-shadow: none !important;
  }

  .@{mdt-prefix}-input-number-handler-wrap {
    border: none !important;

    .@{mdt-prefix}-input-number-handler {
      border: none !important;
    }
  }
}

.@{form-item-cls}-inset {
  padding-left: 12px;
  border: 1px solid @border-color-base;
  border-radius: 2px;
  transition: 0.3s all;

  .@{mdt-prefix}-input-number,
  .@{mdt-prefix}-picker,
  .@{mdt-prefix}-cascader-picker:focus .@{mdt-prefix}-cascader-input,
  .@{mdt-prefix}-select:not(.@{mdt-prefix}-select-customize-input) .@{mdt-prefix}-select-selector,
  .@{mdt-prefix}-input-affix-wrapper,
  .@{mdt-prefix}-input {
    border: none !important;
    box-shadow: none !important;
  }

  .@{mdt-prefix}-input-number-handler-wrap {
    border: none !important;

    .@{mdt-prefix}-input-number-handler {
      border: none !important;
    }
  }

  &:hover {
    .hover();
  }
}

.@{form-item-cls}-inset-active {
  .active();
}

.@{form-item-cls}-active {
  .@{form-item-cls}-control-content-component-has-feedback-icon {
    .active();
  }

  .@{mdt-prefix}-input-number,
  .@{mdt-prefix}-picker,
  .@{mdt-prefix}-cascader-picker:focus .@{mdt-prefix}-cascader-input,
  .@{mdt-prefix}-select:not(.@{mdt-prefix}-select-customize-input) .@{mdt-prefix}-select-selector,
  .@{mdt-prefix}-input {
    .active();
  }
}

.@{form-item-cls} {
  &:hover {
    .@{form-item-cls}-control-content-component-has-feedback-icon {
      .hover();
    }
  }
}

.@{form-item-cls}-error {
  .@{mdt-prefix}-select-selector,
  .@{mdt-prefix}-cascader-picker,
  .@{mdt-prefix}-picker,
  .@{mdt-prefix}-input,
  .@{mdt-prefix}-input-number,
  .@{mdt-prefix}-input-affix-wrapper,
  .@{mdt-prefix}-input-affix-wrapper,
  .@{mdt-prefix}-input {
    border-color: @error-color !important;
  }

  .@{mdt-prefix}-select-selector,
  .@{mdt-prefix}-cascader-picker,
  .@{mdt-prefix}-picker,
  .@{mdt-prefix}-input,
  .@{mdt-prefix}-input-number,
  .@{mdt-prefix}-input-affix-wrapper,
  .@{mdt-prefix}-input-affix-wrapper:hover,
  .@{mdt-prefix}-input:hover {
    border-color: @error-color !important;
  }

  .@{mdt-prefix}-select:not(.@{mdt-prefix}-select-disabled):not(.@{mdt-prefix}-select-customize-input) {
    .@{mdt-prefix}-select-selector {
      background-color: @form-error-input-bg;
      border-color: @error-color !important;
    }

    &.@{mdt-prefix}-select-open .@{mdt-prefix}-select-selector,
    &.@{mdt-prefix}-select-focused .@{mdt-prefix}-select-selector {
      .active(@error-color);
    }
  }

  .@{mdt-prefix}-input-number,
  .@{mdt-prefix}-picker {
    background-color: @form-error-input-bg;
    border-color: @error-color;

    &-focused,
    &:focus {
      .active(@error-color);
    }

    &:not([disabled]):hover {
      background-color: @form-error-input-bg;
      border-color: @error-color;
    }
  }

  .@{mdt-prefix}-cascader-picker:focus .@{mdt-prefix}-cascader-input {
    background-color: @form-error-input-bg;
    .active(@error-color);
  }

  .@{mdt-prefix}-input-affix-wrapper-focused,
  .@{mdt-prefix}-input-affix-wrapper:focus,
  .@{mdt-prefix}-input-focused,
  .@{mdt-prefix}-input:focus {
    .active(@error-color);
  }
}

.@{form-item-cls}-error-help {
  color: @error-color !important;
}

.@{form-item-cls}-warning-help {
  color: @warning-color !important;
}

.@{form-item-cls}-success-help {
  color: @success-color !important;
}

.@{form-item-cls}-warning {
  .@{mdt-prefix}-select-selector,
  .@{mdt-prefix}-cascader-picker,
  .@{mdt-prefix}-picker,
  .@{mdt-prefix}-input,
  .@{mdt-prefix}-input-number,
  .@{mdt-prefix}-input-affix-wrapper,
  .@{mdt-prefix}-input-affix-wrapper,
  .@{mdt-prefix}-input {
    border-color: @warning-color !important;
  }

  .@{mdt-prefix}-select-selector,
  .@{mdt-prefix}-cascader-picker,
  .@{mdt-prefix}-picker,
  .@{mdt-prefix}-input,
  .@{mdt-prefix}-input-number,
  .@{mdt-prefix}-input-affix-wrapper,
  .@{mdt-prefix}-input-affix-wrapper:hover,
  .@{mdt-prefix}-input:hover {
    border-color: @warning-color !important;
  }

  .@{mdt-prefix}-select:not(.@{mdt-prefix}-select-disabled):not(.@{mdt-prefix}-select-customize-input) {
    .@{mdt-prefix}-select-selector {
      background-color: @form-warning-input-bg;
      border-color: @warning-color !important;
    }

    &.@{mdt-prefix}-select-open .@{mdt-prefix}-select-selector,
    &.@{mdt-prefix}-select-focused .@{mdt-prefix}-select-selector {
      .active(@warning-color);
    }
  }

  .@{mdt-prefix}-input-number,
  .@{mdt-prefix}-picker {
    background-color: @form-warning-input-bg;
    border-color: @warning-color;

    &-focused,
    &:focus {
      .active(@warning-color);
    }

    &:not([disabled]):hover {
      background-color: @form-warning-input-bg;
      border-color: @warning-color;
    }
  }

  .@{mdt-prefix}-cascader-picker:focus .@{mdt-prefix}-cascader-input {
    background-color: @form-warning-input-bg;
    .active(@warning-color);
  }

  .@{mdt-prefix}-input-affix-wrapper-focused,
  .@{mdt-prefix}-input-affix-wrapper:focus,
  .@{mdt-prefix}-input-focused,
  .@{mdt-prefix}-input:focus {
    .active(@warning-color);
  }
}

.@{form-item-cls}-success {
  .@{mdt-prefix}-select-selector,
  .@{mdt-prefix}-cascader-picker,
  .@{mdt-prefix}-picker,
  .@{mdt-prefix}-input,
  .@{mdt-prefix}-input-number,
  .@{mdt-prefix}-input-affix-wrapper,
  .@{mdt-prefix}-input-affix-wrapper,
  .@{mdt-prefix}-input {
    border-color: @success-color !important;
  }

  .@{mdt-prefix}-select-selector,
  .@{mdt-prefix}-cascader-picker,
  .@{mdt-prefix}-picker,
  .@{mdt-prefix}-input,
  .@{mdt-prefix}-input-number,
  .@{mdt-prefix}-input-affix-wrapper,
  .@{mdt-prefix}-input-affix-wrapper:hover,
  .@{mdt-prefix}-input:hover {
    border-color: @success-color !important;
  }

  .@{mdt-prefix}-input-affix-wrapper-focused,
  .@{mdt-prefix}-input-affix-wrapper:focus,
  .@{mdt-prefix}-input-focused,
  .@{mdt-prefix}-input:focus {
    border-color: @success-color !important;
    border-right-width: 1px !important;
    outline: 0;
  }
}
