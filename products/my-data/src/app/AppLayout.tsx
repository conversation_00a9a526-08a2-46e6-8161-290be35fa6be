import { FC } from 'react';
import loadable from '@loadable/component';
import { IRoute } from '@mdtProComm/controllers/BaseRouterController';
import { AppController } from './AppController';

const AppHeader = loadable(() => import('@mdtProMicroModules/pages/app-header'));
const AppSideMenu = loadable(() => import('@mdtProMicroModules/datlas/app-side-menu'));
const DatapkgWithGlobalSearch = loadable(() => import('../datapkg-with-global-search'));

interface IAppLayoutProps {
  headerLess?: boolean;
  sideMenuLess?: boolean;
  menuOnHeader?: boolean;
  item: IRoute;
  controller: AppController;
}
export const AppLayout: FC<IAppLayoutProps> = ({ sideMenuLess, headerLess, menuOnHeader, controller, item }) => {
  return (
    <div className="app-container">
      {headerLess ? null : <AppHeader controller={controller.getAppHeaderController()} />}
      <div className="app-container-body">
        {sideMenuLess ? null : (
          <AppSideMenu menuOnHeader={menuOnHeader} controller={controller.getAppSideMenuController()} />
        )}
        <div className="mydata-route-home product-md">
          <div className="mydata-route-home_content">
            {item.withGlobalSearch ? <DatapkgWithGlobalSearch content={<item.View />} /> : <item.View />}
          </div>
        </div>
      </div>
    </div>
  );
};
