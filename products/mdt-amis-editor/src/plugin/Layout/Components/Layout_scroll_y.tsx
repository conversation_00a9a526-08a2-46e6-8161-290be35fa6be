/* eslint-disable sonarjs/no-duplicate-string */
/* eslint-disable @typescript-eslint/explicit-member-accessibility */
import i18n from '../../../languages';
import { FlexPluginBase } from '../FlexPluginBase';

export default class Layout_scroll_y extends FlexPluginBase {
  name = i18n.chain.jsonComp.scrollYContainer;
  isBaseComponent = false;
  pluginIcon = 'layout-3row-plugin';
  description = i18n.chain.jsonComp.scrollYDescription;
  tags = [i18n.chain.jsonComp.commonLayout];
  order = 504;
  scaffold: any = {
    type: 'flex',
    className: 'p-1',
    items: [
      {
        type: 'container',
        size: 'xs',
        body: [],
        wrapperBody: false,
        style: {
          flex: '0 0 auto',
          flexBasis: '60px',
          display: 'block',
          minWidth: 'auto',
          minHeight: 'auto',
        },
      },
      {
        type: 'container',
        size: 'xs',
        body: [],
        wrapperBody: false,
        style: {
          flex: '0 0 auto',
          flexBasis: '60px',
          display: 'block',
          position: 'static',
          minWidth: 'auto',
          minHeight: 'auto',
        },
      },
      {
        type: 'container',
        size: 'xs',
        body: [],
        wrapperBody: false,
        style: {
          flex: '0 0 auto',
          flexBasis: '60px',
          display: 'block',
          position: 'static',
          minWidth: 'auto',
          minHeight: 'auto',
        },
      },
      {
        type: 'container',
        size: 'xs',
        body: [],
        wrapperBody: false,
        style: {
          flex: '0 0 auto',
          flexBasis: '60px',
          display: 'block',
          position: 'static',
          minWidth: 'auto',
          minHeight: 'auto',
        },
      },
      {
        type: 'container',
        size: 'xs',
        body: [],
        wrapperBody: false,
        style: {
          flex: '0 0 auto',
          flexBasis: '60px',
          display: 'block',
          position: 'static',
          minWidth: 'auto',
          minHeight: 'auto',
        },
      },
      {
        type: 'container',
        size: 'xs',
        body: [],
        wrapperBody: false,
        style: {
          flex: '0 0 auto',
          flexBasis: '60px',
          display: 'block',
          position: 'static',
          minWidth: 'auto',
          minHeight: 'auto',
        },
      },
    ],
    direction: 'column',
    justify: 'flex-start',
    alignItems: 'stretch',
    style: {
      position: 'static',
      minHeight: 'auto',
      maxWidth: 'auto',
      minWidth: 'auto',
      height: '200px',
      width: 'auto',
      overflowX: 'auto',
      overflowY: 'scroll',
      margin: '0',
    },
    isFixedHeight: true,
    isFixedWidth: false,
  };
}
