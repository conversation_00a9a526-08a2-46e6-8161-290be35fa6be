import { FC } from 'react';
import { CheckCircle } from '@metro/icons';
import { Divider } from '@metroDesign/divider';
import { Flex } from '@metroDesign/flex';
import { type SpaceProps, Space } from '@metroDesign/space';
import { Typography } from '@metroDesign/typography';
import i18n from '../../languages';
import Dispatch from './dispatch.svg';
import Fill from './fill.svg';
import './index.less';

export interface IProps {
  onClose?: () => void;
  fillFunc?: () => void;
  dispatchFunc?: () => void;
}

const OneTableNewPublishResult: FC<IProps> = ({ onClose, fillFunc, dispatchFunc }) => {
  const operatorProps: SpaceProps = {
    direction: 'vertical',
    align: 'center',
    size: 8,
    className: 'one-table-new-publish-result-operator',
  };
  const handleFill = () => {
    fillFunc?.();
    onClose?.();
  };

  const handleDispatch = () => {
    dispatchFunc?.();
    onClose?.();
  };

  return (
    <Flex vertical gap="middle" align="center" className="one-table-new-publish-result">
      <Flex className="one-table-new-publish-result-icon" justify="center">
        <CheckCircle />
      </Flex>
      <Typography.Title level={4}>{i18n.chain.proMicroModules.oneTable.publishSuccessTitle}</Typography.Title>
      <Typography.Text>{i18n.chain.proMicroModules.oneTable.publishSuccessTip}</Typography.Text>
      <Divider>{i18n.chain.proMicroModules.oneTable.publishSuccessDividerTip}</Divider>
      <Space>
        <Space {...operatorProps} onClick={handleFill}>
          <img src={Fill} />
          <Typography.Text strong>{i18n.chain.proMicroModules.oneTable.btnGet}</Typography.Text>
        </Space>
        <Space {...operatorProps} onClick={handleDispatch}>
          <img src={Dispatch} />
          <Typography.Text strong>{i18n.chain.proMicroModules.oneTable.btnIssued2}</Typography.Text>
        </Space>
      </Space>
    </Flex>
  );
};

export { OneTableNewPublishResult };
