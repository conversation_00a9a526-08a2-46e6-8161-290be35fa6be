import { IPaginationQuery } from './comm';

export interface ITemplateQuery extends IPaginationQuery {
  order?: string; // 排序列, const.enum.TemplateOrder
  start_time?: string; // optional, 按照order列筛选该时间(含)之后的模板
  end_time?: string; // optional, 按照order列筛选该时间(含)之前的模板
  ascending?: boolean; // order列是否升序, 默认false
  account_uuids?: string[]; // 查询某些用户修改/创建的模板
  keyword?: string; // 模糊搜索title与description
  search?: string; // 模糊搜索的字段名, 无则搜索全部 const.enum.TemplateSearch
}

export interface ITemplate {
  extra: object;
  uuid: string;
  title: string;
  description: string;
  features: string[];
  create_time: string;
  update_time: string;
  account_uuid: string; // 模板的创建/最后修改用户
  ready: boolean; // 模板是否开始接受数据输入
}

export interface IPaginationTemplate {
  count: number;
  pages: number;
  current_page: number;
  has_next: boolean; // 是否有下一页
  data: ITemplate[];
}

export interface ITemplatePut {
  ts: string;
}

export interface ITemplatePutData {
  extra: object;
  ready?: boolean;
  title?: string;
  description?: string;
  features?: string[];
}
