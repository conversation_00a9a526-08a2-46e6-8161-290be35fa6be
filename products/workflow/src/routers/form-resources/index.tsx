import { useController } from '@mdtBsComm/hooks/use-controller';
import {
  WorkflowFormResources,
  WorkflowFormResourcesController,
  WorkflowFormResourcesModel,
} from '@mdtProMicroModules/pages/workflow-form-resources';

const FormResourcesView = () => {
  const [controller] = useController(() => {
    const ctrl = new WorkflowFormResourcesController({
      Model: WorkflowFormResourcesModel,
    });
    return [ctrl, null];
  });

  return <WorkflowFormResources controller={controller} />;
};

export default FormResourcesView;
