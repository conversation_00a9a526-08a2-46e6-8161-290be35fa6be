import { FC } from 'react';
import { Result } from '@metroDesign/result';
import { Spin } from '@metroDesign/spin';
import { useObservableState } from '@mdtBsComm/hooks/use-observable-state';
import i18n from '../../../../languages';
import { ForwarderController } from './ForwarderController';

interface IProps {
  controller: ForwarderController;
}

export const Forwarder: FC<IProps> = ({ controller }) => {
  const loading = useObservableState(() => controller.getLoading$());
  return (
    // eslint-disable-next-line react/jsx-no-useless-fragment
    <>
      {loading ? (
        <Spin brand className="page_forwarder" fillParent />
      ) : (
        <Result
          status="noContent"
          title={i18n.chain.proMicroModules.forwarder.noContent}
          subTitle={i18n.chain.proMicroModules.forwarder.noContentDesc}
        />
      )}
    </>
  );
};
