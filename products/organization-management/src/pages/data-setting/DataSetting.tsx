import { FC } from 'react';
import { useObservableState } from '@mdtBsComm/hooks/use-observable-state';
import { ModalWithBtnsCompEmotion } from '@mdtBsComponents/modal-with-btns-comp-emotion';
import Spin from '@mdtDesign/spin';
import { DrawerModifyFormThemeShare } from '@mdtProMicroModules/containers/drawer-modify-form-themes-share';
import { TableCurdWithSimpleSearch } from '@mdtProMicroModules/containers/table-curd-with-simple-search';
import { DataSettingController } from './DataSettingController';

interface IProps {
  controller: DataSettingController;
}
const DataSetting: FC<IProps> = ({ controller }) => {
  const loading = useObservableState(controller.getLoading$());
  const [tableController, modifyController, deleteController] = [
    controller.getTableController(),
    controller.getModifyController(),
    controller.getDeleteController(),
  ];
  return (
    <div style={{ padding: '0 20px 20px 20px', height: '100%', width: '100%' }}>
      {loading ? (
        <Spin style={{ width: '100%', height: '100%' }} />
      ) : (
        <>
          <TableCurdWithSimpleSearch controller={tableController} />
          <DrawerModifyFormThemeShare controller={modifyController} />
          <ModalWithBtnsCompEmotion controller={deleteController} />
        </>
      )}
    </div>
  );
};

export { DataSetting };
