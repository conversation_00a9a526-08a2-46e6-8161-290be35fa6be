import { FC, memo } from 'react';
import { DataListCompCard } from '@mdtBsComponents/data-list-comp-card';
import { ModalWithBtnsComp } from '@mdtBsComponents/modal-with-btns-comp';
import { ModalWithBtnsCompEmotion } from '@mdtBsComponents/modal-with-btns-comp-emotion';
import { IDataset } from '@mdtBsServices/interfaces';
import Button, { LinkButton } from '@mdtDesign/button';
import { Dropmenu } from '@mdtDesign/dropdown';
import { isDatasetEnableDelete, isDatasetEnableRead, isDatasetEnableUpdate } from '@mdtProComm/utils/datasetUtil';
import i18n from '../../languages';
import { DrawerDatabaseDetail } from '../database-detail';
import dbICon from './resources/db-icon.svg';
import emptyImg from './resources/empty-img.svg';
import { useExternalLinkDbContext, useExternalLinkDbProvider } from './externalLinkDbContext';
import { ExternalLinkDbController, MenuKeyEnum } from './ExternalLinkDbController';
import './index.less';

// 子组件-页面的头部=================================================================================
const PageHead: FC = () => {
  const { externalLinkDbController: controller } = useExternalLinkDbContext();

  const createDbBtnClick = () => controller.openConnFormModal();

  return (
    <div className="page_external-db_header">
      <span className="title-text">{i18n.chain.mydata.link.customData}</span>
      <Button type="primary" leftIcon="add" onClick={createDbBtnClick}>
        {i18n.chain.mydata.link.createLink}
      </Button>
    </div>
  );
};

// 子组件-空页面====================================================================================
export const EmptyPage: FC = () => {
  const { externalLinkDbController: controller } = useExternalLinkDbContext();

  const createDbBtnClick = () => controller.openConnFormModal();

  return (
    <div className="page_external-db_empty">
      <div className="container">
        <img width={180} height={160} src={emptyImg} alt="" />
        <p className="tip-text-main">{i18n.chain.mydata.link.emptyContent}</p>
        <div className="tip-text-sec">
          {i18n.chain.mydata.link.clickRight}
          <LinkButton className="tip-link-btn" onClick={createDbBtnClick}>
            {i18n.chain.mydata.link.createLink}
          </LinkButton>
          {i18n.chain.mydata.link.clickRight2}
        </div>
      </div>
    </div>
  );
};

// 子组件-db-item=================================================================================
interface IDbItemProps {
  item: IDataset;
}
export const DbItem: FC<IDbItemProps> = memo(({ item }) => {
  const { externalLinkDbController: controller } = useExternalLinkDbContext();
  const deleteConnController = controller.getDeleteConnController();
  const { name, id: dbId } = item;

  const entryDb = () => controller.entryDb(item);

  const editDbBtnClick = () => controller.openConnFormModal(item);

  const deleteConn = () => deleteConnController.openModal(item);

  const OPERATION_MAP: Record<MenuKeyEnum, (dbKey?: string) => void> = {
    [MenuKeyEnum.VIEW]: entryDb,
    [MenuKeyEnum.EDIT]: editDbBtnClick,
    [MenuKeyEnum.DELETE]: deleteConn,
  };

  const onClickMenu = (info: any) => {
    OPERATION_MAP[info.key as MenuKeyEnum]?.();
  };

  const menus: any[] = [];
  if (isDatasetEnableRead(item)) {
    menus.push({ title: i18n.chain.mydata.link.enterdb, key: MenuKeyEnum.VIEW });
  }
  if (isDatasetEnableUpdate(item)) {
    menus.push({ title: i18n.chain.mydata.editConfig, key: MenuKeyEnum.EDIT });
  }
  if (isDatasetEnableDelete(item)) {
    menus.push({ title: i18n.chain.mydata.link.delConnect, key: MenuKeyEnum.DELETE });
  }

  return (
    <div className="db-item" key={dbId}>
      <div className="db-item-left">
        <img src={dbICon} alt="db icon" />
        <span className="db-item-title" onClick={entryDb}>
          {name}
        </span>
      </div>
      <Dropmenu icon="more" menus={menus} onClickMenu={onClickMenu} values={[]} />
    </div>
  );
});

// 子组件-外链数据库列表=============================================================================
const DbList = () => {
  const { externalLinkDbController: controller } = useExternalLinkDbContext();
  return (
    <div className="page_external-db_list">
      <DataListCompCard controller={controller.getDbListCardController()} />
    </div>
  );
};

interface IProps {
  controller: ExternalLinkDbController;
}
const ExternalLinkDb: FC<IProps> = ({ controller }) => {
  const Provider = useExternalLinkDbProvider();
  const modalModifyFormDatasetController = controller.getdialogModifyFormDatasetController();
  const value = { externalLinkDbController: controller };

  return (
    <>
      <Provider value={value}>
        <PageHead />
        <DbList />
      </Provider>
      <ModalWithBtnsCompEmotion controller={controller.getDeleteConnController()} />
      <ModalWithBtnsComp controller={modalModifyFormDatasetController} />
      <DrawerDatabaseDetail controller={controller.getDrawerController()} />
    </>
  );
};

export { ExternalLinkDb };
