import { COLLECTOR_URL } from '../../config';
import {
  IApiResponse,
  IPaginationTemplate,
  IRequestPromise,
  IRequestRequestConfig,
  ITemplatePut,
  ITemplatePutData,
  ITemplateQuery,
} from '../../interfaces';
import request from '../../request';

// 批量获取模板信息
export const queryTemplate = (config?: IRequestRequestConfig<ITemplateQuery>) => {
  return request({
    ...(config || {}),
    method: 'get',
    url: `${COLLECTOR_URL}/template`,
  }) as IRequestPromise<IApiResponse<IPaginationTemplate>>;
};

// 修改单个模板信息
export const putTemplate = (
  template_uuid: string,
  data: ITemplatePutData,
  config?: IRequestRequestConfig<ITemplatePut>,
) => {
  return request({
    ...(config || {}),
    method: 'put',
    url: `${COLLECTOR_URL}/template/${template_uuid}`,
    data,
  }) as IRequestPromise<IApiResponse<ITemplatePut>>;
};
