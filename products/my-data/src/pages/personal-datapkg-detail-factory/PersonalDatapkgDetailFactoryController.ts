import _ from 'lodash';
import { BehaviorSubject } from 'rxjs';
import { RequestController } from '@mdtBsControllers/request-controller';
import {
  DatapkgDetailFactoryController,
  DatapkgDetailFactoryModel,
} from '@mdtProMicroModules/pages/datapkg-detail-factory';
import { AppController } from '../../app/AppController';
import { UPLOAD_MAX_MB } from '../../config';

export interface IDetailOtherOptions {
  openEdit?: boolean;
}

export class PersonalDatapkgDetailFactoryController extends RequestController {
  // 数据包详情工厂
  private detailFactoryController?: DatapkgDetailFactoryController;

  private app?: AppController;
  private datapkgDetailCache: Record<string, DatapkgDetailFactoryController> = {};
  private pkgId$ = new BehaviorSubject('');
  private backFunc: () => void;

  public constructor(app: AppController, backFunc: () => void) {
    super();
    this.app = app;
    this.backFunc = backFunc;
  }

  // 打开弹窗时进行初始化
  public init(pkgId: string, options?: IDetailOtherOptions) {
    this.loadData(pkgId, options);
  }

  public destroy() {
    super.destroy();
    this.detailFactoryController?.destroy();
    this.detailFactoryController = undefined;
    this.pkgId$.complete();
    _.forEach(this.datapkgDetailCache, (item) => item.destroy());
    this.datapkgDetailCache = {};
    this.backFunc = null!;
    this.app = undefined;
  }

  public getDetailFactoryController() {
    return this.detailFactoryController!;
  }

  public getPkgId$() {
    return this.pkgId$;
  }

  private getDatapkgDetailController = (pkgId: string, options?: IDetailOtherOptions) => {
    const { openEdit } = options || {};
    let cache = this.datapkgDetailCache[pkgId];
    if (!cache) {
      cache = new DatapkgDetailFactoryController(this.app!, {
        pkgId,
        uploadMaxMb: UPLOAD_MAX_MB,
        Model: new DatapkgDetailFactoryModel(this.app!, pkgId),
        backFunc: this.backFunc,
        openEdit,
        dataAppUrl: '',
      });
    } else {
      openEdit && cache.handleEditData();
    }
    this.datapkgDetailCache[pkgId] = cache;
    return cache;
  };

  private loadData(pkgId: string, options?: IDetailOtherOptions) {
    this.pkgId$.next(pkgId);
    this.detailFactoryController = this.getDatapkgDetailController(pkgId, options);
  }
}
