import { FC } from 'react';
import { Spin } from '@metroDesign/spin';
import { useObservableState } from '@mdtBsComm/hooks/use-observable-state';
import { RadioGroup } from '@mdtDesign/radio';
import { DrawerChooseHomePage } from '@mdtProMicroModules/containers/drawer-choose-home-page';
import { OrgManageTreeList } from '@mdtProMicroModules/containers/org-manage-tree-list';
import { RoleManageTreeList } from '@mdtProMicroModules/containers/role-manage-tree-list';
import { TableCurdWithSimpleSearch } from '@mdtProMicroModules/containers/table-curd-with-simple-search';
import i18n from '../../languages';
import { type IFilterType, HomeSettingController } from './HomeSettingController';
import './index.less';

// 子组件-右侧表格====================================================================================
const RightContent: FC<IProps> = ({ controller }) => {
  const current = useObservableState(controller.getCurrentSelectId$());
  const tableController = controller.getTableController(current);
  const homePageController = controller.getHomePageController();
  return (
    <div className="page_home_setting-right">
      <TableCurdWithSimpleSearch controller={tableController} />
      <DrawerChooseHomePage controller={homePageController} />
    </div>
  );
};

const LeftContent: FC<IProps> = ({ controller }) => {
  const isLoading = useObservableState(() => controller.getOrgLoading$());
  const orgController = controller.getOrgController();
  const roleController = controller.getRoleController();
  const filterType = useObservableState(() => controller.getfilterType$());
  const isOrg = filterType === 'org';
  const isRole = filterType === 'role';
  return (
    <div className="page_home_setting-left">
      <Spin spinning={isLoading} fillParent>
        <RadioGroup
          className="table-menu"
          radioType="nav"
          defaultValue={filterType}
          options={[
            {
              label: i18n.chain.orgAdmin.auth.org,
              value: 'org',
            },
            {
              label: i18n.chain.orgAdmin.auth.role,
              value: 'role',
            },
          ]}
          onChange={(type: string) => controller.updatefilterType(type as IFilterType)}
        />

        {isOrg && <OrgManageTreeList controller={orgController} />}
        {isRole && <RoleManageTreeList controller={roleController} />}
      </Spin>
    </div>
  );
};

// 组织与部门页面=======================================================================================
interface IProps {
  controller: HomeSettingController;
}
const HomeSetting: FC<IProps> = ({ controller }) => {
  return (
    <div className="page_home_setting">
      <LeftContent controller={controller} />
      <RightContent controller={controller} />
    </div>
  );
};

export { HomeSetting };
