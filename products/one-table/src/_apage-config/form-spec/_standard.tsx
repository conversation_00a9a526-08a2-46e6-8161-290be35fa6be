import _ from 'lodash';
import i18n from '../../languages';

const reportOptions: Partial<Record<string, string[]>> = {
  form_label: [
    i18n.chain.proMicroModules.oneTable.municipalProject,
    i18n.chain.proMicroModules.oneTable.districtProject,
    i18n.chain.proMicroModules.oneTable.streetProject,
    i18n.chain.proMicroModules.oneTable.dataCollection,
    i18n.chain.proMicroModules.oneTable.dataVerify,
  ],
  form_region_single: [
    i18n.chain.proMicroModules.oneTable.region.person,
    i18n.chain.proMicroModules.oneTable.region.house,
    i18n.chain.proMicroModules.oneTable.region.company,
    i18n.chain.proMicroModules.oneTable.region.matter,
    i18n.chain.proMicroModules.oneTable.region.thing,
  ],
  form_level: [
    i18n.chain.proMicroModules.oneTable.level.country,
    i18n.chain.proMicroModules.oneTable.level.province,
    i18n.chain.proMicroModules.oneTable.level.city,
    i18n.chain.proMicroModules.oneTable.level.district,
    i18n.chain.proMicroModules.oneTable.level.street,
  ],
  fill_frequency: [
    i18n.chain.proMicroModules.oneTable.frequency.year,
    i18n.chain.proMicroModules.oneTable.frequency.halfYear,
    i18n.chain.proMicroModules.oneTable.frequency.quarter,
    i18n.chain.proMicroModules.oneTable.frequency.month,
    i18n.chain.proMicroModules.oneTable.frequency.week,
    i18n.chain.proMicroModules.oneTable.frequency.day,
    i18n.chain.proMicroModules.oneTable.frequency.demand,
    i18n.chain.proMicroModules.oneTable.frequency.temporary,
  ],
};

const transformToOptions = (arr?: string[]) => _.map(arr, (it) => ({ label: it, value: it }));
const getOptionsByType = (type: string) => transformToOptions(reportOptions[type]);
const getRegionOptions = () => getOptionsByType('form_region_single');
const getLevelOptions = () => getOptionsByType('form_level');
const getFrequencyOptions = () => getOptionsByType('fill_frequency');

export const form = {
  colon: false,
  layout: 'vertical',
  labelWidth: '300px',
};

export const name = {
  name: {
    type: 'string',
    title: i18n.chain.proMicroModules.oneTable.infoName,
    required: true,
    'x-decorator': 'FormItem',
    'x-component': 'Input',
    'x-component-props': {
      placeholder: i18n.chain.comPlaceholder.input,
    },
  },
};

export const extraFormField = {
  extra_form_field: {
    type: 'array',
    title: i18n.chain.proMicroModules.oneTable.regionTitle,
    'x-decorator': 'FormItem',
    'x-component': 'Select',
    'x-component-props': {
      placeholder: i18n.chain.comPlaceholder.select,
      options: getRegionOptions(),
    },
  },
};

export const extraFormLevel = {
  extra_form_level: {
    type: 'array',
    title: i18n.chain.proMicroModules.oneTable.levelTitle,
    'x-decorator': 'FormItem',
    'x-component': 'Select',
    'x-component-props': {
      placeholder: i18n.chain.comPlaceholder.select,
      options: getLevelOptions(),
    },
  },
};

export const extraFormFrequency = {
  extra_form_frequency: {
    type: 'array',
    title: i18n.chain.proMicroModules.oneTable.frequencyTitle,
    'x-decorator': 'FormItem',
    'x-component': 'Select',
    'x-component-props': {
      placeholder: i18n.chain.comPlaceholder.select,
      options: getFrequencyOptions(),
    },
  },
};

export const extraFormOwner = {
  extra_form_owner: {
    type: 'string',
    title: i18n.chain.proMicroModules.oneTable.ownerTitle,
    'x-decorator': 'FormItem',
    'x-component': 'Input',
    'x-component-props': {
      placeholder: i18n.chain.comPlaceholder.input,
    },
  },
};

export const extraFormOwnerPhone = {
  extra_form_owner_phone: {
    type: 'string',
    title: i18n.chain.proMicroModules.oneTable.ownerPhoneTitle,
    'x-decorator': 'FormItem',
    'x-component': 'Input',
    'x-component-props': {
      placeholder: i18n.chain.comPlaceholder.input,
    },
  },
};

export const description = {
  description: {
    type: 'string',
    title: i18n.chain.proMicroModules.oneTable.infoDesc,
    'x-decorator': 'FormItem',
    'x-component': 'Input.TextArea',
    'x-component-props': {
      placeholder: i18n.chain.comPlaceholder.input,
    },
  },
};

export const extraAffiliatedOrgs = {
  affiliated_orgs: {
    title: i18n.chain.proMicroModules.oneTable.ownership,
    type: 'array',
    'x-component': 'TreeSelect',
    'x-component-props': {
      allowClear: true,
      multiple: true,
      placeholder: i18n.chain.comPlaceholder.select,
      showCheckedStrategy: 'SHOW_ALL',
      showSearch: true,
      treeCheckStrictly: true,
      treeCheckable: true,
      treeDefaultExpandAll: true,
      treeNodeFilterProp: 'label',
    },
    'x-decorator': 'FormItem',
    'x-decorator-props': {
      colon: false,
      layout: 'vertical',
    },
  },
};

export const extraAffiliatedOrgsSettingValues = {
  affiliated_orgs: {
    title: i18n.chain.proMicroModules.oneTable.ownership,
    'x-component-props': {
      showCheckedStrategy: 'SHOW_ALL',
      treeDefaultExpandAll: true,
    },
    'x-decorator-props': {
      colon: false,
      layout: 'vertical',
    },
    'x-metro-datasource': {
      config: {
        innerApi: {
          api: 'organization',
          authTypeOfApi: 'wf_spec_creator',
        },
      },
      dataRequire: {
        items: {
          properties: {
            label: {
              description: '显示值',
              type: 'string',
            },
            value: {
              description: '存储值',
              type: ['string', 'number'],
            },
          },
          required: ['label', 'value'],
          type: 'object',
        },
        type: 'array',
      },
      dataSourceType: 'innerApi',
      filters: [],
      useFilter: false,
    },
    'x-metro-option-type': 'tree_select',
  },
};

export const schemaProperties = {
  ...name,
  ...extraAffiliatedOrgs,
  ...extraFormField,
  ...extraFormLevel,
  ...extraFormFrequency,
  ...extraFormOwner,
  ...extraFormOwnerPhone,
  ...description,
};

export const allSettingValues = {
  ...extraAffiliatedOrgsSettingValues,
};

export const formSpec = {
  formilySchema: {
    form,
    schema: {
      properties: schemaProperties,
    },
  },
  allSettingValues,
};
