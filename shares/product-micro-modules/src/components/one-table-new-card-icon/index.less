.one-table-new-item-icon {
  position: relative;
  display: inline-flex;
  padding: 2px;
  font-size: 32px;

  &-square {
    border-radius: 6px;
  }

  &-circle {
    border-radius: 100%;
  }

  &-text {
    position: absolute;
    bottom: 2px;
    left: 2px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 13px;
    padding: 2px;
    overflow: hidden;
    color: var(--metro-text-static-0);
    font-size: clamp(8px, 2vw, 10px);
    white-space: nowrap;
    text-overflow: ellipsis;
    border-radius: 1px 1px 5px 5px;
  }

  &-processing {
    color: var(--metro-text-static-0);
    background: var(--metro-primary-default);

    .one-table-new-item-icon-text {
      background: linear-gradient(0deg, rgb(255 255 255 / 20%) 0%, rgb(255 255 255 / 20%) 100%),
        var(--metro-primary-default, #5578f4);
    }
  }

  &-success {
    color: var(--metro-text-static-0);
    background: var(--metro-success-default);

    .one-table-new-item-icon-text {
      background: linear-gradient(0deg, rgb(255 255 255 / 20%) 0%, rgb(255 255 255 / 20%) 100%), #219464;
    }
  }

  &-warning {
    color: var(--metro-text-static-0);
    background: var(--metro-warning-default);

    .one-table-new-item-icon-text {
      background: linear-gradient(0deg, rgb(255 255 255 / 20%) 0%, rgb(255 255 255 / 20%) 100%),
        var(--metro-warning-default, #e37f0a);
    }
  }

  &-complete {
    color: var(--metro-text-2);
    background: var(--metro-fill-1);
  }
}
