import _ from 'lodash';
import { Cell, Edge, Graph, Node } from '@antv/x6';
import type { IApplication, NsNodeCmd } from '@antv/xflow';
import { NsGraph, uuidv4, XFlowNodeCommands } from '@antv/xflow';
import { BehaviorSubject } from 'rxjs';
import {
  IFlowLink,
  IFlowNode,
  IFlowPost,
  IFlowRunResult,
  IFlowRunResultData,
  IJunction,
  IJunctionName,
} from '@mdtBsServices/interfaces';
import Toast from '@mdtDesign/toast';
import { TaskStatusEnum } from '@mdtProComm/constants';
import { addCreateRunTemporaryFlowTask } from '@mdtProTasks/util';
import i18n from '../../../languages';
import FlowEditController from '../FlowEditController';
import { COMMON_PORT_CONFIG, PORT_CONNECTED } from './constants';
import { GraphPortGroupEnum, JunctionNameEnum, NodeStatusEnum, NodeTypeEnum, RunNodeActionEnum } from './enum';
import {
  IEdgeRelation,
  IFlowNodeParams,
  IFlowParams,
  IFlowParamsDataSourceMap,
  IFlowParamsEdgeRelation,
  IFlowParamsNodeColumnsResult,
  IFlowParamsNodeParams,
  IGraphNodePort,
  INodeData,
  IRunNodeData,
} from './interfaces';

type IFlowParamsKey = 'nodeParams' | 'edgeRelation';

const INITIAL_FLOW_PARAMS_MAP = {
  nodeParams: {},
  edgeRelation: {},
};

class GraphController {
  private readonly isRunNode$ = new BehaviorSubject<boolean | IRunNodeData>(false);
  private readonly nodeNeedFrush$ = new BehaviorSubject<boolean>(false);
  private graph: Graph;
  private app: IApplication;
  private parent: FlowEditController;
  private dataSourceMap: IFlowParamsDataSourceMap = {};

  // 整个flow保存的信息
  private flowParamsMap: IFlowParams = _.cloneDeep(INITIAL_FLOW_PARAMS_MAP);

  public constructor(app: IApplication, graph: Graph, parent: FlowEditController) {
    this.app = app;
    this.graph = graph;
    this.parent = parent;

    this.parent.getFlowMeta().subscribe((flowMeta) => {
      if (!flowMeta?._fparams_) return;
      const flowParams = this.flowParamsMap;
      this.flowParamsMap = { ...flowParams, ...(flowMeta._fparams_ as any) };
    });
    this.isRunNode$.subscribe(async (isRunNode) => {
      if (!isRunNode) return;
      if (isRunNode && _.isObject(isRunNode)) {
        const { action, id, data } = isRunNode;
        if (action === RunNodeActionEnum.UPDATA_LABEL) {
          const node = this.getNodeById(id);
          node.setData({ ...node.getData(), ...data });
        }
        return;
      }
      // isRunNode 为 true 时触发节点运行
      const flowEditConfigController = this.parent.getFlowEditConfigController();
      flowEditConfigController.manualTriggerrParamsSave();
      await this.runCurrentNode();
    });
  }

  public destroy() {
    this.graph.dispose();
    this.isRunNode$.complete();
    this.nodeNeedFrush$.complete();
  }

  public getColumnNameTypeMap(nodeId: string, isSelf?: boolean) {
    const columnsData = isSelf ? this.getDataSource(nodeId) : this.getNodeColumns(nodeId);
    const columnNameTypeMap: Record<string, string> = {};
    _.forEach(_.values(columnsData), ({ name, type }) => {
      columnNameTypeMap[name] = type ?? '';
    });
    return columnNameTypeMap;
  }

  public setNodeDataSourceMap(nodeId: string, dataSourceMap: IFlowParamsNodeColumnsResult) {
    // 设置列数据的dataSource
    this.setDataSourceMap(nodeId, dataSourceMap);
    // 刷新一下数据来源
    const lastState = this.nodeNeedFrush$.getValue();
    this.nodeNeedFrush$.next(!lastState);
  }

  public setDataSourceMap(id: string, data: IFlowParamsNodeColumnsResult) {
    this.dataSourceMap[id] = data;
  }

  public getDataSource(id: string) {
    return this.dataSourceMap[id] || {};
  }

  public getNodeNeedFrush() {
    return this.nodeNeedFrush$;
  }

  public getIsRunNode$() {
    return this.isRunNode$;
  }

  // 获取全部的边
  public getAllEdges() {
    const graph = this.graph!;
    return graph.getEdges();
  }

  // 获取所有的节点
  public getAllNodes() {
    const graph = this.graph!;
    return graph.getNodes();
  }

  public getNodeIncomingEdges(node: Node) {
    const graph = this.graph!;
    return (graph.getIncomingEdges(node) || []) as Edge[];
  }

  public getEdgeById(edgeId: string) {
    const graph = this.graph!;
    return graph.getCellById(edgeId) as Edge;
  }

  public getNodeById(nodeId: string) {
    const graph = this.graph!;
    return graph.getCellById(nodeId) as Node;
  }

  public getNodeDataById(nodeId: string) {
    const node = this.getNodeById(nodeId);
    return node?.getData<INodeData>() || {};
  }

  public getInPorts(nodeId: string) {
    const node = this.getNodeById(nodeId);
    return node.getPortsByGroup(GraphPortGroupEnum.IN);
  }

  public getOutPorts(nodeId: string) {
    const node = this.getNodeById(nodeId);
    return node.getPortsByGroup(GraphPortGroupEnum.OUT);
  }

  // 根据当前节点的连线inportId获取与此相连的上一个节点NodeID
  public getUpNodeId(id: string, position: JunctionNameEnum = JunctionNameEnum.DEFAULT_JUNCTION_NAME) {
    const edgeRelation = this.getPreEdgeRelation(id, position);
    return edgeRelation?.upNodeId || '';
  }

  // 改变节点的参数状态
  public setNodeParamsStatus(id: string | Node, status: NodeStatusEnum, statusMsg?: string) {
    const node = typeof id === 'string' ? this.getNodeById(id) : id;
    if (!node) return;
    node.setData({ ...(node.getData() || {}), status, statusMsg });
  }

  // 获取当前节点的下游节点
  public getSuccessors(node: Cell<Cell.Properties>) {
    return this.graph.getSuccessors(node);
  }

  // 根据节点ID获取 inport 获取 edgeRelation
  public getPreEdgeRelation(id: string, positionName: JunctionNameEnum = JunctionNameEnum.DEFAULT_JUNCTION_NAME) {
    const inPorts = this.getInPorts(id) as IGraphNodePort[];
    const port = _.find(inPorts, ({ portPosition }) => portPosition === positionName);
    if (!port) return;
    const inPortId = port.id;
    return _.find(
      this.getFlowParams('edgeRelation') as Record<string, IEdgeRelation>,
      ({ downPortId }) => downPortId === inPortId,
    );
  }

  // --------------------------------------------------------------------------------
  // -----------------------------flow的参数 start--------------------------------------
  public saveFlowParams(key: IFlowParamsKey, id: string, params = {}) {
    this.flowParamsMap[key][id] = params;
  }

  public getFlowParams(key?: IFlowParamsKey, id?: string) {
    if (id) return this.flowParamsMap[key!][id] || {};
    if (key) return this.flowParamsMap[key] || {};
    return this.flowParamsMap;
  }

  public removeFlowParams(key: IFlowParamsKey, id?: string) {
    if (id) {
      delete this.flowParamsMap[key][id];
    } else {
      delete this.flowParamsMap[key];
    }
  }
  // -----------------------------flow的参数 end----------------------------------------
  // --------------------------------------------------------------------------------

  // 获取节点的 columns 有缓存从缓存找，没有就动态计算
  // allData针对于多输入节点，默认返回default数据
  public getNodeColumns(nodeId: string, junctionName: JunctionNameEnum = JunctionNameEnum.DEFAULT_JUNCTION_NAME) {
    const preNodeId = this.getUpNodeId(nodeId, junctionName);
    if (preNodeId === '') return {};
    return this.getDataSource(preNodeId);
  }

  // 处理多分支连线时的junction处理
  public dealEdgeJunction(downNodeType: string, downNodeId: string, portId?: string | null): IJunction | undefined {
    const inPorts = this.getInPorts(downNodeId);

    const inport = _.find(inPorts, ({ id }) => id === portId);
    if (!inport) return;

    const junctionName = inport.portPosition as IJunctionName;

    if (downNodeType === NodeTypeEnum.APPEND || downNodeType === NodeTypeEnum.PIPE) {
      return {
        name: junctionName,
        type: junctionName === JunctionNameEnum.DEFAULT_JUNCTION_NAME ? 'node' : 'list',
        index: inport.index,
      };
    } else if (
      downNodeType === NodeTypeEnum.JOIN ||
      downNodeType === NodeTypeEnum.SJOIN ||
      downNodeType === NodeTypeEnum.UPSERT_DATAFRAME
    ) {
      return {
        name: junctionName,
        type: 'node',
      };
    } else {
      return;
    }
  }

  // 目前在事件处处理了，先保留
  public saveEdgeRelation(edgeId: string, portId?: string | null) {
    const graph = this.graph!;
    const edge = graph.getCellById(edgeId) as Edge;
    const up = edge.getSource() as Edge.TerminalCellData;
    const down = edge.getTarget() as Edge.TerminalCellData;
    const { type: upNodeType = '' } = this.getNodeDataById(up.cell);
    const { type: downNodeType = '' } = this.getNodeDataById(down.cell);

    const junction = this.dealEdgeJunction(downNodeType, down.cell, portId);
    const edgeRelation: IEdgeRelation = {
      id: edgeId,
      upNodeId: up.cell,
      upPortId: up.port!,
      upNodeType,
      downNodeId: down.cell,
      downPortId: down.port!,
      downNodeType,
      junction,
    };
    this.saveFlowParams('edgeRelation', edgeId, edgeRelation);
  }

  // 动态更新append节点的输入桩
  public updateAppendNodeInports(downNodeId: string) {
    const node = this.getNodeById(downNodeId);
    const inPorts = this.getInPorts(downNodeId);
    const num = inPorts.length;
    const isAdd = _.every(inPorts, ({ connected }) => !!connected);
    if (!isAdd) return;

    this.app.executeCommand<NsNodeCmd.UpdateNodePort.IArgs>(XFlowNodeCommands.UPDATE_NODE_PORT.id, {
      node,
      updatePorts: async (ports) => {
        return [
          ...ports,
          {
            id: uuidv4(),
            type: NsGraph.AnchorType.INPUT,
            group: GraphPortGroupEnum.IN,
            portPosition: JunctionNameEnum.APPEND_JUNCTION_NAME,
            index: num,
            tooltip: i18n.chain.dataFactory.flow.inputPile,
            [PORT_CONNECTED]: false,
            ...COMMON_PORT_CONFIG,
          },
        ];
      },
    });
  }

  public getGraphData() {
    const nodes = this.getNodes(true);
    const links = this.getLinks();
    return { nodes, links };
  }

  // 检查整个flow node的参数
  // eslint-disable-next-line sonarjs/cognitive-complexity
  public checkFlowNodeParams(id: string, nodeValue: any) {
    const { label, type, feGroup } = this.getNodeDataById(id);
    const schema: any = this.parent.getNodefromNodeList(type, feGroup)?.params || {};
    let requirs = (schema.required || []) as string[];
    // sjon 做特殊处理
    if (type === NodeTypeEnum.SJOIN) {
      requirs = ['left_geometry', 'right_geometry'];
    } else if (type === NodeTypeEnum.FILTER) {
      requirs = ['operator_filter', 'operator_filter.column'];
      // run flow 后端定义为 extractor, 前端ui要求放入execute，类型不一致
    } else if (type === NodeTypeEnum.RUN_FLOW) {
      requirs = ['flow_id'];
      // 读取远程文件
    } else if (type === NodeTypeEnum.REMOTE_FILE) {
      const nv = _.get(nodeValue, 'scheme');
      const o = _.find(schema.oneOf, (it) => _.get(it, 'properties.scheme.default') === nv);
      requirs = ['scheme', ...(o ? o.required : [])];
    } else if (type === NodeTypeEnum.FS_SHEET) {
      requirs = ['spreadsheet_token', 'config.app_id', 'config.app_secret'];
    } else if (type === NodeTypeEnum.FS_TABLE) {
      requirs = ['app_token', 'config.app_id', 'config.app_secret'];
    } else if (type === NodeTypeEnum.DK_SHEET) {
      requirs = ['union_id', 'workbook_id'];
    } else if (type === NodeTypeEnum.DK_PROCESS_INSTANCES) {
      requirs = ['process_code'];
    } else if (type === NodeTypeEnum.WECHAT_WORK_APPROVAL_DATA) {
      requirs = ['application_id', 'auth_corpid', 'permanent_code', 'start_time'];
    }

    _.forEach(requirs, (field) => {
      // 对于replace节点的repl参数做特殊处理 ，可为''，跳出本次
      if (type === NodeTypeEnum.REPLACE && field === 'repl') return true;

      const fieldValue = _.get(nodeValue, field);
      if (fieldValue === undefined || fieldValue === '') {
        // 遇到必填参数未填，直接跳出forEach循环
        throw new Error(i18n.chain.dataFactory.flow.domRequiredParamsEmpty(label, field));
      }
    });
    return true;
  }

  public saveSuccessAfter(nodeId: string, formValues: IFlowNodeParams) {
    this.saveFlowParams('nodeParams', nodeId, formValues);
    let successFalg = true;
    try {
      // const checkResult = this.checkFlowNodeParams(nodeId, formValues);
      // checkResult && this.setNodeParamsStatus(nodeId, NodeStatusEnum.INFO);
      // 保留状态
      this.checkFlowNodeParams(nodeId, formValues);
    } catch (error: any) {
      successFalg = false;
      this.setNodeParamsStatus(nodeId, NodeStatusEnum.WARNING);
    }
    return successFalg;
  }

  public async getFieldInfo() {
    const flowEditConfigController = this.parent.getFlowEditConfigController();
    const selectNode = flowEditConfigController.getSelectNodeValue();
    if (!selectNode) return;
    const nodeId = selectNode.id;
    const { type } = this.getNodeDataById(nodeId);

    // TODO 将多分支节点归类
    if (type === NodeTypeEnum.JOIN || type === NodeTypeEnum.SJOIN) {
      return this.getFieldInfoForMultiBranch(selectNode);
    }

    const flowData = this.excuteFragmentFlowBefore(selectNode, false);
    if (!flowData) return;
    const upNodeId = this.getUpNodeId(nodeId);

    await this.parent.runTemporaryFlow(nodeId, flowData, false, (result, data) => {
      result && data?.data && this.saveFieldInfo(upNodeId, data.data);
    });
  }

  // 运行当前节点
  private async runCurrentNode() {
    const flowEditConfigController = this.parent.getFlowEditConfigController();
    const selectNode = flowEditConfigController.getSelectNodeValue();
    if (!selectNode) return;
    const flowData = this.excuteFragmentFlowBefore(selectNode, true);
    if (!flowData) return;
    const nodeId = selectNode.id;

    // this.setNodeParamsStatus(nodeId, NodeStatusEnum.PROCESSING);
    await this.parent.runTemporaryFlow(nodeId, flowData, true, (result, msg?: string) => {
      this.setNodeParamsStatus(nodeId, result ? NodeStatusEnum.SUCCESS : NodeStatusEnum.ERROR, msg);
    });
    // 执行完毕，将节点执行设置为false
    this.isRunNode$.next(false);
  }

  private excuteFragmentFlowBefore(selectNode: Node<Node.Properties>, includeCurrentNode: boolean) {
    let flowData: IFlowPost | undefined;
    try {
      flowData = this.getTemporaryFlowData(selectNode.id, includeCurrentNode);
    } catch (error: any) {
      console.error(error?.message);
      Toast.error(error?.message);
    }

    if (!flowData || !_.size(flowData.nodes)) return;

    return flowData;
  }

  // 多分支临时FLOW执行前的验证 {multiBranch: JunctionNameEnum[]}
  private excuteFragmentFlowForMultiBranchBefore(selectNode: Node<Node.Properties>, multiBranch: JunctionNameEnum[]) {
    const id = selectNode.id;

    let flowDatas: (IFlowPost | undefined)[] = [];

    _.forEach(multiBranch, (junctionName, index) => {
      const cellId = this.getPreEdgeRelation(id, junctionName)?.id;
      try {
        flowDatas[index] = cellId ? this.getTemporaryFlowData(cellId) : undefined;
      } catch (error: any) {
        console.error(error?.message);
        Toast.error(error?.message);
      }
    });

    if (!flowDatas[0] && !flowDatas[1]) {
      Toast.warning(i18n.chain.dataFactory.flow.flowInvalidDAG);
      return;
    }

    return flowDatas;
  }

  private getFieldInfoForMultiBranch(selectNode: Node<Node.Properties>) {
    const nodeId = selectNode.id;
    const junctionNames = [JunctionNameEnum.DEFAULT_JUNCTION_NAME, JunctionNameEnum.JOIN_JUNCTION_NAME];
    const flowDatas = this.excuteFragmentFlowForMultiBranchBefore(selectNode, junctionNames);
    if (!flowDatas) return;

    const flowNames = [i18n.chain.dataFactory.flow.temporaryFlowLeft, i18n.chain.dataFactory.flow.temporaryFlowRight];
    const task = addCreateRunTemporaryFlowTask(flowNames, flowDatas);

    task.taskResp$.subscribe((task) => {
      const status = task.status;
      if (status === TaskStatusEnum.SUCCESSFUL) {
        const result = task.result;
        _.forEach(flowNames, (v, index) => {
          if (!result[v]) return;
          const upNodeId = this.getUpNodeId(nodeId, junctionNames[index]);
          const data = (result[v].result as IFlowRunResult)?.data;
          data && this.saveFieldInfo(upNodeId, data);
        });
      }
    });
  }

  private getTemporaryFlowData(id: string, includeCurrentNode = false): IFlowPost {
    const graph = this.graph!;
    const cell = graph.getCellById(id);
    const preNodes = graph.getPredecessors(cell);
    let nodeIds = _.map(preNodes, ({ id }) => id);
    includeCurrentNode && nodeIds.push(id);
    const edgeIds = _.map(nodeIds, (id) => {
      const cell = graph.getCellById(id);
      const edges = graph.getIncomingEdges(cell);
      if (!edges) return [];
      return _.map(edges, ({ id }) => id);
    });

    const nodes = this.getNodes(true, nodeIds);
    const links = this.getLinks(_.flatMap(edgeIds));
    return {
      title: i18n.chain.dataFactory.flow.temporaryFlow,
      flow_type: 'dataflow',
      nodes,
      links,
    };
  }

  // 存储更新flow所需要的node数据
  private getNodes(check = false, nodeIds?: string[]): IFlowNode[] | undefined {
    const graph = this.graph!;
    const graphNodes = nodeIds || _.map(graph.getNodes(), ({ id }) => id);
    if (!_.size(graphNodes)) {
      Toast.warning(i18n.chain.dataFactory.flow.flowRequiredDom);
      return [];
    }
    const nodeParams = this.getFlowParams('nodeParams') as IFlowParamsNodeParams;
    let nodes: IFlowNode[] = [];
    let noComplete = false;

    _.forEach(nodeParams, (nodeValue, nodeKey) => {
      // 如果有一个错，直接下一个
      if (noComplete) return;
      const isHave = _.some(graphNodes, (id) => id === nodeKey);
      if (isHave) {
        const cell = graph.getCellById(nodeKey);
        const { type, label = '' } = cell.getData<INodeData>();
        try {
          check && this.checkFlowNodeParams(nodeKey, nodeValue);
          nodes.push({
            key: nodeKey,
            node_type: type,
            params: nodeValue ?? {},
            title: label,
          });
        } catch (error: any) {
          console.warn(error?.message);
          Toast.warning(i18n.chain.dataFactory.flow.domParamsCompletion);
          this.setNodeParamsStatus(nodeKey, NodeStatusEnum.WARNING);
          noComplete = true;
        }
      }
    });

    return noComplete ? [] : nodes;
  }

  // 存储更新flow所需要的edge数据
  private getLinks(edgeIds?: string[]): IFlowLink[] {
    const graph = this.graph!;
    const graphEdges = edgeIds || _.map(graph.getEdges(), ({ id }) => id);
    const edgeRelationMap = this.getFlowParams('edgeRelation') as IFlowParamsEdgeRelation;
    let links: IFlowLink[] = [];

    _.forEach(graphEdges, (edgeKey) => {
      const edgeRelation = edgeRelationMap[edgeKey];
      const upstream = edgeRelation.upNodeId;
      const downstream = edgeRelation.downNodeId;
      const junction = edgeRelation.junction;

      links.push({
        key: edgeKey,
        upstream,
        downstream,
        junction,
      });
    });
    return links;
  }

  private saveFieldInfo(id: string, result: IFlowRunResultData) {
    if (id === '') return;
    const columns = result.columns;
    const column_types = result.column_types;

    const dataSourceMap: IFlowParamsNodeColumnsResult = {};
    _.forEach(columns, (name, index) => {
      const columnId = `${id}@@${name}`;
      dataSourceMap[columnId] = {
        id: columnId,
        name,
        type: column_types[index],
      };
    });
    // 设置列数据的dataSource
    this.setDataSourceMap(id, dataSourceMap);

    // 刷新一下数据来源
    const lastState = this.nodeNeedFrush$.getValue();
    this.nodeNeedFrush$.next(!lastState);
  }
}

export default GraphController;
