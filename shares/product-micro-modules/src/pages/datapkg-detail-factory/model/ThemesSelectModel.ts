import _ from 'lodash';
import { from, Observable, of } from 'rxjs';
import { map } from 'rxjs/operators';
import { joinKey, splitKey } from '@mdtBsComm/utils/stringUtil';
import { queryAlbumsAsync } from '@mdtBsServices/albums';
import { datapkgBindAlbumsAsync, deleteDatapkgIdFromAlbumsAsync } from '@mdtBsServices/datapkgs';
import { DataNode } from '@mdtDesign/tree';
import { IDataListTaggroupsModel } from '../../../containers/data-list-taggroups';
import { ITheme } from '../DatapkgDetailFactoryModel';

export class ThemesSelectModel implements IDataListTaggroupsModel {
  private themes: ITheme[];
  private appId: number;
  private pkgId: string;
  private oldSelectedTagIds: string[] = [];

  public constructor(appId: number, pkgId: string, themes: ITheme[]) {
    this.appId = appId;
    this.themes = themes;
    this.pkgId = pkgId;
  }

  public destroy() {
    this.themes = [];
    this.oldSelectedTagIds = [];
  }

  // 查询taggroup list(页面展示数据)
  public querySelectedTagList(): Observable<string[]> {
    const themes: ITheme[] = this.themes || [];
    const ids = _.map(themes, (t) => joinKey(t.id, t.name, t.name));
    this.oldSelectedTagIds = ids;
    return of(ids);
  }

  // 更新taggroup list
  public updateTaggroups(newIds: string[]): Observable<boolean> {
    const request = async () => {
      const removeIds = _.difference(this.oldSelectedTagIds, newIds);
      const addIds = _.difference(newIds, this.oldSelectedTagIds);
      if (!_.size(removeIds) && !_.size(addIds)) return false;
      if (_.size(removeIds)) {
        const removeData = {
          album_ids: _.map(removeIds, (id) => splitKey(id)[0]),
        };
        const addResp = await deleteDatapkgIdFromAlbumsAsync(this.pkgId, removeData);
        if (!addResp.success) return false;
      }

      if (_.size(addIds)) {
        const addData = {
          album_ids: _.map(addIds, (id) => splitKey(id)[0]),
        };
        const addResp = await datapkgBindAlbumsAsync(this.pkgId, addData);
        if (!addResp.success) return false;
      }
      return true;
    };

    return from(request());
  }

  // 获取taggroup options（tree select的options）
  public queryTaggroupOptions(): Observable<DataNode[]> {
    return from(queryAlbumsAsync({ params: { apps: this.appId } })).pipe(
      map((resp) => {
        return _.map(resp.data, (theme) => {
          return {
            key: joinKey(theme.id, theme.name, theme.name),
            title: theme.name,
            value: joinKey(theme.id, theme.name, theme.name),
          };
        });
      }),
    );
  }
}
