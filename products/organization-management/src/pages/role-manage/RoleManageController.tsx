import _ from 'lodash';
import { AsyncSubject, of } from 'rxjs';
import { map } from 'rxjs/internal/operators';
import { IBusinessResult } from '@mdtBsComponents/modal-with-btns-comp';
import { IEmotionProps, ModalWithBtnsCompEmotionController } from '@mdtBsComponents/modal-with-btns-comp-emotion';
import { RequestController } from '@mdtBsControllers/request-controller';
import { IRequestCancelToken } from '@mdtBsServices/interfaces';
import { LinkButton } from '@mdtDesign/button';
import toastApi from '@mdtDesign/toast';
import {
  DrawerModifyFormPermissionRoleController,
  IFormData,
  IModalData,
} from '@mdtProMicroModules/containers/drawer-modify-form-permission-role';
import {
  ILoadDataListRslt,
  IVirtualizedTableProps,
  TableCurdWithSimpleSearchController,
} from '@mdtProMicroModules/containers/table-curd-with-simple-search';
import { AppController } from '../../app/AppController';
import { IMoreItemsKey, IRoleManageModel, ITableData } from './RoleManageModel';

class RoleManageController extends RequestController {
  private readonly Model: IRoleManageModel;
  private readonly tableController: TableCurdWithSimpleSearchController<ITableData>;
  private readonly modifyController: DrawerModifyFormPermissionRoleController<ITableData>;
  private readonly deleteController: ModalWithBtnsCompEmotionController<ITableData>;
  private drawerUiDataCache: IModalData = {};
  private app?: AppController;
  private appId: number;

  public constructor(app: AppController, Model: IRoleManageModel) {
    super();
    this.Model = Model;
    this.app = app;
    this.appId = app.getAppId()!;
    this.tableController = new TableCurdWithSimpleSearchController<ITableData>({
      dataListCompTableCurdControllerOptions: {
        dataListControllerOptions: {
          loadDataListFunc: this.queryAllData,
        },
        tableOptions: this.initTableOptions,
        curdOptions: this.initCurdOptions,
        clickCreateBtnFunc: this.createTableData,
        clickEditBtnFunc: this.editTableData,
        clickMoreBtnFunc: this.moreItemsTableData,
      },
      headerOptions: this.initHeaderOptions,
    });
    this.modifyController = new DrawerModifyFormPermissionRoleController<ITableData>({
      loadDrawerUiDataFunc: this.queryDrawerUiData,
      modifyDataFunc: this.modifyDataToService,
    });
    this.deleteController = new ModalWithBtnsCompEmotionController<ITableData>({
      clickOkBtnFunc: this.deleteDataToService,
      modalCompOptions: { modalOptions: this.initDeleteModalOptions },
    });
    this.init();
  }

  public destroy() {
    super.destroy();
    this.tableController.destroy();
    this.modifyController.destroy();
    this.deleteController.destroy();
    this.drawerUiDataCache = {};
    this.app = undefined;
  }

  public getTableController() {
    return this.tableController;
  }

  public getModifyController() {
    return this.modifyController;
  }

  public getDeleteController() {
    return this.deleteController;
  }

  // 加载角色列表
  private queryAllData = (cancelToken?: IRequestCancelToken): ILoadDataListRslt<ITableData> => {
    return this.Model.queryAllRoles(this.appId, cancelToken).pipe(map((v) => [0, v]));
  };

  // 渲染操作拦所需信息
  private initCurdOptions = () => {
    const { enableDelete, ...rest } = this.app!.getUserPermissionController()!.getRoleManagePermission();
    const moreItems = [
      {
        title: '删除',
        icon: 'delete-2',
        key: IMoreItemsKey.DELETE,
        danger: true,
        disabled: !enableDelete,
      },
    ];
    return { ...rest, moreItems };
  };

  // 渲染表头所需信息
  private initTableOptions = (): IVirtualizedTableProps => {
    return {
      columns: [
        {
          name: '角色名',
          code: 'name',
          width: 286,
          render: (value: string, r: ITableData) => (
            <LinkButton onClick={() => this.editTableData(r)}>{value}</LinkButton>
          ),
        },
        { name: '角色描述', code: 'description' },
      ],
      type: 'page-bg',
      primaryKey: 'id',

      withVerticalBorder: false,
    };
  };

  // 渲染头部所需信息
  private initHeaderOptions = () => {
    return {
      createBtnLabel: '新建角色',
      inputPlaceholder: '搜索角色',
      title: '机构角色',
    };
  };

  // 新增数据
  private createTableData = () => {
    return this.modifyController.openModal();
  };

  // 编辑数据
  private editTableData = (orginalData?: ITableData) => {
    const { enableEdit } = this.app!.getUserPermissionController()!.getRoleManagePermission();
    if (!enableEdit) {
      let result = new AsyncSubject<IBusinessResult<ITableData>>();
      result.next({ success: true });
      result.complete();
      return result;
    }
    return this.modifyController.openModal(orginalData);
  };

  // 更多数据
  private moreItemsTableData = (key: string, orginalData?: ITableData) => {
    // 删除数据
    if (key === IMoreItemsKey.DELETE) {
      return this.deleteController.openModal(orginalData);
    }
    let result = new AsyncSubject<IBusinessResult<ITableData>>();
    result.next({ success: true });
    result.complete();
    return result;
  };

  // 加载弹窗role的权限
  private queryDrawerUiData = () => {
    if (_.isEmpty(this.drawerUiDataCache)) {
      const ps = this.app!.getPermissionController();
      const mdtProduct = this.app!.getUserMdtProduct();
      this.drawerUiDataCache = this.Model.getGroupPermissions(
        this.app!.getAppPermission()!,
        ps!.getPermissionLabel.bind(ps),
        ps!.getPermissionModule.bind(ps),
        ps!.getPermissionType.bind(ps),
        mdtProduct,
      );
    }
    return of(this.drawerUiDataCache);
  };

  // 向服务端修改数据
  private modifyDataToService = (params: IFormData, orginalData?: ITableData) => {
    return orginalData ? this.updateDataToService(params, orginalData!) : this.addDataToService(params);
  };

  // 更新数据
  private async updateDataToService(data: IFormData, orginalData: ITableData) {
    const resp = await this.Model.updateRole(data, orginalData).toPromise();
    resp.success && toastApi.success('更新角色成功');
    return resp;
  }

  // 新建数据
  private async addDataToService(data: IFormData) {
    const resp = await this.Model.createRole(data, this.appId).toPromise();
    resp.success && toastApi.success('新建角色成功');
    return resp;
  }

  // 删除数据
  private deleteDataToService = async (orginalData?: ITableData) => {
    const resp = await this.Model.deleteRole(orginalData!).toPromise();
    resp.success && toastApi.success('删除角色成功');
    return resp;
  };

  // 渲染删除弹窗所需信息
  private initDeleteModalOptions = (): IEmotionProps => {
    const delData = this.deleteController.getModalRest();
    return {
      emotion: 'alert',
      title: `确认删除"${delData?.name}"角色`,
      description: '删除后，所属该角色的用户权限将自动失效',
    };
  };

  // 初始请求
  private init() {
    // 注册前端检索
    this.tableController.listenFrontFilter();
    const cancelToken = this.getCancelToken();
    this.tableController.loadDataList(cancelToken);
  }
}

export { RoleManageController };
