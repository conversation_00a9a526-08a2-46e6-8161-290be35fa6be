/* eslint-disable sonarjs/no-duplicate-string */
/* eslint-disable @typescript-eslint/explicit-member-accessibility */
import i18n from '../../../languages';
import { FlexPluginBase } from '../FlexPluginBase';

export default class Layout_scroll_x extends FlexPluginBase {
  name = i18n.chain.jsonComp.scrollXContainer;
  isBaseComponent = false;
  pluginIcon = 'layout-3cols-plugin';
  description = i18n.chain.jsonComp.scrollXDescription;
  tags = [i18n.chain.jsonComp.commonLayout];
  order = 505;
  scaffold: any = {
    type: 'flex',
    className: 'p-1',
    items: [
      {
        type: 'container',
        size: 'xs',
        body: [],
        wrapperBody: false,
        style: {
          flex: '0 0 auto',
          flexBasis: '200px',
          display: 'block',
          position: 'static',
          minWidth: 'auto',
          minHeight: 'auto',
        },
      },
      {
        type: 'container',
        size: 'xs',
        body: [],
        wrapperBody: false,
        style: {
          flex: '0 0 auto',
          flexBasis: '200px',
          display: 'block',
          position: 'static',
          minWidth: 'auto',
          minHeight: 'auto',
        },
      },
      {
        type: 'container',
        size: 'xs',
        body: [],
        wrapperBody: false,
        style: {
          flex: '0 0 auto',
          display: 'block',
          position: 'static',
          minWidth: 'auto',
          minHeight: 'auto',
          flexBasis: '200px',
        },
      },
      {
        type: 'container',
        wrapperBody: false,
        size: 'xs',
        body: [],
        style: {
          flex: '0 0 auto',
          flexBasis: '200px',
          display: 'block',
          position: 'static',
          minWidth: 'auto',
          minHeight: 'auto',
        },
      },
      {
        type: 'container',
        size: 'xs',
        body: [],
        wrapperBody: false,
        style: {
          flex: '0 0 auto',
          flexBasis: '200px',
          display: 'block',
          position: 'static',
          minWidth: 'auto',
          minHeight: 'auto',
        },
      },
      {
        type: 'container',
        size: 'xs',
        body: [],
        wrapperBody: false,
        style: {
          flex: '0 0 auto',
          flexBasis: '200px',
          display: 'block',
          position: 'static',
          minWidth: 'auto',
          minHeight: 'auto',
        },
      },
      {
        type: 'container',
        size: 'xs',
        body: [],
        wrapperBody: false,
        style: {
          flex: '0 0 auto',
          flexBasis: '200px',
          display: 'block',
          position: 'static',
          minWidth: 'auto',
          minHeight: 'auto',
        },
      },
    ],
    direction: 'row',
    justify: 'flex-start',
    alignItems: 'stretch',
    style: {
      position: 'static',
      minHeight: 'auto',
      maxWidth: '1080px',
      minWidth: 'auto',
      height: '200px',
      overflowX: 'scroll',
      overflowY: 'scroll',
      margin: '0 auto',
    },
    isFixedHeight: true,
    isFixedWidth: false,
  };

  onPreview2editor(event: any) {
    console.log('onPreview2editor-event:', event);
  }
}
