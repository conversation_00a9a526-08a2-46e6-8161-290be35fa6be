# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [1.19.7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-components@1.19.6...@mdt/business-components@1.19.7) (2025-05-19)

### Bug Fixes

- 🐛 调整 isAllData 为响应变量,根据监听方式进一步判断 isAllData 的值 ([3e0c3d6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/3e0c3d609ec2932bfcd94e5f30e1270c7679d599))

## [1.19.6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-components@1.19.5...@mdt/business-components@1.19.6) (2025-05-14)

**Note:** Version bump only for package @mdt/business-components

## [1.19.5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-components@1.19.4...@mdt/business-components@1.19.5) (2025-04-27)

**Note:** Version bump only for package @mdt/business-components

## [1.19.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-components@1.19.3...@mdt/business-components@1.19.4) (2025-04-02)

### Bug Fixes

- dm open error ([fd305bb](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/fd305bb25c5a0763bb2e49cd0c74d5865662caa4))

## [1.19.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-components@1.19.2...@mdt/business-components@1.19.3) (2025-03-31)

**Note:** Version bump only for package @mdt/business-components

## [1.19.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-components@1.19.1...@mdt/business-components@1.19.2) (2025-03-10)

### Features

- ✨ 一表通 h5 增加适配配置项 ([676889e](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/676889ee401c9e23b84b56c81381e847c98ffa03))

## [1.19.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-components@1.19.0...@mdt/business-components@1.19.1) (2025-02-27)

### Bug Fixes

- 🐛 修复 h5 样式引发的问题 ([9bd6f06](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/9bd6f06f39358d403c9961d162f56f0b62a439fc))

# [1.19.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-components@1.18.3...@mdt/business-components@1.19.0) (2025-02-24)

**Note:** Version bump only for package @mdt/business-components

## [1.18.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-components@1.18.2...@mdt/business-components@1.18.3) (2025-02-17)

### Bug Fixes

- 🐛 modal drawer 传参恢复 ([60bf07d](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/60bf07d0b5cded91b6189d56890803a3cccbb322))

### Features

- ✨ 个人数据权限共享 ([6bd7811](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/6bd78117bffb5be142533603322d6fcb1a09de91))
- ✨ 增加指定分页加载功能 ([4cbbfcb](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/4cbbfcbb13e1e3170dc9d4c143ca5ba6fa4fd6d2))

## [1.18.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-components@1.18.1...@mdt/business-components@1.18.2) (2025-01-23)

**Note:** Version bump only for package @mdt/business-components

## [1.18.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-components@1.18.0...@mdt/business-components@1.18.1) (2025-01-06)

**Note:** Version bump only for package @mdt/business-components

# [1.18.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-components@1.17.4...@mdt/business-components@1.18.0) (2024-12-23)

### Features

- 数据包 DDL 设置 ([543c22e](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/543c22e19976e5ca690e1d581de808ac74d23a13))

## [1.17.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-components@1.17.3...@mdt/business-components@1.17.4) (2024-12-16)

**Note:** Version bump only for package @mdt/business-components

## [1.17.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-components@1.17.2...@mdt/business-components@1.17.3) (2024-12-03)

**Note:** Version bump only for package @mdt/business-components

## [1.17.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-components@1.17.0...@mdt/business-components@1.17.2) (2024-12-02)

**Note:** Version bump only for package @mdt/business-components

## [1.17.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-components@1.17.0...@mdt/business-components@1.17.1) (2024-12-02)

**Note:** Version bump only for package @mdt/business-components

# [1.17.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-components@1.16.8...@mdt/business-components@1.17.0) (2024-11-26)

### Features

- ✨ h5 适配 ([4675022](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/46750223a044f02f5c3e93cb70ec59f14480197f))
- ✨ 支持排序 ([625852b](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/625852bbf0734e9118ec1a73b340a715bb133840))

## [1.16.8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-components@1.16.7...@mdt/business-components@1.16.8) (2024-11-14)

### Features

- ✨ 周期改版 ([901090d](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/901090db719c8b5efe07849b13e8e46057ea029b))

## [1.16.7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-components@1.16.6...@mdt/business-components@1.16.7) (2024-10-28)

**Note:** Version bump only for package @mdt/business-components

## [1.16.6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-components@1.16.5...@mdt/business-components@1.16.6) (2024-10-27)

### Bug Fixes

- 优化 ([a483603](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/a4836035d5284c6819d7404e70418afdc085661d))

## [1.16.5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-components@1.16.4...@mdt/business-components@1.16.5) (2024-10-22)

**Note:** Version bump only for package @mdt/business-components

## [1.16.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-components@1.16.3...@mdt/business-components@1.16.4) (2024-10-15)

**Note:** Version bump only for package @mdt/business-components

## [1.16.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-components@1.16.2...@mdt/business-components@1.16.3) (2024-09-12)

### Features

- 流程实例界面支持 url ([744660f](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/744660f4dcaf5319690d0cf8ae03bf7f77593755))

## [1.16.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-components@1.16.1...@mdt/business-components@1.16.2) (2024-08-29)

### Bug Fixes

- 🐛 样式调整 ([e7dd153](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/e7dd153667136fdd7a950f8ba97d895b503de1d9))

## [1.16.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-components@1.16.0...@mdt/business-components@1.16.1) (2024-08-27)

### Features

- 表格列可拖拽 ([aa29a52](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/aa29a5211fd50d60ca3d43bd1e96d0778ffb678d))

# [1.16.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-components@1.15.1...@mdt/business-components@1.16.0) (2024-08-09)

**Note:** Version bump only for package @mdt/business-components

## [1.15.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-components@1.15.0...@mdt/business-components@1.15.1) (2024-08-07)

### Features

- ✨ 追加手动分页 ([38f7c2f](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/38f7c2f6740e3b5abf99f92c5d80b78b06a4866b))

# [1.15.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-components@1.14.0...@mdt/business-components@1.15.0) (2024-07-22)

**Note:** Version bump only for package @mdt/business-components

# [1.14.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-components@1.13.3...@mdt/business-components@1.14.0) (2024-07-02)

### Features

- ✨ Modify all i18n ([80ff7fa](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/80ff7faf39a476757bf7285711f3508c03ecb85b))
- ✨ onetable 移动端优化 ([7f9b522](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/7f9b5220d5f454ead6efd1d92149d2d99e334e55))

## [1.13.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-components@1.13.2...@mdt/business-components@1.13.3) (2024-06-24)

### Features

- ✨ 一表通移动端 详情页 ([db002b6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/db002b667a7485a766e079cc4bdebea6c4f9d7d4))

## [1.13.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-components@1.13.1...@mdt/business-components@1.13.2) (2024-06-03)

### Features

- 国际化文案修改 ([b87655b](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/b87655bf55973106ecbfc5ffda41b8070309df0d))

## [1.13.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-components@1.13.0...@mdt/business-components@1.13.1) (2024-05-20)

**Note:** Version bump only for package @mdt/business-components

# [1.13.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-components@1.12.1...@mdt/business-components@1.13.0) (2024-05-13)

### Features

- ✨ 自定义登录 ([ee27c83](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/ee27c83a5b349fd8292dfcbe0e910caf8bc570dd))

## [1.12.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-components@1.12.0...@mdt/business-components@1.12.1) (2024-04-29)

**Note:** Version bump only for package @mdt/business-components

# [1.12.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-components@1.11.13...@mdt/business-components@1.12.0) (2024-03-11)

### Features

- ✨ 文件夹功能 ([9d6680e](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/9d6680e70da17e00d743efcf602480e9e62f0f40))

## [1.11.13](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-components@1.11.12...@mdt/business-components@1.11.13) (2024-03-07)

**Note:** Version bump only for package @mdt/business-components

## [1.11.12](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-components@1.11.11...@mdt/business-components@1.11.12) (2023-12-08)

### Bug Fixes

- 🐛 内存泄露 ([78c1845](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/78c1845ffe65b5c9f2fcdd23f6ba68db5f86932d))

## [1.11.11](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-components@1.11.10...@mdt/business-components@1.11.11) (2023-12-01)

**Note:** Version bump only for package @mdt/business-components

## [1.11.10](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-components@1.11.9...@mdt/business-components@1.11.10) (2023-11-13)

**Note:** Version bump only for package @mdt/business-components

## [1.11.9](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-components@1.11.8...@mdt/business-components@1.11.9) (2023-10-30)

### Performance Improvements

- ⚡️ build faster ([1c14c93](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/1c14c9346709282613bed42addcdfcf7d359575f))

## [1.11.8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-components@1.11.7...@mdt/business-components@1.11.8) (2023-10-25)

**Note:** Version bump only for package @mdt/business-components

## [1.11.7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-components@1.11.6...@mdt/business-components@1.11.7) (2023-09-18)

### Features

- ✨ 流程引擎支持发起数据包填报 ([498bc62](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/498bc62727e3a2dffc46e303fc5a3187629f100c))

## [1.11.6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-components@1.11.5...@mdt/business-components@1.11.6) (2023-09-04)

**Note:** Version bump only for package @mdt/business-components

## [1.11.5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-components@1.11.4...@mdt/business-components@1.11.5) (2023-08-14)

### Bug Fixes

- 🐛 修复虚拟列表自定义动态高度滚动位置异常的问题 ([f0879fa](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/f0879fab8f92a3fe97506acc40ee51f85c2373cc))

## [1.11.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-components@1.11.3...@mdt/business-components@1.11.4) (2023-07-31)

### Features

- ✨ Notices center ([bc24bed](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/bc24bedbe38b612f8f0d507db8951ce2ac3689fa))

## [1.11.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-components@1.11.2...@mdt/business-components@1.11.3) (2023-07-24)

### Performance Improvements

- ⚡️ 性能优化(秒开) ([7142db5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/7142db546bb7e1ea57651c700d2745e1f57a3a60))

## [1.11.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-components@1.11.1...@mdt/business-components@1.11.2) (2023-07-03)

**Note:** Version bump only for package @mdt/business-components

## [1.11.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-components@1.11.0...@mdt/business-components@1.11.1) (2023-06-13)

### Bug Fixes

- 🐛 @i18n-chain/react add dependencies ([5fc7e47](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/5fc7e47c557a03343e4885594bbcd24dbaec912c))

# [1.11.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-components@1.10.1...@mdt/business-components@1.11.0) (2023-06-05)

### Features

- ✨ all i18n finished ([449c38e](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/449c38eb7a0e33455ab4c2abd846079158a7ba9d))

## [1.10.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-components@1.10.0...@mdt/business-components@1.10.1) (2023-05-15)

**Note:** Version bump only for package @mdt/business-components

# [1.10.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-components@1.9.3...@mdt/business-components@1.10.0) (2023-03-13)

### Features

- ✨ add workflow ([646e475](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/646e4758659a102c60ff745761e112f3c5d10958))

## [1.9.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-components@1.9.2...@mdt/business-components@1.9.3) (2023-02-20)

### Features

- ✨ add workflow ([85b5104](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/85b5104b8c48d8b4b11a9a4269a6f5067ac87488))

## [1.9.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-components@1.9.1...@mdt/business-components@1.9.2) (2023-02-13)

**Note:** Version bump only for package @mdt/business-components

## [1.9.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-components@1.9.0...@mdt/business-components@1.9.1) (2023-01-30)

**Note:** Version bump only for package @mdt/business-components

# [1.9.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-components@1.8.3...@mdt/business-components@1.9.0) (2022-12-13)

**Note:** Version bump only for package @mdt/business-components

## [1.8.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-components@1.8.2...@mdt/business-components@1.8.3) (2022-12-12)

### Features

- ✨ [Table 通用]: 可选列表增加全选功能 ([e3e368a](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/e3e368ae2c5e3224e26aea0e0c47ed580ee4ee14))

## [1.8.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-components@1.8.1...@mdt/business-components@1.8.2) (2022-11-30)

### Features

- ✨ [资源共享] 支持分享个人数据包、机构数据包 ([3d66dab](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/3d66dab5e48a864d6a6fb1f0cb7f2d700c1d6f55))

## [1.8.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-components@1.8.0...@mdt/business-components@1.8.1) (2022-11-28)

**Note:** Version bump only for package @mdt/business-components

# [1.8.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-components@1.7.1...@mdt/business-components@1.8.0) (2022-11-21)

### Bug Fixes

- 🐛 memory leak ([a971074](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/a971074e017b8ea025305ce48fdc605ab25fb550))

## [1.7.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-components@1.7.0...@mdt/business-components@1.7.1) (2022-11-14)

### Features

- ✨ [全家桶]: 主题切换, 深色适配 ([549d854](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/549d8543f36ad6b7fa16403cc5bc8d5053341395))
- ✨ [用户管理]: 大模块重构 ([f3fa668](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/f3fa668174590ea5020a9bb910b22bd37d4de067))

# [1.7.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-components@1.6.0...@mdt/business-components@1.7.0) (2022-10-31)

### Features

- ✨ 数据市场、我的数据 优化 ([40ac4c6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/40ac4c67afebdfc586da4221e2cb934c6bea51db))

# [1.6.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-components@1.5.0...@mdt/business-components@1.6.0) (2022-09-30)

### Features

- ✨ 可见数据设置 ([c540a22](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/c540a22a0c8493d9c462318b8e1d9bb256d80dce))

# [1.5.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-components@1.4.1...@mdt/business-components@1.5.0) (2022-09-26)

**Note:** Version bump only for package @mdt/business-components

## [1.4.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-components@1.4.0...@mdt/business-components@1.4.1) (2022-09-19)

**Note:** Version bump only for package @mdt/business-components

# [1.4.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-components@1.3.1...@mdt/business-components@1.4.0) (2022-09-13)

### Features

- ✨ 数据包创建、详情+血缘图优化+个人数据发布审批 ([35a345c](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/35a345cc293c4f4c2adb0186ae7fac41c0ede6ad))

## [1.3.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-components@1.3.0...@mdt/business-components@1.3.1) (2022-09-05)

**Note:** Version bump only for package @mdt/business-components

# [1.3.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-components@1.2.0...@mdt/business-components@1.3.0) (2022-08-30)

**Note:** Version bump only for package @mdt/business-components

# [1.2.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/business-components@1.1.0...@mdt/business-components@1.2.0) (2022-08-12)

### Bug Fixes

- 🐛 fix bug ([11e790f](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/11e790f1b38b898a2475f98ccf6f790049788941))

### Features

- ✨ [通用 header]: 功能扩展，样式优化 ([d843e94](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/d843e9438b766313f08cfbcba5734f88d852ccfb))
- ✨ 模板管理 ([9da7e90](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/9da7e904cacec51c3c25df6ce6602da53b46eb37))
- ✨ 血缘图 ([d9e9402](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/d9e94027679dc46458a0f50022805c64652c81a8))
- ✨ 血缘图 ([dbe2c30](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/dbe2c308386df9286f60026925486879c7aab965))

# 1.1.0 (2022-06-30)

### Features

- ✨ 通用分离 ([8a90e4b](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/8a90e4b5ee9bf1cdd6dd15d86dfa54b989f35dfc))
