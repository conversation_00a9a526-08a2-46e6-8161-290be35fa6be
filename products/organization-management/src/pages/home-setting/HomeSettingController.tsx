import _ from 'lodash';
import { AsyncSubject, BehaviorSubject, from, Observable } from 'rxjs';
import { map, switchMap } from 'rxjs/operators';
import { If } from '@mdtBsComm/components/if';
import { RequestController } from '@mdtBsControllers/request-controller';
import { IPaginationQuery, IUserQuery } from '@mdtBsServices/interfaces';
import { getUserPreferencesBatchAsync } from '@mdtBsServices/preference';
import Button from '@mdtDesign/button';
import { Icon } from '@mdtDesign/icon';
import Tag from '@mdtDesign/tag';
import toastApi from '@mdtDesign/toast';
import Tooltip from '@mdtDesign/tooltip';
import { PreferencesEnum } from '@mdtProComm/constants';
import { IBusinessResult, IOrgs } from '@mdtProComm/interfaces';
import { getOrgPathWithOrg } from '@mdtProComm/utils/orgUtil';
import { FilterEnum } from '@mdtProMicroModules/containers/dialog-home-page-list';
import { DrawerChooseHomePageController, IHomeInfo } from '@mdtProMicroModules/containers/drawer-choose-home-page';
import { IModalData } from '@mdtProMicroModules/containers/drawer-modify-form-permission-user-info';
import {
  OrgManageTreeListController,
  OrgManageTreeListModel,
} from '@mdtProMicroModules/containers/org-manage-tree-list';
import {
  ITableRoleData,
  RoleManageTreeListController,
  RoleManageTreeListModel,
} from '@mdtProMicroModules/containers/role-manage-tree-list';
import {
  IVirtualizedTableProps,
  TableCurdWithSimpleSearchController,
} from '@mdtProMicroModules/containers/table-curd-with-simple-search';
import { AppController } from '../../app/AppController';
import i18n from '../../languages';
import { IHomePage, IHomeSettingModel, IHomeUsers } from './HomeSettingModel';

export interface IOrganizationModalData extends IModalData {
  orgIdNameMap: Record<string, IOrgs>;
}

export type IFilterType = 'org' | 'role';
// 防止app的id和部门的id重合，为appId加上前缀
export const APP_KEY_SUFFIX = 'app_';

class HomeSettingController extends RequestController {
  private Model: IHomeSettingModel;
  private app: AppController;

  // app下的用户表格 - 部门用户表格
  private tableController: TableCurdWithSimpleSearchController<IHomeUsers>;
  // 部门
  private orgController?: OrgManageTreeListController;
  // 角色
  private roleController?: RoleManageTreeListController;

  // 操作大屏的抽屉
  private homePageController: DrawerChooseHomePageController<IHomeInfo>;

  // 是否为根节点
  private isRoot$ = new BehaviorSubject<boolean>(true);
  // 当前选中的节点
  private currentSelectId$ = new BehaviorSubject<string>('');
  // 分组类型
  private filterType$ = new BehaviorSubject<IFilterType>('org');
  // 列表信息缓存
  private pageListCache: IHomePage[];
  // 部门数据加载状态
  private isOrgLoading$ = new BehaviorSubject<boolean>(false);

  // 当前部门层级展示
  private pidPathDisplay: string;
  // 用户列表
  private appUsers: IHomeUsers[];
  // 正在编辑的用户
  private editingUser: IHomeUsers | undefined;
  // 用户偏好
  private userPreferences: Record<string, IHomeInfo> = {};
  // 角色和部门缓存
  private drawerUiDataCache: {
    roleIdNameMap?: Record<number, string>;
    orgIdNameMap?: Record<string, IOrgs>;
    currentRoleId?: string;
  } = {};

  public constructor(app: AppController, Model: IHomeSettingModel) {
    super();
    this.app = app;
    this.Model = Model;
    this.pidPathDisplay = '--';
    this.appUsers = [];
    this.pageListCache = [];

    this.homePageController = new DrawerChooseHomePageController<IHomeInfo>({
      modifyDataFunc: this.modifyHomePageFunc,
      loadPageListFunc: this.loadPageList,
      changeFilterFunc: this.changeFilter,
      onSearch: this.onSearch,
      beforeOpenFunc: async (value?: IHomeInfo) => {
        if (!value?.preview) {
          return value;
        }
        return await this.Model.getUserPageUrl(value, this.app.getTheme()).toPromise();
      },
    });

    this.tableController = new TableCurdWithSimpleSearchController<IHomeUsers>({
      dataListCompTableCurdControllerOptions: {
        dataListControllerOptions: {
          loadDataListFunc: this.queryDataWithTotal,
          loadNextPageDataListFunc: this.queryData,
          getBackendFilterParams: this.getFilterParams,
        },
        clickEditBtnFunc: this.editPageFunc,
        curdOptions: this.initCurdOptions,
        tableOptions: this.initTableOptions,
      },
      headerOptions: this.initHeaderOptions,
    });

    const rootId = this.getRootInfo().key;
    this.currentSelectId$.next(rootId);

    // 先初始化角色和部门数据，然后再加载用户列表
    this.initOrgAndRoleList().subscribe(() => {
      // 初始化用户偏好
      this.initUserPreferences();
      // 加载用户列表
      this.tableController.loadDataList();
    });
    this.tableController.listenBackendFilter(this.tableController.getSingleFilter$());
  }

  public destroy() {
    super.destroy();
    this.tableController.destroy();
    this.orgController?.destroy();
    this.roleController?.destroy();
    this.homePageController.destroy();
    this.isRoot$.complete();
    this.currentSelectId$.complete();
    this.appUsers = [];
    this.pageListCache = [];
    this.app = null!;
    this.Model = null!;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  public getTableController(cur?: string) {
    return this.tableController;
  }

  public getOrgLoading$() {
    return this.isOrgLoading$;
  }

  public getfilterType$() {
    return this.filterType$;
  }

  public updatefilterType(type: IFilterType) {
    this.filterType$.next(type);
    if (type === 'role' && this.roleController) {
      const currentRole = this.roleController.getCurrentSelect$().getValue();
      this.onRoleSelect(currentRole);
    }
    if (type === 'org' && this.orgController) {
      const orgId = this.orgController.getCurrentSelectId$().getValue();
      const isRoot = this.orgController.getIsRoot$().getValue();
      const flatData = this.orgController.getFlatTreeData$().getValue();
      this.onOrgSelect(orgId, isRoot, flatData);
    }
  }

  public getOrgController() {
    if (!this.orgController) {
      this.orgController = new OrgManageTreeListController(
        {
          appId: this.app.getAppId(),
          appName: this.app.getAppName(),
          onOrgSelectFunc: this.onOrgSelect,
          onlyRead: true,
        },
        OrgManageTreeListModel,
      );
    }
    return this.orgController;
  }

  public getRoleController() {
    if (!this.roleController) {
      this.roleController = new RoleManageTreeListController(
        {
          app: this.app,
          onRoleSelect: this.onRoleSelect,
          onlyRead: true,
        },
        RoleManageTreeListModel,
      );
      this.roleController.start();
    }
    return this.roleController;
  }

  public getHomePageController() {
    return this.homePageController;
  }

  public getCurrentSelectId$() {
    return this.currentSelectId$;
  }

  public getRootInfo = () => {
    return {
      title: this.app.getAppName(),
      key: APP_KEY_SUFFIX + this.app.getAppId(),
    };
  };

  public onRoleSelect = (data: ITableRoleData) => {
    this.drawerUiDataCache.currentRoleId = String(data.id);
    this.tableController.loadDataList({ role_ids: String(data.id) });
  };

  public onOrgSelect = (orgId: string, isRoot: boolean, flatData: IOrgs[]) => {
    this.isRoot$.next(isRoot);
    this.currentSelectId$.next(orgId);
    this.drawerUiDataCache.currentRoleId = orgId;
    this.pidPathDisplay = getOrgPathWithOrg(orgId, flatData);
    if (!isRoot) {
      this.tableController.loadDataList({ role_ids: orgId });
      return;
    }
    this.tableController.loadDataList();
  };

  // 渲染表头所需信息
  private initTableOptions = (): IVirtualizedTableProps => {
    return {
      columns: [
        {
          name: i18n.chain.orgAdmin.organization.username,
          code: 'name',
          width: 180,
        },
        {
          name: i18n.chain.orgAdmin.organization.orgs,
          code: 'orgDisplay',
          render: (v: string[]) =>
            this.isRoot$.getValue() ? (
              v?.length ? (
                v.map((v: string) => <Tag key={v} tag={v} />)
              ) : (
                '--'
              )
            ) : (
              <Tag key={this.pidPathDisplay} tag={this.pidPathDisplay} />
            ),
          width: 200,
        },
        {
          name: i18n.chain.orgAdmin.defaultPlay.defaultPage,
          code: 'homeInfo',
          width: 150,
          render: (value: IHomeInfo | undefined) => value?.title || '--',
        },
      ],
      type: 'page-bg',
      primaryKey: 'id',
      emptyContent: i18n.chain.comNoData,
      withVerticalBorder: false,
    };
  };

  private initCurdOptions = () => {
    const { enableEdit, enableEditBatch } = this.app.getUserPermissionController()!.getHomeSettingPermission();
    return {
      enableCreate: false,
      enableEdit,
      enableSelect: enableEditBatch,
    };
  };

  private initHeaderOptions = () => {
    const { enableEditBatch } = this.app.getUserPermissionController()!.getHomeSettingPermission();
    return {
      createBtnLabel: '',
      inputPlaceholder: i18n.chain.comPlaceholder.input,
      title: (
        <div style={{ display: 'flex' }}>
          {i18n.chain.orgAdmin.menu.defaultPlay}
          <Tooltip title={i18n.chain.orgAdmin.defaultPlay.desc} placement="bottom">
            <Icon icon="help-2" size={16} style={{ color: '#a7afb7', marginLeft: '5px' }} />
          </Tooltip>
        </div>
      ),
      renderExtendOthers: () => (
        <If data={enableEditBatch}>
          <Button type="primary" className="page_home_setting_batch_edit_btn" onClick={this.batchEdit}>
            {i18n.chain.orgAdmin.defaultPlay.batchChange}
          </Button>
        </If>
      ),
    };
  };
  // 获取app全部用户
  private getQueryParams = (params: IPaginationQuery) => {
    const { orgIdNameMap, roleIdNameMap } = this.drawerUiDataCache;
    const searchVal = this.tableController.getSingleFilterValue();
    const { key: rootId } = this.getRootInfo();
    const query: any = params || {};
    query.basic_info = true;

    if (this.drawerUiDataCache.currentRoleId !== rootId) {
      query.role_ids = this.drawerUiDataCache.currentRoleId;
    }

    if (searchVal) {
      query.name_like = searchVal;
    }

    return {
      query,
      appId: this.app.getAppId(),
      orgIdNameMap,
      roleIdNameMap,
    };
  };

  private queryData = (params: IPaginationQuery): Observable<IHomeUsers[]> => {
    return this.Model.queryUsers(this.getQueryParams(params)).pipe(
      switchMap((userList) => {
        return from(this.Model.enhanceUsersWithPreferences(userList, this.userPreferences, this.app.getTheme())).pipe(
          map((enhancedUsers) => {
            this.appUsers = enhancedUsers;
            return enhancedUsers;
          }),
        );
      }),
    );
  };

  private queryDataWithTotal = (params: IUserQuery): Observable<[number, IHomeUsers[]]> => {
    return this.Model.queryUsersTotal(this.getQueryParams(params)).pipe(
      switchMap(([total, userList]) => {
        return from(this.Model.enhanceUsersWithPreferences(userList, this.userPreferences, this.app.getTheme())).pipe(
          map((enhancedUsers) => {
            this.appUsers = enhancedUsers;
            return [total, enhancedUsers] as [number, IHomeUsers[]];
          }),
        );
      }),
    );
  };

  // 初始化用户偏好
  private initUserPreferences = () => {
    from(getUserPreferencesBatchAsync({ name: PreferencesEnum.USER_HOME_PAGE }))
      .pipe(
        map((resp) => {
          const prefValue = resp?.data || [];
          return _.assign({}, ..._.map(prefValue, (v) => ({ [v.layer_id]: v.values[PreferencesEnum.USER_HOME_PAGE] })));
        }),
      )
      .subscribe((preferences) => {
        this.userPreferences = preferences;
      });
  };

  // 筛选类型
  private changeFilter = (type: FilterEnum) => {
    const filterData = _.filter(this.pageListCache, (page) => {
      if (type === 'all') return true;
      return type === page.type;
    });
    this.homePageController.updateData([0, filterData]);
  };

  // 搜索筛选
  private onSearch = async (value: string, type: FilterEnum, currentType?: FilterEnum) => {
    if (!value) {
      this.changeFilter(currentType ?? type);
      return;
    }
    let resp = this.pageListCache;
    if (type === FilterEnum.PAGE) {
      resp = await this.Model.queryPageList(this.app.getTheme(), value).toPromise();
    }
    if (type === FilterEnum.PROJECT) {
      resp = await this.Model.queryPageProjectList(this.app.getTheme(), value).toPromise();
    }
    this.homePageController.updateData([0, resp]);
  };

  // 修改用户
  private editPageFunc = (originalData?: IHomeUsers) => {
    const homeInfo: IHomeInfo | undefined = originalData?.homeInfo;
    this.editingUser = originalData;
    this.homePageController.openModal(homeInfo);
    return new AsyncSubject<IBusinessResult<IHomeUsers>>();
  };

  // 批量修改用户
  private batchEdit = () => {
    this.homePageController.openModal();
  };

  // 获取页面list
  private loadPageList = () => {
    return this.Model.queryList(this.app.getTheme()).pipe(
      map((value) => {
        this.pageListCache = value;
        return [0, value] as [number, IHomePage[]];
      }),
    );
  };

  // 提交偏好
  private modifyHomePageFunc = async (value: IHomeInfo, originalData?: IHomeInfo) => {
    const selectedUsers = _.map(_.uniqWith(this.tableController.getSelectedDataListValue(), _.isEqual));
    // 无修改
    if (_.isEqual(value, originalData)) {
      return new Promise<IBusinessResult<IHomeInfo>>((resolve) => {
        resolve({ success: true });
      });
    }
    const allUsers = !_.isEmpty(selectedUsers) ? selectedUsers : [this.editingUser!];
    if (_.isEmpty(value)) {
      // 重置修改
      const resp = await this.Model.deleteHomePagePreference(_.map(allUsers, 'id')).toPromise();
      if (resp.success) {
        toastApi.success(i18n.chain.orgAdmin.defaultPlay.resetSuccess);
        this.tableController.changeSelectedDataList([]);
        _.forEach(allUsers, (user) => {
          this.updateUser({ ...user, homeInfo: value });
        });
      }
      return { success: resp.success };
    }
    // 有修改
    const resp = await this.Model.putHomePagePreference(_.map(allUsers, 'id'), value).toPromise();
    if (resp.success) {
      toastApi.success(i18n.chain.orgAdmin.defaultPlay.setSuccess);
      this.tableController.changeSelectedDataList([]);
      _.forEach(allUsers, (user) => {
        this.updateUser({ ...user, homeInfo: value });
      });
    }
    return { success: resp.success };
  };

  // 更新用户信息
  private updateUser = (user: IHomeUsers): void => {
    this.tableController.editDataInList(user);
    this.appUsers = _.map(this.appUsers, (appUser) => {
      if (_.isEqual(user.id, appUser.id)) {
        return user;
      }
      return appUser;
    });
  };

  // 初始化组织和角色列表
  private initOrgAndRoleList = () => {
    const { key: rootId } = this.getRootInfo();
    this.isOrgLoading$.next(true);

    return this.Model.queryRolesAndOrgs(this.app.getAppId(), rootId).pipe(
      map((value) => {
        const { roleIdNameMap, orgIdNameMap } = value;
        this.drawerUiDataCache = {
          roleIdNameMap,
          orgIdNameMap,
        };
        this.orgController?.init(value.orgList);
        this.isOrgLoading$.next(false);
        return value;
      }),
    );
  };

  private getFilterParams = (value: any[]) => {
    const [name] = value;
    if (name) {
      return { name_like: name };
    }
    return {};
  };
}

export { HomeSettingController };
