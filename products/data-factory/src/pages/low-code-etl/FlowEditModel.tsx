import _ from 'lodash';
import { from } from 'rxjs';
import { map, takeWhile } from 'rxjs/operators';
import { IRequestCancelToken } from '@mdtApis/interfaces/request';
import { getAllDatasetsAsync } from '@mdtBsServices/datasets';
import {
  getFlowAllSupportNodesAsync,
  postFlowAsync,
  putFlowAsync,
  queryFlowsPaginationAsync,
  runFlowOnceAsync,
  temporaryExecuteFlowAsync,
} from '@mdtBsServices/flows';
import {
  IFlowPost,
  IFlowRevision,
  IFlowRunPost,
  IFlowSupportNode,
  ITemporaryExecuteFlow,
} from '@mdtBsServices/interfaces';
import { CreateNodeTypeEnum, ExecuteNodeTypeEnum, NodeGroupEnum, NodeTypeEnum } from '../flow-edit/_util/enum';
import { IFlowEditModel } from '../flow-edit/FlowEditModel';

export type INodeList = Record<NodeGroupEnum, IFlowSupportNode[]>;
export const FLOW_DATA_VIEW_LIMIT = 20;

export class FlowEditModel implements IFlowEditModel {
  public groupNodeList(data: IFlowSupportNode[]) {
    let nodeList: INodeList = {
      extractor: [],
      transformer: [],
      loader: [],
      create: [],
      execute: [],
    };
    _.forEach(NodeTypeEnum, (type) => {
      const node = _.find(data, ({ node_type }) => type === node_type);
      // 防止API里未查出节点报错
      if (!node) return;
      let group = node.group as NodeGroupEnum;
      if (_.find(CreateNodeTypeEnum, (v) => v === node.node_type)) group = NodeGroupEnum.CREATE;
      if (_.find(ExecuteNodeTypeEnum, (v) => v === node.node_type)) group = NodeGroupEnum.EXECUTE;
      nodeList[group].push(node);
    });
    return nodeList;
  }

  // 获取节点列表
  public getNodeList(cancelToken?: IRequestCancelToken) {
    return from(getFlowAllSupportNodesAsync({ cancelToken })).pipe(
      takeWhile((resp) => !resp.canceled),
      map((resp) => {
        return this.groupNodeList(resp.data || []);
      }),
    );
  }

  // 执行临时FLOW
  public runTemporaryFlow(data: ITemporaryExecuteFlow, cancelToken?: IRequestCancelToken) {
    return from(temporaryExecuteFlowAsync(data, { cancelToken })).pipe(
      takeWhile((resp) => !resp.canceled),
      map((resp) => resp.data!),
    );
  }

  public getAlreadyHaveFlowList(cancelToken?: IRequestCancelToken) {
    return from(queryFlowsPaginationAsync({ cancelToken, params: { permission: 'execute', share: 2 } })).pipe(
      takeWhile((resp) => !resp.canceled),
      map((resp) => {
        return resp.data?.dataResult;
      }),
    );
  }

  public runFlow(flowId: string, data: IFlowRunPost, cancelToken?: IRequestCancelToken) {
    return from(runFlowOnceAsync(flowId, data, { cancelToken })).pipe(
      takeWhile((resp) => !resp.canceled),
      map((resp) => resp.data!),
    );
  }

  // 更新FLOW
  public putFlow(flowId: string, data: IFlowPost, params: IFlowRevision, cancelToken?: IRequestCancelToken) {
    return from(putFlowAsync(flowId, data, { cancelToken, params })).pipe(
      takeWhile((resp) => !resp.canceled),
      map((resp) => resp.data),
    );
  }

  // 新增FLOW
  public postFlow(data: IFlowPost, cancelToken?: IRequestCancelToken) {
    return from(postFlowAsync(data, { cancelToken })).pipe(
      takeWhile((resp) => !resp.canceled),
      map((resp) => resp.data),
    );
  }

  // 获取数据源
  public getDatasets(cancelToken?: IRequestCancelToken) {
    return from(getAllDatasetsAsync({ cancelToken })).pipe(
      takeWhile((resp) => !resp.canceled),
      map((resp) => resp.data || []),
    );
  }
}
