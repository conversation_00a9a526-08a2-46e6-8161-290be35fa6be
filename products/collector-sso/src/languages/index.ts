import { createI18n } from '@i18n-chain/core';
import dayjs from 'dayjs';
import duration from 'dayjs/plugin/duration';
import relativeTime from 'dayjs/plugin/relativeTime';
import { overrideI18n } from '@mdtProSso/utils/overrideI18n';
import { LOCALE } from '../config';
import cn from './locales/cn';
import en from './locales/en';

dayjs.extend(duration);
dayjs.extend(relativeTime);

const mergedCn = overrideI18n(cn, LOCALE);
const mergedEn = overrideI18n(en, LOCALE);

const i18n = createI18n({ defaultLocale: { key: 'cn', values: mergedCn } });
i18n.define('en', mergedEn);

export default i18n;
