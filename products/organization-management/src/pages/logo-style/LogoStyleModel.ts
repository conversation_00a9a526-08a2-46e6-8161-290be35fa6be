import { from } from 'rxjs';
import { map } from 'rxjs/operators';
import { getFileUrlAsync } from '@mdtBsServices/files';
import { postPreferencesSpecsAsync, putPreferencesAsync } from '@mdtBsServices/preference';

class LogoStyleModel {
  public static putAppLogo = (name: string, logo: string) => {
    return from(putPreferencesAsync({ name }, { value: logo, at: 'app' })).pipe(
      map((resp) => {
        if (resp.success) {
          return {
            success: true,
            result: { ...resp.data, logo },
          };
        }
        return { success: false };
      }),
    );
  };

  public static getListLogo = (fileId: string) => {
    return from(getFileUrlAsync(fileId, { params: { redirect: false }, quiet: true })).pipe(
      map((resp) => {
        if (resp.success) {
          return {
            success: true,
            result: resp.data?.sign_url.url,
          };
        }
        return { success: false };
      }),
    );
  };

  public static postSpec = (name: string) => {
    return from(postPreferencesSpecsAsync({ name, layer: 'app' })).pipe(
      map((resp) => {
        if (resp.success) {
          return {
            success: true,
          };
        }
        return { success: false };
      }),
    );
  };
}
export { LogoStyleModel };
export type ILogoStyleModel = typeof LogoStyleModel;
