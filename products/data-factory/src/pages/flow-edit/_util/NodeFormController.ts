import _ from 'lodash';
import { Cell } from '@antv/x6';
import { createForm, Form, IFormProps } from '@formily/core';
import { formateDate, transformDateToMilliseconds, transformDateToUnix } from '@mdtBsComm/utils/dayUtil';
import { SqlEditorDataTabelController } from '@mdtProMicroModules/containers/sql-editor-data-tabel';
import FlowEditController from '../FlowEditController';
import { NEED_FIELD_INFO_NODE } from './constants';
import { NodeStatusEnum, NodeTypeEnum } from './enum';
import {
  IFlowNodeParams,
  IFlowNodeParamscopyColumnApiTransformForm,
  IFlowNodeParamscopyColumnFormTransformApi,
  IFlowNodeParamsCreateTable,
  IFlowNodeParamsExecuteSql,
  IFlowNodeParamsFilter,
  IFlowNodeParamsFromDatapkg,
  IFlowNodeParamsFromDingtalkProcessInstances,
  IFlowNodeParamsFromDingtalkSheet,
  IFlowNodeParamsFromFeishuBitable,
  IFlowNodeParamsFromFeishuSpreadsheet,
  IFlowNodeParamsFromPctChange,
  IFlowNodeParamsFromQLangRaw,
  IFlowNodeParamsFromSql,
  IFlowNodeParamsFromWechatWorkApprovalData,
  IFlowNodeParamsGroupBy,
  IFlowNodeParamsinsertSequenceColumnApiToForm,
  IFlowNodeParamsinsertSequenceColumnFormToApi,
  IFlowNodeParamsJoin,
  IFlowNodeParamsNullIf,
  IFlowNodeParamsPipe,
  IFlowNodeParamsPipeForm,
  IFlowNodeParamsRename,
  IFlowNodeParamsRenameForm,
  IFlowNodeParamsReplaceApiTransformForm,
  IFlowNodeParamsReplaceFormTransformApi,
  IFlowNodeParamsStrip,
  IFlowNodeParamsToMarket,
  IFlowNodeParamsToMarketForm,
  IFlowNodeParamsToUpdateDataPkg,
  IFlowNodeParamsToUpdateDataPkgForm,
  IFlowNodeParamstwoFromPctChange,
  IFlowNodeParamsTypecast,
  IFlowNodeParamsTypecastForm,
  IFlowNodeParamsValueMapping,
  IFlowNodeParamsValueMappingForm,
  INodeData,
} from './interfaces';
import { NodeSchemaController } from './NodeSchemaController';
import { NodeSchemaModel } from './NodeSchemaModel';

type ITransformFunc = (formValues: IFlowNodeParams) => IFlowNodeParams;

class NodeFormController {
  private nodeSchemaController: NodeSchemaController;

  private formTransformApiMap: Partial<Record<NodeTypeEnum, (ITransformFunc | undefined)[]>> = {
    // 不能用箭头函数，不然会报在初始化前调用错误
    [NodeTypeEnum.SQL]: [this.sqlFormTransformApi.bind(this)],
    [NodeTypeEnum.FROM_QLANG_RAW]: [this.qlangRawFormTransformApi.bind(this)],
    [NodeTypeEnum.FS_SHEET]: [this.fsSheetFormTransformApi.bind(this)],
    [NodeTypeEnum.FS_TABLE]: [this.fsTableFormTransformApi.bind(this)],
    [NodeTypeEnum.DK_SHEET]: [this.dkSheetFormTransformApi.bind(this)],
    [NodeTypeEnum.DK_PROCESS_INSTANCES]: [
      this.dkProcessInstancesFormTransformApi.bind(this),
      this.dkProcessInstancesApiTransformForm.bind(this),
    ],
    [NodeTypeEnum.WECHAT_WORK_APPROVAL_DATA]: [
      this.wwApprovalDataFormTransformApi.bind(this),
      this.wwApprovalDataApiTransformForm.bind(this),
    ],
    [NodeTypeEnum.DATAPKG]: [this.datapkgFormTransformApi.bind(this)],
    [NodeTypeEnum.EXECUTE_SQL]: [this.executeSqlFormTransformApi.bind(this)],
    [NodeTypeEnum.REPLACE]: [this.replaceFormTransformApi.bind(this), this.replaceApiTransformForm.bind(this)],
    [NodeTypeEnum.JOIN]: [this.joinFormTransformApi.bind(this), this.joinApiTransformForm.bind(this)],
    [NodeTypeEnum.RENAME]: [this.renameFormTransformApi.bind(this), this.renameApiTransformForm.bind(this)],
    [NodeTypeEnum.TYPECAST]: [this.typecastFormTransformApi.bind(this), this.typecastApiTransformForm.bind(this)],
    [NodeTypeEnum.PIPE]: [this.pipeFormTransformApi.bind(this), this.pipeApiTransformForm.bind(this)],
    [NodeTypeEnum.VALUE_MAPPING]: [
      this.valueMappingFormTransformApi.bind(this),
      this.valueMappingApiTransformForm.bind(this),
    ],
    [NodeTypeEnum.COPY_COLUMN]: [
      this.copyColumnFormTransformApi.bind(this),
      this.copyColumnApiTransformForm.bind(this),
    ],
    [NodeTypeEnum.INSERT_CONST_COLUMN]: [
      this.insertSequenceColumnFormTransformApi.bind(this),
      this.insertSequenceColumnApiTransformForm.bind(this),
    ],
    [NodeTypeEnum.GROUPBY]: [undefined, this.groupbyApiTransformForm.bind(this)],
    [NodeTypeEnum.CREATE_TABLE]: [undefined, this.createTableApiTransformForm.bind(this)],
    [NodeTypeEnum.MARKET]: [this.toMarketFormTransformApi.bind(this), this.toMarketApiTransformForm.bind(this)],
    [NodeTypeEnum.UPDATE_DATAPKG]: [
      this.toUpdatePkgFormTransformApi.bind(this),
      this.toUpdatePkgApiTransformForm.bind(this),
    ],
    [NodeTypeEnum.STRIP]: [this.stripFormTransformApi.bind(this), this.stripApiTransformForm.bind(this)],
    [NodeTypeEnum.NULL_IF]: [this.nullIfFormTransformApi.bind(this), this.nullIfApiTransformForm.bind(this)],
    [NodeTypeEnum.FILTER]: [this.filterFormTransformApi.bind(this), this.filterApiTransformForm.bind(this)],
    [NodeTypeEnum.PCT_CHANGE]: [this.pctChangeFormTransformApi.bind(this), this.pctChangeApiTransformForm.bind(this)],
  };

  private sqlMap: Record<string, string> = {};
  private qlangRawMap: Record<string, any> = {};
  private nodeId?: string;
  private nodeData?: INodeData;
  private form?: Form<IFlowNodeParams>;
  private parent?: FlowEditController;
  private oldFormData: Record<string, any> = {};
  private pitchNodeIdList?: string[] = [];

  public constructor(parent: FlowEditController) {
    this.parent = parent;
    this.nodeSchemaController = new NodeSchemaController(parent, NodeSchemaModel);
  }

  public destroy() {
    this.nodeSchemaController.destroy();
    this.parent = undefined;
    this.form = undefined;
    this.nodeData = undefined;
    this.oldFormData = {};
  }

  public getNodeSchemaController() {
    return this.nodeSchemaController;
  }

  public initNode(nodeId: string) {
    const graphController = this.parent!.getGraphController();
    const nodeData = graphController.getNodeDataById(nodeId);
    this.nodeId = nodeId;
    this.nodeData = nodeData;
  }

  public get isNeedGetGield() {
    return _.includes(NEED_FIELD_INFO_NODE, this.nodeData?.type);
  }

  public get formInstance() {
    return this.form;
  }

  public formData() {
    return this.form!.values;
  }

  public getSql() {
    const nodeId = this.nodeId ?? '';
    return this.sqlMap[nodeId];
  }

  public getQlangRaw() {
    const nodeId = this.nodeId ?? '';
    return this.qlangRawMap[nodeId];
  }

  public onSave() {
    const nodeId = this.nodeId;
    const type = this.nodeData?.type;
    if (!this.form || !nodeId || !type) return;
    const graphController = this.parent!.getGraphController();
    const formValues = this.form!.values;
    const apiValues = this.formTransformApiMap[type]?.[0]?.(formValues) || formValues;
    const flag = graphController.saveSuccessAfter(nodeId, apiValues);
    if (JSON.stringify(apiValues) !== this.oldFormData[this.nodeId!]) {
      // 如果不一致，则保留重置状态
      if (this.oldFormData[this.nodeId!] && flag) {
        graphController.setNodeParamsStatus(nodeId, NodeStatusEnum.INFO);
      }
      this.delPitchNodeData();
    }
    this.oldFormData[this.nodeId!] = JSON.stringify(apiValues);
  }

  // 删除下游节点数据
  public delPitchNodeData() {
    //  表格数据列表
    const flowResultList: any = this.parent?.getTaskRespList$().getValue();
    _.forEach(this.pitchNodeIdList, (item) => {
      if (flowResultList[item]) {
        delete flowResultList[item];
      }
    });
    this.parent!.getTaskRespList$().next(flowResultList);
  }

  public getPitchNodeData(node?: Cell) {
    const graphController = this.parent!.getGraphController();
    const downStreamNode = graphController.getSuccessors(node!);
    const pitchNodeIdList = [];
    pitchNodeIdList.push(node!.id);
    _.forEach(downStreamNode, (item) => {
      pitchNodeIdList.push(item.id);
    });
    this.pitchNodeIdList = pitchNodeIdList;
  }

  public saveQlangRaw(config: Record<string, any> = {}) {
    const nodeId = this.nodeId ?? '';
    const qlangRawMap = this.qlangRawMap;
    this.qlangRawMap = { ...qlangRawMap, [nodeId]: config };
    this.onSave();
  }

  public saveSql(controller: SqlEditorDataTabelController) {
    const nodeId = this.nodeId ?? '';
    const sql = controller.getSql$().getValue();
    const sqlMap = this.sqlMap;
    this.sqlMap = { ...sqlMap, [nodeId]: sql };
    this.parent!.getDrawerSqlDataTabelController().closeModal();
    this.onSave();
  }

  public getNodeForm(formProps: IFormProps<IFlowNodeParams> = {}) {
    const initialValues = this.getNodeValues();
    this.form = createForm<IFlowNodeParams>({ initialValues, validateFirst: true, ...formProps });
    return this.form;
  }

  public getNodeSchema() {
    const type = this.nodeData?.type;
    if (!type) return;
    return this.nodeSchemaController.getSchema(type, this.nodeId!);
  }

  // 点击拿到已有from
  public getFromData() {
    const nodeId = this.nodeId;
    const type = this.nodeData?.type;
    if (!type) return {};
    const graphController = this.parent!.getGraphController();
    return graphController.getFlowParams('nodeParams', nodeId) as IFlowNodeParams;
  }

  private getNodeValues() {
    const nodeId = this.nodeId!;
    const type = this.nodeData?.type;
    if (!type) return {};
    const graphController = this.parent!.getGraphController();
    const currentNodeValues = graphController.getFlowParams('nodeParams', nodeId) as IFlowNodeParamsFromSql;
    if (type === NodeTypeEnum.SQL || type === NodeTypeEnum.EXECUTE_SQL) {
      this.sqlMap = { ...this.sqlMap, [nodeId]: currentNodeValues.sql };
    }
    if (type === NodeTypeEnum.FROM_QLANG_RAW) {
      this.qlangRawMap = { ...this.qlangRawMap, [nodeId]: currentNodeValues };
    }

    const fv = this.formTransformApiMap[type]?.[1]?.(currentNodeValues) || currentNodeValues;
    // 移除前端使用的字段
    return _.omit(fv, '_fparams_');
  }

  // form => api
  private joinFormTransformApi(formValues: IFlowNodeParams): IFlowNodeParamsJoin {
    // 排除掉空数组的字段
    const values = formValues as IFlowNodeParamsJoin;
    _.forEach(values, (fieldValue, key) => {
      if (!_.isArray(fieldValue)) return;
      if (fieldValue.length) return;
      // @ts-ignore
      delete values[key];
    });
    delete values.eq;
    return values;
  }

  private replaceFormTransformApi(formValues: IFlowNodeParams): IFlowNodeParamsReplaceFormTransformApi {
    const values = formValues as IFlowNodeParamsReplaceFormTransformApi;
    const result = {} as any;
    result.pattern = values.mapping[0].pattern;
    result.repl = values.mapping[0].repl;
    result.columns = values.columns;
    return { ...result };
  }

  private copyColumnFormTransformApi(formValues: IFlowNodeParams): IFlowNodeParamscopyColumnFormTransformApi {
    const values = formValues as IFlowNodeParamscopyColumnFormTransformApi;
    const result = {} as any;
    result.source = values.mapping[0].key;
    result.destination = values.mapping[0].value;
    return { ...result };
  }

  private insertSequenceColumnFormTransformApi(
    formValues: IFlowNodeParams,
  ): IFlowNodeParamsinsertSequenceColumnApiToForm {
    const values = formValues as IFlowNodeParamsinsertSequenceColumnFormToApi;
    const result = {} as any;

    result.column = values.mapping[0].column;
    result.type = values.mapping[0].type;
    result.value = values.value;
    result.skip_if_exist = values.skip_if_exist;
    return { ...result };
  }

  private sqlFormTransformApi(formValues: IFlowNodeParams): IFlowNodeParamsFromSql {
    const nodeId = this.nodeId ?? '';
    const values = formValues as IFlowNodeParamsFromSql;
    const sql = this.sqlMap[nodeId];
    return { ...values, sql };
  }

  private qlangRawFormTransformApi(formValues: IFlowNodeParams): IFlowNodeParamsFromQLangRaw {
    const nodeId = this.nodeId ?? '';
    const values = formValues as IFlowNodeParamsFromQLangRaw;
    const config = this.qlangRawMap[nodeId] || {};
    return { ...values, ...config };
  }

  private fsSheetFormTransformApi(formValues: IFlowNodeParams): IFlowNodeParamsFromFeishuSpreadsheet {
    const values = formValues;
    const v = _.omitBy({ ...values }, (v) => !v);
    return v as unknown as IFlowNodeParamsFromFeishuSpreadsheet;
  }

  private wwApprovalDataFormTransformApi(formValues: IFlowNodeParams): IFlowNodeParamsFromWechatWorkApprovalData {
    const values = formValues;
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    const data = { ...values } as IFlowNodeParamsFromWechatWorkApprovalData;
    const { start_time, end_time } = data;
    if (start_time) {
      data.start_time = transformDateToUnix(start_time);
    }
    if (end_time) {
      data.end_time = transformDateToUnix(end_time);
    }
    const v = _.omitBy({ ...data }, (v) => !v);
    return v as unknown as IFlowNodeParamsFromWechatWorkApprovalData;
  }

  private fsTableFormTransformApi(formValues: IFlowNodeParams): IFlowNodeParamsFromFeishuBitable {
    const values = formValues;
    const v = _.omitBy({ ...values }, (v) => !v || _.size(String(v)) === 0);
    return v as unknown as IFlowNodeParamsFromFeishuBitable;
  }

  private dkSheetFormTransformApi(formValues: IFlowNodeParams): IFlowNodeParamsFromDingtalkSheet {
    const values = formValues;
    const v = _.omitBy({ ...values }, (v) => {
      if (_.isNumber(v)) {
        return false;
      }
      return !v || _.size(String(v)) === 0;
    });
    return v as unknown as IFlowNodeParamsFromDingtalkSheet;
  }

  private dkProcessInstancesFormTransformApi(formValues: IFlowNodeParams): IFlowNodeParamsFromDingtalkProcessInstances {
    const values = formValues;
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    const data = { ...values } as IFlowNodeParamsFromDingtalkProcessInstances;
    const { start_time, end_time } = data;
    if (start_time) {
      data.start_time = transformDateToMilliseconds(start_time);
    }
    if (end_time) {
      data.end_time = transformDateToMilliseconds(end_time);
    }
    const v = _.omitBy(data, (v) => !v);
    return v as unknown as IFlowNodeParamsFromDingtalkProcessInstances;
  }

  private dkProcessInstancesApiTransformForm(formValues: IFlowNodeParams): IFlowNodeParamsFromDingtalkProcessInstances {
    const values = formValues as IFlowNodeParamsFromDingtalkProcessInstances;
    const startTime = values?.start_time;
    const endTime = values?.end_time;
    if (startTime) {
      values.start_time = formateDate(startTime);
    }
    if (endTime) {
      values.end_time = formateDate(endTime);
    }
    return values;
  }

  private wwApprovalDataApiTransformForm(formValues: IFlowNodeParams): IFlowNodeParamsFromWechatWorkApprovalData {
    const values = formValues as IFlowNodeParamsFromWechatWorkApprovalData;
    const startTime = values?.start_time;
    const endTime = values?.end_time;
    if (startTime) {
      values.start_time = formateDate(startTime);
    }
    if (endTime) {
      values.end_time = formateDate(endTime);
    }
    return values;
  }

  private datapkgFormTransformApi(formValues: IFlowNodeParams): IFlowNodeParamsFromDatapkg {
    const values = formValues as IFlowNodeParamsFromDatapkg;
    const data = { ...values };
    // 删除空数组
    if (_.size(data.only) === 0) {
      delete data.only;
    }
    const of = data.operator_filter;
    if (!of || !of.column || !of.operator) {
      delete data.operator_filter;
    }
    return data;
  }

  private executeSqlFormTransformApi(formValues: IFlowNodeParams): IFlowNodeParamsExecuteSql {
    const nodeId = this.nodeId ?? '';
    const values = formValues as IFlowNodeParamsExecuteSql;
    const sql = this.sqlMap[nodeId];
    return { ...values, sql };
  }

  private renameFormTransformApi(formValues: IFlowNodeParams): IFlowNodeParamsRename {
    const values = formValues as IFlowNodeParamsRenameForm;
    const mapping: Record<string, string> = {};
    if (!values.mapping || !_.size(values.mapping)) return { mapping };

    _.forEach(values.mapping, (it) => {
      if (!it?.key || !it?.value) return;
      mapping[it.key] = it.value;
    });
    return { mapping };
  }

  private pctChangeFormTransformApi(formValues: IFlowNodeParams): IFlowNodeParamsFromPctChange {
    const values = formValues as IFlowNodeParamsFromPctChange;
    const column_mapping: Record<string, string> = {};
    column_mapping[values.key] = values.value;
    const result: IFlowNodeParamstwoFromPctChange = {};
    result.periods = values.periods;
    result.dt = values.dt;
    result.column_mapping = column_mapping;
    result.other_index_cols = values.other_index_cols;
    return result as IFlowNodeParamsFromPctChange;
  }

  private typecastFormTransformApi(formValues: IFlowNodeParams): IFlowNodeParamsTypecast {
    const values = formValues as IFlowNodeParamsTypecastForm;
    const typemapping: Record<string, string> = {};
    if (!values.typemapping || !_.size(values.typemapping)) return { typemapping };

    _.forEach(values.typemapping, (it) => {
      if (!it?.key || !it?.value) return;
      typemapping[it.key] = it.value;
    });
    return { typemapping };
  }

  private valueMappingFormTransformApi(formValues: IFlowNodeParams): IFlowNodeParamsValueMapping {
    const values = formValues as IFlowNodeParamsValueMappingForm;
    let c_src: Record<string, string> | undefined = {};
    let mapping: Record<string, string> | undefined = {};
    if (values.c_src && _.size(values.c_src)) {
      _.forEach(values.c_src, (it) => {
        if (!it?.key || !it?.value) return;
        c_src![it.key] = it.value;
      });
    } else {
      c_src = undefined;
    }
    if (values.mapping && _.size(values.mapping)) {
      _.forEach(values.mapping, (it) => {
        if (!it?.key || !it?.value) return;
        mapping![it.key] = it.value;
      });
    } else {
      mapping = undefined;
    }
    return { ...values, c_src, mapping } as any;
  }

  private pipeFormTransformApi(formValues: IFlowNodeParams): IFlowNodeParamsPipe {
    const values = formValues as IFlowNodeParamsPipeForm;
    const kw: Record<string, string> = {};
    if (!values.kw || !_.size(values.kw)) return { ...values, kw };

    _.forEach(values.kw, (it) => {
      if (!it?.key || !it?.value) return;
      kw[it.key] = it.value;
    });
    return { ...values, kw };
  }

  private toMarketFormTransformApi(formValues: IFlowNodeParams): IFlowNodeParamsToMarket {
    const values = formValues as IFlowNodeParamsToMarketForm;
    const type_mapping: Record<string, string> = {};
    if (!values.type_mapping || !_.size(values.type_mapping)) return { ...values, type_mapping };

    _.forEach(values.type_mapping, (it) => {
      if (!it?.key || !it?.value) return;
      type_mapping[it.key] = it.value;
    });
    return { ...values, type_mapping };
  }

  private nullIfFormTransformApi(formValues: IFlowNodeParams): IFlowNodeParamsNullIf {
    const values = formValues as IFlowNodeParamsNullIf;
    const operator = values.operator_filter?.operator;
    if (operator === 'ilike' || operator === 'like') {
      values.operator_filter.param = `%${values.operator_filter.param}%`;
    }
    return values;
  }

  private stripFormTransformApi(formValues: IFlowNodeParams): IFlowNodeParamsStrip {
    return formValues as IFlowNodeParamsStrip;
  }

  private filterFormTransformApi(formValues: IFlowNodeParams): IFlowNodeParamsFilter {
    const values = formValues as IFlowNodeParamsFilter;
    const operator = values.operator_filter?.operator;
    if (operator === 'ilike' || operator === 'like') {
      values.operator_filter.param = `%${values.operator_filter.param}%`;
    }

    return values;
  }

  private toUpdatePkgFormTransformApi(formValues: IFlowNodeParams): IFlowNodeParamsToUpdateDataPkg {
    const values = formValues as IFlowNodeParamsToUpdateDataPkgForm;
    const type_mapping: Record<string, string> = {};
    if (!values.type_mapping || !_.size(values.type_mapping)) return { ...values, type_mapping };

    _.forEach(values.type_mapping, (it) => {
      if (!it?.key || !it?.value) return;
      type_mapping[it.key] = it.value;
    });
    return { ...values, type_mapping };
  }

  // api => form
  private joinApiTransformForm(formValues: IFlowNodeParams): IFlowNodeParamsJoin {
    const values = formValues as any;
    if (!values.on && !values.left_on && !values.right_on) {
      values.eq = 1;
    }
    if (values.left_on || values.right_on) {
      values.eq = 0;
    }
    values.eq = values.on ? 1 : 0;
    return values;
  }

  private pctChangeApiTransformForm(formValues: IFlowNodeParams): IFlowNodeParamsFromPctChange {
    const values = formValues as IFlowNodeParamsFromPctChange;
    const result: IFlowNodeParamstwoFromPctChange = {};
    result.dt = values.dt;
    result.periods = values.periods;
    result.other_index_cols = values.other_index_cols;
    if (!_.size(values.column_mapping)) return { ...values };
    const column_mapping_key: string = _.keys(values.column_mapping)[0];

    result.key = column_mapping_key === 'undefined' ? '' : column_mapping_key;
    result.value = values.column_mapping![column_mapping_key];
    return result as IFlowNodeParamsFromPctChange;
  }

  private replaceApiTransformForm(formValues: IFlowNodeParams): IFlowNodeParamsReplaceApiTransformForm {
    const values = formValues as IFlowNodeParamsReplaceApiTransformForm;
    let mapping: Array<any> = [];
    const result: any = {};
    mapping.push({ pattern: values.pattern, repl: values.repl });
    result.mapping = mapping;
    result.columns = values.columns;
    return { ...result };
  }

  // eslint-disable-next-line no-undef
  private copyColumnApiTransformForm(formValues: IFlowNodeParams): IFlowNodeParamscopyColumnApiTransformForm {
    const values = formValues as IFlowNodeParamscopyColumnApiTransformForm;
    let mapping: Array<any> = [];
    const result: any = {};
    mapping.push({ key: values.source, value: values.destination });
    result.mapping = mapping;
    return { ...result };
  }

  // eslint-disable-next-line no-undef
  private insertSequenceColumnApiTransformForm(
    formValues: IFlowNodeParams,
  ): IFlowNodeParamsinsertSequenceColumnApiToForm {
    const values = formValues as IFlowNodeParamsinsertSequenceColumnApiToForm;
    let mapping: Array<any> = [];
    const result: any = {};
    mapping.push({ column: values.column, type: values.type });
    result.mapping = mapping;
    result.value = values.value;
    result.skip_if_exist = values.skip_if_exist;
    return { ...result };
  }
  private renameApiTransformForm(formValues: IFlowNodeParams): IFlowNodeParamsRenameForm {
    const values = formValues as IFlowNodeParamsRename;
    let mapping: Array<any> = [{}];

    _.size(values.mapping) && (mapping = _.map(values.mapping, (value, key) => ({ key, value })));

    return { mapping };
  }

  private typecastApiTransformForm(formValues: IFlowNodeParams): IFlowNodeParamsTypecastForm {
    const values = formValues as IFlowNodeParamsTypecast;
    let typemapping: Array<any> = [{}];

    _.size(values.typemapping) && (typemapping = _.map(values.typemapping, (value, key) => ({ key, value })));
    return { typemapping };
  }

  private valueMappingApiTransformForm(formValues: IFlowNodeParams): IFlowNodeParamsValueMappingForm {
    const values = formValues as IFlowNodeParamsValueMapping;
    let c_src: Array<any> = [{}];
    let mapping: Array<any> = [{}];

    _.size(values.c_src) && (c_src = _.map(values.c_src, (value, key) => ({ key, value })));
    _.size(values.mapping) && (mapping = _.map(values.mapping, (value, key) => ({ key, value })));
    return { ...values, mapping, c_src };
  }

  private groupbyApiTransformForm(formValues: IFlowNodeParams): IFlowNodeParamsGroupBy {
    const values = formValues as IFlowNodeParamsGroupBy;
    let agg: Array<any> = [{}];

    _.size(values.agg) && (agg = values.agg);
    return { ...values, agg };
  }

  private createTableApiTransformForm(formValues: IFlowNodeParams): IFlowNodeParamsCreateTable {
    const values = formValues as IFlowNodeParamsCreateTable;
    let fields: Array<any> = [{}];

    _.size(values.table_definition?.fields ?? []) && (fields = values.table_definition.fields);
    return { ...values, table_definition: { ...(values.table_definition ?? {}), fields } };
  }

  private pipeApiTransformForm(formValues: IFlowNodeParams): IFlowNodeParamsPipeForm {
    const values = formValues as IFlowNodeParamsPipe;
    let kw: Array<any> = [];

    _.size(values.kw) && (kw = _.map(values.kw, (value, key) => ({ key, value })));
    return { ...values, kw };
  }

  private toMarketApiTransformForm(formValues: IFlowNodeParams): IFlowNodeParamsToMarketForm {
    const values = formValues as IFlowNodeParamsToMarket;
    let type_mapping: Array<any> = [{}];

    _.size(values.type_mapping) && (type_mapping = _.map(values.type_mapping, (value, key) => ({ key, value })));
    return { ...values, type_mapping };
  }

  private toUpdatePkgApiTransformForm(formValues: IFlowNodeParams): IFlowNodeParamsToUpdateDataPkgForm {
    const values = formValues as IFlowNodeParamsToUpdateDataPkg;
    let type_mapping: Array<any> = [{}];

    _.size(values.type_mapping) && (type_mapping = _.map(values.type_mapping, (value, key) => ({ key, value })));
    return { ...values, type_mapping };
  }

  private nullIfApiTransformForm(formValues: IFlowNodeParams): IFlowNodeParamsNullIf {
    const values = formValues as IFlowNodeParamsNullIf;
    const operator = values.operator_filter?.operator;
    if (operator === 'ilike' || operator === 'like') {
      values.operator_filter.param = values.operator_filter.param.replace(/%/g, '');
    }
    return values;
  }

  private stripApiTransformForm(formValues: IFlowNodeParams): IFlowNodeParamsStrip {
    return formValues as IFlowNodeParamsStrip;
  }

  private filterApiTransformForm(formValues: IFlowNodeParams): IFlowNodeParamsFilter {
    const values = formValues as IFlowNodeParamsFilter;
    const operator = values.operator_filter?.operator;
    if (operator === 'ilike' || operator === 'like') {
      values.operator_filter.param = values.operator_filter.param.replace(/%/g, '');
    }

    return values;
  }
}

export { NodeFormController };
