import _ from 'lodash';
import { useEffect, useRef } from 'react';
import { Field, Form, onFieldValueChange } from '@formily/core';
import { useCreation, useSetState } from 'ahooks';
import tinycolor from 'tinycolor2';
import { EyeToggle } from '../../../../components/eye-toggle';
import { FormView, IFormSpecRefHandle } from '../../../../components/form-view';
import { GroupSetting, submitStyleSchema } from './util';

const setCompDisabled = (field: Field, targets: string[]) => {
  const val = field.value;
  _.forEach(targets, (it) => {
    const targetField = field.query(it).take() as Field;
    targetField?.setComponentProps({ disabled: !val });
  });
};

interface ISubmitStyleConfigProps {
  value?: Record<string, any>;
  onChange?: (val: boolean) => void;
}

export const SubmitStyleConfig = (props: ISubmitStyleConfigProps) => {
  const { value, onChange } = props;
  const onChangeRef = useRef<any>();
  const formRef = useRef<IFormSpecRefHandle>(null);
  onChangeRef.current = onChange;
  const [state, setState] = useSetState<any>(value);
  const [changedCount, setChangedCount] = useSetState<any>(0);
  useEffect(() => {
    if (!_.isEqual(state, value)) {
      setChangedCount(changedCount + 1);
    }
  }, [value]);

  // eslint-disable-next-line sonarjs/cognitive-complexity
  return useCreation(() => {
    const effects = (form: Form) => {
      onFieldValueChange('useBg', (field) => {
        setCompDisabled(field, ['bgColorSelector', 'bgColor', 'bgOpacity']);
      });
      onFieldValueChange('useBorder', (field) => {
        setCompDisabled(field, ['borderColorSelector', 'borderColor', 'borderWidth']);
      });

      onFieldValueChange('bgColorSelector', (field) => {
        const color = tinycolor(field.value);
        if (!color.isValid()) return;
        const bgColorField = field.query('bgColor').take() as Field;
        const bgOpacityField = field.query('bgOpacity').take() as Field;
        bgColorField.setValue(color.toHexString().substring(1));
        bgOpacityField.setValue((color.getAlpha() * 100).toFixed(0));
      });
      const calculateBgColorSelector = () => {
        const selectorField = form.query('bgColorSelector').take() as Field;
        const bgColorField = form.query('bgColor').take() as Field;
        const bgOpacityField = form.query('bgOpacity').take() as Field;
        if (!selectorField || !bgColorField || !bgOpacityField) return;
        const color = tinycolor(bgColorField.value);
        if (!color.isValid()) return;
        color.setAlpha(bgOpacityField.value / 100);
        selectorField.setValue(color.toRgbString());
      };
      onFieldValueChange('bgColor', () => {
        calculateBgColorSelector();
      });
      onFieldValueChange('bgOpacity', () => {
        calculateBgColorSelector();
      });

      onFieldValueChange('borderColorSelector', (field) => {
        const color = tinycolor(field.value);
        if (!color.isValid()) return;
        const borderColorField = field.query('borderColor').take() as Field;
        borderColorField.setValue(color.toHexString().substring(1));
      });
      onFieldValueChange('borderColor', (field) => {
        const selectorField = form.query('borderColorSelector').take() as Field;
        if (!selectorField) return;
        const color = tinycolor(field.value);
        if (!color.isValid()) return;
        selectorField.setValue(color.toRgbString());
      });

      onFieldValueChange('colorSelector', (field) => {
        const color = tinycolor(field.value);
        if (!color.isValid()) return;
        const colorField = field.query('color').take() as Field;
        colorField.setValue(color.toHexString().substring(1));
      });
      onFieldValueChange('color', (field) => {
        const selectorField = form.query('colorSelector').take() as Field;
        if (!selectorField) return;
        const color = tinycolor(field.value);
        if (!color.isValid()) return;
        selectorField.setValue(color.toRgbString());
      });
    };

    const handleChange = (values: any) => {
      setState(values);
      onChangeRef.current?.(values);
    };

    const schema = {
      form: {
        colon: false,
      },
      schema: { properties: submitStyleSchema },
    };
    return (
      <FormView
        ref={formRef}
        formilySchema={schema}
        formData={value}
        onChange={handleChange}
        components={{ GroupSetting, EyeToggle }}
        effects={effects}
      />
    );
  }, [changedCount]);
};
