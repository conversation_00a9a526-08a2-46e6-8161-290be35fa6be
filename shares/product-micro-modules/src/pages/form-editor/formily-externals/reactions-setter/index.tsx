import _ from 'lodash';
import React, { useEffect, useMemo, useState } from 'react';
import { GlobalRegistry } from '@designable/core';
import { TextWidget, usePrefix } from '@designable/react';
import { MonacoInput } from '@designable/react-settings-form';
import { requestIdle } from '@designable/shared';
import { createForm, isVoidField } from '@formily/core';
import { createSchemaField } from '@formily/react';
import { clone, uid } from '@formily/shared';
import { Button } from '@metroDesign/button';
import { Card } from '@metroDesign/card';
import { Modal } from '@metroDesign/modal';
import { Spin } from '@metroDesign/spin';
import { Tag } from '@metroDesign/tag';
import { Tooltip } from '@metroDesign/tooltip';
import { ArrayTable, Form, FormCollapse, FormItem, Input, Select } from '@mdtProFormEditor/metro-formily/index';
import i18n from '../../../../languages';
import { initDeclaration } from './declarations';
import { FieldPropertySetter } from './FieldPropertySetter';
import { FulfillRunHelper } from './helpers';
import { PathSelector } from './PathSelector';
import { IReaction } from './types';
import './styles.less';

export interface IReactionsSetterProps {
  value?: IReaction;
  onChange?: (value: IReaction) => void;
}

interface ITypeViewProps {
  value: string | number | boolean;
}

const TypeView: React.FC<ITypeViewProps> = ({ value }) => {
  const text = String(value);
  if (text.length <= 26) return <Tag>{text}</Tag>;
  return (
    <Tag>
      <Tooltip
        title={
          <div style={{ fontSize: 12 }}>
            <code>
              <pre style={{ whiteSpace: 'pre-wrap', padding: 0, margin: 0 }}>{text}</pre>
            </code>
          </div>
        }
      >
        {text.substring(0, 24)}...
      </Tooltip>
    </Tag>
  );
};

const SchemaField = createSchemaField({
  components: {
    Card,
    FormCollapse,
    Input,
    TypeView,
    Select,
    FormItem,
    PathSelector,
    FieldPropertySetter,
    ArrayTable,
    MonacoInput,
  },
});

const FieldStateProperties = [
  'value',
  'initialValue',
  'inputValue',
  'inputValues',
  'modified',
  'initialized',
  'title',
  'description',
  'mounted',
  'unmounted',
  'active',
  'visited',
  'loading',
  'errors',
  'warnings',
  'successes',
  'feedbacks',
  'valid',
  'invalid',
  'pattern',
  'display',
  'disabled',
  'readOnly',
  'readPretty',
  'visible',
  'hidden',
  'editable',
  'validateStatus',
  'validating',
] as const;

const FieldStateValueTypes: Record<typeof FieldStateProperties[number], string> = {
  modified: 'boolean',
  initialized: 'boolean',
  title: 'string',
  description: 'string',
  mounted: 'boolean',
  unmounted: 'boolean',
  active: 'boolean',
  visited: 'boolean',
  loading: 'boolean',
  errors: 'string[]',
  warnings: 'string[]',
  successes: 'string[]',
  feedbacks: `Array<
  triggerType?: 'onInput' | 'onFocus' | 'onBlur'
  type?: 'error' | 'success' | 'warning'
  code?:
    | 'ValidateError'
    | 'ValidateSuccess'
    | 'ValidateWarning'
    | 'EffectError'
    | 'EffectSuccess'
    | 'EffectWarning'
  messages?: string[]
>
`,
  valid: 'boolean',
  invalid: 'boolean',
  pattern: "'editable' | 'disabled' | 'readOnly' | 'readPretty'",
  display: "'visible' | 'hidden' | 'none'",
  disabled: 'boolean',
  readOnly: 'boolean',
  readPretty: 'boolean',
  visible: 'boolean',
  hidden: 'boolean',
  editable: 'boolean',
  validateStatus: "'error' | 'warning' | 'success' | 'validating'",
  validating: 'boolean',
  value: 'any',
  initialValue: 'any',
  inputValue: 'any',
  inputValues: 'any[]',
};

export const ReactionsSetter: React.FC<IReactionsSetterProps> = (props) => {
  const [modalVisible, setModalVisible] = useState(false);
  const [innerVisible, setInnerVisible] = useState(false);
  const prefix = usePrefix('reactions-setter');
  const form = useMemo(() => {
    return createForm({
      values: clone(props.value),
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [modalVisible, props.value]);
  const formCollapse = useMemo(
    () => FormCollapse.createFormCollapse(['deps', 'state']) || { activeKeys: ['deps', 'state'] },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [modalVisible],
  );
  const openModal = () => setModalVisible(true);
  const closeModal = () => setModalVisible(false);
  useEffect(() => {
    if (modalVisible) {
      requestIdle(
        () => {
          initDeclaration().then(() => {
            setInnerVisible(true);
          });
        },
        {
          timeout: 400,
        },
      );
    } else {
      setInnerVisible(false);
    }
  }, [modalVisible]);

  const btnText =
    _.isEmpty(props.value?.fulfill?.run) && _.isEmpty(props.value?.fulfill?.state)
      ? i18n.chain.proMicroModules.workflow.edit.unset
      : i18n.chain.proMicroModules.workflow.edit.setted;
  return (
    <>
      <Button onClick={openModal}>
        <TextWidget token={btnText} />
      </Button>
      <Modal
        title={GlobalRegistry.getDesignerMessage('SettingComponents.ReactionsSetter.configureReactions')}
        width="70%"
        maskClosable={false}
        centered
        bodyStyle={{ padding: 10 }}
        transitionName=""
        maskTransitionName=""
        okButtonProps={{ disabled: !innerVisible }}
        cancelButtonProps={{ disabled: !innerVisible }}
        open={modalVisible}
        onCancel={closeModal}
        destroyOnClose
        onOk={() => {
          form.submit((values) => {
            props.onChange?.(values);
          });
          closeModal();
        }}
      >
        <Spin spinning={!innerVisible} className={prefix}>
          {innerVisible && (
            <Form form={form}>
              <SchemaField>
                <SchemaField.Void
                  x-component="FormCollapse"
                  x-component-props={
                    {
                      formCollapse,
                      defaultActiveKey: ['deps', 'state'],
                      style: { marginBottom: 10 },
                    } as any
                  }
                >
                  <SchemaField.Void
                    x-component="FormCollapse.CollapsePanel"
                    x-component-props={{
                      key: 'deps',
                      header: GlobalRegistry.getDesignerMessage('SettingComponents.ReactionsSetter.relationsFields'),
                    }}
                  >
                    <SchemaField.Array name="dependencies" default={[{}]} x-component="ArrayTable">
                      <SchemaField.Object>
                        <SchemaField.Void
                          x-component="ArrayTable.Column"
                          x-component-props={{
                            title: GlobalRegistry.getDesignerMessage('SettingComponents.ReactionsSetter.sourceField'),
                            width: 240,
                          }}
                        >
                          <SchemaField.String
                            name="source"
                            x-decorator="FormItem"
                            x-component="PathSelector"
                            x-component-props={{
                              placeholder: GlobalRegistry.getDesignerMessage(
                                'SettingComponents.ReactionsSetter.pleaseSelect',
                              ),
                            }}
                          />
                        </SchemaField.Void>
                        <SchemaField.Void
                          x-component="ArrayTable.Column"
                          x-component-props={{
                            title: GlobalRegistry.getDesignerMessage(
                              'SettingComponents.ReactionsSetter.sourceProperty',
                            ),
                            width: 200,
                          }}
                        >
                          <SchemaField.String
                            name="property"
                            default="value"
                            x-decorator="FormItem"
                            x-component="Select"
                            x-component-props={{ showSearch: true }}
                            enum={[...FieldStateProperties] as any}
                          />
                        </SchemaField.Void>
                        <SchemaField.Void
                          x-component="ArrayTable.Column"
                          x-component-props={{
                            title: GlobalRegistry.getDesignerMessage('SettingComponents.ReactionsSetter.variableName'),
                            width: 200,
                          }}
                        >
                          <SchemaField.String
                            name="name"
                            x-decorator="FormItem"
                            x-validator={{
                              pattern: /^[$_a-zA-Z]+[$_a-zA-Z0-9]*$/,
                              message: GlobalRegistry.getDesignerMessage(
                                'SettingComponents.ReactionsSetter.variableNameValidateMessage',
                              ),
                            }}
                            x-component="Input"
                            x-component-props={{
                              addonBefore: '$deps.',
                              placeholder: GlobalRegistry.getDesignerMessage(
                                'SettingComponents.ReactionsSetter.pleaseInput',
                              ),
                            }}
                            x-reactions={(field) => {
                              if (isVoidField(field)) return;
                              field.query('.source').take((source) => {
                                if (isVoidField(source)) return;

                                const hasSourceValue = source.value;
                                const isFieldEmpty = !field.value && !field.modified;

                                if (hasSourceValue && isFieldEmpty) {
                                  const suggestedName = source.inputValues[1]?.props?.name;
                                  const dependencies = field.form.values?.dependencies || [];
                                  const nameExists = dependencies.some(
                                    (dep: { name: string }) => dep.name === suggestedName,
                                  );
                                  const fallbackName = `v_${uid()}`;
                                  field.value = suggestedName && !nameExists ? suggestedName : fallbackName;
                                }
                              });
                            }}
                          />
                        </SchemaField.Void>

                        <SchemaField.Void
                          x-component="ArrayTable.Column"
                          x-component-props={{
                            title: GlobalRegistry.getDesignerMessage('SettingComponents.ReactionsSetter.variableType'),
                            ellipsis: {
                              showTitle: false,
                            },
                            width: 200,
                            align: 'center',
                          }}
                        >
                          <SchemaField.String
                            name="type"
                            default="any"
                            x-decorator="FormItem"
                            x-component="TypeView"
                            x-reactions={(field) => {
                              if (isVoidField(field)) return;
                              const property = field.query('.property').get('inputValues');
                              if (!property) return;
                              property[0] = property[0] || 'value';
                              field.query('.source').take((source) => {
                                if (isVoidField(source)) return;
                                if (source.value) {
                                  if (
                                    property[0] === 'value' ||
                                    property[0] === 'initialValue' ||
                                    property[0] === 'inputValue'
                                  ) {
                                    field.value = source.inputValues[1]?.props?.type || 'any';
                                  } else if (property[0] === 'inputValues') {
                                    field.value = `any[]`;
                                  } else if (property[0]) {
                                    field.value =
                                      FieldStateValueTypes[property[0] as typeof FieldStateProperties[number]];
                                  } else {
                                    field.value = 'any';
                                  }
                                }
                              });
                            }}
                          />
                        </SchemaField.Void>
                        <SchemaField.Void
                          x-component="ArrayTable.Column"
                          x-component-props={{
                            title: GlobalRegistry.getDesignerMessage('SettingComponents.ReactionsSetter.operations'),
                            align: 'center',
                            width: 80,
                          }}
                        >
                          <SchemaField.Markup
                            type="void"
                            x-component="ArrayTable.Remove"
                            x-component-props={{ style: { fontSize: 16, cursor: 'pointer', verticalAlign: 'middle' } }}
                          />
                        </SchemaField.Void>
                      </SchemaField.Object>
                      <SchemaField.Void
                        title={GlobalRegistry.getDesignerMessage('SettingComponents.ReactionsSetter.addRelationField')}
                        x-component="ArrayTable.Addition"
                        x-component-props={{ style: { marginTop: 8 } }}
                      />
                    </SchemaField.Array>
                  </SchemaField.Void>

                  <SchemaField.Void
                    x-component="FormCollapse.CollapsePanel"
                    x-component-props={{
                      header: GlobalRegistry.getDesignerMessage('SettingComponents.ReactionsSetter.propertyReactions'),
                      key: 'state',
                      className: 'reaction-state',
                    }}
                  >
                    <SchemaField.Markup name="fulfill.state" x-component="FieldPropertySetter" />
                  </SchemaField.Void>
                  <SchemaField.Void
                    x-component="FormCollapse.CollapsePanel"
                    x-component-props={{
                      key: 'run',
                      header: GlobalRegistry.getDesignerMessage('SettingComponents.ReactionsSetter.actionReactions'),
                      className: 'reaction-runner',
                    }}
                  >
                    <SchemaField.String
                      name="fulfill.run"
                      x-component="MonacoInput"
                      x-component-props={{
                        width: '100%',
                        height: 400,
                        language: 'typescript',
                        helpCode: FulfillRunHelper,
                        options: {
                          minimap: {
                            enabled: false,
                          },
                        },
                      }}
                      x-reactions={(field) => {
                        const deps = field.query('dependencies').value();
                        if (Array.isArray(deps)) {
                          field.componentProps.extraLib = `
                          declare var $deps : {
                            ${deps.map(({ name, type }) => {
                              if (!name) return '';
                              return `${name}?:${type || 'any'},`;
                            })}
                          }
                          `;
                        }
                      }}
                    />
                  </SchemaField.Void>
                </SchemaField.Void>
              </SchemaField>
            </Form>
          )}
        </Spin>
      </Modal>
    </>
  );
};
