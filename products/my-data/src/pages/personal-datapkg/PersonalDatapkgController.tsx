import { FolderActionType } from '@mdtBsComponents/data-list-comp-table-curd';
import { IDatapkgsQuery } from '@mdtBsServices/interfaces';
import Breadcrumb from '@mdtDesign/breadcrumb';
import { LinkButton } from '@mdtDesign/button';
import Icon from '@mdtDesign/icon';
import Tag from '@mdtDesign/tag';
import { OwnershipEnum } from '@mdtProComm/constants';
import { getPkgIdFromUrl } from '@mdtProComm/utils/urlUtil';
import { MyDataFilter, MyDataMenuEnum } from '@mdtProMicroModules/components/my-data-filter';
import { FolderOperationCommonController } from '@mdtProMicroModules/containers/folder-operation-common';
import {
  IVirtualizedTableProps,
  TableCurdWithSimpleSearchController,
} from '@mdtProMicroModules/containers/table-curd-with-simple-search';
import { FolderModel, IFolders } from '@mdtProMicroModules/models/FolderModel';
import { CreateDatapkgController } from '@mdtProMicroModules/pages/create-datapkg';
import { DatapkgController, IFilterParams } from '../../_util/DatapkgController';
import { AppController } from '../../app/AppController';
import { ALLOW_JUMP_PRODUCTS, JUMP_COLLECTOR_URL, JUMP_DATA_FACTORY_URL, UPLOAD_MAX_MB } from '../../config';
import i18n from '../../languages';
import { IPersonalDatapkgModel, ITableData } from './PersonalDatapkgModel';

const FOLDER_SPACE = 'datapkg';

class PersonalDatapkgController extends DatapkgController<ITableData> {
  private Model: IPersonalDatapkgModel;
  private createPkgController: CreateDatapkgController;
  private folderOperationCtrl?: FolderOperationCommonController<ITableData>;

  public constructor(app: AppController, Model: IPersonalDatapkgModel) {
    super({
      app,
      menu: MyDataMenuEnum.PERSONAL,
      showCreateBtn: true,
      enableCreateBtn: true,
      emitter: app!.getEmitterController()!.getEmitterIns(),
      tableController: new TableCurdWithSimpleSearchController<ITableData>({
        dataListCompTableCurdControllerOptions: {
          dataListControllerOptions: {
            loadDataListFunc: (params: IFilterParams) => this.queryFirstPageTableData(params),
            loadNextPageDataListFunc: (params: any) => this.queryNextPageTableData(params),
            getBackendFilterParams: () => this.getBackendFilterParams(),
          },
          folderControllerOptions: {
            enableFolder: true,
          },
          curdOptions: () => {
            return {
              ...this.initCurdOptions(),
              dragCode: 'name',
              onFolderCreate: () =>
                this.folderOperationCtrl
                  ?.getCreateFolderController()
                  .openModal(this.tableController.getFolderPathValue()),
              onFolderNavClick: (path: string) =>
                this.tableController.loadDataList({ ...this.getBackendFilterParams(), path }),
            };
          },
          tableOptions: () => this.initTableOptions(),
          clickMoreBtnFunc: (menuKey: string, item?: ITableData) => {
            if (menuKey === FolderActionType.MOVE) {
              return this.folderOperationCtrl?.getTableMoveController().openModal(item);
            }
            if (menuKey === FolderActionType.RENAME) {
              return this.folderOperationCtrl?.getRenameFolderController().openModal(item);
            }
            if (menuKey === FolderActionType.DELETE) {
              return this.folderOperationCtrl?.getDeleteFolderController().openModal(item);
            }
            return this.handleMoreOperation(menuKey, item);
          },
          onDragOverFunc: (path: string, items: ITableData[]) => this.folderOperationCtrl?.moveToTarget(path, items),
          folderActionCallbackFunc: (path: string) => {
            return this.tableController.loadDataList({ ...this.getBackendFilterParams(), path });
          },
          clickCreateBtnFunc: () => this.createPkgController.openModal(),
        },
        headerOptions: () => this.initHeaderOptions(),
      }),
    });
    this.Model = Model;

    const productPs = this.app.getUserPermissionController().getUserProductPermission(ALLOW_JUMP_PRODUCTS);
    this.createPkgController = new CreateDatapkgController({
      Model: Model.getCreateEmptyDatapkgModel(),
      appId: this.app.getAppId(),
      datasetId: this.app!.getDatasetsController()!.getAppDatasetId(),
      ownership: OwnershipEnum.USER,
      maxMb: UPLOAD_MAX_MB,
      disableCreateQe: true,
      disableGotoFactory: !JUMP_DATA_FACTORY_URL || !productPs.enableDataFactory,
      disableGotoCollector: !JUMP_COLLECTOR_URL || !productPs.enableCollector,
      onFileUploadSuccessFunc: async (id?: string) => {
        id &&
          this.tableController.getEnableFolder() &&
          this.tableController.getFolderPathValue() &&
          (await FolderModel.postFolder({
            space: FOLDER_SPACE,
            path: this.tableController.getFolderPathValue(),
            resources: { [FOLDER_SPACE]: [id] },
          }).toPromise());
        this.tableController.loadDataList(this.getBackendFilterParams());
        // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
        id && this.handleShowDetail({ id } as ITableData);
      },
      onSuccessFunc: async (id?: string) => {
        id &&
          this.tableController.getEnableFolder() &&
          this.tableController.getFolderPathValue() &&
          (await FolderModel.postFolder({
            space: FOLDER_SPACE,
            path: this.tableController.getFolderPathValue(),
            resources: { [FOLDER_SPACE]: [id] },
          }).toPromise());
        this.tableController.loadDataList(this.getBackendFilterParams());
      },
      goDatasourceFunc: () => {
        this.app!.getRouterController().goToDatasource();
      },
      goDataFactoryFunc: () => {
        this.app!.jumpToProductDataFactory();
      },
      goCollectorFunc: () => {
        this.app?.jumpToProductCollector();
      },
      openExistedPkgFunc: (pkgId) => {
        this.drawerDetailController.openModal(pkgId, { openEdit: true });
      },
    });

    this.init();
  }

  public destroy() {
    super.destroy();
    this.createPkgController.destroy();
    this.Model = null!;
    this.folderOperationCtrl?.destroy();
  }

  public getFolderOperationCtrl() {
    return this.folderOperationCtrl;
  }

  public getCreatePkgController() {
    return this.createPkgController;
  }

  // 查看详情
  protected handleShowDetail = (originalData: ITableData) => {
    this.drawerDetailController.openModal(originalData.id);
  };

  private initTableOptions = (): IVirtualizedTableProps => {
    return {
      columns: [
        {
          code: 'name',
          name: i18n.chain.mydata.globalSearch.pkgName,
          width: 450,
          render: (val: string, record: ITableData) => (
            <LinkButton className="mydata-route-home_pkg-name" onClick={() => this.handleShowDetail(record)}>
              <span style={{ color: `${record.color}` }}>
                <Icon className="mydata-route-home_pkg-icon" icon={record.icon} />
              </span>
              {val}
            </LinkButton>
          ),
        },
        { code: 'userName', name: i18n.chain.mydata.globalSearch.user, width: 120 },
        {
          code: 'packageTypeDisplay',
          name: i18n.chain.mydata.globalSearch.storageType,
          width: 120,
          render: (val) => {
            return val ? <Tag tag={val[0]} color={val[1]} /> : '-';
          },
        },
        { code: 'updateTimeDisplay', name: i18n.chain.mydata.globalSearch.updateTime, width: 175 },
      ],
      type: 'page-bg',
      primaryKey: 'id',
      withVerticalBorder: false,
      // style: { width: 'calc(100vw - 260px)' },
    };
  };

  private initHeaderOptions = () => {
    const routes = [
      {
        path: '',
        breadcrumbName: i18n.chain.mydata.menu.personalData,
      },
    ];
    const title = (
      <div className="table-title">
        <Breadcrumb className="mydata-route-home_breadcrumb" withPrev={false} routes={routes} />
        <MyDataFilter controller={this.filterController} />
      </div>
    );
    return {
      createBtnLabel: i18n.chain.proMicroModules.datapkg.createPkg,
      hideInput: true,
      title,
    };
  };

  private queryFirstPageTableData = ({ cancelToken, ...rest }: IFilterParams) => {
    const enableFolder = this.tableController.getEnableFolder();
    const searchValue = this.tableController.getSingleFilterValue();
    if (!enableFolder || searchValue) {
      return this.Model.queryFirstPagePkgs(cancelToken, rest);
    }
    const { path, ...pkgRest } = rest as any;
    const folderParams: any = { space: FOLDER_SPACE };
    path && (folderParams.path = path);
    const pkgParams = path ? { folder: path } : { nofolder: true };
    return this.Model!.queryFolderAndPkgs(cancelToken, pkgRest, pkgParams, folderParams);
  };

  private queryNextPageTableData = (params: any) => {
    const { path, ...restParams } = params;
    const { cancelToken, ...rest } = this.getBackendFilterParams();
    const data: IDatapkgsQuery = { ...restParams, ...this.Model.transformFilterParams(rest) };
    const enableFolder = this.tableController.getEnableFolder();
    const searchValue = this.tableController.getSingleFilterValue();
    if (!enableFolder || searchValue) {
      return this.Model.queryNextPagePkgs(data, cancelToken);
    }
    const pkgParams = path ? { folder: path } : { nofolder: true };
    return this.Model.queryNextPagePkgs(data, cancelToken, pkgParams);
  };

  // 初始化
  private init() {
    const tc = this.tableController;
    this.folderOperationCtrl = tc.getEnableFolder()
      ? new FolderOperationCommonController<ITableData>({
          space: FOLDER_SPACE,
          resouceKey: 'id',
          disabledDeleteResource: true,
          getFolderPathFunc: () => tc.getFolderPathValue(),
          transformFolderToVFunc: (item: IFolders) => this.Model.transformFolderToTableData(item),
          loadDataListFunc: (params?: any) => tc.loadDataList({ ...this.getBackendFilterParams(), ...params }),
          addToDataListFunc: (data: ITableData) => tc.addDataToList(data),
          deleteDataFromListFunc: (data: ITableData, modifyTotal?: boolean) => tc.deleteDataFromList(data, modifyTotal),
        })
      : undefined;

    tc.loadDataList(this.getBackendFilterParams());
    tc.listenSingleFilterToChangeFolderPath();
    const pkgId = getPkgIdFromUrl();
    pkgId && this.drawerDetailController.openModal(pkgId);
  }
}

export { PersonalDatapkgController };
