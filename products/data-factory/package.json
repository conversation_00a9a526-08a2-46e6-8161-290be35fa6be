{"name": "data-factory", "cnName": "数据工厂", "description": "数据工厂--功能强大的数据处理平台", "version": "2.38.23", "private": true, "scripts": {"start": "cross-env DEVELOP_ENV=dev craco start", "start:staging": "cross-env DEVELOP_ENV=staging craco start", "start:debug": "cross-env DEVELOP_ENV=debug craco start", "release": "CI=false craco build", "release:analyze": "craco build --analyze", "test": "craco test"}, "cracoConfig": "craco.config.js", "dependencies": {"@mdt/formily": "^0.9.2", "@mdt/product-comm": "^1.28.12", "@mdt/product-micro-modules": "^1.48.23", "@mdt/product-tasks": "^1.25.12", "ajv": "^8.11.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}