import { useController } from '@mdtBsComm/hooks/use-controller';
import { AppController } from '../../app/AppController';
import { Dashboard, DashboardController } from '../../pages/dashboard';

const DashboardView = () => {
  const [controller] = useController(() => {
    const ctrl = new DashboardController(AppController.getInstance());
    return [ctrl, null];
  });

  return <Dashboard controller={controller} />;
};

export default DashboardView;
