import { useController } from '@mdtBsComm/hooks/use-controller';
import { AppController } from '../../app/AppController';
import { DataApproval, DataApprovalController } from '../../pages/data-approval';

const DataApprovalAppView = () => {
  const [controller] = useController(() => {
    const ctrl = new DataApprovalController(AppController.getInstance(), { isSelfApp: true });
    return [ctrl, null];
  });

  return <DataApproval controller={controller} />;
};

export default DataApprovalAppView;
