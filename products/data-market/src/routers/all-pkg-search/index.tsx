import { useController } from '@mdtBsComm/hooks/use-controller';
import { getModel } from '../../_util/modelUtil';
import i18n from '../../languages';
import {
  AllPkgSearch,
  AllPkgSearchController,
  AllPkgSearchModel,
  AllPkgSearchModelBff,
} from '../../pages/all-pkg-search';

const AllPkgSearchView = () => {
  const [controller] = useController(() => {
    const ctrl = new AllPkgSearchController({
      title: i18n.chain.datamarket.menu.allData,
      Model: getModel(AllPkgSearchModel, AllPkgSearchModelBff),
    });
    return [ctrl, null];
  });

  return <AllPkgSearch controller={controller} />;
};

export default AllPkgSearchView;
