import { isTextAreaEntryEdited, TextAreaEntry } from '@bpmn-io/properties-panel';
import { useService } from 'bpmn-js-properties-panel';
import { PREFIX } from '../constants';
import { ButtonEntry } from '../entries/ButtonEntry';
import { getRelevantBusinessObject, isBpmnProcess } from '../utils/ElementUtil';
import { addExtensionElements, getExtensionElement, removeExtensionElements } from '../utils/ExtensionElementsUtil';

const CUSTOM_ELEMENT_KEY = `${PREFIX}:GlobalVariables`;

export function GlobalVariablesProps(props: any) {
  const { element } = props;

  if (!isBpmnProcess(element)) {
    return [];
  }

  return [{ id: 'globalVariables', component: GlobalVariables, isEdited: isTextAreaEntryEdited }];
}

function GlobalVariables(props: any) {
  const { element, id } = props;

  const bpmnFactory = useService('bpmnFactory');
  const commandStack = useService('commandStack');
  const translate = useService('translate');
  const debounce = useService('debounceInput');
  const businessObject = getRelevantBusinessObject(element);
  const mdtExternal = useService('mdtExternalProvider');

  const getValue = () => {
    const extensionElement = getExtensionElement(businessObject, CUSTOM_ELEMENT_KEY);
    const val = extensionElement?.get('body');
    if (!val) {
      setTimeout(() => {
        setValue('{}');
      }, 200);
    }
    return val;
  };

  const setValue = (value: any) => {
    let extensionElement = getExtensionElement(businessObject, CUSTOM_ELEMENT_KEY);
    extensionElement && removeExtensionElements(element, businessObject, extensionElement, commandStack);
    extensionElement = bpmnFactory.create(CUSTOM_ELEMENT_KEY, { body: value });
    addExtensionElements(element, businessObject, extensionElement, bpmnFactory, commandStack);
  };

  const onClick = (e: any) => {
    e.stopPropagation();
    e.preventDefault();
    mdtExternal.handleConfigGlobalVariables(getValue(), setValue);
  };

  const label = ButtonEntry({ children: translate('Setting'), onClick });
  return TextAreaEntry({ element, id, label, getValue, setValue, debounce, disabled: true });
}
