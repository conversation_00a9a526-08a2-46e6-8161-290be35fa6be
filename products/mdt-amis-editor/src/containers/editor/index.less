@editor-default-color: #151b26; // 默认的字体色
@editor-active-color: #2468f2;
@editor-hover-color: #5086f5;
@editor-border-color: #e8e9eb; // 默认的边框色
@default-icon-color: #84868c; // 默认的icon颜色
@active-bg-color: #e6f0ff; // 激活态的背景色
@hover-bg-color: #f7f7f9; // 激活态的背景色
@disabled-color: #b8babf; // 禁用文字颜色
@disabled-bg-color: #f7f7f9; // 禁用背景颜色
@white: #fff;
@exit-btn-color: #332e2e;
@preview-btn-color: #151a26;

.editor-page {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100vw;
  height: 100vh;
  min-height: 510px;

  .editor-header {
    position: relative;
    z-index: 1000;
    display: flex;
    flex: 0 0 48px;
    box-sizing: border-box;
    background: @white;
    border-bottom: 1px solid @editor-border-color;

    .editor-header-icon svg,
    .shortcut-icon-btn svg {
      display: inline-block;
      width: 16px;
      height: 16px;
    }

    .editor-title {
      display: flex;
      flex: 1 1 565px;
      align-items: center;
      justify-content: flex-start;
      padding: 0 15px;
      font-weight: 500;
      font-size: 16px;
      letter-spacing: 0;
      user-select: none;
    }

    .editor-view-mode-group-container {
      display: flex;
      flex: 0 1 150px;
      align-items: center;
      justify-content: center;
    }

    .editor-header-actions {
      display: flex;
      flex: 1 1 565px;
      align-items: center;
      justify-content: flex-end;
      padding: 0 24px;
      font-size: 12px;
      white-space: nowrap;

      .margin-left-space {
        margin-left: 30px;
      }
    }

    .editor-view-mode-group {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100px;
      height: 32px;
      font-weight: 400;
      font-size: 14px;
      letter-spacing: 0;
      text-align: center;
      background-color: #f2f2f4;
      border-radius: 4px;

      .editor-view-mode-btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 24px;
        padding: 0;
        border-radius: 4px;
        cursor: pointer;
        transition: transform ease-out 0.2s;
        user-select: none;

        svg {
          color: @editor-default-color;
        }

        &:first-child {
          margin-right: 12px;
        }

        &:hover > svg {
          color: @editor-active-color;
        }

        &.is-active {
          background: @editor-active-color;

          svg {
            color: @white;
          }

          &:hover {
            background: #5086f5;
          }
        }
      }
    }

    .header-action-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      margin-left: 18px;
      cursor: pointer;
      transition: transform ease-out 0.2s;
      user-select: none;

      /* stylelint-disable-next-line  */
      > svg {
        width: 16px;
        fill: @editor-default-color;
      }

      /* stylelint-disable-next-line  */
      &:hover > svg {
        fill: @editor-active-color;
      }

      &.disabled {
        cursor: not-allowed;

        /* stylelint-disable-next-line  */
        > svg {
          fill: @disabled-color;
        }
      }

      // 历史记录icon单独处理
      > svg.icon-editor-history {
        color: @editor-default-color;

        &:hover {
          color: @editor-active-color;
        }

        &.disabled,
        &.disabled:hover {
          color: @disabled-color;
          cursor: not-allowed;
        }
      }
    }

    .header-action-btn {
      min-width: 74px;
      margin-left: 8px;
      padding: 0 16px;
      user-select: none;
    }
  }

  .editor-inner {
    position: relative;
    flex: 1 1 auto;
    overflow-x: hidden;
    overflow-y: auto;
  }

  .editor-close {
    display: inline-flex;
    align-items: center;
    padding-left: 10px;
    font-size: 16px;
    cursor: pointer;
  }
}

.drawer-api-editor .metro-drawer-body {
  padding: 0;
}
