import _ from 'lodash';
import { from, Observable } from 'rxjs';
import { map, takeWhile } from 'rxjs/operators';
import { strToMd5 } from '@mdtBsComm/utils/md5Util';
import { checkPasswordAsync, putUserAsync } from '@mdtBsServices/auth';

export interface IUserInfoPasswordDialogModel {
  checkPassword: (password: string) => Observable<boolean>;
  updatePassword: (oldPassword: string, newPassword: string) => Observable<any>;
}
export class UserInfoPasswordDialogModel implements IUserInfoPasswordDialogModel {
  private readonly userId?: number;
  public constructor(userId?: number) {
    this.userId = userId;
  }
  // 检查密码
  public checkPassword(password: string) {
    return from(checkPasswordAsync(password)).pipe(
      takeWhile((resp) => !resp.canceled),
      map((resp) => {
        if (resp.success) {
          return _.isBoolean(resp.data) && resp.data ? true : false;
        }
        return false;
      }),
    );
  }

  // 更新密码
  public updatePassword(oldPassword: string, newPassword: string) {
    return from(
      putUserAsync({
        id: this.userId || 0,
        old_password: strToMd5(oldPassword),
        password: strToMd5(newPassword),
      }),
    ).pipe(takeWhile((resp) => !resp.canceled));
  }
}
