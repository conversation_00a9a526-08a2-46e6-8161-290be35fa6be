module.exports = {
  extends: [
    "react-app/jest",
    "alloy",
    "alloy/react",
    "alloy/typescript",
    "plugin:sonarjs/recommended",
    "plugin:react-hooks/recommended"
  ],
  plugins: ["simple-import-sort", "mdt-frontend"],
  rules: {
    "simple-import-sort/imports": "error",
    'max-params': ['error', 4],
    'max-nested-callbacks': ['error', 4],
    "@typescript-eslint/naming-convention": [
      "error",
      { "selector": "interface", "format": ["PascalCase"], "custom": { "regex": "^I[A-Z]", "match": true } },
    ],
    "@typescript-eslint/no-unused-vars": [
      "error",
      { "vars": "all", "args": "after-used", "ignoreRestSiblings": false, "caughtErrors": "none"}
    ],
    "@typescript-eslint/no-invalid-this": 0,
    "mdt-frontend/no-alias-internal-imports": [
      "error",
      [
        {alias: "@mdtBpmnPropertiesPanel", dir: 'packages/bpmn-js-properties-panel'},
        {alias: "@mdtBsBffServices", dir: 'packages/business-bff-services'},
        {alias: "@mdtBsComm", dir: 'packages/business-comm'},
        {alias: "@mdtBsComponents", dir: 'packages/business-components'},
        {alias: "@mdtBsControllers", dir: 'packages/business-controllers'},
        {alias: "@mdtBsServices", dir: 'packages/business-services'},
        {alias: "@mdtLogin", dir: 'packages/login'},
        {alias: "@mdtApis", dir: 'packages/restful-apis'},
        {alias: "@mdtFormily", dir: 'packages/formily'},
        {alias: "@mdtDesignable", dir: 'packages/form-design'},
        {alias: "@mdtProComm", dir: 'shares/product-comm'},
        {alias: "@mdtProSso", dir: 'shares/product-sso'},
        {alias: "@mdtProFormEditor", dir: 'shares/product-form-editor'},
        {alias: "@mdtProMicroModules", dir: 'shares/product-micro-modules'},
        {alias: "@mdtProTasks", dir: 'shares/product-tasks'}
      ]
    ]
  },
  overrides: [
    {
      files: ["*.tsx", "*.ts"],
      rules: {
        "simple-import-sort/imports": [
          "error",
          {
            groups: [
              ["^lodash", "^react", "^@?\\w", "^@@\\w", "^(@mdt|@datlas.*|$)", "^\\u0000", "^\\.\\.(?!/?$)", "^\\.\\./?$", "^\\./(?=.*/)(?!/?$)", "^\\.(?!/?$)", "^\\./?$", "^.+\\.s?(less|css)$"],
            ],
          },
        ],
      },
    }
  ],
  globals: {
    __IS_DEVELOPMENT: false,
    __DEVELOP_PROXY_API_URL: false,
    __DEVELOP_ENV_ORIGIN: false,
    __webpack_public_path__: true,
  }
}
