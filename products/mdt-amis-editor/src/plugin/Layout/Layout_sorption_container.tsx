/* eslint-disable sonarjs/no-duplicate-string */
/* eslint-disable @typescript-eslint/explicit-member-accessibility */
import { registerEditorPlugin } from 'amis-editor-core';
import i18n from '../../languages';
import { defaultFlexColumnSchema, FlexPluginBase } from './FlexPluginBase';

export default class Layout_fixed_top extends FlexPluginBase {
  static id = 'Layout_fixed_top';
  static scene = ['layout'];

  name = i18n.chain.jsonComp.layout_fixed_top;
  isBaseComponent = true;
  pluginIcon = 'layout-fixed-top';
  description = i18n.chain.jsonComp.layout_fixed_topDescription;
  order = -1;
  scaffold: any = {
    type: 'flex',
    isSorptionContainer: true,
    sorptionPosition: 'top',
    className: 'p-1',
    items: [defaultFlexColumnSchema(), defaultFlexColumnSchema(), defaultFlexColumnSchema(), defaultFlexColumnSchema()],
    style: {
      position: 'fixed',
      inset: '0 auto auto 0',
      zIndex: 10,
      width: '100%',
      overflowX: 'auto',
      margin: '0',
      overflowY: 'auto',
    },
    isFixedWidth: true,
    isFixedHeight: false,
    originPosition: 'right-bottom',
  };
  panelTitle = i18n.chain.jsonComp.layout_fixed_top;
}

registerEditorPlugin(Layout_fixed_top);
