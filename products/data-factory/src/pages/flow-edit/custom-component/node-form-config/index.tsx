import _ from 'lodash';
import { ChangeEvent, FC, useEffect, useRef, useState } from 'react';
import { IApplication, IPosition, MODELS, useModelAsync } from '@antv/xflow';
import { createSchemaField, FormConsumer } from '@formily/react';
import { useObservableState } from '@mdtBsComm/hooks/use-observable-state';
import { Button as MdtButton } from '@mdtDesign/button';
import Edit from '@mdtDesign/icons/edit';
import { Input as MdtInput } from '@mdtDesign/input';
import { TooltipText } from '@mdtDesign/tooltip';
import {
  ArrayItems,
  ArrayTable,
  Button,
  Checkbox,
  CodeEditor,
  DatePicker,
  Form,
  FormCollapse,
  FormDialog,
  FormDrawer,
  FormGrid,
  FormItem,
  Input,
  NumberPicker,
  Password,
  PreviewText,
  Radio,
  Select,
  Space,
  Switch,
  TimePicker,
  Upload,
} from '@mdtFormily/index';
import { IS_DEVELOP } from '../../../../config';
import i18n from '../../../../languages';
import { RunNodeActionEnum } from '../../_util/enum';
import { INodeData } from '../../_util/interfaces';
import { useFlowEditContext } from '../../flowEditContext';
import RemoteFile from './RemoteFile';
import './index.less';

interface IProps {
  app: IApplication;
  /** 位置信息 */
  position: IPosition;
}
const NodeFormConfig: FC<IProps> = ({ app, position }) => {
  const [SELECTED_NODE] = useModelAsync<MODELS.SELECTED_NODE.IState, null>({
    // 异步获取Model
    getModel: async () => MODELS.SELECTED_NODE.getModel(app.modelService) as any,
    initialState: null,
  });

  return SELECTED_NODE ? (
    <div className="node-form-config" style={{ ...position, width: position.width ?? 380 }}>
      <NodeFormSetting key={SELECTED_NODE.id} nodeId={SELECTED_NODE.id} />
    </div>
  ) : null;
};

export default NodeFormConfig;

const SchemaField = createSchemaField({
  components: {
    CodeEditor,
    FormItem,
    FormDrawer: FormDrawer.Button,
    FormDialog: FormDialog.Button,
    Button,
    Input,
    Password,
    Radio,
    Select,
    Switch,
    NumberPicker,
    FormGrid,
    Checkbox,
    DatePicker,
    TimePicker,
    ArrayItems,
    Space,
    PreviewText,
    Upload,
    FormCollapse,
    ArrayTable,
    RemoteFile,
  },
});

const EditTitle: FC<{ data: INodeData }> = ({ data }) => {
  const [title, setTitle] = useState(data.label || '');
  const [edit, setEdit] = useState(false);
  const ref = useRef<any>(null);
  const isRunNode$ = data.isRunNode$!;

  useEffect(() => {
    let sub = isRunNode$.subscribe((runInfo) => {
      if (_.isObject(runInfo) && runInfo.action === RunNodeActionEnum.UPDATA_LABEL) {
        setTitle(runInfo.data.label);
      }
    });
    return () => {
      sub.unsubscribe();
    };
  }, [isRunNode$]);

  const notifyLabel = (newLabel: string) => {
    isRunNode$?.next({
      id: data.id,
      action: RunNodeActionEnum.UPDATA_LABEL,
      data: { label: newLabel || title, editingTitle: false },
    });
    setEdit(false);
  };

  const onBlur = (e: ChangeEvent<HTMLInputElement>) => {
    notifyLabel(e.target.value.trim());
  };

  const onPressEnter = (e: KeyboardEvent) => {
    e.preventDefault();
    e.stopPropagation();
    notifyLabel(ref.current.input.value.trim());
  };

  useEffect(() => {
    edit && ref.current?.focus();
  }, [edit]);

  return edit ? (
    <MdtInput
      ref={ref}
      defaultValue={title}
      style={{ width: '90%', height: '100%' }}
      size="compact"
      allowClear={false}
      onBlur={onBlur}
      onPressEnter={onPressEnter}
    />
  ) : (
    <>
      <div className="title" onClick={() => setEdit(true)}>
        <TooltipText text={title} />
      </div>
      <Edit size={18} className="titleIcon" onClick={() => setEdit(true)} />
    </>
  );
};

const NodeFormSetting: FC<{ nodeId: string }> = ({ nodeId }) => {
  const { flowEditController: controller } = useFlowEditContext();
  const graphController = controller.getGraphController();
  const nodeFormController = controller.getNodeFormController();
  const nodeData = graphController.getNodeDataById(nodeId);

  // 列信息获取后触发刷新表单
  useObservableState(() => {
    // 刷新之前保存form 表单当前的值
    nodeFormController.onSave();
    return graphController.getNodeNeedFrush();
  });

  nodeFormController.initNode(nodeId);

  return (
    <>
      <div className="node-form-config-title node-form-config-item">
        <EditTitle data={nodeData} />
      </div>
      {nodeFormController.isNeedGetGield && (
        <div className="node-form-config-item">
          <MdtButton
            type="primary"
            style={{ width: '100%' }}
            onClick={async () => await graphController.getFieldInfo()}
          >
            {i18n.chain.dataFactory.flow.getKeyInfo}
          </MdtButton>
        </div>
      )}
      <div className="nodeWrap">
        <Form form={nodeFormController.getNodeForm()} layout="vertical" size="default">
          <SchemaField {...nodeFormController.getNodeSchema()} />

          {/* schema 测试 */}
          {IS_DEVELOP ? (
            <FormConsumer>
              {() => (
                <code>
                  <pre>{JSON.stringify(nodeFormController.formInstance?.values, null, 2)}</pre>
                </code>
              )}
            </FormConsumer>
          ) : null}
        </Form>
      </div>
    </>
  );
};
