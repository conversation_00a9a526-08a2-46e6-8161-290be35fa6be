import { useController } from '@mdtBsComm/hooks/use-controller';
import {
  OneTableMissionCenterOne,
  OneTableMissionCenterOneController,
  OneTableMissionCenterOneModelBff,
} from '@mdtProMicroModules/pages/one-table-mission-center-one';

const OneTableMissionCenterOneView = () => {
  const [controller] = useController(() => {
    return [
      new OneTableMissionCenterOneController({
        Model: OneTableMissionCenterOneModelBff,
      }),
      null,
    ];
  }, []);

  return <OneTableMissionCenterOne controller={controller} />;
};

export default OneTableMissionCenterOneView;
