/* eslint-disable sonarjs/no-duplicate-string */
/* eslint-disable @typescript-eslint/explicit-member-accessibility */
/**
 * @file Flex 常见布局 1:2
 */
import i18n from '../../../languages';
import { FlexPluginBase } from '../FlexPluginBase';

export default class Layout1_2 extends FlexPluginBase {
  name = i18n.chain.jsonComp.layout1_2;
  isBaseComponent = false;
  pluginIcon = 'layout-2cols-plugin';
  description = i18n.chain.jsonComp.layout1_2Description;
  tags = [i18n.chain.jsonComp.commonLayout];
  order = 201;
  scaffold: any = {
    type: 'flex',
    className: 'p-1',
    items: [
      {
        type: 'wrapper',
        size: 'xs',
        body: [],
        style: {
          flex: '1 1 auto',
          flexBasis: 'auto',
          flexGrow: 1,
          display: 'block',
        },
      },
      {
        type: 'wrapper',
        size: 'xs',
        body: [],
        style: {
          flex: '1 1 auto',
          flexBasis: 'auto',
          flexGrow: 2,
          display: 'block',
        },
      },
    ],
    alignItems: 'stretch',
  };
}
