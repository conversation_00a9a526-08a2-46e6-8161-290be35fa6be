import { isTextFieldEntryEdited, TextFieldEntry } from '@bpmn-io/properties-panel';
import { is } from 'bpmn-js/lib/util/ModelUtil';
import { useService } from 'bpmn-js-properties-panel';
import { CMD_KEY_UPDATEMODDLEPROPERTIES, KEY_BPMN_FORMALEXPRESSION } from '../constants';
import { ButtonEntry } from '../entries/ButtonEntry';
import { createElement } from '../utils/ElementUtil';
import { getLoopCharacteristics$2 } from './MultiInstanceProps';

function isStandardLoopSupported(element: any) {
  const loopCharacteristics = getLoopCharacteristics$2(element);
  return !!loopCharacteristics && is(loopCharacteristics, 'bpmn:StandardLoopCharacteristics');
}

export function StandardLoopProps(props: any) {
  const { element } = props;

  if (!isStandardLoopSupported(element)) {
    return [];
  }

  return [
    {
      id: 'StandardLoopConfig',
      component: StandardLoopConfig,
      isEdited: isTextFieldEntryEdited,
    },
  ];
}

function updateLoopCharacteristicsAttrs(element: any, properties: any) {
  const loopCharacteristics = getLoopCharacteristics$2(element);
  return {
    element,
    moddleElement: loopCharacteristics,
    properties: properties,
  };
}

function createFormalExpression$1(parent: any, body: any, bpmnFactory: any) {
  return createElement(KEY_BPMN_FORMALEXPRESSION, { body: body }, parent, bpmnFactory);
}

function updateFormalExpression(element: any, propertyName: any, newValue: any, bpmnFactory: any) {
  const loopCharacteristics = getLoopCharacteristics$2(element);
  const expressionProps: any = {};
  if (!newValue) {
    // remove formal expression
    expressionProps[propertyName] = undefined;
    return {
      element,
      moddleElement: loopCharacteristics,
      properties: expressionProps,
    };
  }
  const existingExpression = loopCharacteristics.get(propertyName);
  if (!existingExpression) {
    // add formal expression
    expressionProps[propertyName] = createFormalExpression$1(loopCharacteristics, newValue, bpmnFactory);
    return {
      element,
      moddleElement: loopCharacteristics,
      properties: expressionProps,
    };
  }

  // edit existing formal expression
  return {
    element,
    moddleElement: existingExpression,
    properties: {
      body: newValue,
    },
  };
}

function StandardLoopConfig(props: any) {
  const { element } = props;
  const debounce = useService('debounceInput');
  const commandStack = useService('commandStack');
  const translate = useService('translate');
  const mdtExternal = useService('mdtExternalProvider');
  const bpmnFactory = useService('bpmnFactory');

  const getValue = () => {
    const ele = getLoopCharacteristics$2(element);
    if (!ele) return '';
    const obj = {
      testBefore: ele.get('testBefore'),
      loopMaximum: ele.get('loopMaximum'),
      interval: ele.get('interval'),
      recordLoop: ele.get('recordLoop'),
      loopCondition: ele.get('loopCondition')?.get('body'),
    };
    return JSON.stringify(obj);
  };

  const setValue = (value: Record<string, any>) => {
    const { loopCondition, ...reset } = value || {};
    commandStack.execute(CMD_KEY_UPDATEMODDLEPROPERTIES, updateLoopCharacteristicsAttrs(element, reset));
    commandStack.execute(
      CMD_KEY_UPDATEMODDLEPROPERTIES,
      updateFormalExpression(element, 'loopCondition', loopCondition, bpmnFactory),
    );
  };

  const onClick = (e: any) => {
    e.stopPropagation();
    e.preventDefault();
    mdtExternal.handleConfigStandardLoop(getValue(), setValue);
  };

  const label = ButtonEntry({ children: translate('Setting'), onClick });

  return TextFieldEntry({
    element,
    id: 'standardLoopInput',
    label,
    getValue,
    setValue,
    debounce,
    disabled: true,
  });
}
