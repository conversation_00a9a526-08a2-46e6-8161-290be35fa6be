import { useController } from '@mdtBsComm/hooks/use-controller';
import {
  OneTableNewFormManagement,
  OneTableNewFormManagementController,
  OneTableNewFormManagementModel,
} from '@mdtProMicroModules/pages/one-table-new-form-management';
import { useOneTableNewOpenWorkItemIdentifier } from '@mdtProMicroModules/shared/useOpenWorkItemIdentifier';

const NewFormManagementView = () => {
  const [controller] = useController(() => {
    return [
      new OneTableNewFormManagementController({
        Model: OneTableNewFormManagementModel,
      }),
      null,
    ];
  }, []);
  useOneTableNewOpenWorkItemIdentifier(controller.openWorkItemIdentifier);
  return <OneTableNewFormManagement controller={controller} />;
};

export default NewFormManagementView;
