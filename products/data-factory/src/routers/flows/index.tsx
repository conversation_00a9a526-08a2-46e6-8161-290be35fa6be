import { useController } from '@mdtBsComm/hooks/use-controller';
import { AppController } from '../../app/AppController';
import FlowHome from '../../pages/low-code-etl';
import FlowHomeController from '../../pages/low-code-etl/FlowHomeController';
import { FlowHomeModel } from '../../pages/low-code-etl/FlowHomeModel';

const FlowHomeView = () => {
  const [controller] = useController(() => {
    const ctrl = new FlowHomeController(AppController.getInstance(), FlowHomeModel);
    return [ctrl, null];
  });

  return <FlowHome controller={controller} />;
};

export default FlowHomeView;
