import {
  IBatchTicketPost,
  IEntity,
  ITicket,
  ITicketPost,
  ITicketResolutionPut,
  ITicketsQuery,
} from '@mdtApis/interfaces';
import { ICancelTokenOption } from '../interfaces';

export interface ITicketModel extends ITicket {
  initiatorApp?: IEntity;
  initiatorUser?: IEntity;
  resolverUser?: IEntity;
  createTimeDisplay: string;
  permissionDisplay: string;
  resolutionLabels: [string, string];
}

export interface IQueryTicketListOptions extends ICancelTokenOption {
  params: ITicketsQuery;
  respData: string;
}

export interface ICreateTicketOptions extends ICancelTokenOption {
  data: ITicketPost;
}

export interface IUpdateTicketResolutionOptions extends ICancelTokenOption {
  ticketId: string;
  data: ITicketResolutionPut;
}

export interface IBatchcreateTicketOptions extends ICancelTokenOption {
  data: IBatchTicketPost[];
}
