import _ from 'lodash';
import { BehaviorSubject, Observable, of } from 'rxjs';
import { map, skip } from 'rxjs/internal/operators';
import { LinkButton } from '@mdtDesign/button';
import Tag from '@mdtDesign/tag';
import toastApi from '@mdtDesign/toast';
import { OrgAuthModeEnum, ResourceReceiverEnum } from '@mdtProComm/constants';
import { IOrgs, IOrgTrees, IPaginationQuery, IUserQuery } from '@mdtProComm/interfaces';
import { orgsToOrgTrees } from '@mdtProComm/utils/orgUtil';
import { getActiveTabFromUrl } from '@mdtProComm/utils/urlUtil';
import { BaseAuthOrgManageController } from '@mdtProMicroModules/containers/base-auth-org-manage-controller';
import {
  DrawerModifyFormPermissionOnlyController,
  IModalData as IPermissionModalData,
  IPermission,
} from '@mdtProMicroModules/containers/drawer-modify-form-permission-only';
import {
  ILoadDataListRslt,
  IVirtualizedTableProps,
  TableCurdWithSimpleSearchController,
} from '@mdtProMicroModules/containers/table-curd-with-simple-search';
import { IUserModel } from '@mdtProMicroModules/models/UserModel';
import { MyResourceShareModel, ResourceShareCreateModel } from '@mdtProMicroModules/pages/resource-share-list';
import { IReceiver } from '@mdtProMicroModules/pages/resource-share-menu';
import { DrawerResouceShareOtherController } from '@mdtProMicroModules/pages/resource-share-other';
import { AppController } from '../../app/AppController';
import i18n from '../../languages';
import { IAuthManageModel, ITableRoleData, ITableUserData } from './AuthManageModel';

// 防止app的id和部门的id重合，为appId加上前缀
export const APP_KEY_SUFFIX = 'app_';

type IPermissionItem = ITableUserData | ITableRoleData | IOrgTrees;

class AuthManageController {
  private app: AppController;
  private UserModel: IUserModel;
  private Model: IAuthManageModel;

  // 成员表格
  private tableUserController: TableCurdWithSimpleSearchController<ITableUserData>;
  // 机构角色表格
  private tableRoleController: TableCurdWithSimpleSearchController<ITableRoleData>;
  // 部门表格
  private tableOrgController: BaseAuthOrgManageController<IOrgTrees>;
  private userPermissionController: DrawerModifyFormPermissionOnlyController<IPermission, ITableUserData>;
  private rolePermissionController: DrawerModifyFormPermissionOnlyController<IPermission, ITableRoleData>;
  private orgPermissionController: DrawerModifyFormPermissionOnlyController<IPermission, IOrgTrees>;
  private resouceShareController: DrawerResouceShareOtherController;

  // 初始化loading
  private initLoading$ = new BehaviorSubject<boolean>(true);
  // 当前模块
  private mode$ = new BehaviorSubject<OrgAuthModeEnum | undefined>(undefined);
  // 当前菜单
  private menu$ = new BehaviorSubject<any[]>([]);
  // 权限表缓存
  private drawerPermissionsCache: IPermissionModalData = {};
  // // 角色对应的用户数量
  // private orgCountMap: Record<number, number> = {};
  // 角色和部门缓存
  private drawerUiDataCache: {
    roleIdNameMap?: Record<number, string>;
    orgIdNameMap?: Record<string, IOrgs>;
    roleIdPermissionMap?: Record<number, number[]>;
    orgIdPermissionMap?: Record<string, number[]>;
  } = {};

  public constructor(app: AppController, Model: IAuthManageModel, UserModel: IUserModel) {
    this.Model = Model;
    this.UserModel = UserModel;
    this.app = app;

    const titleMenus = [
      {
        label: i18n.chain.orgAdmin.auth.user,
        value: OrgAuthModeEnum.USER,
      },
      {
        label: i18n.chain.orgAdmin.auth.org,
        value: OrgAuthModeEnum.ORGANIZATION,
      },
      {
        label: i18n.chain.orgAdmin.auth.role,
        value: OrgAuthModeEnum.ROLE,
      },
    ];

    this.userPermissionController = new DrawerModifyFormPermissionOnlyController<IPermission, ITableUserData>({
      loadDrawerUiDataFunc: this.queryDrawerOrgPermissionUiData,
      modifyDataFunc: this.modifyUserDataToService,
      uiOptions: this.initUserPermissionUiOptions,
    });
    this.rolePermissionController = new DrawerModifyFormPermissionOnlyController<IPermission, ITableRoleData>({
      loadDrawerUiDataFunc: this.queryDrawerOrgPermissionUiData,
      modifyDataFunc: this.modifyUserRoleToService,
      uiOptions: this.initPermissionUiOptions,
    });
    this.orgPermissionController = new DrawerModifyFormPermissionOnlyController<IPermission, IOrgTrees>({
      loadDrawerUiDataFunc: this.queryDrawerOrgPermissionUiData,
      modifyDataFunc: this.modifyUserOrgToService,
      uiOptions: this.initPermissionUiOptions,
    });

    this.tableUserController = new TableCurdWithSimpleSearchController<ITableUserData>({
      dataListCompTableCurdControllerOptions: {
        dataListControllerOptions: {
          loadDataListFunc: this.queryUserWithTotal,
          loadNextPageDataListFunc: this.queryUser,
          getBackendFilterParams: this.getFilterParams,
        },
        curdOptions: () => this.initCurdOptions(OrgAuthModeEnum.USER),
        tableOptions: this.initTableUserOptions,
      },
      headerOptions: this.initHeaderOptions,
    });

    this.tableRoleController = new TableCurdWithSimpleSearchController<ITableRoleData>({
      dataListCompTableCurdControllerOptions: {
        dataListControllerOptions: {
          loadDataListFunc: this.queryRole,
        },
        curdOptions: () => this.initCurdOptions(OrgAuthModeEnum.ROLE),
        tableOptions: this.initTableRoleOptions,
      },
      headerOptions: this.initHeaderOptions,
    });

    this.tableOrgController = new BaseAuthOrgManageController<IOrgTrees>({
      dataListCompTableCurdControllerOptions: {
        dataListControllerOptions: {
          loadDataListFunc: this.queryOrg,
          equalItemKey: 'key',
        },
        tableOptions: this.initTableOrgOptions,
        curdOptions: () => this.initCurdOptions(OrgAuthModeEnum.ORGANIZATION),
      },
      headerOptions: this.initHeaderOptions,
      searchKey: 'title',
    });
    this.resouceShareController = new DrawerResouceShareOtherController();

    const authManage = this.app.getUserPermissionController().getAuthManagePermission();
    const menu = _.filter(titleMenus, ({ value }) => authManage[value]);
    this.menu$.next(menu);
    if (!_.isEmpty(menu)) {
      let activeMode = menu[0].value;
      // 资源共享跳到权限设置时需要把部门设置默认tab
      const activeTab = getActiveTabFromUrl();
      if (activeTab) {
        const activeMenu = _.find(menu, ['value', activeTab]);
        activeMenu && (activeMode = activeMenu.value);
      }
      this.onChangeMode(activeMode);
      // 初始化角色和部门数据缓存
      this.initOrgAndRoleList().subscribe(() => {
        this.initLoading$.next(false);
        this.tableUserController.loadDataList();
        this.tableRoleController.loadDataList();
        this.tableOrgController.loadDataList();
        this.tableUserController.listenBackendFilter(this.tableUserController.getSingleFilter$());
        this.tableRoleController.listenFrontFilter();
      });
      // 切换tab清空搜索条件
      this.mode$.pipe(skip(1)).subscribe(() => {
        this.resetSearchVal();
      });
    }
  }

  public destroy() {
    this.tableUserController.destroy();
    this.tableRoleController.destroy();
    this.tableOrgController.destroy();
    this.userPermissionController.destroy();
    this.rolePermissionController.destroy();
    this.orgPermissionController.destroy();
    this.resouceShareController.destroy();
    this.drawerPermissionsCache = {};
    // this.orgCountMap = {};
    this.mode$.complete();
    this.menu$.complete();
    this.initLoading$.complete();
    this.app = null!;
    this.Model = null!;
    this.UserModel = null!;
  }

  public getInitLoading$() {
    return this.initLoading$;
  }

  public getMode$() {
    return this.mode$;
  }

  public getMenu$() {
    return this.menu$;
  }

  public onChangeMode(mode: OrgAuthModeEnum) {
    this.mode$.next(mode);
  }

  public getTableUserController() {
    return this.tableUserController;
  }

  public getTableRoleController() {
    return this.tableRoleController;
  }

  public getTableOrgController() {
    return this.tableOrgController;
  }

  public getUserPermissionController() {
    return this.userPermissionController;
  }

  public getRolePermissionController() {
    return this.rolePermissionController;
  }

  public getOrgPermissionController() {
    return this.orgPermissionController;
  }

  public getResouceShareController() {
    return this.resouceShareController;
  }

  public openPermission = (item: IPermissionItem, type: OrgAuthModeEnum) => {
    if (type === OrgAuthModeEnum.USER) {
      const cell = item as ITableUserData;
      this.drawerPermissionsCache.roleList = _.concat(cell.organization, cell.role);
      return this.userPermissionController.openModal(cell).subscribe((result) => {
        result.success && this.tableUserController.editDataInList(result.result!);
      });
    }
    if (type === OrgAuthModeEnum.ROLE) {
      const cell = item as ITableRoleData;
      this.drawerPermissionsCache.roleList = [];
      return this.rolePermissionController.openModal(cell).subscribe((result) => {
        result.success && this.tableRoleController.editDataInList(result.result!);
      });
    }
    if (type === OrgAuthModeEnum.ORGANIZATION) {
      const cell = item as IOrgTrees;
      this.drawerPermissionsCache.roleList = [];
      return this.orgPermissionController.openModal(cell).subscribe((result) => {
        result.success && this.tableOrgController.editDataInListFunc(result.result!);
      });
    }
  };

  public openResourceShare = (item: IPermissionItem, type: OrgAuthModeEnum) => {
    const receiver: IReceiver = {
      type: OrgAuthModeEnum.ROLE === type ? ResourceReceiverEnum.ROLE : ResourceReceiverEnum.ORGANIZATION,
      title: _.get(item, 'name') || _.get(item, 'title'),
      key: _.get(item, 'id') || _.get(item, 'key'),
    };
    const options = {
      app: this.app,
      receiverId: receiver.key as string,
      receiverType: receiver.type,
    };
    this.resouceShareController.openModal({
      Model: new MyResourceShareModel(options),
      CreateResourceModel: new ResourceShareCreateModel(options),
      receiver,
    });
  };

  private resetSearchVal() {
    this.tableOrgController.changeSearchVal('');
    this.tableRoleController.changeSingleFilter('');
    this.tableUserController.changeSingleFilter('');
  }
  // 获取app全部用户
  private buildQueryParams = (params: IPaginationQuery) => {
    const query: IUserQuery = {
      ...params,
      enable_status: 'all',
    };

    return {
      appId: this.app.getAppId(),
      query,
      roleIdNameMap: this.drawerUiDataCache.roleIdNameMap,
      orgIdNameMap: this.drawerUiDataCache.orgIdNameMap,
    };
  };

  private queryUser = (params: IPaginationQuery): Observable<ITableUserData[]> => {
    return this.UserModel.queryUsersPagination(this.buildQueryParams(params)).pipe(
      map((data) => {
        return data as unknown as ITableUserData[];
      }),
    );
  };

  private queryUserWithTotal = (params: IPaginationQuery): Observable<[number, ITableUserData[]]> => {
    return this.UserModel.queryUsersPaginationTotal(this.buildQueryParams(params)).pipe(
      map(([total, data]) => {
        return [total, data as unknown as ITableUserData[]];
      }),
    );
  };

  private getFilterParams = (value: any[]) => {
    const [name] = value;
    if (name) {
      return { name_like: name };
    }
    return {};
  };

  // 获取角色
  private queryRole = (): ILoadDataListRslt<ITableRoleData> => {
    return of([
      0,
      _.map(this.drawerUiDataCache.roleIdNameMap, (name, id) => ({
        id: Number(id),
        name,
        description: '',
        permission: this.drawerUiDataCache.roleIdPermissionMap?.[Number(id)] || [],
      })),
    ]);
  };

  // 获取机构
  private queryOrg = (): ILoadDataListRslt<IOrgTrees> => {
    const { key: rootId } = this.getRootInfo();
    const orgList = _.map(this.drawerUiDataCache.orgIdNameMap, (org) => ({
      ...org,
      permission: this.drawerUiDataCache.orgIdPermissionMap?.[org.key] || [],
    }));
    const orgTrees = orgsToOrgTrees(orgList, rootId);
    return of([0, orgTrees]);
  };
  // 服务端修改用户授权
  private modifyUserDataToService = async (params: IPermission, originalData?: ITableUserData) => {
    const resp = await this.Model.updateUser(params, originalData!, this.app.getAppId()).toPromise();
    resp.success && toastApi.success(i18n.chain.orgAdmin.auth.editAuthSuccess);
    return resp;
  };
  // 服务端修改角色授权
  private modifyUserRoleToService = async (params: IPermission, originalData?: ITableRoleData) => {
    const resp = await this.Model.updateRole(params, originalData!).toPromise();
    resp.success && toastApi.success(i18n.chain.orgAdmin.auth.editAuthSuccess);
    return resp;
  };
  // 服务端修改部门授权
  private modifyUserOrgToService = async (params: IPermission, originalData?: IOrgTrees) => {
    const resp = await this.Model.updateOrg(params, originalData!).toPromise();
    resp.success && toastApi.success(i18n.chain.orgAdmin.auth.editAuthSuccess);
    return resp;
  };

  // 渲染表头所需信息
  private initTableUserOptions = (): IVirtualizedTableProps => {
    return {
      columns: [
        {
          name: i18n.chain.orgAdmin.organization.username,
          code: 'name',
          ellipsis: true,
        },
        {
          name: i18n.chain.orgAdmin.organization.orgs,
          code: 'orgDisplay',
          render: (v: string[]) => (v?.length ? v.map((v: string) => <Tag key={v} tag={v} />) : '--'),
        },
        {
          name: i18n.chain.orgAdmin.organization.roles,
          code: 'roleDisplay',
          render: (v: string[]) => (v?.length ? v.map((v: string) => <Tag key={v} tag={v} />) : '--'),
        },
        {
          name: i18n.chain.orgAdmin.organization.enable,
          code: 'disableDisplay',
          width: 120,
          render: (value: string, r: ITableUserData) => (
            <Tag key={value} tag={value} color={!r.disable ? 'green-700' : 'red-700'} />
          ),
        },
        { name: i18n.chain.orgAdmin.organization.expireTime, code: 'expireTimeDisplay' },
      ],
      type: 'page-bg',
      primaryKey: 'id',
      emptyContent: i18n.chain.comNoData,
      withVerticalBorder: false,
    };
  };

  private initTableRoleOptions = (): IVirtualizedTableProps => {
    return {
      columns: [
        {
          name: i18n.chain.orgAdmin.auth.roleName,
          code: 'name',
          width: 180,
        },
        { name: i18n.chain.orgAdmin.auth.roleDesc, code: 'description', render: (v: string) => v || '--' },
      ],
      type: 'page-bg',
      primaryKey: 'id',
      emptyContent: i18n.chain.comNoData,
      withVerticalBorder: false,
    };
  };

  private initTableOrgOptions = (): IVirtualizedTableProps => {
    return {
      columns: [
        {
          name: i18n.chain.orgAdmin.auth.orgName,
          code: 'title',
        },
        // {
        //   name: i18n.chain.orgAdmin.auth.orgCount,
        //   code: 'key',
        //   render: (k: string) => `${_.toNumber(this.orgCountMap[_.toNumber(k)])}`,
        // },
      ],
      type: 'page-bg',
      primaryKey: 'key',
      emptyContent: i18n.chain.comNoData,
      withVerticalBorder: false,
    };
  };

  private initHeaderOptions = () => {
    return {
      createBtnLabel: '',
      inputPlaceholder: i18n.chain.comPlaceholder.input,
      title: '',
    };
  };

  private initCurdOptions = (type: OrgAuthModeEnum) => {
    return {
      columnWidth: 200,
      otherBtns: (item: IPermissionItem) => {
        const resouceBtn =
          OrgAuthModeEnum.USER === type ? null : (
            <LinkButton onClick={() => this.openResourceShare(item, type)}>
              {i18n.chain.orgAdmin.auth.resource}
            </LinkButton>
          );
        return (
          <>
            <LinkButton onClick={() => this.openPermission(item, type)}>{i18n.chain.orgAdmin.auth.auth}</LinkButton>
            {resouceBtn}
          </>
        );
      },
    };
  };

  private initUserPermissionUiOptions = () => {
    return {
      title: (
        <div>
          {i18n.chain.orgAdmin.auth.authSetting}
          <span className="page_auth-manage-permission-desc">{i18n.chain.orgAdmin.auth.authSettingDesc} </span>
        </div>
      ),
    };
  };

  private initPermissionUiOptions = () => {
    return {
      title: i18n.chain.orgAdmin.auth.authSetting,
    };
  };

  private queryDrawerOrgPermissionUiData = () => {
    const ps = this.app!.getPermissionController();
    const mdtProduct = this.app!.getUserMdtProduct();
    if (_.isEmpty(this.drawerPermissionsCache.permissionOption)) {
      this.drawerPermissionsCache.permissionOption = this.Model.getGroupPermissions(
        this.app!.getAppPermission()!,
        ps!.getPermissionLabel.bind(ps),
        ps!.getPermissionModule.bind(ps),
        ps!.getPermissionType.bind(ps),
        mdtProduct,
      );
    }
    return of(this.drawerPermissionsCache);
  };

  private getRootInfo = () => {
    return {
      title: this.app.getAppName(),
      key: APP_KEY_SUFFIX + this.app.getAppId(),
    };
  };

  private initOrgAndRoleList = () => {
    return this.UserModel.queryRolesAndOrgs(this.app.getAppId(), this.getRootInfo().key).pipe(
      map((resp) => {
        this.drawerUiDataCache = {
          roleIdNameMap: resp.roleIdNameMap,
          orgIdNameMap: resp.orgIdNameMap,
          roleIdPermissionMap: resp.roleIdPermissionMap,
          orgIdPermissionMap: resp.orgIdPermissionMap,
        };
        return resp;
      }),
    );
  };
}

export { AuthManageController };
