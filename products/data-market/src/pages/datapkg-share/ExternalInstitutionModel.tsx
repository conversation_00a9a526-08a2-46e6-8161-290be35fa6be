import _ from 'lodash';
import { from } from 'rxjs';
import { Observable } from 'rxjs/internal/Observable';
import { map, mergeMap } from 'rxjs/operators';
import { IBusinessResult } from '@mdtBsComm/interfaces';
import { deleteAuthorizationAsync, postAuthorizationAsync, queryExternalAppsAsync } from '@mdtBsServices/auth';
import { CommonModel } from '@mdtProComm/models/CommonModel';
import {
  IExternalInstitution,
  ITableData,
  ITableExternalInstitutionModel,
} from '@mdtProMicroModules/containers/table-external-institution';
import { AppController } from '../../app/AppController';

export class ExternalInstitutionModel implements ITableExternalInstitutionModel {
  private appId: number;

  public constructor(app: AppController) {
    this.appId = app.getAppId();
  }

  // 查询外部机构列表
  public queryInstitutionList(): Observable<ITableData[]> {
    const params = {
      resource_type: 'app',
      entity_type: 'app',
      privilege_type: 'read',
      resource_ids: this.appId,
    };
    let appIds: number[];

    return from(queryExternalAppsAsync({ params })).pipe(
      mergeMap((resp) => {
        appIds = _.get(resp, 'data[0].apps') || [];
        return from(CommonModel.getAppIdNameMap(appIds));
      }),
      map((appIdNameMap) => {
        return _.map(appIds, (id) => ({
          id: String(id),
          name: appIdNameMap[id],
        }));
      }),
    );
  }

  // 创建
  public createInstitution(data: IExternalInstitution): Observable<IBusinessResult<ITableData>> {
    return from(
      postAuthorizationAsync({
        resource_ids: [`${this.appId}`],
        grantee_name_id_pair: [
          {
            grantee_id: data.id,
            grantee_name: data.name,
            grantee_app_id: data.id,
          },
        ],
      }),
    ).pipe(
      map((resp) => {
        return {
          success: resp.success,
          result: resp.success ? data : undefined,
        };
      }),
    );
  }

  // 删除
  public deleteInstitution(data: ITableData): Observable<IBusinessResult<ITableData>> {
    return from(
      deleteAuthorizationAsync({
        resource_ids: [`${this.appId}`],
        grantee_ids: [data.id],
      }),
    ).pipe(
      map((deleteResp) => {
        return {
          success: deleteResp.success,
          result: deleteResp.success ? data : undefined,
        };
      }),
    );
  }

  // 通过机构id查询名称
  public queryNameById(id: string): Observable<string> {
    return from(CommonModel.getAppIdNameMap([Number(id)])).pipe(
      map((map) => {
        return map[Number(id)];
      }),
    );
  }
}
