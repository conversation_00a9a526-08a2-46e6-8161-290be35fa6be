<!DOCTYPE html>
<html lang="zh-cmn-Hans">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no, viewport-fit=cover" />
    <meta name="theme-color" content="#000000" />
    <title></title>
    <meta name="description" content="<%- description %>" />
    <style rel="stylesheet">html,body,#root,#micro-root{width:100%;height:100%;margin:0;padding:0;}*{box-sizing:border-box;}</style>
    <script>window.__dm_memory_leak_list=[];</script>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root">
      <style>
        :root {
          --color: #1890ff;
        }
        .dm-center {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
        }
        .dm-spin {
          position: absolute;
          display: none;
          box-sizing: border-box;
          margin: 0;
          padding: 0;
          color: var(--color);
          font-size: 14px;
          font-variant: tabular-nums;
          line-height: 1.5;
          text-align: center;
          list-style: none;
          opacity: 0;
          transition: transform 0.3s cubic-bezier(0.78, 0.14, 0.15, 0.86);
          font-feature-settings: 'tnum';
        }
        .dm-spin-spinning {
          position: static;
          display: inline-block;
          opacity: 1;
        }
        .dm-spin-dot {
          position: relative;
          display: inline-block;
          width: 20px;
          height: 20px;
          font-size: 20px;
        }
        .dm-spin-dot-item {
          position: absolute;
          display: block;
          width: 9px;
          height: 9px;
          background-color: var(--color);
          border-radius: 100%;
          transform: scale(0.75);
          transform-origin: 50% 50%;
          opacity: 0.3;
          animation: dmSpinMove 1s infinite linear alternate;
        }
        .dm-spin-dot-item:nth-child(1) {
          top: 0;
          left: 0;
        }
        .dm-spin-dot-item:nth-child(2) {
          top: 0;
          right: 0;
          animation-delay: 0.4s;
        }
        .dm-spin-dot-item:nth-child(3) {
          right: 0;
          bottom: 0;
          animation-delay: 0.8s;
        }
        .dm-spin-dot-item:nth-child(4) {
          bottom: 0;
          left: 0;
          animation-delay: 1.2s;
        }
        .dm-spin-dot-spin {
          transform: rotate(45deg);
          animation: dmRotate 1.2s infinite linear;
        }
        .dm-spin-lg .dm-spin-dot {
          width: 32px;
          height: 32px;
          font-size: 32px;
        }
        .dm-spin-lg .dm-spin-dot em {
          width: 14px;
          height: 14px;
        }
        @-webkit-keyframes dmSpinMove {
          to {
            opacity: 1;
          }
        }
        @keyframes dmSpinMove {
          to {
            opacity: 1;
          }
        }
        @-webkit-keyframes dmRotate {
          to {
            transform: rotate(405deg);
          }
        }
        @keyframes dmRotate {
          to {
            transform: rotate(405deg);
          }
        }
        .dm-window_title_tip {
          display: flex;
          align-items: center;
          margin-top: 30px;
          font-size: 16px;
        }
      </style>
      <div class="dm-center" style="flex-direction: column; height: 100%">
        <div class="dm-center">
          <div class="dm-center" style="box-sizing: content-box; height: 37px">
            <div class="dm-spin dm-spin-lg dm-spin-spinning">
              <span class="dm-spin-dot dm-spin-dot-spin">
                <em class="dm-spin-dot-item"></em>
                <em class="dm-spin-dot-item"></em>
                <em class="dm-spin-dot-item"></em>
                <em class="dm-spin-dot-item"></em>
              </span>
            </div>
          </div>
          <div class="dm-window_title_tip"></div>
        </div>
      </div>
    </div>
    <%- extra %>
  </body>
</html>
