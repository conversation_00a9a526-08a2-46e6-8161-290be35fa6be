import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';
import { updateDatlasConfig } from './configManager';
import { ProductTemplateContext, ProductTemplateManager } from './productTemplateManager';

export function activate(context: vscode.ExtensionContext) {
  console.log('MDT Product Creator 扩展已激活！');

  // 注册创建产品命令
  let disposable = vscode.commands.registerCommand('mdt-product-creator.createProduct', async () => {
    try {
      await createProduct();
    } catch (error) {
      vscode.window.showErrorMessage(`创建产品失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  });

  context.subscriptions.push(disposable);
}

async function createProduct() {
  // 获取工作区根目录
  const workspaceFolders = vscode.workspace.workspaceFolders;
  if (!workspaceFolders) {
    throw new Error('未打开工作区');
  }
  const rootPath = workspaceFolders[0].uri.fsPath;

  // 首先选择创建类型：产品或模块
  const itemType = await vscode.window.showQuickPick(
    [
      {
        label: '产品',
        description: '创建完整的前端产品，如data-market',
        detail: '产品会包含完整的路由、权限等功能，可以独立访问',
        value: 'product',
      },
      {
        label: '模块',
        description: '创建功能模块，如resource-share',
        detail: '模块通常作为产品的一部分，依赖于产品进行访问',
        value: 'module',
      },
    ],
    {
      placeHolder: '请选择要创建的类型',
      ignoreFocusOut: true,
    },
  );

  if (!itemType) {
    throw new Error('未选择创建类型');
  }

  const isProduct = itemType.value === 'product';

  // 1. 获取产品名称和中文名称
  const productId = await vscode.window.showInputBox({
    prompt: `请输入${isProduct ? '产品' : '模块'}ID（英文，如 ${isProduct ? 'data-factory' : 'resource-share'}）`,
    placeHolder: `${isProduct ? 'product' : 'module'}-name`,
    ignoreFocusOut: true,
  });

  if (!productId) {
    throw new Error(`${isProduct ? '产品' : '模块'}ID不能为空`);
  }

  // 验证产品ID格式
  if (!/^[a-z][a-z0-9-]*$/.test(productId)) {
    throw new Error(`${isProduct ? '产品' : '模块'}ID必须以小写字母开头，只能包含小写字母、数字和短横线`);
  }

  const productName = await vscode.window.showInputBox({
    prompt: `请输入${isProduct ? '产品' : '模块'}中文名称`,
    placeHolder: `${isProduct ? '产品' : '模块'}名称`,
    ignoreFocusOut: true,
  });

  if (!productName) {
    throw new Error(`${isProduct ? '产品' : '模块'}中文名称不能为空`);
  }

  const productDesc = await vscode.window.showInputBox({
    prompt: `请输入${isProduct ? '产品' : '模块'}描述`,
    placeHolder: `${isProduct ? '产品' : '模块'}描述`,
    ignoreFocusOut: true,
  });

  if (!productDesc) {
    throw new Error(`${isProduct ? '产品' : '模块'}描述不能为空`);
  }

  // 2. 检查产品目录是否已存在
  const productDirPath = path.join(rootPath, 'products', productId);
  if (fs.existsSync(productDirPath)) {
    throw new Error(`${isProduct ? '产品' : '模块'}目录 ${productId} 已存在`);
  }

  // 3. 开始创建产品
  await vscode.window.withProgress(
    {
      location: vscode.ProgressLocation.Notification,
      title: `正在创建${isProduct ? '产品' : '模块'}: ${productName}`,
      cancellable: false,
    },
    async (progress: vscode.Progress<{ increment: number; message: string }>) => {
      progress.report({ increment: 0, message: `准备创建${isProduct ? '产品' : '模块'}...` });

      // 创建产品模板上下文
      const templateContext: ProductTemplateContext = {
        productId,
        productName,
        productDesc,
        date: new Date().toISOString().split('T')[0],
        isProduct,
      };

      // 创建产品模板管理器
      const templateManager = new ProductTemplateManager(rootPath, templateContext);

      progress.report({ increment: 30, message: `创建${isProduct ? '产品' : '模块'}文件...` });

      // 创建产品所有文件
      templateManager.createAll();

      progress.report({ increment: 20, message: '更新datlas配置...' });

      // 更新 datlasConfig.ts 中的产品配置
      updateDatlasConfig(rootPath, productId, productName, isProduct);

      // 获取产品简写名称
      const shortName = getProductShortName(productId);

      progress.report({ increment: 20, message: '更新常量文件...' });

      // 直接更新constants文件，添加枚举值
      updateConstantsFile(rootPath, productId, shortName);

      progress.report({ increment: 15, message: '更新AppHeader配置...' });

      // 更新AppHeader和utils相关文件
      updateAppHeaderFiles(rootPath, productId, productName, isProduct);

      progress.report({ increment: 15, message: '完成' });
    },
  );

  vscode.window.showInformationMessage(`${isProduct ? '产品' : '模块'} ${productName} 创建成功！`);
}

/**
 * 获取产品简写名称
 * 例如：data-market -> dm, user -> us
 */
function getProductShortName(id: string): string {
  const words = id.split('-');
  if (words.length === 1) {
    // 如果只有一个单词，取前两个字母
    return words[0].substring(0, 2);
  } else {
    // 如果有多个单词，取每个单词的首字母
    return words.map((word) => word.charAt(0)).join('');
  }
}

/**
 * 更新constants文件，添加枚举值
 * @param rootPath 项目根路径
 * @param productId 产品ID
 * @param shortName 产品简写名称
 */
function updateConstantsFile(rootPath: string, productId: string, shortName: string): void {
  // 修正为正确的路径 constants 而非 contants
  const constantsPath = path.join(rootPath, 'shares', 'product-comm', 'src', 'constants', 'enum.ts');

  if (!fs.existsSync(constantsPath)) {
    vscode.window.showWarningMessage(`未找到常量文件: ${constantsPath}，请手动添加枚举值`);
    return;
  }

  // 读取constants文件内容
  let content = fs.readFileSync(constantsPath, 'utf8');

  const productIdUpper = productId.toUpperCase().replace(/-/g, '_');

  // 检查枚举值是否已存在
  if (content.includes(`${productIdUpper} = `)) {
    // 枚举值已存在，不需要更新
    return;
  }

  // 更新ProductPrefixEnum
  const prefixEnumRegex = /export enum ProductPrefixEnum \{([^}]*)\}/s;
  const prefixMatch = content.match(prefixEnumRegex);

  if (prefixMatch) {
    const newPrefixContent = `${prefixMatch[1]}  ${productIdUpper} = '${shortName}',\n`;
    content = content.replace(prefixEnumRegex, `export enum ProductPrefixEnum {${newPrefixContent}}`);
  } else {
    vscode.window.showWarningMessage(`在${constantsPath}中未找到ProductPrefixEnum，请手动添加`);
  }

  // 更新ModuleIdEnum
  const moduleEnumRegex = /export enum ModuleIdEnum \{([^}]*)\}/s;
  const moduleMatch = content.match(moduleEnumRegex);

  if (moduleMatch) {
    const newModuleContent = `${moduleMatch[1]}  ${productIdUpper} = '${productId}',\n`;
    content = content.replace(moduleEnumRegex, `export enum ModuleIdEnum {${newModuleContent}}`);
  } else {
    vscode.window.showWarningMessage(`在${constantsPath}中未找到ModuleIdEnum，请手动添加`);
  }

  // 保存更新后的内容
  fs.writeFileSync(constantsPath, content);

  vscode.window.showInformationMessage(
    `已在${constantsPath}中添加枚举值：\nProductPrefixEnum.${productIdUpper} = '${shortName}'\nModuleIdEnum.${productIdUpper} = '${productId}'`,
  );
}

/**
 * 更新AppHeader相关文件
 * @param rootPath 项目根路径
 * @param productId 产品ID
 * @param productName 产品名称
 * @param isProduct 是否是产品
 */
function updateAppHeaderFiles(rootPath: string, productId: string, productName: string, isProduct: boolean): void {
  // 1. 更新 AppHeaderController.tsx
  updateAppHeaderController(rootPath, productId, productName, isProduct);

  // 2. 更新 AppHeader.tsx
  updateAppHeader(rootPath, productId, productName, isProduct);

  // 3. 更新 product-comm/src/utils
  updateProductUtils(rootPath, productId, productName, isProduct);

  // 4. 更新 AbstractAppController.ts
  updateAbstractAppController(rootPath, productId, productName);

  // 5. 更新 BaseUserPermissionController.ts
  updateBaseUserPermissionController(rootPath, productId, isProduct);

  // 6. 如果是产品，更新IProductPermission接口
  if (isProduct) {
    updateProductPermissionInterface(rootPath, productId);
  }
}

/**
 * 更新AppHeaderController.tsx文件
 * @param rootPath 项目根路径
 * @param productId 产品ID
 * @param productName 产品名称
 * @param isProduct 是否是产品
 */
function updateAppHeaderController(rootPath: string, productId: string, productName: string, isProduct: boolean): void {
  const appHeaderControllerPath = path.join(
    rootPath,
    'shares',
    'product-micro-modules',
    'src',
    'pages',
    'app-header',
    'AppHeaderController.tsx',
  );

  if (!fs.existsSync(appHeaderControllerPath)) {
    vscode.window.showWarningMessage(`未找到AppHeaderController文件: ${appHeaderControllerPath}`);
    return;
  }

  // 读取文件内容
  let content = fs.readFileSync(appHeaderControllerPath, 'utf8');

  const productIdUpper = productId.toUpperCase().replace(/-/g, '_');
  const camelProductId = toCamelCase(productId);
  const capitalizedProductId = camelProductId.charAt(0).toUpperCase() + camelProductId.slice(1);

  // 检查方法是否已存在
  if (content.includes(`click${capitalizedProductId}`)) {
    // 方法已存在，不需要更新
    return;
  }

  // 1. 添加新的点击方法 - 使用注释作为参考点
  const commentRegex = /\/\*\* 产品跳转\(脚本注释\) \*\//;
  const commentMatch = content.match(commentRegex);

  if (commentMatch) {
    // 添加新方法在注释上方
    const newMethod = `  public click${capitalizedProductId}() {
    this.app.jumpToProduct${capitalizedProductId}({}, { replace: this.isJumpProductReplace });
  }

  `;
    content = content.replace(commentRegex, `${newMethod}/** 产品跳转(脚本注释) */`);

    // 保存更新后的内容
    fs.writeFileSync(appHeaderControllerPath, content);

    vscode.window.showInformationMessage(`已在${appHeaderControllerPath}中添加方法：click${capitalizedProductId}`);
  } else {
    vscode.window.showWarningMessage(`在AppHeaderController中未找到注释: /** 产品跳转(脚本注释) */`);
  }

  // 2. 更新接口定义
  if (isProduct) {
    // 在IProductOpt中添加新产品定义
    const productOptRegex = /export interface IProductOpt {[\s\S]*?enableOneTable\?: boolean;/;
    const productOptMatch = content.match(productOptRegex);

    if (productOptMatch) {
      const optItem = `\n  enable${capitalizedProductId}?: boolean;`;
      content = content.replace(productOptRegex, `${productOptMatch[0]}${optItem}`);

      // 保存更新后的内容
      fs.writeFileSync(appHeaderControllerPath, content);
    }
  } else {
    // 在IDynamicOpt中添加新模块定义
    const dynamicOptRegex = /export interface IDynamicOpt {[\s\S]*?enable\w+\?: boolean;/;
    const dynamicOptMatch = content.match(dynamicOptRegex);

    if (dynamicOptMatch) {
      const optItem = `\n  enableMenu${capitalizedProductId}?: boolean;`;
      content = content.replace(dynamicOptRegex, `${dynamicOptMatch[0]}${optItem}`);

      // 保存更新后的内容
      fs.writeFileSync(appHeaderControllerPath, content);
    }
  }
}

/**
 * 更新AppHeader.tsx文件
 * @param rootPath 项目根路径
 * @param productId 产品ID
 * @param productName 产品名称
 * @param isProduct 是否是产品
 */
function updateAppHeader(rootPath: string, productId: string, productName: string, isProduct: boolean): void {
  const appHeaderPath = path.join(
    rootPath,
    'shares',
    'product-micro-modules',
    'src',
    'pages',
    'app-header',
    'AppHeader.tsx',
  );

  if (!fs.existsSync(appHeaderPath)) {
    vscode.window.showWarningMessage(`未找到AppHeader文件: ${appHeaderPath}`);
    return;
  }

  // 读取文件内容
  let content = fs.readFileSync(appHeaderPath, 'utf8');

  const productIdUpper = productId.toUpperCase().replace(/-/g, '_');
  const camelProductId = toCamelCase(productId);
  const capitalizedProductId = camelProductId.charAt(0).toUpperCase() + camelProductId.slice(1);

  if (isProduct) {
    // 1. 添加JUMP_XX_URL导入
    const urlImportRegex = /import \{[\s\S]*?JUMP_[A-Z_]+_URL/;
    const urlImportMatch = content.match(urlImportRegex);

    if (urlImportMatch && !content.includes(`JUMP_${productIdUpper}_URL`)) {
      const urlConstName = `JUMP_${productIdUpper}_URL`;
      const originalImport = urlImportMatch[0];
      const newImport = `${originalImport}, ${urlConstName}`;
      content = content.replace(urlImportRegex, newImport);
    }

    // 2. 在产品配置注释下方添加新产品配置
    const productCommentRegex = /\/\*\* 产品配置\(脚本注释\) \*\//;
    const productCommentMatch = content.match(productCommentRegex);

    if (productCommentMatch) {
      // 检查产品配置是否已存在
      if (content.includes(`const ${camelProductId} =`)) {
        // 产品配置已存在，不需要添加
        return;
      }

      const commentIndex = productCommentMatch.index!;
      const lineEndIndex = content.indexOf('\n', commentIndex);

      // 添加新的产品配置，使用IconDataMap作为图标组件
      const newProductConfig = `
  const ${camelProductId} = authObj.enable${capitalizedProductId} && (
    <div className="prod_icon_list-item" onClick={() => onClickHandle(() => controller.click${capitalizedProductId}())}>
      <IconDataMap />
      <span>{getProductName(ModuleIdEnum.${productIdUpper})}</span>
      <ProductLink url={controller.getProductAuthUrl(JUMP_${productIdUpper}_URL)} />
    </div>
  );
`;
      content = content.substring(0, lineEndIndex + 1) + newProductConfig + content.substring(lineEndIndex + 1);

      // 3. 在产品列表注释上方添加新产品引用
      const productListCommentRegex = /{\s*\/\*\* 产品列表\(脚本注释\) \*\/\s*}/;
      const productListMatch = content.match(productListCommentRegex);

      if (productListMatch) {
        const productListIndex = productListMatch.index!;

        // 在产品列表注释上方添加新产品
        const newProductRef = `      {${camelProductId}}\n      `;
        content = content.substring(0, productListIndex) + newProductRef + content.substring(productListIndex);
      }

      // 保存更新后的内容
      fs.writeFileSync(appHeaderPath, content);

      vscode.window.showInformationMessage(`已在${appHeaderPath}中添加产品配置: ${camelProductId}`);
    } else {
      vscode.window.showWarningMessage(`在AppHeader中未找到产品配置注释: /** 产品配置(脚本注释) */`);
    }
  } else {
    // 为模块添加配置
    const moduleCommentRegex = /\/\*\* 模块配置\(脚本注释\) \*\//;
    const moduleCommentMatch = content.match(moduleCommentRegex);

    if (moduleCommentMatch) {
      // 检查模块配置是否已存在
      const moduleName = `menu${capitalizedProductId}`;
      if (content.includes(`const ${moduleName} =`)) {
        // 模块配置已存在，不需要添加
        return;
      }

      const commentIndex = moduleCommentMatch.index!;
      const lineEndIndex = content.indexOf('\n', commentIndex);

      // 添加新的模块配置
      const newModuleConfig = `
  const ${moduleName} = dynamicOpt.enableMenu${capitalizedProductId} ? (
    <RcMenuItem key={ModuleIdEnum.${productIdUpper}} onClick={() => controller.click${capitalizedProductId}()}>
      <span>${productName}</span>
    </RcMenuItem>
  ) : null;
`;
      content = content.substring(0, lineEndIndex + 1) + newModuleConfig + content.substring(lineEndIndex + 1);

      // 在模块列表注释上方添加新模块引用
      const moduleListCommentRegex = /{\s*\/\*\* 模块列表\(脚本注释\) \*\/\s*}/;
      const moduleListMatch = content.match(moduleListCommentRegex);

      if (moduleListMatch) {
        const moduleListIndex = moduleListMatch.index!;

        // 在模块列表注释上方添加新模块
        const newModuleRef = `        {${moduleName}}\n        `;
        content = content.substring(0, moduleListIndex) + newModuleRef + content.substring(moduleListIndex);
      }

      // 保存更新后的内容
      fs.writeFileSync(appHeaderPath, content);

      vscode.window.showInformationMessage(`已在${appHeaderPath}中添加模块配置: ${moduleName}`);
    } else {
      vscode.window.showWarningMessage(`在AppHeader中未找到模块配置注释: /** 模块配置(脚本注释) */`);
    }
  }
}

/**
 * 更新product-comm/src/utils文件
 * @param rootPath 项目根路径
 * @param productId 产品ID
 * @param productName 产品名称
 * @param isProduct 是否是产品
 */
function updateProductUtils(rootPath: string, productId: string, productName: string, isProduct: boolean): void {
  // 1. 更新getProductName.ts
  updateGetProductName(rootPath, productId, productName);

  // 2. 根据类型更新getProductIds.ts或getModuleIds.ts
  if (isProduct) {
    updateGetProductIds(rootPath, productId);
  } else {
    updateGetModuleIds(rootPath, productId);
  }
}

/**
 * 更新getProductName.ts文件
 * @param rootPath 项目根路径
 * @param productId 产品ID
 * @param productName 产品名称
 */
function updateGetProductName(rootPath: string, productId: string, productName: string): void {
  const commonUtilPath = path.join(rootPath, 'shares', 'product-comm', 'src', 'utils', 'commonUtil.ts');

  if (!fs.existsSync(commonUtilPath)) {
    vscode.window.showWarningMessage(`未找到commonUtil.ts文件: ${commonUtilPath}`);
    return;
  }

  // 读取文件内容
  let content = fs.readFileSync(commonUtilPath, 'utf8');

  const productIdUpper = productId.toUpperCase().replace(/-/g, '_');

  // 检查是否已存在该产品的名称定义
  if (content.includes(`[ModuleIdEnum.${productIdUpper}]`)) {
    return;
  }

  // 查找getProductName函数
  const getProductNameRegex =
    /export const getProductName = \(moduleId: ModuleIdEnum \| string, defaultVal\?: string\) => {[\s\S]*?const productNameMap: Record<string, string> = {([\s\S]*?)};/;
  const match = content.match(getProductNameRegex);

  if (match) {
    const mapContent = match[1];
    // 在productNameMap中添加新的映射
    const newMapContent = `${mapContent}${
      mapContent.trim().endsWith(',') ? '' : ','
    }\n    [ModuleIdEnum.${productIdUpper}]: '${productName}',`;

    content = content.replace(
      getProductNameRegex,
      `export const getProductName = (moduleId: ModuleIdEnum | string, defaultVal?: string) => {
  const productNameMap: Record<string, string> = {${newMapContent}
  };`,
    );

    // 保存更新后的内容
    fs.writeFileSync(commonUtilPath, content);

    vscode.window.showInformationMessage(
      `已在${commonUtilPath}中添加产品名称定义：[ModuleIdEnum.${productIdUpper}]: '${productName}'`,
    );
  } else {
    vscode.window.showWarningMessage(`在${commonUtilPath}中未找到getProductName函数`);
  }
}

/**
 * 更新getProductIds.ts文件
 * @param rootPath 项目根路径
 * @param productId 产品ID
 */
function updateGetProductIds(rootPath: string, productId: string): void {
  const commonUtilPath = path.join(rootPath, 'shares', 'product-comm', 'src', 'utils', 'commonUtil.ts');

  if (!fs.existsSync(commonUtilPath)) {
    vscode.window.showWarningMessage(`未找到commonUtil.ts文件: ${commonUtilPath}`);
    return;
  }

  // 读取文件内容
  let content = fs.readFileSync(commonUtilPath, 'utf8');

  const productIdUpper = productId.toUpperCase().replace(/-/g, '_');

  // 检查是否已存在该产品ID
  if (content.includes(`ModuleIdEnum.${productIdUpper},`)) {
    return;
  }

  // 查找getProductIds函数
  const getProductIdsRegex = /export const getProductIds = \(\) => \[([\s\S]*?)\];/;
  const match = content.match(getProductIdsRegex);

  if (match) {
    const idsContent = match[1];
    // 添加新的产品ID
    const newIdsContent = idsContent.trim() + (idsContent.trim() ? '\n  ' : '') + `ModuleIdEnum.${productIdUpper},`;

    content = content.replace(getProductIdsRegex, `export const getProductIds = () => [${newIdsContent}\n];`);

    // 保存更新后的内容
    fs.writeFileSync(commonUtilPath, content);

    vscode.window.showInformationMessage(
      `已在${commonUtilPath}中的getProductIds函数中添加：ModuleIdEnum.${productIdUpper}`,
    );
  } else {
    vscode.window.showWarningMessage(`在${commonUtilPath}中未找到getProductIds函数`);
  }
}

/**
 * 更新getModuleIds.ts文件
 * @param rootPath 项目根路径
 * @param productId 产品ID
 */
function updateGetModuleIds(rootPath: string, productId: string): void {
  const commonUtilPath = path.join(rootPath, 'shares', 'product-comm', 'src', 'utils', 'commonUtil.ts');

  if (!fs.existsSync(commonUtilPath)) {
    vscode.window.showWarningMessage(`未找到commonUtil.ts文件: ${commonUtilPath}`);
    return;
  }

  // 读取文件内容
  let content = fs.readFileSync(commonUtilPath, 'utf8');

  const productIdUpper = productId.toUpperCase().replace(/-/g, '_');

  // 检查是否已存在该模块ID
  if (content.includes(`ModuleIdEnum.${productIdUpper},`)) {
    return;
  }

  // 查找getModuleIds函数
  const getModuleIdsRegex = /export const getModuleIds = \(\) => \[([\s\S]*?)\];/;
  const match = content.match(getModuleIdsRegex);

  if (match) {
    const idsContent = match[1];
    // 添加新的模块ID
    const newIdsContent = idsContent.trim() + (idsContent.trim() ? '\n  ' : '') + `ModuleIdEnum.${productIdUpper},`;

    content = content.replace(getModuleIdsRegex, `export const getModuleIds = () => [${newIdsContent}\n];`);

    // 保存更新后的内容
    fs.writeFileSync(commonUtilPath, content);

    vscode.window.showInformationMessage(
      `已在${commonUtilPath}中的getModuleIds函数中添加：ModuleIdEnum.${productIdUpper}`,
    );
  } else {
    vscode.window.showWarningMessage(`在${commonUtilPath}中未找到getModuleIds函数`);
  }
}

/**
 * 将短横线分隔的字符串转换为驼峰命名
 * @param str 短横线分隔的字符串
 * @returns 驼峰命名的字符串
 */
function toCamelCase(str: string): string {
  return str.replace(/-([a-z])/g, (match, letter) => letter.toUpperCase());
}

/**
 * 更新AbstractAppController.ts文件，添加新产品的跳转方法
 * @param rootPath 项目根路径
 * @param productId 产品ID
 * @param productName 产品名称
 */
function updateAbstractAppController(rootPath: string, productId: string, productName: string): void {
  const abstractAppControllerPath = path.join(
    rootPath,
    'shares',
    'product-micro-modules',
    'src',
    'controllers',
    'AbstractAppController.ts',
  );

  if (!fs.existsSync(abstractAppControllerPath)) {
    vscode.window.showWarningMessage(`未找到AbstractAppController文件: ${abstractAppControllerPath}`);
    return;
  }

  // 读取文件内容
  let content = fs.readFileSync(abstractAppControllerPath, 'utf8');

  const camelProductId = toCamelCase(productId);
  const capitalizedProductId = camelProductId.charAt(0).toUpperCase() + camelProductId.slice(1);

  // 检查方法是否已存在
  if (content.includes(`jumpToProduct${capitalizedProductId}`)) {
    // 方法已存在，不需要更新
    return;
  }

  // 找到产品跳转相关注释
  const commentRegex = /\/\*\* 产品跳转相关\(脚本注释\) \*\//;
  const commentMatch = content.match(commentRegex);

  if (commentMatch) {
    const commentIndex = commentMatch.index!;
    const commentEndIndex = commentIndex + commentMatch[0].length;

    // 找到jumpToProductResourceShare方法作为参考点
    const referenceMethodRegex =
      /public jumpToProductResourceShare\(param\?: Record<string, any>, options\?: IJumpToOtherProductOptions\) {[\s\S]*?}/;
    const referenceMatch = content.match(referenceMethodRegex);

    if (referenceMatch) {
      // 找到注释后的第一个换行符位置
      const newLineIndex = content.indexOf('\n', commentEndIndex);

      // 准备新方法
      const newMethod = `
  public jumpToProduct${capitalizedProductId}(param?: Record<string, any>, options?: IJumpToOtherProductOptions) {
    this.handleEmptyFn(param, options);
  }
    
  `;

      // 在注释后插入新方法
      content = content.substring(0, newLineIndex + 1) + newMethod + content.substring(newLineIndex + 1);

      // 保存更新后的内容
      fs.writeFileSync(abstractAppControllerPath, content);

      vscode.window.showInformationMessage(
        `已在${abstractAppControllerPath}中添加跳转方法：jumpToProduct${capitalizedProductId}`,
      );
    } else {
      vscode.window.showWarningMessage(`在AbstractAppController中未找到jumpToProductResourceShare方法作为参考`);
    }
  } else {
    vscode.window.showWarningMessage(`在AbstractAppController中未找到注释: /** 产品跳转相关(脚本注释) */`);
  }
}

/**
 * 更新BaseUserPermissionController.ts文件，添加新的权限项
 * @param rootPath 项目根路径
 * @param productId 产品/模块ID
 * @param isProduct 是否是产品（而非模块）
 */
function updateBaseUserPermissionController(rootPath: string, productId: string, isProduct: boolean): void {
  const baseUserPermissionControllerPath = path.join(
    rootPath,
    'shares',
    'product-comm',
    'src',
    'controllers',
    'BaseUserPermissionController.ts',
  );

  if (!fs.existsSync(baseUserPermissionControllerPath)) {
    vscode.window.showWarningMessage(`未找到BaseUserPermissionController文件: ${baseUserPermissionControllerPath}`);
    return;
  }

  // 读取文件内容
  let content = fs.readFileSync(baseUserPermissionControllerPath, 'utf8');

  // 处理productId，转换为驼峰命名
  const camelProductId = toCamelCase(productId);
  const capitalizedProductId = camelProductId.charAt(0).toUpperCase() + camelProductId.slice(1);
  const shortName = getProductShortName(productId);

  if (isProduct) {
    // 为产品添加权限项
    const productPermissionComment = /\/\*\* 产品添加\(脚本注释\) \*\//;
    const commentMatch = content.match(productPermissionComment);

    if (commentMatch) {
      const commentIndex = commentMatch.index!;
      const lineEndIndex = content.indexOf('\n', commentIndex);

      // 检查权限项是否已存在
      const permissionKey = `enable${capitalizedProductId}:`;
      if (content.includes(permissionKey)) {
        return; // 权限项已存在，不需要添加
      }

      // 在注释下一行添加新的权限项
      const newPermission = `      ${permissionKey} true,\n`;
      content = content.substring(0, lineEndIndex + 1) + newPermission + content.substring(lineEndIndex + 1);

      // 保存更新后的内容
      fs.writeFileSync(baseUserPermissionControllerPath, content);

      vscode.window.showInformationMessage(
        `已在${baseUserPermissionControllerPath}中添加产品权限项：${permissionKey} true`,
      );
    } else {
      vscode.window.showWarningMessage(`在BaseUserPermissionController中未找到产品权限注释: /** 产品添加(脚本注释) */`);
    }
  } else {
    // 为模块添加权限项
    const modulePermissionComment = /\/\*\* 模块添加\(脚本注释\) \*\//;
    const commentMatch = content.match(modulePermissionComment);

    if (commentMatch) {
      const commentIndex = commentMatch.index!;
      const lineEndIndex = content.indexOf('\n', commentIndex);

      // 检查权限项是否已存在
      const permissionKey = `enableMenu${capitalizedProductId}:`;
      if (content.includes(permissionKey)) {
        return; // 权限项已存在，不需要添加
      }

      // 在注释下一行添加新的权限项
      const newPermission = `      ${permissionKey} true,\n`;
      content = content.substring(0, lineEndIndex + 1) + newPermission + content.substring(lineEndIndex + 1);

      // 保存更新后的内容
      fs.writeFileSync(baseUserPermissionControllerPath, content);

      vscode.window.showInformationMessage(
        `已在${baseUserPermissionControllerPath}中添加模块权限项：${permissionKey} true`,
      );
    } else {
      vscode.window.showWarningMessage(`在BaseUserPermissionController中未找到模块权限注释: /** 模块添加(脚本注释) */`);
    }
  }
}

/**
 * 更新IProductPermission接口，添加新产品的权限字段
 * @param rootPath 项目根路径
 * @param productId 产品ID
 */
function updateProductPermissionInterface(rootPath: string, productId: string): void {
  const interfacesFilePath = path.join(rootPath, 'shares', 'product-comm', 'src', 'interfaces', 'index.ts');

  if (!fs.existsSync(interfacesFilePath)) {
    vscode.window.showWarningMessage(`未找到接口文件: ${interfacesFilePath}`);
    return;
  }

  // 读取接口文件内容
  let content = fs.readFileSync(interfacesFilePath, 'utf8');

  // 处理productId，转换为驼峰命名
  const camelProductId = toCamelCase(productId);
  const capitalizedProductId = camelProductId.charAt(0).toUpperCase() + camelProductId.slice(1);

  // 查找IProductPermission接口
  const interfaceRegex = /export interface IProductPermission \{([\s\S]*?)\}/;
  const interfaceMatch = content.match(interfaceRegex);

  if (interfaceMatch && interfaceMatch.index !== undefined) {
    // 检查权限字段是否已存在
    const permissionField = `enable${capitalizedProductId}: boolean;`;
    if (content.includes(permissionField)) {
      return; // 权限字段已存在，不需要添加
    }

    // 获取接口内容
    const interfaceContent = interfaceMatch[1];
    const lastPropertyIndex = interfaceContent.lastIndexOf(';');

    if (lastPropertyIndex !== -1) {
      // 在最后一个属性后添加新的权限字段
      const newContent =
        content.substring(
          0,
          interfaceMatch.index + 'export interface IProductPermission {'.length + lastPropertyIndex + 1,
        ) +
        `\n  ${permissionField}` +
        content.substring(
          interfaceMatch.index + 'export interface IProductPermission {'.length + lastPropertyIndex + 1,
        );

      // 保存更新后的内容
      fs.writeFileSync(interfacesFilePath, newContent);

      vscode.window.showInformationMessage(`已在${interfacesFilePath}中添加产品权限字段：${permissionField}`);
    }
  } else {
    vscode.window.showWarningMessage(`在${interfacesFilePath}中未找到IProductPermission接口`);
  }
}

export function deactivate() {}
