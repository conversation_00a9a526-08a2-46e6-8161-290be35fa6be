import { useController } from '@mdtBsComm/hooks/use-controller';
import { WorkflowEdit, WorkflowEditController, WorkflowEditModel } from '@mdtProMicroModules/pages/workflow-edit';
import { AppController } from '../../app/AppController';

const BpmnEditorView = () => {
  const [controller] = useController(() => {
    const ctrl = new WorkflowEditController(AppController.getInstance(), {
      Model: WorkflowEditModel,
      cancelEdit: () => {},
    });
    return [ctrl, null];
  });

  return <WorkflowEdit controller={controller} />;
};

export default BpmnEditorView;
