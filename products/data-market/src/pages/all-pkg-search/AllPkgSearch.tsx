import { FC } from 'react';
import { TableCardDatapkgSearch } from '@mdtProMicroModules/containers/table-card-datapkg-search';
import { DrawerDatapkgDetailPreview } from '@mdtProMicroModules/pages/datapkg-detail-preview';
import { AllPkgSearchController } from './AllPkgSearchController';

// 全部数据包检索页面==================================================================================
interface IProps {
  controller: AllPkgSearchController;
}
const AllPkgSearch: FC<IProps> = ({ controller }) => {
  return (
    <>
      <TableCardDatapkgSearch controller={controller.getTableCardController()} />
      <DrawerDatapkgDetailPreview controller={controller.getDrawerController()} />
    </>
  );
};

export { AllPkgSearch };
