import _ from 'lodash';
import { FormField } from '@mdtBsComm/components/form';
import { useObservableState } from '@mdtBsComm/hooks/use-observable-state';
import { greaterThanTime, lessThanNow } from '@mdtBsComm/utils/dayUtil';
import Checkbox from '@mdtDesign/checkbox';
import DatePicker from '@mdtDesign/date-picker';
import Input from '@mdtDesign/input';
import Select from '@mdtDesign/select';
import TreeSelect from '@mdtDesign/tree-select';
import { IUsers } from '@mdtProComm/interfaces';
import i18n from '../../languages';
import {
  DrawerModifyFormPermissionUserInfoController,
  FormDataAttrs,
  IModalData,
} from './DrawerModifyFormPermissionUserInfoController';
import './index.less';

// 编辑用户基本信息表单================================================================================
interface IProps<V extends IUsers> {
  modalUiData: IModalData;
  controller: DrawerModifyFormPermissionUserInfoController<V>;
}
export function DrawerModifyFormPermissionUserInfoInner<V extends IUsers>({ modalUiData, controller }: IProps<V>) {
  const isBatch = useObservableState(controller.getIsBatch$());
  const batchDom = (
    <>
      <FormField
        name={FormDataAttrs.EXPIRE_TIME}
        label={i18n.chain.proMicroModules.permission.expireTime}
        required={true}
        direction="column"
        className="module_permission-user-info_date-picker"
        rules={[{ required: true, message: i18n.chain.proMicroModules.permission.expireTimeEmptyError }]}
      >
        <DatePicker
          allowClear={false}
          disabledDate={(date) => greaterThanTime(date, modalUiData.appExpireTime) || lessThanNow(date)}
        />
      </FormField>
      <FormField
        valuePropName="checked"
        label={i18n.chain.proMicroModules.permission.isFreeze}
        direction="column"
        name={FormDataAttrs.DISABLE}
      >
        <Checkbox>{i18n.chain.proMicroModules.permission.freeze}</Checkbox>
      </FormField>
    </>
  );
  return (
    <>
      {isBatch && batchDom}
      {!isBatch && (
        <>
          <FormField
            name={FormDataAttrs.NAME}
            label={i18n.chain.proMicroModules.permission.username}
            required
            direction="column"
            rules={[{ required: true, message: i18n.chain.proMicroModules.permission.userNameEmptyError }]}
          >
            <Input allowClear={false} block />
          </FormField>
          <FormField
            name={FormDataAttrs.EMAIL}
            label={i18n.chain.proMicroModules.permission.emailDesc}
            required
            direction="column"
            rules={[
              { required: true, message: i18n.chain.proMicroModules.permission.emailEmptyError },
              { type: 'email', message: i18n.chain.proMicroModules.permission.emailInvalid },
            ]}
          >
            <Input allowClear={false} block />
          </FormField>
          <FormField name={FormDataAttrs.PHONE} label={i18n.chain.proMicroModules.permission.phone} direction="column">
            <Input allowClear={false} block />
          </FormField>
          <FormField
            name={FormDataAttrs.ORGANIZATION}
            label={i18n.chain.proMicroModules.permission.fromOrgs}
            direction="column"
            className="module_permission-user-info_tree-select"
          >
            <TreeSelect
              treeData={modalUiData.orgTreeList}
              treeDefaultExpandAll
              multiple
              filterTreeNode={(inputValue, treeNode) => {
                const { title } = treeNode || {};
                if (_.isString(title)) {
                  return _.includes(title, inputValue);
                }
                // @ts-ignore
                return title?.props?.title ? _.includes(title.props.title, inputValue) : false;
              }}
            />
          </FormField>
          <FormField
            label={i18n.chain.proMicroModules.permission.fromRoles}
            name={FormDataAttrs.ROLE}
            direction="column"
          >
            <Select
              block
              placeholder={i18n.chain.proMicroModules.permission.fromRolesDesc}
              options={modalUiData.roleList}
              mode="multiple"
              className="module_permission-user-info_roles"
            />
          </FormField>
          {batchDom}
        </>
      )}
    </>
  );
}
