import { Observable } from 'rxjs';
import { FormCompController } from '@mdtBsComponents/form-comp';
import { IBusinessResult, ModalWithBtnsCompDialogController } from '@mdtBsComponents/modal-with-btns-comp-dialog';
import toast<PERSON><PERSON> from '@mdtDesign/toast';
import i18n from '../../languages';
import { IExternalInstitution } from '../table-external-institution';
import { DialogModifyFormInstitutionInner } from './DialogModifyFormInstitutionInner';

export type IModifyDataFunc<F, V> = (params: F, originalData?: V) => Promise<IBusinessResult<V>>;

export interface IControllerOptions<V extends IExternalInstitution> {
  selfAppId: number;
  modifyDataFunc: IModifyDataFunc<IExternalInstitution, V>;
  queryNameFunc: (id: string) => Observable<string>;
}

class DialogModifyFormInstitutionController<V extends IExternalInstitution> extends ModalWithBtnsCompDialogController<
  DialogModifyFormInstitutionController<V>,
  V,
  null
> {
  // 表单控制器
  private formCompController: FormCompController<IExternalInstitution>;
  private modifyDataFunc;
  private queryNameFunc;
  private readonly selfAppId: number;

  public constructor(options: IControllerOptions<V>) {
    super({
      modalCompOptions: {
        modalOptions: () => this.initModalOptions(),
      },
      InnerView: DialogModifyFormInstitutionInner,
      clickOkBtnFunc: () => this.onClickDrawerOkBtn(),
    });
    this.modifyDataFunc = options.modifyDataFunc;
    this.queryNameFunc = options.queryNameFunc;
    this.selfAppId = options.selfAppId;
    this.formCompController = new FormCompController();

    this.computeConfirmBtnDisabled();
  }

  public openModal(data?: V) {
    this.formCompController.changeFormData(data);
    return super.openModal(data);
  }

  public getFormCompController() {
    return this.formCompController;
  }

  public destroy() {
    super.destroy();
    this.formCompController.destroy();
    this.modifyDataFunc = null!;
    this.queryNameFunc = null!;
  }

  // 点击弹窗OK按钮
  private onClickDrawerOkBtn = async () => {
    await this.formCompController.validateFormData();

    if (this.formCompController.dataHasError()) {
      return {
        success: false,
      };
    }

    const backendValidate = await this.validate();
    if (!backendValidate) {
      return {
        success: false,
      };
    }

    const value = this.formCompController!.getFormData$().getValue()!;
    return this.modifyDataFunc({ ...value }, this.getModalRest());
  };

  private validate = async () => {
    const value = this.formCompController!.getFormData$().getValue()!;
    if (value.id === `${this.selfAppId}`) {
      toastApi.error(i18n.chain.proMicroModules.institution.cannotAddOwnerApp);
      return false;
    }
    const name = await this.queryNameFunc(value.id).toPromise();
    if (!name) {
      toastApi.error(i18n.chain.proMicroModules.institution.idNotExist);
      return false;
    }
    if (value.name !== name) {
      toastApi.error(i18n.chain.proMicroModules.institution.idNotMatchApp);
      return false;
    }
    return true;
  };

  private initModalOptions() {
    return {
      width: 420,
      title: this.getModalRest()
        ? i18n.chain.proMicroModules.institution.editApp
        : i18n.chain.proMicroModules.institution.addApp,
      centered: true,
    };
  }

  private computeConfirmBtnDisabled() {
    this.formCompController.getFormDataError$().subscribe((val) => this.changeDisabledOkBtn(val));
  }
}

export { DialogModifyFormInstitutionController };
