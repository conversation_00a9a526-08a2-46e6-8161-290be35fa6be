import { IFloworkFormSpec, IRequestOwnerObj } from '@mdtApis/interfaces';
import type { IFeatureFlags } from '../utils/oneTableNewUtil';
export * from './oneTableInfo';
export * from './workflowInfo';

export interface IOneTableNewOperatorDataComm {
  formId: string;
  formName: string;
  formSpec: IFloworkFormSpec;
  formOwner: IRequestOwnerObj;
  rootWfId: string;
  assignWfId: string;
  pkgId: string;
  isFormManageLevelUser: boolean; // 是否是报表管理者
  isNeedApproval: boolean; // 是否需要审批
  primaryAssignee: number; // 负责人
  featureFlags: IFeatureFlags; // 功能开关
  isPeriodic: boolean; // 是否是周期
  isEndless: boolean; // 是否是常态化
  isCollaborate: boolean; // 是否是协同者
  versionName: string;
  versionGroup: number;
}

export interface IOnetableNewFormConfig {
  name?: string;
  description?: string;
  formSpec: Record<string, any>;
  extraMeta: Record<string, any>;
}

export interface IOneTableNewCardComm {
  id: string;
  name: string;
  createTime: string;
}
