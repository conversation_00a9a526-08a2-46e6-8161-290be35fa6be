.one-table-new-mission-center {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  background-color: var(--metro-bg-0);

  .total {
    padding: 6px 16px 0;
  }

  .metro-card {
    height: max-content;
    border-radius: 12px;

    .metro-divider-vertical {
      height: 32px;
    }
  }

  .metro-card-bordered,
  .metro-card-actions {
    border-color: var(--metro-border-0);
  }

  .metro-card-small > .metro-card-body {
    padding: 12px 12px 6px;
  }

  .metro-card-actions > li {
    margin: 4px;
  }

  .card-curd_header {
    position: relative;
    padding: 12px 16px 48px;
    font-size: 20px;
    background: var(--metro-bg-0);

    .form-status-tabs {
      position: absolute;
      bottom: 4px;
      left: 16px;
    }

    .form-statu-sub-tabs {
      position: absolute;
      right: 16px;
      bottom: 4px;
    }
  }

  .card-curd_list-wrap {
    margin: 16px 16px 0;
    background: transparent;

    .dmc-empty {
      background: transparent;
    }

    .item-wrap {
      cursor: pointer;
    }

    .item-status {
      position: absolute;
      top: 12px;
      right: 4px;
    }

    .item-label {
      // stylelint-disable-next-line
      display: -webkit-box;
      height: 39px;
      margin: 10px 0 8px;
      overflow: hidden;
      font-weight: 500;
      font-size: 14px;
      line-height: 20px;
      text-overflow: ellipsis;
      word-break: break-all;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }
  }

  .date {
    font-size: 12px;
  }
}

.one-table-new-mission-center-card-item {
  &:hover {
    border-color: var(--metro-primary-default);

    .item-label {
      color: var(--metro-primary-default);
    }
  }
}
