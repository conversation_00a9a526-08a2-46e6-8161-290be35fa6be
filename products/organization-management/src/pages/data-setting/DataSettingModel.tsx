import _ from 'lodash';
import { from, Observable } from 'rxjs';
import { map, takeWhile } from 'rxjs/operators';
import { IBusinessResult } from '@mdtBsComm/interfaces';
import { queryAlbumsAsync } from '@mdtBsServices/albums';
import { queryGrantedAppsAsync } from '@mdtBsServices/auth';
import { IRequestCancelToken } from '@mdtBsServices/interfaces';
import { putPreferencesAsync } from '@mdtBsServices/preference';
import { TreeLabelValueItemInterface } from '@mdtDesign/check-list';
import { PreferencesEnum } from '@mdtProComm/constants';
import { AppController } from '../../app/AppController';

// 列表结构
export interface ITableData {
  users?: number[];
  themes?: TreeLabelValueItemInterface[];
  apps?: TreeLabelValueItemInterface[];
  noShowOtherApp?: boolean;
}

// 服务存储偏好的格式
interface IResultInterface {
  users?: number[];
  themes?: string[];
  apps?: number[];
  noShowOtherApp?: boolean;
}
export type IResult = IResultInterface[];

type IMapType = Record<string | number, string>;

const LAYER_ENUM = {
  APP: 'app',
  USER: 'user',
};

export class DataSettingModel {
  public static themeIdNameMap: IMapType;
  public static appIdNameMap: IMapType;
  public static layerId?: number;
  private static app: AppController;

  public static initModel(app: AppController) {
    this.app = app;
    return this;
  }

  // 获取可访问的机构列表
  public static queryGrantedApps(): Observable<IBusinessResult<TreeLabelValueItemInterface[]>> {
    return from(queryGrantedAppsAsync()).pipe(
      map((resp) => {
        const data = _.map(resp.data, ({ app_id, name }) => {
          return {
            key: app_id,
            title: name,
          };
        });
        this.initAppIdNameMap(data);
        return {
          success: resp.success,
          result: data,
        };
      }),
    );
  }

  // 获取本机构的主题库
  public static queryAppAlbums(appId: number, cancelToken?: IRequestCancelToken) {
    return from(queryAlbumsAsync({ cancelToken, params: { apps: appId } })).pipe(
      takeWhile((resp) => !resp.canceled),
      map((resp) => {
        const data = _.map(resp.data, ({ id, name }): TreeLabelValueItemInterface => {
          return {
            key: id,
            title: name,
          };
        });
        this.initThemeIdNameMap(data);
        return {
          ...resp,
          data,
        };
      }),
    );
  }

  // 获取自定义主题库列表
  public static getCustomizeThemeList() {
    const listData: IResult = this.app.getPreferencesController()?.getDatapkgVisible();
    return [0, _.map(listData, (item) => this.transformToTableData(item))] as [number, ITableData[]];
  }

  // 删改增自定义主题库
  public static updateCustomizeThemeList(list: ITableData[]): Observable<IBusinessResult<number>> {
    const extraList: IResult = _.map(list, ({ users, themes, apps, noShowOtherApp }) => {
      return {
        users,
        themes: _.map(themes, ({ key }) => key as string),
        apps: _.map(apps, ({ key }) => _.toNumber(key)),
        noShowOtherApp,
      };
    });
    return from(
      putPreferencesAsync({ name: PreferencesEnum.DATAPKG_VISIBLE }, { value: extraList, at: LAYER_ENUM.APP }),
    ).pipe(takeWhile((resp) => !resp.canceled));
  }

  // 初始化theme-map
  public static initThemeIdNameMap(data: TreeLabelValueItemInterface[]) {
    this.themeIdNameMap = this.transformDataToMap(data);
  }

  // 初始化app-map
  public static initAppIdNameMap(data: TreeLabelValueItemInterface[]) {
    this.appIdNameMap = this.transformDataToMap(data);
  }

  // 转换treeObj -> Record<string, string>
  private static transformDataToMap(data: TreeLabelValueItemInterface[]): IMapType {
    const result: IMapType = {};
    _.forEach(data, ({ key, title }) => {
      result[key] = title;
    });
    return result;
  }

  // 翻译结构
  private static transformToTableData(list: IResultInterface): ITableData {
    const mapListFn = (list: any, mapObj: any) =>
      _.map(list, (id) => {
        return {
          key: id,
          title: mapObj[id],
        };
      });
    return {
      ...list,
      users: list.users,
      themes: mapListFn(list.themes, this.themeIdNameMap),
      apps: mapListFn(list.apps, this.appIdNameMap),
    };
  }
}

export type IDataSettingModel = typeof DataSettingModel;
