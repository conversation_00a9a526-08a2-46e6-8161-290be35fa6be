import type { NsGraph } from '@antv/xflow';
import { BehaviorSubject } from 'rxjs';
import { IJunction } from '@mdtBsServices/interfaces';
import { IPanelNode } from '@mdtProMicroModules/components/canvas-collapse-panel/interface';
import { GraphPortGroupEnum, JunctionNameEnum, NodeGroupEnum, NodeStatusEnum, NodeTypeEnum } from './enum';

export interface IGraphNodePort extends NsGraph.INodeAnchor {
  id: string;
  group: GraphPortGroupEnum;
  // 原position 有冲突，改为portPosition 输入桩才有
  portPosition?: JunctionNameEnum;
  // 只有输入桩才会有connected
  connected?: boolean;
  // 输入桩才有
  index?: number;
}

export interface IRunNodeData {
  action: string;
  id: string;
  data?: any;
}

export interface INodeData extends IPanelNode {
  type: NodeTypeEnum;
  // group 与XFlow冲突，用 feGroup
  feGroup: NodeGroupEnum;
  status?: NodeStatusEnum;
  // 通过更改变量状态，触发节点运行 仅在编辑时存在
  isRunNode$?: BehaviorSubject<IRunNodeData | boolean>;
  description?: string;
}

export interface IFlowNodeParamsFromCsv {
  filepath: string;
  encoding?: string;
  sep?: string;
}

export interface IFlowNodeParamsFromFileId {
  file_id: string;
}

export interface IFlowNodeParamsFromCollectorApi {
  extra_suffix?: string;
  key: string;
  meta_suffix?: string;
  secret: string;
  template_id: string;
  meta?: boolean;
}

export interface IFlowNodeParamsFromDatapkg {
  datapkg_id: string;
  with_geometry?: boolean;
  only?: string[];
  operator_filter?: {
    column: string;
    operator: string;
    param: any;
  };
}

export interface IFlowNodeParamsFromFeishuSpreadsheet {
  config: {
    app_id: string;
    app_secret: string;
  };
  spreadsheet_token: string;
  range?: string;
  sheet_id?: string;
  sheet_index?: string;
  sheet_name?: string;
}

export interface IFlowNodeParamsFromWechatWorkApprovalData {
  application_id: string;
  auth_corpid: string;
  end_time?: number | string;
  permanent_code: string;
  start_time: number | string;
  template_id?: string;
}

export interface IFlowNodeParamsFromDingtalkSheet {
  union_id: string;
  workbook_id: string;
  range?: string;
  sheet_id?: string;
  sheet_index?: string;
  sheet_name?: string;
  with_format?: boolean;
}

export interface IFlowNodeParamsFromDingtalkProcessInstances {
  process_code: string;
  end_time?: string | number;
  start_time?: string | number;
  status?: string[];
}

export interface IFlowNodeParamsFromFeishuBitable {
  config: {
    app_id: string;
    app_secret: string;
  };
  app_token: string;
  columns?: string[];
  table_id?: string;
  with_user_id?: boolean;
  with_record_id?: boolean;
  table_name?: string;
}

export interface IFlowNodeParamsFromSql {
  conn_id: string;
  sql: string;
}

export interface IFlowNodeParamsFromQLang {
  qlang_id: string;
}

export interface IFlowNodeParamsFromQLangRaw {
  query: string;
  _fparams_: Record<string, any>;
}

export interface IFlowNodeParamsFromPctChange {
  column_mapping?: Record<string, string>;
  dt: string;
  other_index_cols?: string[];
  periods?: number;
  key: string;
  value: string;
}

export interface IFlowNodeParamstwoFromPctChange {
  column_mapping?: object;
  dt?: string;
  other_index_cols?: string[];
  periods?: number;
  key?: string;
  value?: string;
}

export interface IFlowNodeParamsFromDbTable {
  columns?: string[];
  conn_id: string;
  schema_name?: string;
  table_name: string;
  order_by?: {
    column: string;
    desc?: boolean;
  }[];
}

export interface IFlowNodeParamsMapChineseNum {
  columns: string[];
  null_if_error?: boolean;
}

export interface IFlowNodeParamsStrip {
  cols?: string[];
  dir?: string;
  to_strip?: string;
}

export interface IFlowNodeParamsTypecast {
  errors?: string[];
  typemapping: object;
}

export interface IFlowNodeParamsNullIf {
  operator_filter: {
    column: string;
    operator: string;
    param: any;
    param_type: string;
  };
}

export interface IFlowNodeParamsTypecastForm {
  errors?: string[];
  typemapping: Array<{ key: string; value: string }>;
}

export interface IFlowNodeParamsRename {
  mapping: Record<string, string>;
}

export interface IFlowNodeParamsRenameForm {
  mapping: Array<{ key: string; value: string }>;
}

export interface IFlowNodeParamsInsertConstColumn {
  column: string;
  type: string;
  value: string;
}

export interface IFlowNodeParamsInsertSequenceColumn {
  column: string;
  start?: number;
  step?: number;
  stop?: number;
}

export interface IFlowNodeParamsAppend {
  sort?: boolean;
}

export interface IFlowNodeParamsJoin {
  how?: string;
  left_columns?: string[];
  left_on?: string[];
  on?: string[];
  right_columns?: string[];
  right_on?: string[];
  left_index?: boolean;
  right_index?: boolean;
  sort?: boolean;
  suffixes?: string[];
  validate?: string;
  eq?: string;
}

export interface IFlowNodeParamsSJoin {
  how?: string;
  left_geometry?: string;
  right_geometry?: string;
  op?: string;
  suffixes?: string[];
}

export interface IFlowNodeParamsFromDict {
  val: {
    name: string;
    type: string;
    values: string[];
  }[];
}

export interface IFlowNodeParamsToInsertDbTable {
  chunksize?: number;
  conn_id: string;
  schema_name?: string;
  table_name: string;
}

export interface IFlowNodeParamsToUpdateDbTable {
  conn_id: string;
  index_columns: string[];
  schema_name?: string;
  skip_absent_columns?: boolean;
  table_name: string;
}

export interface IFlowNodeParamsToUpsertDbTable extends IFlowNodeParamsToInsertDbTable {
  conflict_update_columns?: string[];
  index_columns: string[];
  skip_conflict?: boolean;
}

export interface IFlowNodeParamsToDeleteDbTable extends IFlowNodeParamsToInsertDbTable {
  index_columns?: string[];
  skip_absent_columns?: boolean;
}

export interface IFlowNodeParamsToUpdateDataPkg {
  dataset_id: string;
  package_name: string;
  action?: string;
  allow_empty_geometry?: boolean;
  ensure_multi_geometry?: boolean;
  city_id?: string;
  description?: string;
  tags?: string[];
  type_mapping?: object;
  table_action?: string;
}

export interface IFlowNodeParamsToUpdateDataPkgForm extends IFlowNodeParamsToUpdateDataPkg {
  type_mapping: Array<{ key: string; value: string }>;
}

export interface IFlowNodeParamsToMarket {
  dataset_id: string;
  package_name: string;
  allow_empty_geometry?: boolean;
  available_columns?: string[];
  city_id?: number;
  description?: string;
  geometry_type?: string;
  ensure_multi_geometry?: boolean;
  key_columns?: object;
  type_mapping?: object;
  tags?: string[];
}

export interface IFlowNodeParamsReplaceApiTransformForm {
  pattern: string;
  repl: string;
  columns: string[];
}

export interface IFlowNodeParamsReplaceFormTransformApi {
  columns: string[];
  mapping: Array<{ pattern: string; repl: string }>;
}

export interface IFlowNodeParamscopyColumnFormTransformApi {
  mapping: Array<{ key: string; value: string }>;
}

export interface IFlowNodeParamscopyColumnApiTransformForm {
  source: string;
  destination: string;
}

export interface IFlowNodeParamsToMarketForm extends IFlowNodeParamsToMarket {
  type_mapping: Array<{ key: string; value: string }>;
}

export interface IFlowNodeParamsinsertSequenceColumnFormToApi {
  value: string;
  skip_if_exist: boolean;
  mapping: Array<{ column: string; type: string }>;
}

export interface IFlowNodeParamsinsertSequenceColumnApiToForm {
  value: string;
  column: string;
  type: string;
  skip_if_exist: boolean;
}

export interface IFlowNodeParamsToConsole {
  head?: number;
  tail?: number;
  pretty?: boolean;
  title?: string;
}

export interface IFlowNodeParamsOnlyColumns {
  columns: string[];
}

export interface IFlowNodeParamsSplit {
  delete_source?: boolean;
  delimiter?: string;
  destinations: string[];
  errors?: string;
  max_split?: number;
  source: string;
}

export interface IFlowNodeParamsCreateTableTableDefinitionField {
  constraints?: object;
  description?: string;
  format?: string;
  name: string;
  title?: string;
  type: string;
}

export interface IFlowNodeParamsCreateTableTableDefinition {
  auto_id_column?: boolean;
  name: string;
  schema?: string;
  title?: string;
  fields: IFlowNodeParamsCreateTableTableDefinitionField[];
}

export interface IFlowNodeParamsCreateTable {
  conn_id: string;
  delete_existing?: boolean;
  drop_table?: boolean;
  sync_create_columns?: boolean;
  sync_drop_columns?: boolean;
  table_definition: IFlowNodeParamsCreateTableTableDefinition;
}

export interface IFlowNodeParamsConvertInt {
  errors?: string;
}

export interface IFlowNodeParamsConvertNumeric {
  columns: string[];
  precision?: number;
}

export interface IFlowNodeParamsExecuteSql {
  conn_id: string;
  sql: string;
}

export interface IFlowNodeParamsPipe {
  kw?: object;
  snippet: string;
}

export interface IFlowNodeParamsPipeForm {
  kw?: Array<{ key: string; value: string }>;
  snippet: string;
}

export interface IFlowNodeParamsCopyColumn {
  destination: string;
  source: string;
}

export interface IFlowNodeParamsUpdate {
  on: string[];
  overwrite?: boolean;
  errors?: string;
}

export interface IFlowNodeParamsUpsert {
  on: string[];
  aligned?: string;
}

export interface IFlowNodeParamsReplace {
  columns: string[];
  pattern: string;
  repl: string;
}

export interface IFlowNodeParamsGroupBy {
  by: string[];
  agg: {
    agg: string;
    source: string;
    target: string;
  }[];
}

export interface IFlowNodeParamsValueMapping {
  c_src: object;
  empty?: string;
  otherwise?: string;
  mapping: object;
}

export interface IFlowNodeParamsValueMappingForm extends IFlowNodeParamsValueMapping {
  c_src: Array<{ key: string; value: string }>;
  mapping: Array<{ key: string; value: string }>;
}

export interface IFlowNodeParamsDropDuplicates {
  keep?: string;
  columns: string[];
}

export interface IFlowNodeParamsDropColumns {
  columns: string[];
}

export interface IFlowNodeParamsSort {
  by: string[];
  algorithm?: string;
  asc?: boolean;
  ignore_index?: boolean;
  na_first?: boolean;
}

export interface IFlowNodeParamsFilter {
  operator_filter: {
    column: string;
    operator: string;
    param: any;
    param_type: string;
  };
  inverse?: boolean;
  ignore_index?: boolean;
}

export interface IFlowNodeParamsGeocode {
  addr: string;
  c_lat?: string;
  c_lng?: string;
  srs?: string;
  ignore_existing?: boolean;
}

export interface IFlowNodeParamsNormLnglatGeometry {
  c_geometry?: string;
  c_lat?: string;
  c_lng?: string;
}

export type IFlowNodeParams =
  | IFlowNodeParamsFromCsv
  | IFlowNodeParamsFromFileId
  | IFlowNodeParamsFromCollectorApi
  | IFlowNodeParamsFromDatapkg
  | IFlowNodeParamsFromSql
  | IFlowNodeParamsFromQLangRaw
  | IFlowNodeParamsFromFeishuSpreadsheet
  | IFlowNodeParamsFromFeishuBitable
  | IFlowNodeParamsFromDbTable
  | IFlowNodeParamsMapChineseNum
  | IFlowNodeParamsTypecast
  | IFlowNodeParamsStrip
  | IFlowNodeParamsTypecastForm
  | IFlowNodeParamsRename
  | IFlowNodeParamsRenameForm
  | IFlowNodeParamsInsertConstColumn
  | IFlowNodeParamsInsertSequenceColumn
  | IFlowNodeParamsAppend
  | IFlowNodeParamsJoin
  | IFlowNodeParamsSJoin
  | IFlowNodeParamsGeocode
  | IFlowNodeParamsFromDict
  | IFlowNodeParamsToInsertDbTable
  | IFlowNodeParamsToUpdateDbTable
  | IFlowNodeParamsToUpsertDbTable
  | IFlowNodeParamsToDeleteDbTable
  | IFlowNodeParamsToMarket
  | IFlowNodeParamsToConsole
  | IFlowNodeParamsOnlyColumns
  | IFlowNodeParamsSplit
  | IFlowNodeParamsNormLnglatGeometry
  | IFlowNodeParamsSort
  | IFlowNodeParamsFilter
  | IFlowNodeParamsDropColumns
  | IFlowNodeParamsDropDuplicates
  | IFlowNodeParamsCreateTable
  | IFlowNodeParamsConvertNumeric
  | IFlowNodeParamsExecuteSql
  | IFlowNodeParamsPipe
  | IFlowNodeParamsCopyColumn
  | IFlowNodeParamsReplace
  | IFlowNodeParamsUpdate
  | IFlowNodeParamsUpsert
  | IFlowNodeParamsToUpdateDataPkg
  | IFlowNodeParamsConvertInt
  | IFlowNodeParamsGroupBy
  | IFlowNodeParamsValueMapping
  | IFlowNodeParamsNullIf
  | IFlowNodeParamsFromPctChange
  | IFlowNodeParamsReplaceFormTransformApi
  | IFlowNodeParamsFromDingtalkSheet
  | IFlowNodeParamsFromDingtalkProcessInstances
  | IFlowNodeParamsFromWechatWorkApprovalData;

export interface IFlowParamsNodeColumn {
  id: string;
  name: string;
  type?: string;
}

export interface IEdgeRelation {
  id: string;
  upNodeId: string;
  upNodeType: string;
  upPortId: string;
  downNodeId: string;
  downNodeType: string;
  downPortId: string;
  junction?: IJunction;
}

export type IFlowParamsNodeColumnsResult = Record<string, IFlowParamsNodeColumn>;

// 此处key为source ID
export type IFlowParamsDataSourceMap = Record<string, IFlowParamsNodeColumnsResult>;
export type IFlowParamsNodeParams = Record<string, IFlowNodeParams>;
export type IFlowParamsEdgeRelation = Record<string, IEdgeRelation>;

export interface IFlowParams {
  // 后续有新的需要存储的可以拓展
  nodeParams: IFlowParamsNodeParams;
  edgeRelation: IFlowParamsEdgeRelation;
}
