import { cn as microCn } from '@mdtProMicroModules/languages';

const cn = {
  ...microCn,
  edit: '编辑',
  preview: '预览',
  fullEdit: '全屏编辑',
  editorTitle: 'JSON可视化编辑器',
  qlang: 'QLang',
  lowcodeQlang: '低码QLang',
  datapkg: '数据包',
  custom: '自定义',
  requestType: '请求类型',
  requestPlaceholder: '请选择类型',
  queryData: '查询数据',
  addData: '新增数据',
  updateData: '更新数据',
  deleteData: '删除数据',
  selectDataPkg: '选择数据包',
  actionSettings: '操作设置',
  selectAction: '请选择操作',
  qeEditor: 'Qe低码编辑器',
  qeBuilder: 'Qe编辑器',
  full: '全屏',
  lowcodeSql: '低码Sql',
  previewEdit: '预览模式编辑',
  sqlDep: 'Sql依赖',
  requestAdaptation: '请求适配',
  apiSettingTitle: 'Api快捷设置',
  formError: '请检查表单是否填写正确',
  commonSetting: '常规设置',
  advanceSetting: '高级设置',
  noDepText: '暂无依赖',
  cnName: '简体中文',
  enName: 'English',
  userInfo: '用户信息',
  userId: '用户ID',
  uuid: '用户唯一标识',
  userName: '用户名',
  email: '邮箱',
  phone: '手机号',
  expireTime: '用户过期时间',
  appInfo: '机构信息',
  appId: '机构ID',
  appName: '机构名称',
  appExpireTime: '机构过期时间',
  browserVar: '浏览器变量',
  jsonComp: {
    commonLayout: '常见布局',
    leftRightEqually: '左右均分',
    leftRightEquallyDesc: '常见布局：左右均分布局（基于 CSS Flex 实现的布局容器）。',
    stickyContainer: '吸底容器',
    stickyDescription: '常见布局：吸底容器（基于 CSS Flex 实现的布局容器）。',
    stickyTopContainer: '吸顶容器',
    stickyTopDescription: '常见布局：吸顶容器（基于 CSS Flex 实现的布局容器）。',
    floatingContainer: '悬浮容器',
    floatingDescription: '常见布局：悬浮容器（基于 CSS Flex 实现的布局容器）。',
    scrollXContainer: 'x轴滚动容器',
    scrollXDescription: 'x轴滚动容器: 基于 CSS Flex 实现的布局容器。',
    scrollYContainer: 'y轴滚动容器',
    scrollYDescription: 'y轴滚动容器: 基于 CSS Flex 实现的布局容器。',
    layout1_1_1_v2: '上中下',
    layout1_1_1_v2Description: '常见布局：上中下布局（基于 CSS Flex 实现的布局容器）。',
    layout1_1_1: '三栏均分',
    layout1_1_1Description: '常见布局：三栏均分布局（基于 CSS Flex 实现的布局容器）。',
    layout1_1_v2: '上下布局',
    layout1_1_v2Description: '常见布局：上下布局（基于 CSS Flex 实现的布局容器）。',
    layout1_2_3: '1:2:3 三栏',
    layout1_2_3Description: '常见布局：1:2:3 三栏布局（基于 CSS Flex 实现的布局容器）。',
    layout1_2_v2: '一拖二',
    layout1_2_v2Description: '常见布局：一拖二布局（基于 CSS Flex 实现的布局容器）。',
    layout1_2_v3: '左一右二',
    layout1_2_v3Description: '常见布局：左一右二布局（基于 CSS Flex 实现的布局容器）。',
    layout1_2_v4: '经典布局',
    layout1_2_v4Description: '常见布局：经典布局（基于 CSS Flex 实现的布局容器）。',
    layout1_2: '1:2 布局',
    layout1_2Description: '常见布局：1:2 布局（基于 CSS Flex 实现的布局容器）。',
    layout1_3: '1:3 布局',
    layout1_3Description: '常见布局：1:3 布局（基于 CSS Flex 实现的布局容器）。',
    layout2_1_v2: '二拖一',
    layout2_1_v2Description: '常见布局：二拖一布局（基于 CSS Flex 实现的布局容器）。',
    layout2_1_v3: '左二右一',
    layout2_1_v3Description: '常见布局：左二右一布局（基于 CSS Flex 实现的布局容器）。',
    flexContainer: '布局容器',
    flexContainerDescription:
      '布局容器 是基于 CSS Flex 实现的布局效果，它比 Grid 和 HBox 对子节点位置的可控性更强，比用 CSS 类的方式更易用',
    flexContainerAttributeTitle: '属性',
    flexContainerBasicTitle: '基础',
    flexContainerAdvancedTitle: '高级',
    flexContainerAppearanceTitle: '外观',
    flexContainerBaseChildren: '子节点集合',
    flexContainerShortcutLayoutSetting: '快捷版式设置',
    flexContainerInnerAlignmentSetting: '内部对齐设置',
    flexContainerRowGap: '行间隔',
    flexContainerColumnGap: '列间隔',
    topInsertLayoutContainer: '上方插入布局容器',
    bottomInsertLayoutContainer: '下方插入布局容器',
    addFlexColumnElement: '新增列级元素',
    insertBelowNewRow: '下方插入新行',
    insertAboveNewRow: '上方插入新行',
    heightSetting: '高度设置',
    widthSetting: '宽度设置',
    elasticHeight: '弹性高度',
    elasticWidth: '弹性宽度',
    fixedHeight: '固定高度',
    fixedWidth: '固定宽度',
    firstCol: '第一列',
    secondCol: '第二列',
    thirdCol: '第三列',
    background: '背景',
    up: '上方',
    down: '下方',
    left: '左侧',
    right: '右侧',
    insertColContainer: '插入列级容器',
    layout_fixed: '悬浮容器',
    layout_fixedDescription: '悬浮容器: 基于 CSS Fixed 实现的特殊布局容器。',
    layout_fixed_top: '吸附容器',
    layout_fixed_topDescription: '吸附容器: 可设置成吸顶或者吸底展示。',
    mdtUpload: 'mdt上传文件',
    mdtUploadDesc: '用于上传和展示mdt文件',
    mdtComponent: 'MDT 组件',
    valChange: '值变化',
    valChangeDesc: '上传文件值变化时触发（上传失败同样会触发）',
    data: '数据',
    dataTitle: '上传的文件',
    removeFile: '移除文件',
    removeFileDesc: '移除文件时触发',
    removeFileDesc2: '被移除的文件',
    uploadSuccessDesc: '上传文件成功时触发',
    uploadSuccessDesc2: '远程上传请求成功后返回的响应数据',
    uploadFailDesc: '上传文件失败时触发',
    uploadFailDesc2: '远程上传请求失败后返回的错误信息',
    clearData: '清空数据',
    clearDataDesc: '清除选择的文件',
    setValue: '赋值',
    setValueDesc: '触发组件数据更新',
    basic: '基本',
    limitSize: '限制单个大小',
    compressImage: '开启图片压缩',
    limitSizeMsg: '超过大小限制的错误提示',
    caremaText: '移动端拍照',
    watermarkLabel1: '添加用户名及id',
    wartermarkLabel2: '日期',
    minCount: '最小上传数量',
    maxCount: '最大上传数量',
    fileType: '文件类型',
    fileTypeDesc: '请填入文件的后缀，多个类型用<code>,</code>隔开',
    style: '外观',
    styleDesc: '描述',
    styleDesc2: '选择按钮',
    styleDesc3: '上传按钮',
    event: '事件',
    uploadSuccess: '上传成功',
    uploadFail: '上传失败',
    property: '属性',
    uniqueKey: '唯一索引',
    columnSelectPlaceholder: '默认使用"id"作为唯一索引',
    columnSelectTip:
      '默认情况下，唯一索引将自动根据所选数据包进行填充。确认后，将不再自动填充唯一索引。如需在切换数据包时重新获取唯一索引，请先清除当前值再重新选择数据包。',
  },
};

export type Locale = typeof cn;
export default cn;
