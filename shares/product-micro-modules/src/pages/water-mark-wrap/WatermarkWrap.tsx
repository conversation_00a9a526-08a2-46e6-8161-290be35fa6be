/* eslint-disable react/jsx-no-useless-fragment */
import _ from 'lodash';
import { FC } from 'react';
import { Watermark } from '@metroDesign/watermark';
import { useObservableState } from '@mdtBsComm/hooks/use-observable-state';
import { WatermarkWrapController } from './WatermarkWrapController';

export interface IProps {
  controller?: WatermarkWrapController;
}

export const WatermarkWrap: FC<IProps> = ({ controller, children }) => {
  if (!controller) {
    return <>{children}</>;
  }

  // eslint-disable-next-line react-hooks/rules-of-hooks
  const visibility = useObservableState(() => controller.getVisibility$());

  const content = controller.getContent();
  const noWatermark = !content || _.isEmpty(content) || !visibility;

  if (noWatermark) {
    return <>{children}</>;
  }

  const restProps = controller.getOptions();
  return (
    <Watermark content={content} {...restProps}>
      {children}
    </Watermark>
  );
};
