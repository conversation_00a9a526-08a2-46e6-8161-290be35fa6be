import { memo } from 'react';
import Add from '@metro/icons/dist/esm/react/Add';
import Delete from '@metro/icons/dist/esm/react/Delete1';
import EditOutlined from '@metro/icons/dist/esm/react/EditOutlined';
import { Button } from '@metroDesign/button';
import { useObservableState } from '@mdtBsComm/hooks/use-observable-state';
import { ModalWithBtnsCompDialog } from '@mdtBsComponents/modal-with-btns-comp-dialog';
import i18n from '../../languages';
import { CardCurdWithSimpleSearch } from '../card-curd-with-simple-search';
import { ResourceShareController } from './ResourceShareController';
import './index.less';

interface IProps {
  controller?: ResourceShareController;
}

const ResourceShareInner = memo((props: IProps) => {
  const controller = props.controller!;

  const values = useObservableState(controller.getOptionsController()!.getSelectedDataList$()!);

  return (
    <div className="module_resource-share-inner">
      <div className="module_resource-share-inner-header">
        <div className="module_resource-share-inner-header-title">
          {i18n.chain.proMicroModules.resourceShare.allShared}
        </div>
        <div className="module_resource-share-inner-header-btns">
          <Button.Link primary icon={<Add />} onClick={controller.onClickAddUser}>
            {i18n.chain.proMicroModules.resourceShare.addUser}
          </Button.Link>
        </div>
      </div>
      <div className="module_resource-share-curd_list-wrap">
        <CardCurdWithSimpleSearch controller={controller.getOptionsController()!} />
        {!!values.length && (
          <div className="module_resource-share-curd_list-wrap-footer">
            <div className="module_resource-share-curd_list-wrap-footer-title">
              {i18n.chain.proMicroModules.resourceShare.selected(values.length)}
            </div>
            <div className="module_resource-share-curd_list-wrap-footer-btns">
              <Button.Link danger icon={<Delete />} onClick={controller.onClickDeleteItems}>
                {i18n.chain.proMicroModules.resourceShare.delete}
              </Button.Link>
              <Button.Link primary icon={<EditOutlined />} onClick={controller.onClickEditItems}>
                {i18n.chain.proMicroModules.resourceShare.editPermission}
              </Button.Link>
            </div>
          </div>
        )}
      </div>
      <ModalWithBtnsCompDialog controller={controller.getAddUserController()!} />
      <ModalWithBtnsCompDialog controller={controller.getEditPermissionModalController()!} />
    </div>
  );
});

export { ResourceShareInner };
