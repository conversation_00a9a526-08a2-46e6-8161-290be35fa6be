import _ from 'lodash';
import { from, Observable } from 'rxjs';
import { map, takeWhile } from 'rxjs/operators';
import { DATASET_DATA, DATASET_ROWS_DATA } from '@mdtBsBffServices/dataset';
import { IBusinessResult } from '@mdtBsComm/interfaces';
import {
  IDatapkgGeometryType,
  IDataset,
  IDatasetDatapkgFromTablePost,
  IOwnership,
  IRequestCancelToken,
} from '@mdtBsServices/interfaces';
import { DatasetBffService } from '@mdtProComm/bff-services/DatasetBffService';
import { IFormData as IGeneratePkgFormData } from '@mdtProMicroModules/containers/dialog-generate-pkg-form';
import { ExternalLinkDbBffService } from '../../bff-service/externalLinkDbBffService';

interface IGeneratePkgOptions {
  dbId: string;
  schema: string;
  tableName: string;
  ownership: IOwnership;
  values: IGeneratePkgFormData;
}

export class DatabaseDetailModelBff {
  // 获取数据包详情
  public static getDatabase(dbId: string, cancelToken?: IRequestCancelToken): Observable<IDataset> {
    return from(DatasetBffService.queryDatasetList({ respData: DATASET_DATA, cancelToken })).pipe(
      map((list) => {
        return _.find(list, ['id', dbId]) || ({} as any);
      }),
    );
  }

  // 获取表信息
  public static getTableInfo(dbId: string, schema: string, table: string, cancelToken?: IRequestCancelToken) {
    return from(
      ExternalLinkDbBffService.queryDatasetRowsBySchemaTable({
        datasetId: dbId,
        schema,
        table,
        respData: DATASET_ROWS_DATA,
        cancelToken,
      }),
    ).pipe(
      takeWhile((resp) => !resp.canceled),
      map((resp) =>
        resp.success
          ? resp.page_data
          : {
              columns: [],
              types: [],
              values: [],
            },
      ),
    );
  }

  // 生成数据包
  public static generatePkg({
    dbId,
    schema,
    tableName,
    ownership,
    values,
  }: IGeneratePkgOptions): Observable<IBusinessResult<void>> {
    const data: IDatasetDatapkgFromTablePost = {
      name: values.name,
      geometry_type: values.geometryType as IDatapkgGeometryType,
      table_schema: schema,
      table_name: tableName,
      ownership,
    };
    return from(ExternalLinkDbBffService.createDatapkgFromTable({ datasetId: dbId, data })).pipe(
      map((resp) => {
        if (resp.success) {
          return { success: true };
        }
        return { success: false };
      }),
    );
  }
}
