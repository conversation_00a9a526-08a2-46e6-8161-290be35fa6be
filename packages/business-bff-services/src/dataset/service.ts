import _ from 'lodash';
import { gql } from '@apollo/client';
import { IDatapkgId, IDataset, IDatasetId, IDatasetRows, IDatasetTabel } from '@mdtApis/interfaces';
import { ApolloService, bffResponseData, bffResponseErrorType, bffResponsePaginationData } from '../ApolloService';
import { IResponsenData, IResponsePaginationData } from '../interfaces';
import {
  ICreateDatapkgFromFileOptions,
  ICreateDatapkgFromSqlOptions,
  ICreateDatapkgFromTableOptions,
  ICreateDatasetOptions,
  ICreateEmptyDatapkgOptions,
  IDeleteDatasetOptions,
  IPingDatasetOptions,
  IPingUnsaveDatasetOptions,
  IQueryDatasetAllTablesOptions,
  IQueryDatasetListOptions,
  IQueryDatasetOptions,
  IQueryDatasetPkgsOptions,
  IQueryDatasetRowsByDialectOptions,
  IQueryDatasetRowsBySchemaTableOptions,
  IUpdateDatasetOptions,
} from './interface';

// 查询数据源列表
export const queryDatasetList = (
  productName: string,
  options: IQueryDatasetListOptions,
): Promise<IResponsenData<IDataset[]>> => {
  const { cancelToken, respData } = options || {};
  const queryName = `${productName}DatasetList`;
  const query = gql(`
    query ${_.snakeCase(queryName)}{
      ${queryName} {
        ${bffResponseErrorType}
        ... on ComDatasetListResponse {
          ${bffResponseData} {
            ${respData}
          }
        }
      }
    }
  `);
  return ApolloService.query({
    query,
    context: { cancelToken },
  });
};

// 查询数据源详情
export const queryDataset = (productName: string, options: IQueryDatasetOptions): Promise<IResponsenData<IDataset>> => {
  const { datasetId, cancelToken, respData } = options || {};
  const query = gql(`
    query queryDataset($datasetId: String!){
      ${productName}DatasetQuery(datasetId: $datasetId) {
        ${bffResponseErrorType}
        ... on ComDatasetResponse {
          ${bffResponseData} {
            ${respData}
          }
        }
      }
    }
  `);
  return ApolloService.query({
    query,
    variables: {
      datasetId,
    },
    context: { cancelToken },
  });
};

// 查询数据源下数据包
type IDatasetPkgsResponse = IResponsePaginationData<{ datapkgs: { id: string; name: string }[] }>;
export const queryDatasetPkgs = (
  productName: string,
  options: IQueryDatasetPkgsOptions,
): Promise<IDatasetPkgsResponse> => {
  const { data, datasetId, cancelToken } = options;
  const query = gql(`
    query getDatasetPkgs($datasetId: String!, $data: DatasetDatapkgIdsQuery!){
      ${productName}DatasetDatapkgs(datasetId: $datasetId, data: $data) {
        ${bffResponseErrorType}
        ... on ComPaginatedDatasetDatapkgIds {
          ${bffResponsePaginationData} {
            datapkgs {
              id
              name
            }
          }
        }
      }
    }
  `);
  return ApolloService.query({
    query,
    variables: {
      data,
      datasetId,
    },
    context: { cancelToken },
  });
};

// 创建空数据包
export const createEmptyDatapkg = (
  productName: string,
  options: ICreateEmptyDatapkgOptions,
): Promise<IResponsenData<IDatapkgId>> => {
  const { datasetId, data, cancelToken } = options;
  const mutation = gql(`
    mutation createEmptyDatapkg($datasetId: String!, $data: DatasetDatapkgFromEmptyInput!){
      ${productName}DatasetCreateEmptyDatapkg(datasetId: $datasetId, data: $data) {
        ${bffResponseErrorType}
        ... on ComDatasetCreateDatapkgResponse {
          ${bffResponseData} {
            datapkg_id
          }
        }
      }
    }
  `);
  return ApolloService.mutate({
    context: { cancelToken },
    mutation,
    variables: {
      datasetId,
      data,
    },
  });
};

// 从文件创建数据包
export const createDatapkgFromFile = (
  productName: string,
  options: ICreateDatapkgFromFileOptions,
): Promise<IResponsenData<IDatapkgId>> => {
  const { datasetId, data, cancelToken } = options;
  const mutation = gql(`
    mutation createDatapkgFromFile($datasetId: String!, $data: DatasetDatapkgFromFileInput!){
      ${productName}DatasetCreateDatapkgFromFile(datasetId: $datasetId, data: $data) {
        ${bffResponseErrorType}
        ... on ComDatasetCreateDatapkgResponse {
          ${bffResponseData} {
            datapkg_id
          }
        }
      }
    }
  `);
  return ApolloService.mutate({
    context: { cancelToken },
    mutation,
    variables: {
      datasetId,
      data,
    },
  });
};

// 从Sql创建数据包
export const createDatapkgFromSql = (
  productName: string,
  options: ICreateDatapkgFromSqlOptions,
): Promise<IResponsenData<IDatapkgId>> => {
  const { datasetId, data, cancelToken } = options;
  const mutation = gql(`
    mutation createDatapkgFromSql($datasetId: String!, $data: DatasetDatapkgFromSqlInput!){
      ${productName}DatasetCreateDatapkgFromSql(datasetId: $datasetId, data: $data) {
        ${bffResponseErrorType}
        ... on ComDatasetCreateDatapkgResponse {
          ${bffResponseData} {
            datapkg_id
          }
        }
      }
    }
  `);
  return ApolloService.mutate({
    context: { cancelToken },
    mutation,
    variables: {
      datasetId,
      data,
    },
  });
};

// 从Table创建数据包
export const createDatapkgFromTable = (
  productName: string,
  options: ICreateDatapkgFromTableOptions,
): Promise<IResponsenData<IDatapkgId>> => {
  const { datasetId, data, cancelToken } = options;
  const mutation = gql(`
    mutation createDatapkgFromTable($datasetId: String!, $data: DatasetDatapkgFromTableInput!){
      ${productName}DatasetCreateDatapkgFromTable(datasetId: $datasetId, data: $data) {
        ${bffResponseErrorType}
        ... on ComDatasetCreateDatapkgResponse {
          ${bffResponseData} {
            datapkg_id
          }
        }
      }
    }
  `);
  return ApolloService.mutate({
    context: { cancelToken },
    mutation,
    variables: {
      datasetId,
      data,
    },
  });
};

// 数据集自带的查询语句查询数据
export const queryDatasetRowsByDialect = (
  productName: string,
  options: IQueryDatasetRowsByDialectOptions,
): Promise<IResponsenData<IDatasetRows>> => {
  const { datasetId, data, respData, cancelToken } = options;
  const mutation = gql(`
    mutation queryDatasetRowsByDialect($datasetId: String!, $data: DatasetRowsDialectQuery!){
      ${productName}DatasetRowsByDialect(datasetId: $datasetId, data: $data) {
        ${bffResponseErrorType}
        ... on ComDatasetRowsByDialectResponse {
          ${bffResponseData} {
            ${respData}
          }
        }
      }
    }
  `);
  return ApolloService.mutate({
    context: { cancelToken },
    mutation,
    variables: {
      datasetId,
      data,
    },
  });
};

// 获取数据集所有Schema下的所有表格
export const queryDatasetAllTables = (
  productName: string,
  options: IQueryDatasetAllTablesOptions,
): Promise<IResponsenData<IDatasetTabel[]>> => {
  const { datasetId, cancelToken } = options;
  const query = gql(`
    query queryDatasetAllTables($datasetId: String!){
      ${productName}DatasetAllTables(datasetId: $datasetId) {
        ${bffResponseErrorType}
        ... on ComDatasetTableListResponse {
          ${bffResponseData} {
            name
            schema
            type
          }
        }
      }
    }
  `);
  return ApolloService.query({
    context: { cancelToken },
    query,
    variables: {
      datasetId,
    },
  });
};

// 获取数据集所有Schema下的所有表格
export const queryDatasetRowsBySchemaTable = (
  productName: string,
  options: IQueryDatasetRowsBySchemaTableOptions,
): Promise<IResponsenData<IDatasetRows>> => {
  const { datasetId, schema, table, respData, cancelToken } = options;
  const query = gql(`
    query queryDatasetRowsBySchemaTable($datasetId: String!, $schema: String!, $table: String!){
      ${productName}DatasetRowsBySchemaTable(datasetId: $datasetId, schema: $schema, table: $table) {
        ${bffResponseErrorType}
        ... on ComDatasetRowsByDialectResponse {
          ${bffResponseData} {
            ${respData}
          }
        }
      }
    }
  `);
  return ApolloService.query({
    context: { cancelToken },
    query,
    variables: {
      datasetId,
      schema,
      table,
    },
  });
};

// 删除数据集
export const deleteDataset = (
  productName: string,
  options: IDeleteDatasetOptions,
): Promise<IResponsenData<IDatasetId>> => {
  const { datasetId, cancelToken } = options;
  const mutation = gql(`
    mutation deleteDataset($datasetId: String!){
      ${productName}DatasetDelete(datasetId: $datasetId) {
        ${bffResponseErrorType}
        ... on ComDatasetDeleteResponse {
          ${bffResponseData} {
            dataset_id
          }
        }
      }
    }
  `);
  return ApolloService.mutate({
    context: { cancelToken },
    mutation,
    variables: {
      datasetId,
    },
  });
};

// 创建数据集
export const createDataset = (
  productName: string,
  options: ICreateDatasetOptions,
): Promise<IResponsenData<IDataset>> => {
  const { data, respData, cancelToken } = options;
  const mutation = gql(`
    mutation createDataset($data: CreateDatasetInput!){
      ${productName}DatasetCreate(data: $data) {
        ${bffResponseErrorType}
        ... on ComDatasetResponse {
          ${bffResponseData} {
            ${respData}
          }
        }
      }
    }
  `);
  return ApolloService.mutate({
    context: { cancelToken },
    mutation,
    variables: {
      data,
    },
  });
};

// 修改数据集
export const updateDataset = (
  productName: string,
  options: IUpdateDatasetOptions,
): Promise<IResponsenData<IDataset>> => {
  const { datasetId, data, respData, cancelToken } = options;
  const mutation = gql(`
    mutation updateDataset($datasetId: String!, $data: UpdateDatasetInput!){
      ${productName}DatasetUpdate(datasetId: $datasetId, data: $data) {
        ${bffResponseErrorType}
        ... on ComDatasetResponse {
          ${bffResponseData} {
            ${respData}
          }
        }
      }
    }
  `);
  return ApolloService.mutate({
    context: { cancelToken },
    mutation,
    variables: {
      datasetId,
      data,
    },
  });
};

// 测试连接dataset
export const pingDataset = (productName: string, options: IPingDatasetOptions): Promise<IResponsenData<IDataset>> => {
  const { datasetId, data, cancelToken } = options;
  const mutation = gql(`
    mutation pingDataset($datasetId: String!, $data: UpdateDatasetInput){
      ${productName}DatasetPing(datasetId: $datasetId, data: $data) {
        ${bffResponseErrorType}
        ... on BoolResponse {
          ${bffResponseData}
        }
      }
    }
  `);
  return ApolloService.mutate({
    context: { cancelToken },
    mutation,
    variables: {
      datasetId,
      data,
    },
  });
};

// 测试连接dataset(未保存)
export const pingUnsaveDataset = (
  productName: string,
  options: IPingUnsaveDatasetOptions,
): Promise<IResponsenData<IDataset>> => {
  const { data, cancelToken } = options;
  const mutation = gql(`
    mutation pingUnsaveDataset($data: CreateDatasetInput!){
      ${productName}DatasetUnsavePing(data: $data) {
        ${bffResponseErrorType}
        ... on BoolResponse {
          ${bffResponseData}
        }
      }
    }
  `);
  return ApolloService.mutate({
    context: { cancelToken },
    mutation,
    variables: {
      data,
    },
  });
};
