import _ from 'lodash';
import { FormSpecTypeEnum } from '@mdtBpmnPropertiesPanel/properties/FormSpecProps';
import { parseStrToObj } from '@mdtBsComm/utils/stringUtil';
import type { IFormSpec as ICommFormSpec } from '@mdtProComm/interfaces';
import { getXmlDocument } from '../xmlUtil';
import { getGlobalVarsOptions } from './getGlobalVarsOptions';
import { getScriptTaskSchema } from './getScriptTaskSchema';
import { parseXmlToDom } from './parseDataFromXml';

export type IFormSpec = ICommFormSpec;

interface IFormSpecMap {
  [key: string]: IFormSpec;
}

const FORMILY = 'formily';

// 获取根节点及指定节点的form spec
// eslint-disable-next-line sonarjs/cognitive-complexity
export const parseFormSpecFromXml = (xml: string, nodeIds?: string[]): IFormSpecMap => {
  const result: IFormSpecMap = {};
  const doc = getXmlDocument(xml);
  if (!doc) {
    return result;
  }

  try {
    const rootFormSpecType = doc.querySelector('process>extensionElements>formSpec>specType')?.textContent || FORMILY;
    const rootFomSpec = parseStrToObj(
      doc.querySelector('process>extensionElements>formSpec>spec')?.textContent || '{}',
    );
    const rootFormDefaultValueConfig = parseStrToObj(
      doc.querySelector('process>extensionElements>formSpec>defaultValueConfig')?.textContent || '{}',
    );
    if (!_.isEmpty(rootFomSpec)) {
      result.rootFormSpec = {
        type: rootFormSpecType,
        spec: rootFomSpec,
        defaultValueConfig: _.isEmpty(rootFormDefaultValueConfig) ? undefined : (rootFormDefaultValueConfig as any),
      };
    }
    _.forEach(nodeIds, (id) => {
      const node = doc.querySelector(`#${id}`);
      if (!node) return;
      const nodeName = node.nodeName;
      // 脚本节点没有form表单，需要自定义
      if (nodeName === 'scriptTask') {
        const globalVarsOptions = getGlobalVarsOptions(xml);
        result[id] = {
          type: FormSpecTypeEnum.FORMILY_V2,
          spec: getScriptTaskSchema(node, globalVarsOptions),
        };
        return;
      }
      const nodeFormSpecType = node.querySelector(`extensionElements formSpec specType`)?.textContent || FORMILY;
      const nodeSpec = parseStrToObj(node.querySelector(`extensionElements formSpec spec`)?.textContent || '{}');
      const nodeFormDefaultValueConfig = parseStrToObj(
        node.querySelector(`extensionElements formSpec defaultValueConfig`)?.textContent || '{}',
      );
      !_.isEmpty(nodeSpec) &&
        (result[id] = {
          type: nodeFormSpecType,
          spec: nodeSpec,
          defaultValueConfig: _.isEmpty(nodeFormDefaultValueConfig) ? undefined : (nodeFormDefaultValueConfig as any),
        });
    });
  } catch (e) {
    console.error('parseFormSpecFromXml error', e);
  }
  return result;
};

export const parseRootFormSpecFromXml = (xml: string): IFormSpec => {
  const doc = parseXmlToDom(xml);
  const emptyData = { type: '', spec: {} };
  if (!doc) return emptyData;

  try {
    const rootFormSpecType =
      doc.querySelector('process>extensionElements>formSpec>specType')?.textContent || FormSpecTypeEnum.FORMILY;
    const rootFormSpecSchema = doc.querySelector('process>extensionElements>formSpec>spec')?.textContent || '';
    return {
      type: rootFormSpecType,
      spec: JSON.parse(rootFormSpecSchema || '{}'),
    };
  } catch (e) {
    console.error('parseRootFormSpecFromXml error', e);
    return emptyData;
  }
};
