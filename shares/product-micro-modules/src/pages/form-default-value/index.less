.form-default-value-page {
  width: 100%;
  height: 100%;
  background-color: var(--metro-bg-0);
  border-top: 1px solid var(--dmc-split-page-color);

  .metro-form-view {
    max-width: 100%;
    padding: 20px 40px;
  }

  .metro-array-items-for-enum {
    .metro-formily-array-items-list {
      .metro-space-item {
        margin-right: 10px !important;
      }

      .metro-formily-array-items-item {
        margin-bottom: 0;

        .metro-space-align-center {
          align-items: center;
          width: 100%;
        }

        .metro-space-item:nth-child(2) {
          flex: 1;
        }

        .metro-formily-item-label {
          display: none;
        }
      }

      .metro-formily-item {
        margin-bottom: 10px;
        padding: 0;
        border: none;
      }
    }
  }
}

.drawer-form-default-value.drawer-within-page .drawer-within-page-inner-tool {
  left: auto;
}
