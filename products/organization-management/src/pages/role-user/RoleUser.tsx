import { FC } from 'react';
import { useObservableState } from '@mdtBsComm/hooks/use-observable-state';
import { ModalWithBtnsCompDialog } from '@mdtBsComponents/modal-with-btns-comp-dialog';
import { ModalWithBtnsCompEmotion } from '@mdtBsComponents/modal-with-btns-comp-emotion';
import { RoleManageTreeList } from '@mdtProMicroModules/containers/role-manage-tree-list';
import { TableCurdWithSimpleSearch } from '@mdtProMicroModules/containers/table-curd-with-simple-search';
import { RoleUserController } from './RoleUserController';
import './index.less';

// 子组件-右侧表格====================================================================================
const RightContent: FC<IProps> = ({ controller }) => {
  const tableController = controller.getTableController();
  const roleManageTreeListController = controller.getRoleManageTreeListController();
  const { name } = useObservableState(roleManageTreeListController.getCurrentSelect$());
  const removeRoleUserController = controller.getRemoveRoleUserController();
  const addRoleUserController = controller.getAddRoleUserController();
  return (
    <div className="page_role-user-right">
      <div className="page_role-user-right-title">{name}</div>
      <TableCurdWithSimpleSearch controller={tableController} />
      <ModalWithBtnsCompEmotion controller={removeRoleUserController} />
      <ModalWithBtnsCompDialog controller={addRoleUserController} />
    </div>
  );
};

// 角色成员页面=======================================================================================
interface IProps {
  controller: RoleUserController;
}
const RoleUser: FC<IProps> = ({ controller }) => {
  const roleManageTreeListController = controller.getRoleManageTreeListController();
  return (
    <div className="page_role-user">
      <RoleManageTreeList controller={roleManageTreeListController} />
      <RightContent controller={controller} />
    </div>
  );
};

export { RoleUser };
