.low-code-etl-edit-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;

  .x6-graph-background {
    background-color: transparent !important;
  }

  .xflow-toolbar {
    background: var(--dmc-primary-panel-2);
    border: 1px solid var(--dmc-split-color);
  }

  .xflow-toolbar-item {
    color: var(--dmc-text-color) !important;
  }

  .x6-toolbar-item:hover {
    background-color: transparent !important;
  }
}

.drawer-flow-edit {
  .dmc-btn-icon {
    display: none;
  }

  .leftBox {
    padding-left: 0;
  }
}

// .component_modal-drawer_header {
//   // 隐藏抽屉头部
//   display: none;
// }
