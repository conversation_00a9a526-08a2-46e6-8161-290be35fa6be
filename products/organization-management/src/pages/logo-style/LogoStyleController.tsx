import _ from 'lodash';
import { combineLatest, of } from 'rxjs';
import { map } from 'rxjs/operators';
import { RequestController } from '@mdtBsControllers/request-controller';
import { LinkButton } from '@mdtDesign/button';
import { Dropmenu } from '@mdtDesign/dropdown';
import { Dropelement } from '@mdtDesign/dropzone';
import toastApi from '@mdtDesign/toast';
import { AccessLevelEnum, PreferencesEnum, TaskStatusEnum } from '@mdtProComm/constants';
import {
  ILoadDataListRslt,
  IVirtualizedTableProps,
  TableCurdWithSimpleSearchController,
} from '@mdtProMicroModules/containers/table-curd-with-simple-search';
import defaultAppLogo from '@mdtProMicroModules/resource/logo.png';
import { CreateUploadFileTask } from '@mdtProTasks/CreateUploadFileTask';
import { AppController } from '../../app/AppController';
import { UPLOAD_LOGO_MAX_KB } from '../../config';
import i18n from '../../languages';
import { ILogoStyleModel } from './LogoStyleModel';

// 上传的类型
const ACCEPT_TYPES = '.png, .jpg, .jpeg, .svg';
// 默认在服务端存储的logo
const DEFAULT_PARAMS_LOGO = '';
// 最大上传size
const MAX_SIZE = 1024 * UPLOAD_LOGO_MAX_KB;

export interface IProduct {
  id: string;
  name: string;
  logo?: string;
}

// 菜单
enum CurdEnum {
  RESET = 'reset',
}

class LogoStyleController extends RequestController {
  private app: AppController;
  private Model: ILogoStyleModel;
  private tableController: TableCurdWithSimpleSearchController<IProduct>;

  public constructor(app: AppController, Model: ILogoStyleModel) {
    super();
    this.app = app;
    this.Model = Model;
    this.tableController = new TableCurdWithSimpleSearchController<IProduct>({
      dataListCompTableCurdControllerOptions: {
        dataListControllerOptions: {
          loadDataListFunc: this.queryAllData,
        },
        tableOptions: this.initTableOptions,
        curdOptions: this.initCurdOptions,
      },
      headerOptions: this.initHeaderOptions,
    });
    this.tableController.loadDataList();
  }

  public destroy() {
    super.destroy();
    this.tableController.destroy();
    this.app = null!;
    this.Model = null!;
  }

  public getTableController() {
    return this.tableController;
  }

  private queryAllData = (): ILoadDataListRslt<IProduct> => {
    const prefC = this.app.getPreferencesController()!;
    // 产品权限筛选
    const { enableDataFactory, enableDataMarket, enableDataMap } = this.app
      .getUserPermissionController()
      .getUserProductPermission(true);
    const list: { id: string; logo: string; name: string }[] = [];
    enableDataFactory &&
      list.push({
        id: PreferencesEnum.DATAFACTORY_LOGO,
        logo: prefC.getDatafactoryLogo(),
        name: i18n.chain.product.dataFactory,
      });
    enableDataMarket &&
      list.push({
        id: PreferencesEnum.DATAMARKET_LOGO,
        logo: prefC.getDatamarketLogo(),
        name: i18n.chain.product.dataMarket,
      });
    enableDataMap &&
      list.push({ id: PreferencesEnum.DATAMAP_LOGO, logo: prefC.getDatamapLogo(), name: i18n.chain.product.datlas });
    // 默认图片Promise
    const noneLogoPromise = new Promise<any>((resolve) => {
      resolve({ success: true, result: DEFAULT_PARAMS_LOGO });
    });

    const allReqPromise = _.map(list, ({ logo }) => {
      return !_.isEmpty(logo) ? this.Model.getListLogo(logo) : noneLogoPromise;
    });

    return _.isEmpty(allReqPromise)
      ? of([0, []] as [number, IProduct[]])
      : combineLatest(allReqPromise).pipe(
          map((allResp) => {
            const logos = _.map(allResp, (resp) => (resp.success && resp.result ? resp.result : defaultAppLogo));
            return [0, _.map(list, (app, i) => ({ ...app, logo: logos[i] }))];
          }),
        );
  };

  private initCurdOptions = () => {
    return {
      columnWidth: 150,
      otherBtns: (record: IProduct) => (
        <>
          <Dropelement accept={ACCEPT_TYPES} onDropAccepted={(files) => this.onUploadLogFunc(files, record)}>
            <LinkButton size="compact">{i18n.chain.orgAdmin.logo.changeLogo}</LinkButton>
          </Dropelement>
          <Dropmenu
            menus={[{ title: i18n.chain.orgAdmin.logo.reset, key: CurdEnum.RESET }]}
            icon="more"
            iconType="only-icon"
            noSelected
            onClickMenu={() => this.onReset(record)}
          />
        </>
      ),
    };
  };

  private onReset = async (record: IProduct) => {
    const resp = await this.Model.putAppLogo(record.id, DEFAULT_PARAMS_LOGO).toPromise();
    if (resp.success) {
      toastApi.success(i18n.chain.orgAdmin.logo.resetSuccess);
      this.tableController.editDataInList({ ...record, logo: defaultAppLogo });
    }
  };

  private onUploadLogFunc = (files: File[], record: IProduct) => {
    const file = files[0];
    if (file.size > MAX_SIZE) {
      toastApi.error(i18n.chain.orgAdmin.logo.maxSize(UPLOAD_LOGO_MAX_KB));
      return;
    }
    const task = new CreateUploadFileTask(file, 'image', { accessLevel: AccessLevelEnum.ANYONE });
    const handleUploaded = async (rslt: string) => {
      task.destroy();
      const resp = await this.Model.putAppLogo(record.id, rslt).toPromise();
      if (resp.success) {
        toastApi.success(i18n.chain.orgAdmin.logo.editLogoSuccess);
        this.fileByBase64(file, (logo: string) => {
          this.tableController.editDataInList({ ...record, logo });
        });
      }
    };
    task.taskResp$.subscribe(async (resp) => {
      const status = resp.status;
      if (status === TaskStatusEnum.SUCCESSFUL) {
        handleUploaded(resp.result);
      } else if (status === TaskStatusEnum.FAILED) {
        handleUploaded(i18n.chain.orgAdmin.logo.fileUploadFailed(file.name));
      }
    });
  };

  private fileByBase64 = (file: File, cb: any) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = function (e) {
      cb(e.target?.result);
    };
  };

  // 渲染表头所需信息
  private initTableOptions = (): IVirtualizedTableProps => {
    return {
      columns: [
        { name: i18n.chain.orgAdmin.logo.product, code: 'name' },
        {
          name: 'Logo',
          code: 'logo',
          render: (logo: string) => <img className="page_login-style-logo" src={logo} alt="logo" draggable={false} />,
        },
      ],
      type: 'page-bg',
      primaryKey: 'id',
      emptyContent: i18n.chain.comNoData,
      withVerticalBorder: false,
    };
  };

  // 渲染头部所需信息
  private initHeaderOptions = () => {
    return {
      createBtnLabel: '',
      hideInput: true,
      title: i18n.chain.orgAdmin.menu.logo,
    };
  };
}

export { LogoStyleController };
