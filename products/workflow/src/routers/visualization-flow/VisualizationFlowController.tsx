import { MdtDisableModeler } from '@mdtProComm/components/bpmn/viewer';
import { ParseBpmnProcessSpec } from './ParseBpmnProcessSpec';

class VisualizationFlowController {
  private flow: any;

  public constructor(flow: any) {
    this.flow = flow;
  }

  public drawerXml(canvas: HTMLDivElement) {
    const xmlViewer = MdtDisableModeler(canvas);
    xmlViewer.importXML(this.flow.bpmn_xml).then(() => {
      const canvas = xmlViewer.get('canvas');
      canvas.zoom('fit-viewport');
    });
  }

  public async drawerEngineContext(canvas: HTMLDivElement) {
    const flow = this.flow;
    const xmlViewer = MdtDisableModeler(canvas);
    // 可视化spec
    const xml = await this.parseEngineContext(flow.engine_context || flow.spec);
    xml &&
      xmlViewer.importXML(xml).then(() => {
        const canvas = xmlViewer.get('canvas');
        canvas.zoom('fit-viewport');
      });
  }

  private async parseEngineContext(engineContext: any) {
    const type = engineContext.typename;
    // 先处理BpmnProcessSpec类型，其他类型后续再处理
    if (type === 'BpmnProcessSpec') {
      return this.parseBpmnProcessSpec(engineContext);
    }
    return '';
  }

  private async parseBpmnProcessSpec(engineContext: any) {
    const parse = new ParseBpmnProcessSpec({ engineContext });
    const xml = await parse.getBpmnProcessSpecXml();
    parse.destroy();
    return xml;
  }
}

export { VisualizationFlowController };
