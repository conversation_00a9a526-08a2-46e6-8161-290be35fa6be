import { useEffect, useRef } from 'react';

const useClickAway = (onClickAway: (event: MouseEvent) => void) => {
  const ref = useRef<HTMLElement>(null);
  const onClickAwayRef = useRef(onClickAway);
  onClickAwayRef.current = onClickAway;

  useEffect(() => {
    const handler = (event: any) => {
      if (ref.current?.contains(event.target)) return;
      onClickAwayRef.current(event);
    };
    document.addEventListener('click', handler);
    return () => {
      document.removeEventListener('click', handler);
    };
  }, []);

  return ref;
};

export { useClickAway };
