/* eslint-disable sonarjs/no-duplicate-string */
/* eslint-disable @typescript-eslint/explicit-member-accessibility */
/**
 * @file Flex 常见布局 上下布局
 */
import i18n from '../../../languages';
import { FlexPluginBase } from '../FlexPluginBase';

export default class Layout1_1_v2 extends FlexPluginBase {
  name = i18n.chain.jsonComp.layout1_1_v2;
  isBaseComponent = false;
  pluginIcon = 'layout-2row-plugin';
  description = i18n.chain.jsonComp.layout1_1_v2Description;
  tags = [i18n.chain.jsonComp.commonLayout];
  order = 203;
  scaffold: any = {
    type: 'flex',
    className: 'p-1',
    items: [
      {
        type: 'wrapper',
        size: 'xs',
        body: [],
        style: {
          flex: '1 1 auto',
          flexBasis: 'auto',
          flexGrow: 1,
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'flex-start',
          alignItems: 'stretch',
        },
      },
      {
        type: 'wrapper',
        size: 'xs',
        body: [],
        style: {
          flex: '1 1 auto',
          flexBasis: 'auto',
          flexGrow: 1,
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'flex-start',
          alignItems: 'stretch',
        },
      },
    ],
    direction: 'column',
    justify: 'center',
    alignItems: 'stretch',
  };
}
