import { useController } from '@mdtBsComm/hooks/use-controller';
import { getModel } from '../../_util/modelUtil';
import { useAppContext } from '../../app/appContext';
import {
  InstitutionDatapkg,
  InstitutionDatapkgController,
  InstitutionDatapkgModel,
} from '../../pages/institution-datapkg';
import { InstitutionDatapkgModelBff } from '../../pages/institution-datapkg/InstitutionDatapkgModelBff';

// 本机构===========================================================================================
const SelfAppData = () => {
  const { appController } = useAppContext();

  const [controller] = useController(() => {
    // TODO 后续可以考虑缓存到App Controller, 加速打开速度
    const ctrl = new InstitutionDatapkgController(
      appController,
      getModel(InstitutionDatapkgModel, InstitutionDatapkgModelBff),
    );
    return [ctrl, null];
  });

  return <InstitutionDatapkg controller={controller} />;
};

export default SelfAppData;
