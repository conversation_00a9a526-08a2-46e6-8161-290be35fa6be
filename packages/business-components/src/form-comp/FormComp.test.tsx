import _ from 'lodash';
import { fireEvent, render } from '@testing-library/react';
// import { faker } from '@faker-js/faker';
import { screen } from 'query-extensions';
import { FormField } from '@mdtBsComm/components/form';
import { Input } from '@mdtDesign/input';
import { FormComp } from './FormComp';
import { FormCompController } from './FormCompController';

const controllers: FormCompController[] = [];
let formCompController: FormCompController;
export interface IFormData {
  name: string;
  geometryType: string;
}

describe('FormCompTest', () => {
  describe('默认样式', () => {
    beforeAll(() => {
      formCompController = new FormCompController<IFormData>();
      controllers.push(formCompController);
    });
    test('正确渲染', () => {
      render(
        <FormComp controller={formCompController}>
          <FormField name="lhj" label="数据包名称">
            <Input placeholder="请输入" allowClear={false} block />
          </FormField>
        </FormComp>,
      );
      expect(screen.getByText('数据包名称')).toBeVisible();
    });
  });

  describe('触发事件', () => {
    beforeAll(() => {
      formCompController = new FormCompController<IFormData>();
      controllers.push(formCompController);
    });

    // test('校验不通过', () => {
    //   render(
    //     <FormComp controller={formCompController}>
    //       <FormField name="lhj" label="数据包名称" required
    //         rules={[{ required: true, message: '请输入内容' }]}>
    //         <Input placeholder="请输入" allowClear={false} block />
    //       </FormField>
    //     </FormComp>,
    //   );
    //   formCompController.getChangedValues$()
    //   formCompController.getFormDataError$()
    //   expect(screen.getByText('请输入内容')).toBeVisible();
    // })

    test('触发onChange事件', () => {
      render(
        <FormComp controller={formCompController}>
          <FormField name="lhj" label="数据包名称" required rules={[{ required: true, message: '请输入内容' }]}>
            <Input placeholder="请输入" allowClear={false} block />
          </FormField>
        </FormComp>,
      );
      const input = screen.getBySelector('input');
      fireEvent.input(input, { target: { value: '测试' } });
    });
  });
});

afterAll(() => {
  _.forEach(controllers, (it) => {
    it.destroy();
  });
  controllers.length = 0;
  // mockData.length = 0;
});
