import _ from 'lodash';
import { FC } from 'react';
import { Button } from '@metroDesign/button';
import { Input } from '@metroDesign/input';
import { BehaviorSubject } from 'rxjs';
import { useObservableState } from '@mdtBsComm/hooks/use-observable-state';
import { IframeChannelController } from '@mdtBsControllers/iframe-channel-controller';
import { IframeAmisEditorMessageEnum } from '@mdtProComm/constants';
import { AppController } from '../../app/AppController';
import { EditorController } from '../../containers/editor';
import i18n from '../../languages';

const INPUT_STYLE = { width: 200 };

const InputTitle: FC<{ controller: IFrameEditorController }> = ({ controller }) => {
  const name = useObservableState(controller.name$);

  return (
    <div className="editor-title">
      <Input.WithoutBorder
        onChange={(e) => {
          controller.name$.next(_.trimStart(e.target.value));
        }}
        style={INPUT_STYLE}
        value={name}
        placeholder={i18n.chain.comPlaceholder.input}
      />
    </div>
  );
};

const ButtonBlock: FC<{ ctrl: IFrameEditorController }> = ({ ctrl }) => {
  const loading = useObservableState(ctrl.btbStats$);

  return (
    <>
      <Button style={{ marginLeft: 100 }} loading={loading} className="header-action-btn" onClick={ctrl.handleSave}>
        {i18n.chain.comButton.save}
      </Button>
      <Button
        danger
        style={{ marginLeft: 28 }}
        loading={loading}
        className="header-action-btn"
        onClick={ctrl.handleCancel}
      >
        {i18n.chain.comButton.giveup}
      </Button>
      <Button type="primary" loading={loading} className="header-action-btn" onClick={ctrl.handleFinish}>
        {i18n.chain.comButton.finish}
      </Button>
    </>
  );
};

export class IFrameEditorController extends EditorController {
  public name$ = new BehaviorSubject<string>('');
  public btbStats$ = new BehaviorSubject<boolean>(false);

  public constructor(app: AppController) {
    super(app, {});
  }

  public handleParentMessage = (data: any) => {
    const { payload, type } = data || {};
    if (type === IframeAmisEditorMessageEnum.VALUE) {
      this.setValueFromParent(payload);
    }
  };

  public loadFinish() {
    IframeChannelController.sendMessageToParent(IframeAmisEditorMessageEnum.LOADED, '');
  }

  public renderHeaderTitle = () => {
    return <InputTitle controller={this} />;
  };

  public renderExtraBtns = () => {
    return <ButtonBlock ctrl={this} />;
  };

  public destroy() {
    this.name$.complete();
  }

  public handleSave = () => {
    this.btbStats$.next(true);
    const payload = this.getPayload();
    IframeChannelController.sendMessageToParent(IframeAmisEditorMessageEnum.SAVE, payload);
  };

  public handleFinish = () => {
    this.btbStats$.next(true);
    const payload = this.getPayload();
    IframeChannelController.sendMessageToParent(IframeAmisEditorMessageEnum.SUCCESS, payload);
  };

  public handleCancel = () => {
    this.btbStats$.next(true);
    const payload = this.getPayload();
    IframeChannelController.sendMessageToParent(IframeAmisEditorMessageEnum.CLOSE, payload);
  };

  private getPayload = () => {
    return {
      vaultTitle: this.name$.getValue(),
      amisjson: this.value$.getValue(),
    };
  };

  private setValueFromParent(payload: any) {
    if (!_.isNil(payload.vaultTitle)) {
      this.name$.next(payload.vaultTitle);
    }
    if (!_.isNil(payload.amisjson)) {
      this.value$.next(payload.amisjson);
    }
    if (!_.isNil(payload.btbStats)) {
      this.btbStats$.next(payload.btbStats);
    }
  }
}
