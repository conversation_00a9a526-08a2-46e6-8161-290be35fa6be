import loadable from '@loadable/component';
import { PermissionEnum } from '@mdtProComm/constants/permission';
import { IRoute } from '@mdtProComm/controllers/BaseRouterController';
import { DatlasRouterController } from '@mdtProMicroModules/datlas/comm/DatlasRouterController';
import { RoutePathEnum } from './constants';

const Flows = loadable(() => import('../routers/flows'));
const Monitor = loadable(() => import('../routers/monitor'));

export const allAuthRoutes: IRoute[] = [
  {
    path: RoutePathEnum.FLOWS,
    View: Flows,
    permissionKey: PermissionEnum.MENU_LOW_ETL,
    // headerLess: true, // 不展示header
    // sideMenuLess: true, // 不展示sideMenu
  },
  {
    path: RoutePathEnum.MONITOR,
    View: Monitor,
    permissionKey: PermissionEnum.MENU_LOW_ETL,
  },
];

class RouterController extends DatlasRouterController {
  public getAllAuthRoutes() {
    return allAuthRoutes;
  }

  public gotoFlows() {
    this.gotoPath(RoutePathEnum.FLOWS);
  }

  public gotoMonitor() {
    this.gotoPath(RoutePathEnum.MONITOR);
  }
}

export { RouterController };
