include:
  - project: 'new-datamap/scripts/mdt-script'
    ref: main
    file: '.gitlab-ci.yml'

variables:
  PROJECT_NAME: mdt-frontend
  PROJECT_PATH: ${CI_PROJECT_NAMESPACE}/${PROJECT_NAME}
  CACHE_IMAGE: ${DOCKER_REGISTRY}/${PROJECT_NAME}:cache
  BASE_IMAGE: ${DOCKER_REGISTRY}/${PROJECT_NAME}:base

cache:
  untracked: true
  key: '$CI_COMMIT_REF_SLUG'
  paths:
    - node_modules/

stages:
  - build
  - deploy

build_cache_image:
  stage: build
  tags:
#    - shared-shell-runner-hk
    - build-ssh-new-datamap
  script:
    - docker build --build-arg REGISTRY=${DOCKER_REGISTRY} -t ${CACHE_IMAGE} -f docker/cache/Dockerfile .
    - docker push ${CACHE_IMAGE}
  rules:
    - if: '$CI_COMMIT_BRANCH == "main" || $CI_COMMIT_BRANCH == "release-dev"'
      when: manual

build_base_image:
  stage: build
  tags:
#    - shared-shell-runner-hk
    - build-ssh-new-datamap
  script:
    - docker build --build-arg REGISTRY=${DOCKER_REGISTRY} -t ${BASE_IMAGE} -f docker/Dockerfile .
    - docker push ${BASE_IMAGE}
  rules:
    - if: $CI_COMMIT_BRANCH == 'main'
      changes:
        - docker/Dockerfile
        - yarn.lock
      when: always
    - if: '$CI_COMMIT_BRANCH == "main" || $CI_COMMIT_BRANCH == "release-dev"'
      when: manual

.build_resource:
  stage: build
  image: ${BASE_IMAGE}
  variables:
    # https://github.com/webpack/webpack-sources/issues/66
    NODE_OPTIONS: --max-old-space-size=8096
  tags:
    - k8s-devops-dev
  cache:
    policy: pull

  script:
    - ln -s /npm/node_modules ./node_modules
    - lerna bootstrap
    - yarn release:${PRODUCT_NAME}
    - cd products/${PRODUCT_NAME}/build
    - node ../../../.scripts/after-product-build.js --tag=$CI_COMMIT_TAG
    - cd ../../../

.deploy_project_resource:
  extends: .deploy_mdt_resource
  variables:
    COMMAND_DEPLOY: yarn mdtfps deploy --project ${PRODUCT_NAME} --tag $CI_COMMIT_TAG

upload_build_cache:
  extends: .build_resource
  variables:
    PRODUCT_NAME: sso
  rules:
    - if: '$CI_COMMIT_BRANCH == "main" || $CI_COMMIT_BRANCH == "release-dev"'
      when: manual
  cache:
    policy: pull-push

build_data-factory:
  extends: .build_resource
  variables:
    PRODUCT_NAME: data-factory
  rules:
    - if: '$CI_COMMIT_TAG =~ /^dev_data-factory_.*/'
    - if: '$CI_COMMIT_TAG =~ /^staging_data-factory_.*/'
    - if: '$CI_COMMIT_TAG =~ /^prod_data-factory_.*/'
    - if: '$CI_COMMIT_TAG =~ /^pri_data-factory_.*/'
    - if: '$CI_COMMIT_MESSAGE =~ /.*data-factory@\d.*/ && $CI_COMMIT_BRANCH == "main"'

build_designable:
  extends: .build_resource
  variables:
    PRODUCT_NAME: designable
  rules:
    - if: '$CI_COMMIT_TAG =~ /^dev_designable_.*/'
    - if: '$CI_COMMIT_TAG =~ /^staging_designable_.*/'
    - if: '$CI_COMMIT_TAG =~ /^prod_designable_.*/'
    - if: '$CI_COMMIT_TAG =~ /^pri_designable_.*/'
    - if: '$CI_COMMIT_MESSAGE =~ /.*designable@\d.*/ && $CI_COMMIT_BRANCH == "main"'

build_data-market:
  extends: .build_resource
  variables:
    PRODUCT_NAME: data-market
  rules:
    - if: '$CI_COMMIT_TAG =~ /^dev_data-market_.*/'
    - if: '$CI_COMMIT_TAG =~ /^staging_data-market_.*/'
    - if: '$CI_COMMIT_TAG =~ /^prod_data-market_.*/'
    - if: '$CI_COMMIT_TAG =~ /^pri_data-market_.*/'
    - if: '$CI_COMMIT_MESSAGE =~ /.*data-market@\d.*/ && $CI_COMMIT_BRANCH == "main"'

build_organization-management:
  extends: .build_resource
  variables:
    PRODUCT_NAME: organization-management
  rules:
    - if: '$CI_COMMIT_TAG =~ /^dev_organization-management_.*/'
    - if: '$CI_COMMIT_TAG =~ /^staging_organization-management_.*/'
    - if: '$CI_COMMIT_TAG =~ /^prod_organization-management_.*/'
    - if: '$CI_COMMIT_TAG =~ /^pri_organization-management_.*/'
    - if: '$CI_COMMIT_MESSAGE =~ /.*organization-management@\d.*/ && $CI_COMMIT_BRANCH == "main"'

build_devops-frontend:
  extends: .build_resource
  variables:
    PRODUCT_NAME: devops-frontend
  rules:
    - if: '$CI_COMMIT_TAG =~ /^dev_devops-frontend_.*/'
    - if: '$CI_COMMIT_TAG =~ /^staging_devops-frontend_.*/'
    - if: '$CI_COMMIT_TAG =~ /^prod_devops-frontend_.*/'
    - if: '$CI_COMMIT_TAG =~ /^pri_devops-frontend_.*/'
    - if: '$CI_COMMIT_MESSAGE =~ /.*devops-frontend@\d.*/ && $CI_COMMIT_BRANCH == "main"'

build_my-data:
  extends: .build_resource
  variables:
    PRODUCT_NAME: my-data
  rules:
    - if: '$CI_COMMIT_TAG =~ /^dev_my-data_.*/'
    - if: '$CI_COMMIT_TAG =~ /^staging_my-data_.*/'
    - if: '$CI_COMMIT_TAG =~ /^prod_my-data_.*/'
    - if: '$CI_COMMIT_TAG =~ /^pri_my-data_.*/'
    - if: '$CI_COMMIT_MESSAGE =~ /.*my-data@\d.*/ && $CI_COMMIT_BRANCH == "main"'

build_sso:
  extends: .build_resource
  variables:
    PRODUCT_NAME: sso
  rules:
    - if: '$CI_COMMIT_TAG =~ /^dev_sso_.*/'
    - if: '$CI_COMMIT_TAG =~ /^staging_sso_.*/'
    - if: '$CI_COMMIT_TAG =~ /^prod_sso_.*/'
    - if: '$CI_COMMIT_TAG =~ /^pri_sso_.*/'
    - if: '$CI_COMMIT_MESSAGE =~ /.*[^-]sso@\d.*/ && $CI_COMMIT_BRANCH == "main"'

build_collector-sso:
  extends: .build_resource
  variables:
    PRODUCT_NAME: collector-sso
  rules:
    - if: '$CI_COMMIT_TAG =~ /^dev_collector-sso_.*/'
    - if: '$CI_COMMIT_TAG =~ /^staging_collector-sso_.*/'
    - if: '$CI_COMMIT_TAG =~ /^prod_collector-sso_.*/'
    - if: '$CI_COMMIT_TAG =~ /^pri_collector-sso_.*/'
    - if: '$CI_COMMIT_MESSAGE =~ /.*collector-sso@\d.*/ && $CI_COMMIT_BRANCH == "main"'

build_mdt-amis-editor:
  extends: .build_resource
  variables:
    PRODUCT_NAME: mdt-amis-editor
  rules:
    - if: '$CI_COMMIT_TAG =~ /^dev_mdt-amis-editor_.*/'
    - if: '$CI_COMMIT_TAG =~ /^staging_mdt-amis-editor_.*/'
    - if: '$CI_COMMIT_TAG =~ /^prod_mdt-amis-editor_.*/'
    - if: '$CI_COMMIT_TAG =~ /^pri_mdt-amis-editor_.*/'
    - if: '$CI_COMMIT_MESSAGE =~ /.*mdt-amis-editor@\d.*/ && $CI_COMMIT_BRANCH == "main"'

build_one-table:
  extends: .build_resource
  variables:
    PRODUCT_NAME: one-table
  rules:
    - if: '$CI_COMMIT_TAG =~ /^dev_one-table_.*/'
    - if: '$CI_COMMIT_TAG =~ /^staging_one-table_.*/'
    - if: '$CI_COMMIT_TAG =~ /^prod_one-table_.*/'
    - if: '$CI_COMMIT_TAG =~ /^pri_one-table_.*/'
    - if: '$CI_COMMIT_MESSAGE =~ /.*one-table@\d.*/ && $CI_COMMIT_BRANCH == "main"'

build_micro-mdt:
  extends: .build_resource
  variables:
    PRODUCT_NAME: micro-mdt
  rules:
    - if: '$CI_COMMIT_TAG =~ /^dev_micro-mdt_.*/'
    - if: '$CI_COMMIT_TAG =~ /^staging_micro-mdt_.*/'
    - if: '$CI_COMMIT_TAG =~ /^prod_micro-mdt_.*/'
    - if: '$CI_COMMIT_TAG =~ /^pri_micro-mdt_.*/'
    - if: '$CI_COMMIT_MESSAGE =~ /.*micro-mdt@\d.*/ && $CI_COMMIT_BRANCH == "main"'

build_resource-share:
  extends: .build_resource
  variables:
    PRODUCT_NAME: resource-share
  rules:
    - if: '$CI_COMMIT_TAG =~ /^dev_resource-share_.*/'
    - if: '$CI_COMMIT_TAG =~ /^staging_resource-share_.*/'
    - if: '$CI_COMMIT_TAG =~ /^prod_resource-share_.*/'
    - if: '$CI_COMMIT_TAG =~ /^pri_resource-share_.*/'
    - if: '$CI_COMMIT_MESSAGE =~ /.*resource-share@\d.*/ && $CI_COMMIT_BRANCH == "main"'

build_workflow:
  extends: .build_resource
  variables:
    PRODUCT_NAME: workflow
  rules:
    - if: '$CI_COMMIT_TAG =~ /^dev_workflow_.*/'
    - if: '$CI_COMMIT_TAG =~ /^staging_workflow_.*/'
    - if: '$CI_COMMIT_TAG =~ /^prod_workflow_.*/'
    - if: '$CI_COMMIT_TAG =~ /^pri_workflow_.*/'
    - if: '$CI_COMMIT_MESSAGE =~ /.*workflow@\d.*/ && $CI_COMMIT_BRANCH == "main"'

deploy_data-factory:
  extends: .deploy_project_resource
  variables:
    PRODUCT_NAME: data-factory
  rules:
    - if: '$CI_COMMIT_TAG =~ /^dev_data-factory_.*/'
    - if: '$CI_COMMIT_TAG =~ /^staging_data-factory_.*/'
    - if: '$CI_COMMIT_TAG =~ /^prod_data-factory_.*/'
    - if: '$CI_COMMIT_MESSAGE =~ /.*data-factory@\d.*/ && $CI_COMMIT_BRANCH == "main"'
      when: manual
  dependencies:
    - build_data-factory
  needs: ["build_data-factory"]

deploy_designable:
  extends: .deploy_project_resource
  variables:
    PRODUCT_NAME: designable
  rules:
    - if: '$CI_COMMIT_TAG =~ /^dev_designable_.*/'
    - if: '$CI_COMMIT_TAG =~ /^staging_designable_.*/'
    - if: '$CI_COMMIT_TAG =~ /^prod_designable_.*/'
    - if: '$CI_COMMIT_MESSAGE =~ /.*designable@\d.*/ && $CI_COMMIT_BRANCH == "main"'
      when: manual
  dependencies:
    - build_designable
  needs: ["build_designable"]

deploy_data-market:
  extends: .deploy_project_resource
  variables:
    PRODUCT_NAME: data-market
  rules:
    - if: '$CI_COMMIT_TAG =~ /^dev_data-market_.*/'
    - if: '$CI_COMMIT_TAG =~ /^staging_data-market_.*/'
    - if: '$CI_COMMIT_TAG =~ /^prod_data-market_.*/'
    - if: '$CI_COMMIT_MESSAGE =~ /.*data-market@\d.*/ && $CI_COMMIT_BRANCH == "main"'
      when: manual
  dependencies:
    - build_data-market
  needs: [ "build_data-market" ]

deploy_organization-management:
  extends: .deploy_project_resource
  variables:
    PRODUCT_NAME: organization-management
  rules:
    - if: '$CI_COMMIT_TAG =~ /^dev_organization-management_.*/'
    - if: '$CI_COMMIT_TAG =~ /^staging_organization-management_.*/'
    - if: '$CI_COMMIT_TAG =~ /^prod_organization-management_.*/'
    - if: '$CI_COMMIT_MESSAGE =~ /.*organization-management@\d.*/ && $CI_COMMIT_BRANCH == "main"'
      when: manual
  dependencies:
    - build_organization-management
  needs: [ "build_organization-management" ]

deploy_devops-frontend:
  extends: .deploy_project_resource
  variables:
    PRODUCT_NAME: devops-frontend
  rules:
    - if: '$CI_COMMIT_TAG =~ /^dev_devops-frontend_.*/'
    - if: '$CI_COMMIT_TAG =~ /^staging_devops-frontend_.*/'
    - if: '$CI_COMMIT_TAG =~ /^prod_devops-frontend_.*/'
    - if: '$CI_COMMIT_MESSAGE =~ /.*devops-frontend@\d.*/ && $CI_COMMIT_BRANCH == "main"'
      when: manual
  dependencies:
    - build_devops-frontend
  needs: [ "build_devops-frontend" ]

deploy_my-data:
  extends: .deploy_project_resource
  variables:
    PRODUCT_NAME: my-data
  rules:
    - if: '$CI_COMMIT_TAG =~ /^dev_my-data_.*/'
    - if: '$CI_COMMIT_TAG =~ /^staging_my-data_.*/'
    - if: '$CI_COMMIT_TAG =~ /^prod_my-data_.*/'
    - if: '$CI_COMMIT_MESSAGE =~ /.*my-data@\d.*/ && $CI_COMMIT_BRANCH == "main"'
      when: manual
  dependencies:
    - build_my-data
  needs: [ "build_my-data" ]

deploy_sso:
  extends: .deploy_project_resource
  variables:
    PRODUCT_NAME: sso
  rules:
    - if: '$CI_COMMIT_TAG =~ /^dev_sso_.*/'
    - if: '$CI_COMMIT_TAG =~ /^staging_sso_.*/'
    - if: '$CI_COMMIT_TAG =~ /^prod_sso_.*/'
    - if: '$CI_COMMIT_MESSAGE =~ /.*[^-]sso@\d.*/ && $CI_COMMIT_BRANCH == "main"'
      when: manual
  dependencies:
    - build_sso
  needs: [ "build_sso" ]

deploy_collector-sso:
  extends: .deploy_project_resource
  variables:
    PRODUCT_NAME: collector-sso
  rules:
    - if: '$CI_COMMIT_TAG =~ /^dev_collector-sso_.*/'
    - if: '$CI_COMMIT_TAG =~ /^staging_collector-sso_.*/'
    - if: '$CI_COMMIT_TAG =~ /^prod_collector-sso_.*/'
    - if: '$CI_COMMIT_MESSAGE =~ /.*collector-sso@\d.*/ && $CI_COMMIT_BRANCH == "main"'
      when: manual
  dependencies:
    - build_collector-sso
  needs: [ "build_collector-sso" ]

deploy_mdt-amis-editor:
  extends: .deploy_project_resource
  variables:
    PRODUCT_NAME: mdt-amis-editor
  rules:
    - if: '$CI_COMMIT_TAG =~ /^dev_mdt-amis-editor_.*/'
    - if: '$CI_COMMIT_TAG =~ /^staging_mdt-amis-editor_.*/'
    - if: '$CI_COMMIT_TAG =~ /^prod_mdt-amis-editor_.*/'
    - if: '$CI_COMMIT_MESSAGE =~ /.*mdt-amis-editor@\d.*/ && $CI_COMMIT_BRANCH == "main"'
      when: manual
  dependencies:
    - build_mdt-amis-editor
  needs: [ "build_mdt-amis-editor" ]

deploy_one-table:
  extends: .deploy_project_resource
  variables:
    PRODUCT_NAME: one-table
  rules:
    - if: '$CI_COMMIT_TAG =~ /^dev_one-table_.*/'
    - if: '$CI_COMMIT_TAG =~ /^staging_one-table_.*/'
    - if: '$CI_COMMIT_TAG =~ /^prod_one-table_.*/'
    - if: '$CI_COMMIT_MESSAGE =~ /.*one-table@\d.*/ && $CI_COMMIT_BRANCH == "main"'
      when: manual
  dependencies:
    - build_one-table
  needs: [ "build_one-table" ]

deploy_micro-mdt:
  extends: .deploy_project_resource
  variables:
    PRODUCT_NAME: micro-mdt
  rules:
    - if: '$CI_COMMIT_TAG =~ /^dev_micro-mdt_.*/'
    - if: '$CI_COMMIT_TAG =~ /^staging_micro-mdt_.*/'
    - if: '$CI_COMMIT_TAG =~ /^prod_micro-mdt_.*/'
    - if: '$CI_COMMIT_MESSAGE =~ /.*micro-mdt@\d.*/ && $CI_COMMIT_BRANCH == "main"'
      when: manual
  dependencies:
    - build_micro-mdt
  needs: [ "build_micro-mdt" ]

deploy_resource-share:
  extends: .deploy_project_resource
  variables:
    PRODUCT_NAME: resource-share
  rules:
    - if: '$CI_COMMIT_TAG =~ /^dev_resource-share_.*/'
    - if: '$CI_COMMIT_TAG =~ /^staging_resource-share_.*/'
    - if: '$CI_COMMIT_TAG =~ /^prod_resource-share_.*/'
    - if: '$CI_COMMIT_MESSAGE =~ /.*resource-share@\d.*/ && $CI_COMMIT_BRANCH == "main"'
      when: manual
  dependencies:
    - build_resource-share
  needs: [ "build_resource-share" ]

deploy_workflow:
  extends: .deploy_project_resource
  variables:
    PRODUCT_NAME: workflow
  rules:
    - if: '$CI_COMMIT_TAG =~ /^dev_workflow_.*/'
    - if: '$CI_COMMIT_TAG =~ /^staging_workflow_.*/'
    - if: '$CI_COMMIT_TAG =~ /^prod_workflow_.*/'
    - if: '$CI_COMMIT_MESSAGE =~ /.*workflow@\d.*/ && $CI_COMMIT_BRANCH == "main"'
      when: manual
  dependencies:
    - build_workflow
  needs: [ "build_workflow" ]
