import { useObservableState } from '@mdtBsComm/hooks/use-observable-state';
import { DataListCompTableWithCurd } from '@mdtBsComponents/data-list-comp-table-curd';
import { ModalWithBtnsCompDialog } from '@mdtBsComponents/modal-with-btns-comp-dialog';
import { ModalWithBtnsCompEmotion } from '@mdtBsComponents/modal-with-btns-comp-emotion';
import { LinkButton } from '@mdtDesign/button';
import Spin from '@mdtDesign/spin';
import Toggle from '@mdtDesign/toggle';
import i18n from '../../languages';
import {
  useTableDatapkgDesensitizeContext,
  useTableDatapkgDesensitizeProvider,
} from './tableDatapkgDesensitizeContext';
import { TableDatapkgDesensitizeController } from './TableDatapkgDesensitizeController';
import './index.less';

interface IProps {
  controller: TableDatapkgDesensitizeController;
}

export const FieldDesensitizeTool = ({ controller }: IProps) => {
  const isEnabled = useObservableState(controller.getIsEnabled$());
  return (
    <div className="module_table-datapkg-desensitize_tool">
      <LinkButton leftIcon="add-2" onClick={() => controller.onClickCreateBtn()} className="table-desensitize_tool-btn">
        {i18n.chain.proMicroModules.desensitize.addDesensitize}
      </LinkButton>
      <div className="table-desensitize_enable-wrap">
        {i18n.chain.proMicroModules.desensitize.activeDesensitize}
        <Toggle
          className="table-desensitize_enable"
          checked={isEnabled}
          onChange={controller.handleEnableChange}
          checkedChildren={<div />}
          unCheckedChildren={<div />}
        />
      </div>
    </div>
  );
};

const TableDatapkgDesensitizeInner = () => {
  const { tableDatapkgDesensitizeController: controller } = useTableDatapkgDesensitizeContext();
  const isLoading = useObservableState(controller.getIsLoading$());
  return isLoading ? (
    <Spin size="large" tip={i18n.chain.comDataLoading} />
  ) : (
    <div className="module_table-datapkg-desensitize">
      <DataListCompTableWithCurd className="table-desensitize_table" controller={controller} />
      <ModalWithBtnsCompEmotion controller={controller.getDeleteController()} />
      <ModalWithBtnsCompDialog controller={controller.getModifyController()} />
    </div>
  );
};

const TableDatapkgDesensitize = ({ controller }: IProps) => {
  const Provider = useTableDatapkgDesensitizeProvider();
  const value = { tableDatapkgDesensitizeController: controller };

  return (
    <Provider value={value}>
      <TableDatapkgDesensitizeInner />
    </Provider>
  );
};

export { TableDatapkgDesensitize };
