import { IRowValue } from './comm';
import { IDatapkgColumnType } from './datapkgs';

export type ITaskStatus = 'idle' | 'ready' | 'running' | 'transient_failed' | 'failed' | 'successful' | 'canceled';

export interface ITasksQuery {
  status?: ITaskStatus | string;
  user_id?: number | string;
  app_id?: number | string;
  cmd?: string;
  create_time_start?: number;
  creat_time_end?: number;
  update_time_start?: number;
  update_time_end?: number;
  order_by?: string;
}

export type ITaskCmd =
  | 'petal_create_general_market_package'
  | 'slack'
  | 'petal_task'
  | 'export_hires_map'
  | 'file_preview';

export interface ITaskCmdPCGMP {
  file: string;
  name: string;
  city: string;
  description?: string;
  tags?: string[];
  app?: string; // app名称
  no_approval?: boolean;
  no_header_check?: boolean;
  column_type?: Record<string, IDatapkgColumnType>;
}

export interface ITaskCmdFilePreview {
  url?: string;
  file_id?: string;
  limit?: number;
}

export type ITaskCmdSlack = Record<string, IRowValue>;

export interface ITaskCmdPetalTask {
  task_name: string;
  cliargs: string[];
}

export interface ITaskCmdExportHiresMapStyleKey {
  name: string;
  key: string;
}

export type ITaskCmdExportHiresMapHiresType = 'hd_ll' | 'hd' | 'fhd' | 'normal';

export interface ITaskCmdExportHiresMap {
  token: string;
  bbox: number[];
  style_keys: ITaskCmdExportHiresMapStyleKey[];
  hires_type: ITaskCmdExportHiresMapHiresType;
  zoom: number;
  export_layer: boolean;
  map_legends: string[];
  map_name: string;
}

export interface ITaskPostFilePreview {
  url?: string;
  task_id?: string;
  file_id?: string;
  encoding?: string;
  task_description?: string;
  limit?: number;
}

export interface ITask<T = any> {
  task_id: string;
  status: ITaskStatus;
  app_id: number;
  user_id: number;
  create_time: string;
  update_time: string;
  result?: T;
  error?: string;
  cmd: ITaskCmd;
  args: ITaskCmdFilePreview | ITaskCmdPCGMP | ITaskCmdSlack | ITaskCmdPetalTask | ITaskCmdExportHiresMap;
  description?: string;
}

export interface ITaskProgressReport {
  progress: number;
  progress_total: number;
  stage?: string;
  status?: ITaskStatus;
  message?: string;
}

export interface ITaskProgressPost {
  user_id: number;
  report: ITaskProgressReport;
}

export interface ITaskPatch {
  status?: ITaskStatus;
  result?: any;
  error?: string;
}

export interface ITaskResult<T = any> {
  task_id: string;
  result: T;
}
