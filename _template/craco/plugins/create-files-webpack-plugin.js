const fs = require('fs');

class CreateFilesWebpackPlugin {
  constructor(options) {
    this.patterns = options.patterns || [];
  }
  apply(compiler) {
    compiler.hooks.compilation.tap('CreateFilesWebpackPlugin', () => {
      for (const pattern of this.patterns) {
        fs.writeFileSync(pattern.path, pattern.content, pattern.options);
      }
      this.patterns.length = 0;
    });
  }
}

module.exports = CreateFilesWebpackPlugin;
