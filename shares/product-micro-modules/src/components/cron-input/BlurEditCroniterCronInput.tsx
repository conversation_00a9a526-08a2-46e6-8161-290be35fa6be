import { useState } from 'react';
import { EditorCroniterCronInput } from './EditCroniterCronInput';

export const BlurEditCroniterCronInput = (props: any) => {
  const { value, onChange } = props;

  const [storeValue, setStoreValue] = useState(value);

  const handleChange = (nv: any) => {
    setStoreValue(nv.target ? nv.target.value : nv);
  };

  const handeBlur = () => {
    onChange(storeValue);
  };

  const onCronChange = (value: string) => {
    onChange(value);
  };

  return (
    <EditorCroniterCronInput
      value={storeValue}
      onChange={handleChange}
      onCronChange={onCronChange}
      inputProps={{ onBlur: handeBlur }}
    />
  );
};
