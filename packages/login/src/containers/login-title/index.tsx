import { FC } from 'react';
import { ConfigKeyEnum, useConfig } from '../../config';

export interface ILoginTitle {
  title?: any;
}

const DEFAULT_LOGO = (
  <svg width="24" height="23" viewBox="0 0 24 23" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M12.1826 0.223511C9.93728 0.223511 7.74241 0.88935 5.87528 2.13682C4.00815 3.38428 2.55286 5.15738 1.69391 7.23184C0.834411 9.3063 0.609773 11.589 1.04766 13.7912C1.48609 15.9935 2.56697 18.0163 4.15466 19.6041C5.74234 21.1916 7.7652 22.273 9.96766 22.7109C12.1701 23.1493 14.4524 22.9241 16.5273 22.0652C18.6017 21.2057 20.3744 19.7507 21.6219 17.8837C22.8694 16.0167 23.5351 13.8218 23.5351 11.5764C23.5351 8.56542 22.3392 5.67779 20.21 3.54869C18.0813 1.41964 15.1936 0.223511 12.1826 0.223511ZM18.9587 13.3816C18.9587 16.0611 16.1551 19.9299 12.869 19.9299H12.8478L12.8419 17.7472C14.9955 17.4168 16.4302 15.0889 16.4302 15.0889H7.93504C7.93504 15.0889 9.3697 17.4168 11.5228 17.7472L11.5168 19.9276H11.4957C8.21122 19.9276 5.40646 16.0588 5.40646 13.3793V11.5764H9.80379L12.1826 13.9421L14.5609 11.5764H18.9587V13.3816ZM17.5838 10.7658C17.5648 9.72981 17.4063 8.70113 17.1122 7.70755L12.1826 12.7303L7.25515 7.70755C6.95888 8.7008 6.79772 9.72948 6.77602 10.7658L5.40646 10.7481C5.39452 9.41835 6.13899 6.36718 6.61106 5.06106L12.1826 10.8L17.7596 5.06106C18.2246 6.36957 18.9718 9.42074 18.9598 10.7517L17.5838 10.7658Z"
      fill="#2D69CB"
    />
  </svg>
);
const DefalutTitle: FC = () => <div className="mdt-login-title-default">{DEFAULT_LOGO}Login Center</div>;

const LoginTitle: FC<ILoginTitle> = ({ title: propsTilte }) => {
  const configTitle = useConfig(ConfigKeyEnum.TITLE, true) as any;
  const title = propsTilte || configTitle || <DefalutTitle />;
  // eslint-disable-next-line react/jsx-no-useless-fragment
  return <>{title ? <div className="mdt-login-title-container">{title}</div> : null}</>;
};

LoginTitle.displayName = 'LoginTitle';
export { LoginTitle };
