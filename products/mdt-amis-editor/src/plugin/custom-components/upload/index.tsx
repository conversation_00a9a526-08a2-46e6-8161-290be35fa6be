import { FC } from 'react';
import { FormItem } from 'amis-core';
import { type IProps as UploadProps, Upload } from '@mdtProMicroModules/components/upload';

interface IProps extends Omit<UploadProps, 'value' | 'onChange'> {
  value?: string;
  onChange?: (value: string) => void;
}
const JsonUpload: FC<IProps> = (props) => {
  const { value, onChange, ...rest } = props;
  return (
    <Upload
      {...rest}
      value={value ? JSON.parse(value) : undefined}
      onChange={(val) => {
        onChange?.(JSON.stringify(val));
      }}
    />
  );
};
FormItem({
  type: 'upload',
  // @ts-ignore
})(JsonUpload);
