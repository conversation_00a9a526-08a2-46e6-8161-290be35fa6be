import React from 'react';
import { createBehavior, createResource } from '@designable/core';
import { DnFC } from '@designable/react';
import i18n from '../../../languages';
import { Select as FormilySelect } from '../../../metro-formily/index';
import { withOtherOption } from '../../common/withOtherOption';
import { AllLocales } from '../../locales';
import { AllSchemas } from '../../schemas';
import { createFieldSchema } from '../Field/util';

export const Select: DnFC<React.ComponentProps<typeof FormilySelect>> = withOtherOption(FormilySelect);

Select.Behavior = createBehavior({
  name: 'Select',
  extends: ['Field'],
  selector: (node) => node.props?.['x-component'] === 'Select',
  designerProps: (node) => {
    if (node.props?.['x-component-props']?.['mode'] === 'multiple') {
      return {
        propsSchema: createFieldSchema(AllSchemas.Checkbox.Group),
      };
    }
    return {
      propsSchema: createFieldSchema(AllSchemas.Radio.Group),
    };
  },
  designerLocales: AllLocales.RadioGroup,
});

Select.Resource = createResource({
  icon: 'SelectSource',
  elements: [
    {
      componentName: 'Field',
      props: {
        type: 'string',
        'x-decorator': 'FormItem',
        'x-component': 'Select',
        'x-component-props': {
          placeholder: i18n.chain.comPlaceholder.select,
          showSearch: true,
          allowClear: true,
          optionFilterProp: 'label',
        },
      },
    },
  ],
});
