import { FC } from 'react';
import { TooltipText } from '@mdtDesign/tooltip';
import { AppSideMenuController } from './AppSideMenuController';

const AppSideMenuExtra: FC<{ controller: AppSideMenuController }> = ({ controller }) => {
  const { appName, appExpireTime } = controller.getAppInfo();

  return (
    <div className="org-manage-app-info">
      <div className="org-manage-app-info-icon">{appName.substr(0, 2)}</div>
      <div>
        <div className="org-manage-app-info-title">
          <TooltipText text={appName} className="org-manage-app-info-title" />
        </div>
        <div className="org-manage-app-info-expire">{appExpireTime}</div>
      </div>
    </div>
  );
};

export { AppSideMenuExtra };
