import { AUDIT_URL } from '../../config';
import {
  IApiResponse,
  IAuditImpersonateQuery,
  IAuditImpersonateResponse,
  IAuditQuery,
  IAuditResponse,
  IRequestPromise,
  IRequestRequestConfig,
} from '../../interfaces';
import request from '../../request';

export const queryAudit = (query?: IAuditQuery, config?: IRequestRequestConfig<IAuditQuery>) => {
  return request({
    ...(config || {}),
    method: 'get',
    url: `${AUDIT_URL}`,
    params: { ...config?.params, ...query },
  }) as IRequestPromise<IApiResponse<IAuditResponse[]>>;
};

export const queryAuditImpersonate = (
  query?: IAuditImpersonateQuery,
  config?: IRequestRequestConfig<IAuditImpersonateResponse>,
) => {
  return request({
    ...(config || {}),
    method: 'get',
    url: `${AUDIT_URL}/users/impersonate`,
    params: { ...config?.params, ...query },
  }) as IRequestPromise<IApiResponse<IAuditResponse[]>>;
};
