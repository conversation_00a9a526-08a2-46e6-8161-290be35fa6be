import _ from 'lodash';
import type { ElkExtendedEdge, ElkNode, ElkPort } from 'elkjs/lib/elk.bundled';
import { BuildBpmnUtil, getConnectedId, INode } from './BuildBpmnUtil';

interface IOptions {
  engineContext: any;
}

const TIMER_HEIGHT = 36;

export abstract class ParseBpmn<T> {
  protected engineContext: any;
  private processName = '';

  public constructor(opts: IOptions) {
    const et = opts.engineContext;
    this.engineContext = et;
    this.processName = 'Root';
  }

  public destroy() {
    this.engineContext = null;
  }

  public async getBpmnProcessSpecXml() {
    const et = this.engineContext;
    if (!et) return '';
    return this.parseEngineContext(et);
  }

  private async parseEngineContext(et: any) {
    const tasks: Record<string, T> = this.getNodes(et);
    const removeIds = ['Root', 'Start', 'End'];
    // 统计可视化节点，用于连线判断
    const existsMap: Record<string, boolean> = {};
    // 解析Start节点
    const startNodes = this.parseStartNode(tasks, removeIds, existsMap);
    // 解析End节点
    const endNodes = this.parseEndNode(tasks, removeIds, existsMap);
    // 解析Boundary节点
    const boundaryNodes = this.parseBoundaryNodes(tasks, removeIds, existsMap);
    // 解析剩余节点
    const leftNodes = this.parseLeftNodes(tasks, removeIds, existsMap);
    // 构建节点及边
    const nodeAndEdges = this.buildNodeAndEdges(existsMap, startNodes, endNodes, boundaryNodes, leftNodes);
    // 使用elkjs
    const config: ElkNode = await this.buildSpecLayoutByElk(nodeAndEdges.nodes, nodeAndEdges.edges);
    // const config: ElkNode = await this.buildSpecLayoutByDagre(nodeAndEdges.nodes, nodeAndEdges.edges);
    return this.buildXml(config, startNodes, endNodes, boundaryNodes, leftNodes);
  }

  // eslint-disable-next-line max-params
  private buildXml(
    config: ElkNode,
    startNodes: INode[],
    endNodes: INode[],
    boundaryNodes: INode[],
    leftNodes: INode[],
  ) {
    const nodeConfigMap: Record<string, ElkNode> = {};
    _.forEach(config.children, (node) => {
      nodeConfigMap[node.id] = node;
      _.forEach(node.ports, (port) => {
        nodeConfigMap[port.id] = port;
      });
    });
    const nodeModels: string[] = [];
    const edgeModels: string[] = [];
    const nodeShapes: string[] = [];
    const edgeShapes: string[] = [];
    _.forEach([...startNodes, ...endNodes, ...leftNodes, ...boundaryNodes], (node) => {
      nodeModels.push(BuildBpmnUtil.getBpmnModelNodeXml(node));
      nodeShapes.push(BuildBpmnUtil.getBpmnDiagramShapeXml(this.getNodeShapeParams(node, nodeConfigMap[node.id])));
    });
    _.forEach(boundaryNodes, (node) => {
      const boundaryConfig = this.getNodeShapeParams(node, nodeConfigMap[node.id]);
      _.forEach(node.childrenNodes, (childNode) => {
        nodeModels.push(BuildBpmnUtil.getBpmnModelBoundaryXml(childNode, node.id));
        // 计算boundary节点的子元素位置
        const cf = nodeConfigMap[childNode.id];
        const childConfig = this.getNodeShapeParams(childNode, cf);
        childConfig.height = TIMER_HEIGHT;
        childConfig.x = boundaryConfig.x + childConfig.x;
        childConfig.y = boundaryConfig.y + childConfig.y;
        // 下方的子元素需要向上移动半个高度
        if (_.get(cf, 'properties["port.side"]') !== 'NORTH') {
          childConfig.y -= TIMER_HEIGHT / 2;
        }
        nodeShapes.push(BuildBpmnUtil.getBpmnDiagramShapeXml(this.getNodeShapeParams(childNode, childConfig)));
      });
    });
    _.forEach(config.edges, (edge) => {
      edgeModels.push(BuildBpmnUtil.getBpmnModelEdgeXml(edge.id, edge.sources[0], edge.targets[0]));
      const sections = _.get(edge, 'sections[0]', { startPoint: { x: 0, y: 0 }, endPoint: { x: 0, y: 0 } });
      const points = [sections.startPoint, ...(sections.bendPoints || []), sections.endPoint];
      edgeShapes.push(BuildBpmnUtil.getBpmnDiagramEdgeXml({ id: edge.id, points }));
    });
    const modelXml = BuildBpmnUtil.getBpmnProcessModelXml(this.processName, nodeModels.join('') + edgeModels.join(''));
    const diagramXml = BuildBpmnUtil.getBpmnDiagramXml(this.processName, nodeShapes.join('') + edgeShapes.join(''));
    return BuildBpmnUtil.getBpmnProcessXml(modelXml, diagramXml);
  }

  private getNodeShapeParams(node: INode, cf: ElkNode) {
    return {
      id: node.id,
      x: cf.x || 0,
      y: cf.y || 0,
      width: cf.width || 0,
      height: cf.height || 0,
      label: node.label || '',
    };
  }

  private async buildSpecLayoutByElk(nodes: ElkNode[], edges: ElkExtendedEdge[]) {
    const ELK = await import('elkjs/lib/elk.bundled').then((resp) => resp.default);
    const elk = new ELK();
    // https://eclipse.dev/elk/reference/options.html 配置项
    const config = {
      layoutOptions: {
        separateConnectedComponents: 'false',
        algorithm: 'layered',
        fixedAlignment: 'RIGHTDOWN',
        // interactiveLayout: 'true',
        'spacing.edgeNodeBetweenLayers': '30',
        'spacing.edgeEdgeBetweenLayers': '30',
        'spacing.nodeNodeBetweenLayers': '60',
        'spacing.nodeNode': '50.0',
        'spacing.edgeNode': '50.0',
        'spacing.edgeEdge': '20.0',
      },
      id: this.processName,
      children: nodes,
      edges,
    };
    return elk.layout(config).catch(() => {
      console.log('elk layout error');
      return config;
    });
  }

  // eslint-disable-next-line max-params
  private buildNodeAndEdges(
    existsMap: Record<string, boolean>,
    startNodes: INode[],
    endNodes: INode[],
    boundaryNodes: INode[],
    leftNodes: INode[],
  ) {
    let edges: ElkExtendedEdge[] = [];
    const nodes: ElkNode[] = [];
    // Start节点
    _.forEach(startNodes, (node) => {
      nodes.push(this.getNodeConfig(node));
    });
    // End节点
    _.forEach(endNodes, (node) => {
      nodes.push(this.getNodeConfig(node));
      edges.push(...this.fillELKEgde(node.id, node.inputs, existsMap, 'End'));
    });
    // Boundary节点
    _.forEach(boundaryNodes, (node) => {
      nodes.push(this.getNodeConfig(node));
      edges.push(...this.fillELKEgde(node.id, node.inputs, existsMap, 'Boundary'));
    });
    // 剩余节点
    _.forEach(leftNodes, (node) => {
      nodes.push(this.getNodeConfig(node));
      edges.push(...this.fillELKEgde(node.id, node.inputs, existsMap, 'Left'));
    });
    // 排除自己连接自己的情况
    edges = edges.filter((it) => {
      return it.sources[0] !== it.targets[0];
    });
    return { nodes, edges };
  }

  private getNodeSize(typename: string) {
    if (_.includes(typename, 'Task') || _.includes(typename, 'CallActivity')) {
      return [100, 80];
    }
    return [36, 36];
  }

  private getNodeConfig(node: INode) {
    // boundary节点的子元素，模拟成端点
    const ports: ElkPort[] = _.map(node.children, (it, index) => ({
      id: it,
      width: 36,
      height: TIMER_HEIGHT / 2,
      properties: {
        // 位置按照上下上下排列
        'port.side': `${index % 2 === 0 ? 'NORTH' : 'SOUTH'}`,
        'port.index': `${index}`,
      },
    }));
    const [width, height] = this.getNodeSize(node.typename);
    const config: any = { id: node.id, width, height };
    // 事件节点，需要增加一个端点
    if (node.label) {
      config.labels = [{ text: node.label }];
    }
    if (ports.length) {
      config.ports = ports;
      config.properties = { portConstraints: 'FIXED_ORDER' };
    }
    return config;
  }

  private fillELKEgde(target: string, sources: string[], existsMap: Record<string, boolean>, tip: string) {
    const edges: ElkExtendedEdge[] = [];
    _.forEach(sources, (source) => {
      const id = getConnectedId(source, target);
      if (existsMap[source]) {
        edges.push({ id, sources: [source], targets: [target] });
      } else {
        console.log(`${tip} line not exist: ${id}`);
      }
    });
    return edges;
  }

  protected abstract getNodes(et: any): Record<string, T>;

  // 解析Start节点
  protected abstract parseStartNode(
    tasks: Record<string, T>,
    removeIds: string[],
    existsMap: Record<string, boolean>,
  ): INode[];

  // 解析End节点
  protected abstract parseEndNode(
    tasks: Record<string, T>,
    removeIds: string[],
    existsMap: Record<string, boolean>,
  ): INode[];

  // 解析End节点
  protected abstract parseBoundaryNodes(
    tasks: Record<string, T>,
    removeIds: string[],
    existsMap: Record<string, boolean>,
  ): INode[];

  // 解析剩余节点
  protected abstract parseLeftNodes(
    tasks: Record<string, T>,
    removeIds: string[],
    existsMap: Record<string, boolean>,
  ): INode[];
}
