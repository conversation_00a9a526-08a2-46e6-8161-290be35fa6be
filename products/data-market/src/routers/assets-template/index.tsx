import { map } from 'rxjs/operators';
import { useController } from '@mdtBsComm/hooks/use-controller';
import { TaskStatusEnum } from '@mdtProComm/constants';
import { defaultUploadFunc } from '@mdtProMicroModules/components/markdown-editor';
import { defaultLoadFunc } from '@mdtProMicroModules/components/markdown-preview';
import { TableMdTemplate, TableMdTemplateController } from '@mdtProMicroModules/pages/table-md-template';
import { addCreateTemplateFromUploadLocalFileTask } from '@mdtProTasks/util';
import { getModel } from '../../_util/modelUtil';
import { AppController } from '../../app/AppController';
import { TableMdTemplateMode } from './TableMdTemplateModel';
import { TableMdTemplateModeBff } from './TableMdTemplateModelBff';

const TableMdTemplateView = () => {
  const [controller] = useController(() => {
    const app = AppController.getInstance();
    const tmpModel = getModel(
      new TableMdTemplateMode(app, app.getUserPermissionController().getDocTemplatePermission()),
      new TableMdTemplateModeBff(app, app.getUserPermissionController().getDocTemplatePermission()),
    );
    const ctrl = new TableMdTemplateController({
      Model: tmpModel,
      app: app,
      uploadTask: (file: File) =>
        addCreateTemplateFromUploadLocalFileTask(file, defaultUploadFunc, defaultLoadFunc)
          .getCreateTemplateTaskResp$()
          .pipe(
            map((resp) => {
              if (TaskStatusEnum.SUCCESSFUL === resp.status) {
                resp.result = tmpModel.transformTableData(resp.result);
              }
              return resp;
            }),
          ),
      uploadFunc: defaultUploadFunc,
      loadFunc: defaultLoadFunc,
    });
    return [ctrl, null];
  });

  return <TableMdTemplate controller={controller} />;
};

export default TableMdTemplateView;
