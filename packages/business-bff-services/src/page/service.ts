import _ from 'lodash';
import { gql } from '@apollo/client';
import { ApolloService, bffResponseErrorType, bffResponsePaginationData } from '../ApolloService';
import { IResponsePaginationData } from '../interfaces';
import { IPageModel, IQueryPagesOptions } from './interface';

// page列表
const queryPages = (queryName: string, options: IQueryPagesOptions): Promise<IResponsePaginationData<IPageModel[]>> => {
  const { params, respData, cancelToken } = options;
  const query = gql(`
    query ${_.snakeCase(queryName)}($params: QueryPagesParams){
      ${queryName}(params: $params) {
        ${bffResponseErrorType}

        ... on ComPaginatedPageResp {
          ${bffResponsePaginationData} {
            ${respData}
          }
        }
      }
    }
  `);
  return ApolloService.query({
    context: { cancelToken },
    query,
    variables: {
      params,
    },
  });
};

// page列表首页
export const queryPageFirstPage = (
  productName: string,
  options: IQueryPagesOptions,
): Promise<IResponsePaginationData<IPageModel[]>> => {
  return queryPages(`${productName}PageFirstPage`, options);
};

// page列表下一页
export const queryPageNextPage = (
  productName: string,
  options: IQueryPagesOptions,
): Promise<IResponsePaginationData<IPageModel[]>> => {
  return queryPages(`${productName}PageNextPage`, options);
};
