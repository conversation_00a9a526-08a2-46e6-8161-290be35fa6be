/* stylelint-disable color-function-notation */
.datapkg-with-global-search {
  height: 100%;

  & &_search {
    position: absolute;
    top: 50px;
    right: 26px;
    z-index: 30;
    display: flex;
    flex-direction: column;
    width: 360px;
  }

  & &_search-tip {
    margin-top: 4px;
    padding: 14px 10px;
    color: var(--metro-text-1);
    word-break: break-word;
    background: var(--dmc-primary-panel-2);
    border: 1px solid var(--dmc-page-700-color);
    border-radius: 4px;
    box-shadow: 0 8px 22px -4px rgba(7, 19, 39, 6%), 0 5px 6px -4px rgba(7, 19, 39, 4%),
      0 1px 2px -4px rgba(7, 19, 39, 3%);
  }

  & &_enter-text {
    color: var(--dmc-blue-700-color);
  }

  & &_enter-val {
    color: var(--dmc-input-text-color);
  }
}
