import _ from 'lodash';
import { from } from 'rxjs';
import { map, takeWhile } from 'rxjs/operators';
import { IRequestCancelToken } from '@mdtApis/interfaces/request';
import { formateDateWithoutMillisecond } from '@mdtBsComm/utils/dayUtil';
import { batchQueryDatapkgsQualityReportPaginationAsync, getDatapkgsQualityStatsAsync } from '@mdtBsServices/datapkgs';
import { IDatapkgQualityReport, IDatapkgsQualityReportQuery } from '@mdtBsServices/interfaces';
import i18n from '../../languages';

const DATE_FORMATTER_YYYYDDMM = 'YYYY-MM-DD HH:mm';

export enum MonitorTitleEnum {
  ALL_DATA = 'allData',
  QUALIFIED = 'qualified',
  NO_QUALIFIED = 'unqualified',
  NOT_MONITORED = 'notMonitored',
}

const InitialBoxNum = {
  [MonitorTitleEnum.ALL_DATA]: 0,
  [MonitorTitleEnum.QUALIFIED]: 0,
  [MonitorTitleEnum.NO_QUALIFIED]: 0,
  [MonitorTitleEnum.NOT_MONITORED]: 0,
};

export interface IMenuInfo {
  key: string;
  title: string;
  itemNum: number;
}

export interface IQuality {
  datapkgId: string;
  datapkgTime: string;
  datapkgName: string;
  result: boolean | null;
  resultTime: string;
}

export enum QualityEnum {
  DATAPKG_ID = 'datapkgId',
  DATAPKG_TIME = 'datapkgTime',
  DATAPKG_NAME = 'datapkgName',
  RESULT = 'result',
  RESULT_TIME = 'resultTime',
}

export class TableQualityListModel {
  // 获取质量监控列表
  public static getQualityList(data: IDatapkgsQualityReportQuery, cancelToken?: IRequestCancelToken) {
    return from(batchQueryDatapkgsQualityReportPaginationAsync(data, { cancelToken })).pipe(
      takeWhile((resp) => !resp.canceled),
      map((resp) => {
        const { total_count, dataResult = [] } = resp.data!;
        return [total_count, this.transformQualityToFrontEnd(dataResult)] as [number, IQuality[]];
      }),
    );
  }

  public static getNextQualityList(data: IDatapkgsQualityReportQuery, params: any, cancelToken?: IRequestCancelToken) {
    return from(batchQueryDatapkgsQualityReportPaginationAsync(data, { cancelToken, params })).pipe(
      takeWhile((resp) => !resp.canceled),
      map((resp) => {
        const { dataResult = [] } = resp.data!;
        return this.transformQualityToFrontEnd(dataResult);
      }),
    );
  }

  public static queryMenuInfo(cancelToken?: IRequestCancelToken) {
    const initialBoxNum = _.cloneDeep(InitialBoxNum);
    return from(getDatapkgsQualityStatsAsync({ cancelToken })).pipe(
      takeWhile((resp) => !resp.canceled),
      map((resp) => {
        const data = resp.data || [];
        _.forEach(data, (it) => {
          // 合格
          if (it.auto_result && it.manual_result === null) {
            initialBoxNum[MonitorTitleEnum.QUALIFIED] += it.count;
            return;
          }
          // 不合格
          if (it.auto_result === false && it.manual_result !== false) {
            initialBoxNum[MonitorTitleEnum.NO_QUALIFIED] += it.count;
            return;
          }
          // 未监测
          if (it.auto_result === null && it.manual_result === null) {
            initialBoxNum[MonitorTitleEnum.NOT_MONITORED] += it.count;
          }
        });
        // 可使用数据包数量 = 合格 + 不合格 + 未监测
        initialBoxNum[MonitorTitleEnum.ALL_DATA] =
          initialBoxNum[MonitorTitleEnum.QUALIFIED] +
          initialBoxNum[MonitorTitleEnum.NO_QUALIFIED] +
          initialBoxNum[MonitorTitleEnum.NOT_MONITORED];
        return _.map(MonitorTitleEnum, (item) => ({
          key: item,
          title: i18n.chain.dataFactory.monitor[item],
          itemNum: initialBoxNum[item],
        }));
      }),
    );
  }

  private static transformQualityToFrontEnd(data: IDatapkgQualityReport[]): IQuality[] {
    return _.map(data, (item) => ({
      datapkgId: item.datapkg_id,
      datapkgTime: item.datapkg_time ? formateDateWithoutMillisecond(item.datapkg_time, DATE_FORMATTER_YYYYDDMM) : '--',
      datapkgName: item.name,
      result: item.auto_result,
      resultTime: item.auto_time ? formateDateWithoutMillisecond(item.auto_time, DATE_FORMATTER_YYYYDDMM) : '--',
    }));
  }
}

export type ITableQualityListModel = typeof TableQualityListModel;
