@mention-prefix-cls: ~'mdt-mentions';

.@{mention-prefix-cls} {
  position: relative;
  display: inline-block;
  box-sizing: border-box;
  width: 100%;
  height: auto;
  margin: 0;
  padding: 4px 10px;
  overflow: hidden;
  white-space: pre-wrap;
  vertical-align: bottom;
  border: 1px solid var(--dmc-border-btn-color);
  border-radius: 6px;

  &-focused {
    padding: 3px 9px;
    border-color: var(--dmc-input-border-focus-color);
    border-width: 2px;
  }

  > textarea,
  &-measure {
    margin: 0;
    padding: 3px 5px;
    overflow-x: hidden;
    overflow-y: auto;
    vertical-align: top;
    word-wrap: break-word;
  }

  > textarea {
    width: 100%;
    border: none;
    outline: none;
    resize: none;

    &::placeholder {
      color: var(--dmc-text-2);
    }
  }

  &-measure {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: -1;
    pointer-events: none;

    > span {
      display: inline-block;
      min-height: 1em;
    }
  }

  &-dropdown {
    position: absolute;
    top: -9999px;
    left: -9999px;
    z-index: 1000;
    box-sizing: border-box;
    height: auto;
    margin: 0;
    padding: 0;
    font-variant: initial;
    background-color: var(--dmc-primary-panel-2);
    border-radius: 3px;
    outline: none;

    &-hidden {
      display: none;
    }

    &-menu {
      max-height: 250px;
      margin-bottom: 0;
      padding-left: 0;
      overflow: auto;
      list-style: none;
      outline: none;

      &-item {
        position: relative;
        display: block;
        min-width: 100px;
        padding: 5px 10px;
        overflow: hidden;
        font-weight: normal;
        white-space: nowrap;
        text-overflow: ellipsis;
        cursor: pointer;
        transition: background 0.3s ease;

        &:hover {
          color: var(--dmc-select-text-hover-color);
          background: var(--dmc-select-item-hover-bg-color);
        }

        &-disabled {
          cursor: not-allowed;

          &:hover {
            cursor: not-allowed;
          }
        }

        &-selected {
          background-color: var(--dmc-page-900-color);
        }
      }
    }
  }
}
