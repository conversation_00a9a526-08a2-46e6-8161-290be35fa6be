import { from } from 'rxjs';
import { map, takeWhile } from 'rxjs/operators';
import { queryGrantedAppsAsync } from '@mdtBsServices/auth';
import { getDatapkgColumnsAsync, queryDatapkgsAsync } from '@mdtBsServices/datapkgs';
import { getDatasetAllSchemasAsync, getDatasetAllTablesBySchemaAsync } from '@mdtBsServices/index';
import { IDatapkgsAppQuery, IDatapkgsQuery, IRequestCancelToken } from '@mdtBsServices/interfaces';
import { IFlowParamsNodeColumnsResult } from './interfaces';

export class NodeSchemaModel {
  // 获取APP列表
  public static queryApps(cancelToken?: IRequestCancelToken) {
    return from(queryGrantedAppsAsync({ cancelToken })).pipe(
      takeWhile((resp) => !resp.canceled),
      map((resp) => resp.data ?? []),
    );
  }

  public static queryDatapkgs(data: IDatapkgsQuery, params?: IDatapkgsAppQuery, cancelToken?: IRequestCancelToken) {
    return from(queryDatapkgsAsync(data, { params, cancelToken })).pipe(
      takeWhile((resp) => !resp.canceled),
      map((resp) => resp.data ?? []),
    );
  }

  public static queryDatapkgColumns(pkgId: string, cancelToken?: IRequestCancelToken) {
    return from(getDatapkgColumnsAsync(pkgId, { cancelToken })).pipe(
      takeWhile((resp) => !resp.canceled),
      map((resp) => {
        const rslt: IFlowParamsNodeColumnsResult = {};
        (resp.data || []).forEach((it) => {
          rslt[it.id] = { id: it.id, name: it.name, type: it.type };
        });
        return rslt;
      }),
    );
  }

  public static getDatasetAllSchemas(datasetId: string, cancelToken?: IRequestCancelToken) {
    return from(getDatasetAllSchemasAsync(datasetId, { cancelToken })).pipe(
      takeWhile((resp) => !resp.canceled),
      map((resp) => resp.data ?? []),
    );
  }

  public static getDatasetAllTables(datasetId: string, schema: string, cancelToken?: IRequestCancelToken) {
    return from(getDatasetAllTablesBySchemaAsync(datasetId, schema, { cancelToken })).pipe(
      takeWhile((resp) => !resp.canceled),
      map((resp) => resp.data ?? []),
    );
  }
}

export type INodeSchemaModel = typeof NodeSchemaModel;
