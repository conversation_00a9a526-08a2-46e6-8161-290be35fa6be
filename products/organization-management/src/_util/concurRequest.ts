import _ from 'lodash';

// 默认请求并发数
const DEFAULT_MAX_NUM = 6;

/**
 * 并发请求，可以设置最大并发数
 * @param promiseParams
 * @param {number} maxNum
 * @returns
 */
const concurRequest = (
  { promiseArgs, promiseFunc }: { promiseArgs: any[]; promiseFunc: (...data: any) => Promise<any> },
  maxNum = DEFAULT_MAX_NUM,
) => {
  return new Promise((resolve) => {
    if (_.isEmpty(promiseArgs)) {
      resolve([]);
      return;
    }
    const results: any[] = [];
    let index = 0;
    let count = 0;
    async function request() {
      if (_.isEqual(index, promiseArgs.length)) {
        return;
      }
      const i = index;
      const data = promiseArgs[index];
      index++;
      try {
        const resp = await promiseFunc(...data);
        results[i] = resp;
      } catch (error) {
        results[i] = error;
      } finally {
        count++;
        if (_.isEqual(count, promiseArgs.length)) {
          resolve(results);
        }
        request();
      }
    }
    const times = Math.min(maxNum, promiseArgs.length);
    for (let i = 0; i < times; i++) {
      request();
    }
  });
};

export { concurRequest };
