import _ from 'lodash';
import { BehaviorSubject, forkJoin } from 'rxjs';
import { takeWhile } from 'rxjs/operators';
import { ApolloService } from '@mdtBsBffServices/ApolloService';
import { createDate, DATE_FORMATTER_1, formateDate } from '@mdtBsComm/utils/dayUtil';
import { saveCompressValueToUrl, urlToBase64Async } from '@mdtBsComm/utils/urlUtil';
import { IframeChannelController } from '@mdtBsControllers/iframe-channel-controller';
import { RequestController } from '@mdtBsControllers/request-controller';
import { queryAlbumsAsync } from '@mdtBsServices/albums';
import { authByTokenAsync, logoutAsync, queryGrantedAppsAsync, queryPermissionsAsync } from '@mdtBsServices/auth';
import { getAllDatasetsAsync } from '@mdtBsServices/datasets';
import { getFileUrlAsync } from '@mdtBsServices/files';
import { getPreferencesAsync } from '@mdtBsServices/preference';
import { IRequestOptions, RequestClass } from '@mdtBsServices/request';
import { ISocketOptions } from '@mdtBsServices/socket';
import { LanguageEnum } from '@mdtProComm/constants';
import { ApiNotifyController } from '@mdtProComm/controllers/ApiNotifyController';
import { BaseUserPermissionController } from '@mdtProComm/controllers/BaseUserPermissionController';
import { DatasetsController } from '@mdtProComm/controllers/DatasetsController';
import { EmitterController } from '@mdtProComm/controllers/EmitterController';
import { NameCacheController } from '@mdtProComm/controllers/NameCacheController';
import { PermissionController } from '@mdtProComm/controllers/PermissionController';
import { PreferencesController } from '@mdtProComm/controllers/PreferencesController';
import { SocketController } from '@mdtProComm/controllers/SocketController';
import {
  IJumpToOtherProductOptions,
  ILoginApp,
  ILoginUser,
  IPreviewGeometryData,
  IRequestError,
  IStoreToken,
} from '@mdtProComm/interfaces';
import { isInsideIframe } from '@mdtProComm/utils/commonUtil';
import { loadProjLocale } from '@mdtProComm/utils/localeUtil';
import { loadProjTheme } from '@mdtProComm/utils/themeUtil';
import {
  getDmCloudFlagFromUrl,
  getPlatformFromUrl,
  getThemeFromUrl,
  modifyParamsOfUrl,
} from '@mdtProComm/utils/urlUtil';
import { destroyTaskList } from '@mdtProTasks/single-globel';
import { unregister } from '../datlas/comm/serviceWorkerRegistration';
import i18n from '../languages';
import { WatermarkWrapController } from '../pages/water-mark-wrap';
import defaultLogo from '../resource/logo.png';
import { IFrameChannelTypeEnum } from '../shared/enums';
import { installPsdChangeTip } from '../utils/installPsdChangeTip';
import { type IOptions, installUserExpireTip } from '../utils/installUserExpireTip';
import {
  getCustomLoginFromStore,
  getDatlasFaviconFromStorage,
  removeCustomLoginFromStore,
  removeDatlasFaviconFromStorage,
  saveCustomLoginToStore,
  saveDatlasFaviconToStorage,
} from '../utils/storageUtil';
import { AlbumController } from './AlbumController';
import { AutoLogoutController } from './AutoLogoutController';
import { GrantedAppController } from './GrantedAppController';

const DEFAULT_THEME = 'light';
const DEFAULT_USER_ID = 0;
const DEFAULT_APP_ID = 0;
const DEFAULT_APP_NAME = '';
const DEFAULT_APP_EXPIRE_TIME = undefined;

interface IUserInfo {
  auth: string;
  userId: number;
  userName: string;
  nickname?: string;
  userPermission: Record<string, number[]>;
  appId: number;
  appName: string;
  appPermission: Record<string, number[]>;
  appExpireTime: string | null;
  wechatBinded: boolean;
  dingtalkBinded: boolean;
  phone: string;
  email: string;
  phoneConfirmed: boolean;
  emailConfirmed: boolean;
  uuid: string;
  wechatVcode: string;
  dingtalkVcode: string;
  expireTime: string;
  passwordUpdateTime?: string;
  mdtProduct: number[];
  roles?: number[];
  role?: string[];
  expired?: boolean;
}

interface IGetFileSourceOptions {
  storage?: {
    getFunc: any;
    setFunc: any;
    removeFunc: any;
  };
  defaultSource?: any;
  toBase64?: boolean;
  sendRedirectly?: boolean;
}

type FixedLengthArray<T, L extends number, R extends T[] = []> = R['length'] extends L
  ? R
  : FixedLengthArray<T, L, [T, ...R]>;
export type IRequirementData<L extends number = 5> = FixedLengthArray<any, L>;

abstract class AbstractAppController extends RequestController {
  // 认证相关
  private auth$ = new BehaviorSubject<boolean>(false);
  private visible$ = new BehaviorSubject<boolean>(false);
  private orginUser?: ILoginUser;
  private user?: IUserInfo;
  private storeToken?: string;
  private storeImpersonateToken?: string;
  // 产品模块名
  private moduleId?: string;
  // 请求相关
  private request?: RequestClass;
  // 请求错误通知
  private apiNotify?: ApiNotifyController;
  // socket
  private socketController?: SocketController;
  // 事件中心
  private emitterController?: EmitterController;
  // 用户活动监控
  private autoLogoutController?: AutoLogoutController;
  // 单个name缓存
  private nameCacheController?: NameCacheController;
  // 用户权限
  // 权限集合
  private permissionController?: PermissionController;
  // 偏好集合
  private preferencesController?: PreferencesController;
  // dataset集合
  private datasetsController?: DatasetsController;
  // 跨机构apps
  private grantedAppController?: GrantedAppController;
  // 主题库
  private albumController?: AlbumController;
  // 水印
  private watermarkWrapController?: WatermarkWrapController;
  // iframe失效通知
  private iframeChannelController?: IframeChannelController;
  // url传递的参数
  private urlQ: Record<string, any> = {};
  // 偏好相关
  private theme?: string;
  private language?: string;
  private chooseAppAuto?: boolean;
  private chooseIdentityAuto?: boolean;
  private chooseIdentitySwitchMainIdentity?: boolean;
  private bffUrl?: string;
  private modifyTitle?: boolean;
  private destroyed?: boolean;
  private datlasContext?: Record<string, any>;

  public constructor(bffUrl?: string) {
    super();
    const st = this.initLatestToken();
    this.storeToken = st.tk;
    this.storeImpersonateToken = st.itk;
    this.apiNotify = new ApiNotifyController({});
    this.request = new RequestClass(this.innerGetRequestOptions());
    this.bffUrl = bffUrl;
  }

  public destroy() {
    super.destroy();
    this.destroyed = true;
    this.resetAuthRelation();
    // 删除请求实例
    this.request?.destroy();
    this.request = undefined;
    // 删除通知管理
    this.apiNotify?.destroy();
    this.apiNotify = undefined;
    this.auth$.complete();
    this.visible$.complete();
    this.datlasContext = undefined;
    destroyTaskList();
  }

  public get isDestroyed() {
    return !!this.destroyed;
  }

  // 是否偏好修改了标题
  public getModifyTitleByPref() {
    return this.modifyTitle;
  }

  // 自动token认证
  public autoAuthByToken() {
    const tk = this.storeToken;
    const itk = this.storeImpersonateToken;
    tk ? this.authToken(tk, itk) : this.authFaith();
  }

  // 主动退出
  public loginOut() {
    // 退出前使token失效
    logoutAsync({ quiet: true });
    this.authFaith(true);
  }

  // 其他方式登录成功
  public login(user: ILoginUser, itk?: string) {
    this.orginUser = user;
    this.storeToken = user.auth;
    this.storeImpersonateToken = itk;
    this.initRequestToken(user.auth, itk);
    this.authSuccess(user);
  }

  // 请求必须数据
  public async initRequirementData(requirementData?: IRequirementData, initController = true) {
    // 不使用传递数据，自己获取数据
    const ensureGetData = !(_.isArray(requirementData) && requirementData.length === 5);
    // 微应用下，尝试避免多次获取数据
    const result = !ensureGetData ? requirementData : await this.getRequirementData();
    initController && (await this.initRequirementController(result));
    return result;
  }

  public async initRequirementController(requirementData: IRequirementData) {
    const prefC = this.getPreferencesController();
    const graC = this.getGrantedAppController();
    const albumC = this.getAlbumController();
    const dc = this.getDatasetsController();
    const permC = this.getPermissionController();

    const [pr, dr, pref, gra, albumR] = requirementData;
    await this.initPreferences(pref);
    pr.success && permC.initPermission(pr.data);
    gra.success && graC.initGrantedApp(gra.data, prefC);
    albumR.success && albumC.initAlbum(albumR.data, prefC);
    dr.success && dc.initDatasets(dr.data);
  }

  public getStoreToken(): IStoreToken {
    const tk = this.storeToken;
    const itk = this.storeImpersonateToken;
    const token: IStoreToken = { tk };
    itk && (token.itk = itk);
    return token;
  }

  public getSocketController() {
    return this.socketController!;
  }

  public getEmitterController() {
    return this.emitterController!;
  }

  public getNameCacheController() {
    return this.nameCacheController!;
  }

  public getPermissionController() {
    return this.permissionController!;
  }

  public getPreferencesController() {
    return this.preferencesController!;
  }

  public getWatermarkWrapController() {
    return this.watermarkWrapController;
  }

  public getIframeChannelController() {
    if (!this.iframeChannelController) {
      this.iframeChannelController = new IframeChannelController();
    }
    return this.iframeChannelController;
  }

  public getDatasetsController() {
    return this.datasetsController!;
  }

  public getGrantedAppController() {
    return this.grantedAppController!;
  }

  public getAlbumController() {
    return this.albumController!;
  }

  public getImpersonateToken() {
    return this.storeImpersonateToken;
  }

  public getAuth$() {
    return this.auth$;
  }

  public getUserToken() {
    return this.storeToken || '';
  }

  public getUser() {
    return this.user;
  }

  public getUserId() {
    return this.user?.userId || DEFAULT_USER_ID;
  }

  public getUserUuid() {
    return this.user?.uuid || '';
  }

  public getUserExpireTime() {
    return this.user?.expireTime || '';
  }

  public getPasswordUpdateTime() {
    return this.user?.passwordUpdateTime || '';
  }

  public getUserName() {
    return this.user?.userName || '';
  }

  public getUserNickName() {
    return this.user?.nickname || '';
  }

  public getUserRoles() {
    return this.user?.roles || [];
  }

  public updateUserRoles(roles: number[]) {
    if (this.user) {
      this.user.roles = roles;
    }
  }

  public getUserRole() {
    return this.user?.role || [];
  }

  public updateUserRole(role: string[]) {
    if (this.user) {
      this.user.role = role;
    }
  }

  public getUserPermission() {
    return this.user?.userPermission;
  }

  public getUserWechatBinded() {
    return this.user?.wechatBinded;
  }

  public getUserWechatVcode() {
    return this.user?.wechatVcode;
  }

  public getUserDingtalkVcode() {
    return this.user?.dingtalkVcode;
  }

  public getUserMdtProduct() {
    return this.user?.mdtProduct || [];
  }

  public updateUserWechatBind = (bind: boolean) => {
    if (this.user) {
      this.user.wechatBinded = bind;
    }
  };

  public getUserDingtalkBinded() {
    return this.user?.dingtalkBinded;
  }

  public updateUserDingtalkBind = (bind: boolean) => {
    if (this.user) {
      this.user.dingtalkBinded = bind;
    }
  };

  public getUserPhone() {
    return this.user?.phone;
  }

  public updateUserPhone(phone: string) {
    if (this.user) {
      this.user.phone = phone;
    }
  }

  public getUserEmail() {
    return this.user?.email;
  }

  public updateUserEmail(email: string) {
    if (this.user) {
      this.user.email = email;
    }
  }

  public getDatlasContext() {
    if (!this.datlasContext) {
      this.datlasContext = {
        // 当前用户相关
        currentUserId: this.getUserId(), // 当前用户ID
        currentUserName: this.getUserName(), // 当前用户名称
        currentToken: this.getUserToken(), // 当前token

        // 当前应用相关
        currentAppId: this.getAppId(), // 当前应用ID
        currentAppName: this.getAppName(), // 当前应用名称

        // 系统环境相关
        api: this.getRequestOptions().baseUrl,

        // 组织相关
        orgName: this.getUserOrgName(),
      };
    }
    return this.datlasContext;
  }

  public getUserPhoneConfirmed() {
    return this.user?.phoneConfirmed;
  }

  public updateUserPhoneConfirmed(phoneConfirmed: boolean) {
    if (this.user) {
      this.user.phoneConfirmed = phoneConfirmed;
    }
  }

  public getUserEmailConfirmed() {
    return this.user?.emailConfirmed;
  }

  public updateUserEmailConfirmed(emailConfirmed: boolean) {
    if (this.user) {
      this.user.emailConfirmed = emailConfirmed;
    }
  }

  public getTheme() {
    return this.theme!;
  }

  public changeTheme(theme: string) {
    loadProjTheme(theme);
    this.theme = theme;
    this.getPreferencesController().updateTheme(theme);
  }

  public isCnLanguage() {
    return this.language === LanguageEnum.CN;
  }

  public getLanguage() {
    return this.language!;
  }

  public changeLanguage(language: string) {
    this.language = language;
    this.getPreferencesController().updateLanguage(language);
  }

  public getChooseAppAuto() {
    return this.chooseAppAuto;
  }

  public changeChooseAppAuto(chooseAppAuto: boolean) {
    this.chooseAppAuto = chooseAppAuto;
    this.getPreferencesController().updateChooseAppAuto(chooseAppAuto);
  }

  public getChooseIdentityAuto() {
    return this.chooseIdentityAuto;
  }

  public changeChooseIdentityAuto(chooseIdentityAuto: boolean) {
    this.chooseIdentityAuto = chooseIdentityAuto;
    this.getPreferencesController().updateChooseIdentityAuto(chooseIdentityAuto);
  }

  public getChooseIdentitySwitchMainIdentity() {
    return this.chooseIdentitySwitchMainIdentity;
  }

  public changeChooseIdentitySwitchMainIdentity(chooseIdentitySwitchMainIdentity: boolean) {
    this.chooseIdentitySwitchMainIdentity = chooseIdentitySwitchMainIdentity;
    this.getPreferencesController().updateChooseIdentitySwitchMainIdentity(chooseIdentitySwitchMainIdentity);
  }

  public getAppId() {
    return this.user?.appId || DEFAULT_APP_ID;
  }

  public getAppName() {
    return this.user?.appName || DEFAULT_APP_NAME;
  }

  public getAppExpireTime() {
    return this.user?.appExpireTime || DEFAULT_APP_EXPIRE_TIME;
  }

  public getAppPermission() {
    return this.user?.appPermission;
  }

  public getAllApps() {
    return this.orginUser?.apps.all || [];
  }

  public getIdentities() {
    return this.orginUser?.identities || [];
  }

  public getUserOrgName() {
    const identity = _.find(this.getIdentities(), ({ id }) => _.eq(id, this.getUserUuid()));
    const name = identity?.nickname || identity?.name;
    return name?.split('|')?.[1];
  }

  public getUserOrgId() {
    const identity = _.find(this.getIdentities(), ({ id }) => _.eq(id, this.getUserUuid()));
    return identity?.entity_id;
  }

  public getSearchDatapkgAppIdsStr() {
    const granted = this.getGrantedAppController().getGrantedAppIds();
    return [this.getAppId(), ...granted].join(',');
  }

  public getVisible$() {
    return this.visible$;
  }

  /** 产品跳转相关(脚本注释) */
  public jumpToProductResourceShare(param?: Record<string, any>, options?: IJumpToOtherProductOptions) {
    this.handleEmptyFn(param, options);
  }

  public jumpToProductFormDesign(param?: Record<string, any>, options?: IJumpToOtherProductOptions) {
    this.handleEmptyFn(param, options);
  }

  public jumpToProductWorkflow(param?: Record<string, any>, options?: IJumpToOtherProductOptions) {
    this.handleEmptyFn(param, options);
  }

  public jumpToProductOneTable(param?: Record<string, any>, options?: IJumpToOtherProductOptions) {
    this.handleEmptyFn(param, options);
  }

  public jumpToProductMapEditor(param?: Record<string, any>, options?: IJumpToOtherProductOptions) {
    this.handleEmptyFn(param, options);
  }

  public jumpToProductDataMarket(param?: Record<string, any>, options?: IJumpToOtherProductOptions) {
    this.handleEmptyFn(param, options);
  }

  public jumpToProductDataFactory(param?: Record<string, any>, options?: IJumpToOtherProductOptions) {
    this.handleEmptyFn(param, options);
  }

  public jumpToProductDataMap(param?: Record<string, any>, options?: IJumpToOtherProductOptions) {
    this.handleEmptyFn(param, options);
  }

  public jumpToProductMyData(param?: Record<string, any>, options?: IJumpToOtherProductOptions) {
    this.handleEmptyFn(param, options);
  }

  public jumpToOrganizationManagement(param?: Record<string, any>, options?: IJumpToOtherProductOptions) {
    this.handleEmptyFn(param, options);
  }

  public jumpToProductCollector(param?: Record<string, any>, options?: IJumpToOtherProductOptions) {
    this.handleEmptyFn(param, options);
  }

  public jumpToDatlasAdmin(param?: Record<string, any>, options?: IJumpToOtherProductOptions) {
    this.handleEmptyFn(param, options);
  }

  public jumpToHome(param?: Record<string, any>) {
    this.handleEmptyFn(param);
  }

  public clickHeaderLogo(param?: Record<string, any>) {
    this.handleEmptyFn(param);
  }

  public appendRedirectParam(url: string) {
    const redirect = encodeURIComponent(window.location.href);
    return modifyParamsOfUrl(url, 'redirect', redirect);
  }

  public appendPlatformParam(url: string) {
    const platform = getPlatformFromUrl();
    return modifyParamsOfUrl(url, 'platform', platform);
  }

  public appendForceParam(url: string) {
    return modifyParamsOfUrl(url, 'force', true);
  }

  public appendCustomLoginParam = (url: string) => {
    const pfController = this.preferencesController;
    const storageCustomLogin = getCustomLoginFromStore();
    // 鉴权失败failback
    if (!pfController) {
      return storageCustomLogin ? modifyParamsOfUrl(url, 'config', saveCompressValueToUrl(storageCustomLogin)) : url;
    }
    return pfController.appendCustomLoginParam(url);
  };

  // 跳转到sso
  public redirectToSso(ssoUrl: string, logout?: boolean) {
    const pfController = this.preferencesController;
    const combine = this.processUrl(
      this.appendRedirectParam,
      this.appendPlatformParam,
      logout && this.appendForceParam,
      this.appendCustomLoginParam,
      pfController?.appendLanguageParam,
    );
    window.location.href = combine(ssoUrl);
  }
  // 登录一致性：切换app使原有界面的token失效, 并刷新sso的存储token
  public async refreshSsoToken(ssoUrl: string, appToken: string) {
    await logoutAsync({ quiet: true });
    const data = { freshtk: true, tk: appToken };

    const redirect = this.getBaseRedirectUrl();
    const url = modifyParamsOfUrl(ssoUrl, 'q', saveCompressValueToUrl(data), 'redirect', redirect);
    this.sendChangeAppToParent(url);
    window.location.replace(url);
  }

  // 获取地理数据预览地址
  public getPreviewGeoDataHref(data: IPreviewGeometryData, dataAppUrl?: string) {
    const language = this.language;
    if (!dataAppUrl) return '';
    const params = {
      ...data,
      q: saveCompressValueToUrl({ ...this.getStoreToken(), language }),
    };
    const url = new URL(`mapv/add/${data.packageUuid}`, dataAppUrl).toString();
    return modifyParamsOfUrl(url, params);
  }

  // 获取产品的url,同时附带token
  public getProductAuthUrl(productUrl: string, params?: Record<string, any>) {
    const qv = { ...(params || {}), ...this.getStoreToken() };
    return modifyParamsOfUrl(productUrl, 'q', saveCompressValueToUrl(qv));
  }

  // 跳转到其他产品
  public jumpToOtherProduct(productUrl: string, params?: Record<string, any>, options?: IJumpToOtherProductOptions) {
    const { replace, windowName, windowFeatures } = options || {};
    const url = this.getProductAuthUrl(productUrl, params);
    const target = windowName || productUrl;
    replace ? window.location.replace(url) : window.open(url, target, windowFeatures);
  }

  public clearApiDataCache() {
    if ('caches' in window) {
      caches.keys().then((cacheNames) => {
        cacheNames.forEach((cacheName) => {
          if (cacheName.includes('api-data-')) {
            caches.delete(cacheName);
          }
        });
      });
    }
  }

  public getWatermark(): string[] {
    const prefC = this.getPreferencesController();
    const showWatermark = prefC?.getDatlasEnableWatermark();
    if (!showWatermark) {
      return [];
    }
    return [this.getUserName(), formateDate(createDate(), DATE_FORMATTER_1)];
  }

  public updateHtmlInfo = async () => {
    const prefC = this.getPreferencesController();
    // 标题
    const title = prefC?.getDatlasWebTitle();
    if (title) {
      document.title = title;
      this.modifyTitle = true;
    }

    // 描述
    const description = prefC?.getDatlasWebDescription();
    let metaDescription = document.querySelector('meta[name="description"]');
    if (metaDescription && description) {
      metaDescription.setAttribute('content', description);
      metaDescription = null;
    }

    // 网页logo
    const favicon = await this.getFileSource(prefC?.getDatlasFavicon(), {
      storage: {
        getFunc: getDatlasFaviconFromStorage,
        setFunc: saveDatlasFaviconToStorage,
        removeFunc: removeDatlasFaviconFromStorage,
      },
      defaultSource: defaultLogo,
      toBase64: true,
    });

    let faviconLink: HTMLLinkElement | null = document.querySelector('link[rel*="icon"]');
    if (!faviconLink) {
      faviconLink = document.createElement('link');
      faviconLink.rel = 'icon';
      document.head.appendChild(faviconLink);
    }
    faviconLink.setAttribute('href', favicon);
    faviconLink = null;
  };

  public initWatermark = () => {
    this.watermarkWrapController = new WatermarkWrapController(this);
  };

  public registerPasswordChangeTip = async () => {
    await installPsdChangeTip(this);
  };

  public registerUserExipreTip = async (options?: IOptions) => {
    await installUserExpireTip(this, options);
  };

  public initSubscribeThemeChange = () => {
    this.getIframeChannelController().subscribe(IFrameChannelTypeEnum.CHANGE_THEME, this.changeTheme);
  };

  public handleServiceWorker = () => {
    const prefC = this.getPreferencesController();
    const disableServiceWorker = prefC.getDatlasDisableServiceWorker();
    if (disableServiceWorker) {
      unregister();
    }
  };

  public listenUserInactivity(): void {
    const inactivityTime = this.getPreferencesController()?.getDatlasInactivityLogoutInterval();
    const neverHandle = _.isEqual(inactivityTime, 0);
    if (getDmCloudFlagFromUrl() || isInsideIframe() || !inactivityTime || neverHandle) {
      return;
    }
    this.autoLogoutController = new AutoLogoutController(inactivityTime * 1000, this.onUserTimeout);
    this.autoLogoutController.start();
  }

  /**
   * storage 只接受 Record<string, any>类型
   * @param fileId string
   * @param options IGetFileSourceOptions
   * @returns
   */
  public getFileSource = async (fileId?: string, options?: IGetFileSourceOptions) => {
    const { storage, defaultSource, toBase64, sendRedirectly } = options || {};

    let isDifferent = false;

    const needStorage = !_.isEmpty(storage);
    if (!fileId) {
      storage?.removeFunc();
      return defaultSource;
    }

    if (needStorage) {
      const storageValue = storage!.getFunc();
      // 命中缓存
      if (_.isEqual(_.keys(storageValue)[0], fileId)) {
        return storageValue[fileId] || defaultSource;
      } else {
        isDifferent = true;
      }
    }

    // 没命中缓存 or 直接请求
    if (sendRedirectly || !needStorage || isDifferent) {
      const { data } = await getFileUrlAsync(fileId, { params: { redirect: false }, quiet: true });
      let finalValue = data?.sign_url.url || '';

      toBase64 && (finalValue = (await urlToBase64Async(finalValue)) as string);
      storage?.setFunc({ [fileId]: finalValue });
      return finalValue;
    }

    return defaultSource;
  };

  public initApolloClient() {
    this.bffUrl &&
      ApolloService.initClient({
        token: this.storeToken!,
        url: this.bffUrl,
        deal401: this.deal401,
      });
  }

  public getAxiosInstance() {
    return this.request!.getIns();
  }

  protected getRequest() {
    return this.request;
  }

  protected changeVisible(visible: boolean) {
    this.visible$.next(visible);
  }

  // 获取原始用户信息
  protected getOrginUser() {
    return this.orginUser;
  }

  // 跳转到后台管理
  protected jumpToDatlasAdminFunc(url: string, params?: any) {
    const { id, appid, token, itoken } = params;
    window.open(`${url}#/?id=${id}&appid=${appid}&token=${token}&itoken=${itoken}`, '_blank');
  }

  // token验证失败
  protected authFaith(logout?: boolean) {
    this.clearApiDataCache();
    this.sendLogoutToParent();
    this.resetAuthRelation();
    this.auth$.next(false);
    this.afterAuthFaith(logout);
  }

  protected initInnerRequireController() {
    this.initApolloClient();
    // 权限中心
    this.permissionController = new PermissionController();
    // 偏好中心
    this.preferencesController = new PreferencesController();
    // dataset集合
    this.datasetsController = new DatasetsController();
    this.grantedAppController = new GrantedAppController();
    this.albumController = new AlbumController();
  }

  protected initEventController() {
    // 全局事件中心
    this.emitterController = new EmitterController();
    // socket中心
    const sp = this.getSocketOptions(this.storeToken!);
    this.socketController = new SocketController(sp);
  }

  // token验证成功
  protected async authSuccess(loginUser: ILoginUser) {
    this.setLoginUser(loginUser);
    const user = this.user!;
    // 名称缓存
    this.nameCacheController = new NameCacheController(user.appId);
    // 追加登录用户及app对应的名称
    this.nameCacheController.addAppNameToCache(user.userId, user.userName);
    this.nameCacheController.addUserNameToCache(user.appId, user.appName);
    this.initInnerRequireController();
    this.initEventController();
    // 应该在这里保存token及获取必要数据
    await this.afterAuthSuccess();
    // 设置认证成功的标志
    this.auth$.next(true);
    // 准备工作完成后，开始显示页面
    this.visible$.next(true);
  }

  protected setLoginUser(loginUser: ILoginUser) {
    this.orginUser = loginUser;
    const user = this.transformLoginUser(loginUser);
    this.user = user;
  }

  protected initRequestToken = (tk: string, itk?: string) => {
    // 设置请求token
    this.request!.updateRequestToken(tk, itk);
  };

  // 初始化语言配置，实际使用时需要在外部复写
  protected initLanguageConfig(prefLanguage: string) {
    return prefLanguage;
  }

  private onUserTimeout = () => {
    const inactivityTime = this.getPreferencesController()?.getDatlasInactivityLogoutInterval();
    alert(i18n.chain.proMicroModules.userInactivityLogout(Math.ceil(inactivityTime / 60)));
    this.loginOut();
  };

  // 格式转换
  private transformLoginUser(user: ILoginUser): IUserInfo {
    const appId = user.customer.app_id;
    const currentApp = _.find(user.apps.all, ({ id }) => id === appId) as ILoginApp;
    return {
      auth: user.auth,
      userId: user.customer.id,
      userName: user.customer.name,
      nickname: user.customer.nickname,
      userPermission: user.customer.permission,
      appId: appId,
      appName: currentApp.name,
      appPermission: currentApp.permission,
      appExpireTime: currentApp.expire_time,
      wechatBinded: user.customer.wechatbinded,
      dingtalkBinded: user.customer.dingtalkbinded,
      phone: user.customer.phone,
      email: user.customer.email,
      phoneConfirmed: user.customer.phone_confirmed,
      emailConfirmed: user.customer.email_confirmed,
      uuid: user.customer.uuid,
      wechatVcode: user.customer.verify_code,
      dingtalkVcode: user.customer.dtverify_code,
      expireTime: user.customer.expire_time,
      mdtProduct: user.mdt_product,
      roles: user.customer.roles,
      role: user.customer.role,
      expired: user.customer.expired,
      passwordUpdateTime: user.customer.password_update_time,
    };
  }

  // 验证已存在的token
  private async authToken(tk: string, itk?: string) {
    this.initRequestToken(tk, itk);
    const resp = await authByTokenAsync({ quiet: true });
    resp.success ? this.authSuccess(resp.data!) : this.authFaith();
  }

  // 重置
  private resetAuthRelation() {
    // 去除token
    // this.request?.removeRequestToken();
    // 删除事件中心
    this.emitterController?.destroy();
    this.emitterController = undefined;
    // 删除socket
    this.socketController?.destroy();
    this.socketController = undefined;
    // 删除权限
    this.permissionController?.destroy();
    this.permissionController = undefined;
    // 删除名称缓存
    this.nameCacheController?.destroy();
    this.nameCacheController = undefined;
    // 偏好
    this.preferencesController?.destroy();
    this.preferencesController = undefined;
    // 删除dataset
    this.datasetsController?.destroy();
    this.datasetsController = undefined;
    // 删除跨机构apps
    this.grantedAppController?.destroy();
    this.grantedAppController = undefined;
    //  删除主题库
    this.albumController?.destroy();
    this.albumController = undefined;
    // 删除水印
    this.watermarkWrapController?.destroy();
    this.watermarkWrapController = undefined;
    // 删除iframe失效通知
    this.iframeChannelController?.destroy();
    this.iframeChannelController = undefined;
    // 自动退出
    this.autoLogoutController?.detroy();
    this.autoLogoutController = undefined;
    ApolloService.destroy();
    // 删除临时存储
    this.storeImpersonateToken = '';
    this.storeToken = '';
    this.user = undefined;
    this.orginUser = undefined;
  }

  // 通知的提示
  private notifyErrorMsg = (msg: string, error: IRequestError) => {
    this.apiNotify!.addNotify(msg, error);
  };

  // 处理401的状况
  private deal401 = () => {
    this.authFaith();
  };

  // 设置默认值
  private innerGetRequestOptions(): IRequestOptions {
    const opts = this.getRequestOptions() || {};
    return { deal401: this.deal401, notifyErrorMsg: this.notifyErrorMsg, ...opts };
  }

  // 处理可能没有被外部执行的空函数
  private handleEmptyFn(...params: any) {
    console.log(`params=${!!params}`);
  }

  private async initOther() {
    // preferences
    // 权限国际化需要依赖i18n加载完成
    await this.initLanguage();
    this.initTheme();
    this.initChooseAppAuto();
    this.initChooseIdentityAuto();
    this.updateCustomLoginKey();
  }

  private async getRequirementData() {
    const cancelToken = this.getCancelToken();
    return await forkJoin(
      queryPermissionsAsync({ cancelToken }),
      getAllDatasetsAsync({ cancelToken }),
      getPreferencesAsync(undefined, { cancelToken }),
      queryGrantedAppsAsync({ cancelToken }),
      queryAlbumsAsync({ cancelToken, params: { apps: this.getAppId() } }),
    )
      .pipe(takeWhile((resp) => !_.some(resp, 'canceled')))
      .toPromise();
  }

  // 存在的意义是要保证获取偏好的同时，多语言也加载完成
  private async initPreferences(resp: any) {
    if (resp.success) {
      const controller = this.getPreferencesController();
      controller.initPreferences(resp.data, this.getUserId());
      await this.initOther();
    }
  }

  private async initLanguage() {
    const prefC = this.getPreferencesController();
    this.language = this.initLanguageConfig(prefC.getLanguage());
    loadProjLocale(this.language as LanguageEnum);
    await i18n.locale(this.language);
  }

  private initTheme() {
    const prefC = this.getPreferencesController();
    const prefTheme = prefC.getTheme();
    const urlTheme = getThemeFromUrl();
    // url的参数 > 用户偏好 > 配置文件 > 默认值
    this.theme = urlTheme || prefTheme || DEFAULT_THEME;
    loadProjTheme(this.theme);
  }

  private initChooseAppAuto() {
    const prefC = this.getPreferencesController();
    const chooseAppAuto = prefC.getChooseAppAuto();
    this.chooseAppAuto = chooseAppAuto;
  }

  private initChooseIdentityAuto() {
    const prefC = this.getPreferencesController();
    const chooseIdentityAuto = prefC.getChooseIdentityAuto();
    this.chooseIdentityAuto = chooseIdentityAuto;
  }

  // 更新自定义登录偏好
  private updateCustomLoginKey() {
    const prefC = this.getPreferencesController();
    const prefCustomLoginKey = prefC.getDefaultCustomLoginTemplate()?.config;
    prefCustomLoginKey ? saveCustomLoginToStore(prefCustomLoginKey) : removeCustomLoginFromStore();
  }

  private processUrl = (...fns: any[]) => {
    return function (originUrl: string) {
      return _.reduce(
        _.compact(fns),
        (url, fn) => {
          return fn?.(url);
        },
        originUrl,
      );
    };
  };

  private getBaseRedirectUrl(encode = true) {
    const urlObj = new URL(window.location.href);
    const pathSegments = _.filter(urlObj.pathname.split('/'), Boolean);
    const basePathname = !_.isEmpty(pathSegments) ? `/${pathSegments[0]}` : '';
    // 构建基地址，包括一级路由
    const baseUrl = `${urlObj.protocol}//${urlObj.host}${basePathname}`;
    return encode ? encodeURIComponent(baseUrl) : baseUrl;
  }

  private sendLogoutToParent() {
    const isFrame = window !== window.top;
    if (isFrame) {
      IframeChannelController.sendMessageToParent(IFrameChannelTypeEnum.LOGOUT, true);
    }
  }

  private sendChangeAppToParent(url: string) {
    const isFrame = window !== window.top;
    if (isFrame) {
      IframeChannelController.sendMessageToParent(IFrameChannelTypeEnum.LOGOUT, { url });
    }
  }

  public abstract getUserPermissionController(): BaseUserPermissionController;

  // 认证成功后
  protected abstract afterAuthSuccess(): void;
  // 认证失败后
  protected abstract afterAuthFaith(logout?: boolean): void;
  // 获取socket的配置
  protected abstract getSocketOptions(auth: string): ISocketOptions;
  // 初始化token
  protected abstract initLatestToken(): IStoreToken;
  // 获取request的配置
  protected abstract getRequestOptions(): IRequestOptions;
}

export { AbstractAppController };
