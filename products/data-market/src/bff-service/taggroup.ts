import {
  BaseBffService,
  createTaggroup,
  deleteTag<PERSON><PERSON>pi,
  ICreateTaggroupOptions,
  IDeleteTaggroupOptions,
  IQueryTaggroupListOptions,
  IUpdateTaggroupOptions,
  queryTaggroupList,
  updateTaggroupApi,
} from '@mdtBsBffServices/services';

export class DMTaggroupBffService extends BaseBffService {
  public queryTaggroupList(options: IQueryTaggroupListOptions) {
    return queryTaggroupList(this.productName, options);
  }

  public createTaggroup(options: ICreateTaggroupOptions) {
    return createTaggroup(this.productName, options);
  }

  public updateTaggroupApi(options: IUpdateTaggroupOptions) {
    return updateTaggroupApi(this.productName, options);
  }

  public deleteTaggroupApi(options: IDeleteTaggroupOptions) {
    return deleteTaggroupApi(this.productName, options);
  }
}
