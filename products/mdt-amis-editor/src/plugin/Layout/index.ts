// 常见布局
import Layout_fixed from './Components/Layout_fixed';
import Layout_fixed_bottom from './Components/Layout_fixed_bottom';
import Layout_fixed_top from './Components/Layout_fixed_top';
import Layout_scroll_x from './Components/Layout_scroll_x';
import Layout_scroll_y from './Components/Layout_scroll_y';
import Layout1_1 from './Components/Layout1_1';
import Layout1_1_1 from './Components/Layout1_1_1';
import Layout1_1_1_v2 from './Components/Layout1_1_1_v2';
import Layout1_1_v2 from './Components/Layout1_1_v2';
import Layout1_2 from './Components/Layout1_2';
import Layout1_2_3 from './Components/Layout1_2_3';
import Layout1_2_v2 from './Components/Layout1_2_v2';
import Layout1_2_v3 from './Components/Layout1_2_v3';
import Layout1_2_v4 from './Components/Layout1_2_v4';
import Layout1_3 from './Components/Layout1_3';
import Layout2_1_v2 from './Components/Layout2_1_v2';
import Layout2_1_v3 from './Components/Layout2_1_v3';

const LayoutList: Array<any> = [
  Layout1_1,
  Layout1_2,
  Layout1_3,
  Layout1_1_v2,
  Layout1_1_1,
  Layout1_2_3,
  Layout1_1_1_v2,
  Layout1_2_v2,
  Layout2_1_v2,
  Layout1_2_v3,
  Layout2_1_v3,
  Layout1_2_v4,
  Layout_fixed_top,
  Layout_fixed_bottom,
  Layout_fixed,
  Layout_scroll_x,
  Layout_scroll_y,
];

export default LayoutList;
