import _ from 'lodash';
import { combineLatest, from } from 'rxjs';
import { map, takeWhile } from 'rxjs/operators';
import { IRequestCancelToken } from '@mdtApis/interfaces/request';
import { formateDateWithoutMillisecond } from '@mdtBsComm/utils/dayUtil';
import { queryResourceAsync } from '@mdtBsServices/auth';
import {
  deleteBatchFlowAsync,
  deleteFlowAsync,
  getFlowAllSupportNodesAsync,
  queryFlowsPaginationAsync,
  queryFlowsRunsAsync,
  runFlowOnceAsync,
} from '@mdtBsServices/flows';
import {
  IFlow,
  IFlowRevision,
  IFlowRun,
  IFlowRunPost,
  IFlowRunStatus,
  IFlowsQuery,
  IFlowsRunsQuery,
  IServerResponse,
} from '@mdtBsServices/interfaces';
import { CommonModel } from '@mdtProComm/models/CommonModel';
import { ITableTimedTaskModel } from '@mdtProMicroModules/containers/table-timed-task';
import { type IFolders, FolderModel } from '@mdtProMicroModules/models/FolderModel';
import { AppController } from '../../app/AppController';
import i18n from '../../languages';
import { IFlowEditModel } from '../flow-edit/FlowEditModel';
import { FlowEditModel } from './FlowEditModel';
import { FlowViewTableTimedTaskModel } from './TableTimedTaskModel';

const DATE_FORMATTER_YYYYDDMM = 'YYYY-MM-DD HH:mm';

export interface IFlowList {
  key: string;
  taskName: string;
  user: string;
  updateTime: string;
  result: string[];
  revision: number;
  flow: IFlow;
  hasRead?: boolean;
  hasUpdate?: boolean;
  hasExecute?: boolean;
  hasDelete?: boolean;
}

export enum FlowListEnum {
  KEY = 'key',
  TASK_NAME = 'taskName',
  USER = 'user',
  UPDATE_TIME = 'updateTime',
  RESULT = 'result',
  // 删除使用
  REVISION = 'revision',
  // 下载使用
  FLOW = 'flow',
}

export interface IFlowRunList {
  key: string;
  startTime: string;
  endTime: string;
  consumTime: string;
  result: string[];
}

export enum FlowRunListEnum {
  KEY = 'key',
  START_TIME = 'startTime',
  END_TIME = 'endTime',
  CONSUM_TIME = 'consumTime',
  RESULT = 'result',
}

interface IRespData {
  total: number;
  list: IFlow[];
  userIdNameMap: Record<number, string>;
  flowIdResultMap: Record<string, IFlowRun>;
  flowIdPermissionMap: Record<string, string[]>;
}

const DEFAULT_PERMISSIONS = {
  hasRead: true,
  hasUpdate: true,
  hasExecute: true,
  hasDelete: true,
};

const PermissionKeyMap: Record<string, string> = {
  read: 'hasRead',
  update: 'hasUpdate',
  execute: 'hasExecute',
  delete: 'hasDelete',
};

export class FlowHomeModel extends FolderModel {
  // 获取flow列表
  public static getFlowFolderList(params: IFlowsQuery, folderParams: any, cancelToken?: IRequestCancelToken) {
    const flowParams = folderParams?.path ? { ...params, folder: folderParams.path } : { ...params, nofolder: true };
    return combineLatest([this.getFlowData(flowParams, cancelToken), this.getFolder(folderParams, cancelToken)]).pipe(
      takeWhile(([resp, resp2]) => !resp.canceled || !resp2.canceled),
      map(([resp, folderResp]) => {
        // 接口报错处理
        if (!resp.data) return [0, []] as [number, IFlowList[]];
        const { total = 0, list = [], userIdNameMap = {}, flowIdResultMap = {}, flowIdPermissionMap = {} } = resp.data;
        const folderResult = _.map(folderResp.data, this.transformFolderToTableData);
        return [
          total,
          [...folderResult, ...this.transformFlowToFrontEnd(list, userIdNameMap, flowIdResultMap, flowIdPermissionMap)],
        ] as [number, IFlowList[]];
      }),
    );
  }

  public static getFlowList(params: IFlowsQuery, cancelToken?: IRequestCancelToken) {
    return from(this.getFlowData(params, cancelToken)).pipe(
      takeWhile((resp) => !resp.canceled),
      map((resp) => {
        // 接口报错处理
        if (!resp.data) return [0, []] as [number, IFlowList[]];
        const { total = 0, list = [], userIdNameMap = {}, flowIdResultMap = {}, flowIdPermissionMap = {} } = resp.data;
        return [total, this.transformFlowToFrontEnd(list, userIdNameMap, flowIdResultMap, flowIdPermissionMap)] as [
          number,
          IFlowList[],
        ];
      }),
    );
  }

  // 获取下一页flow列表
  public static getNextFlowList(params: IFlowsQuery, cancelToken?: IRequestCancelToken) {
    return from(this.getFlowData(params, cancelToken)).pipe(
      takeWhile((resp) => !resp.canceled),
      map((resp) =>
        this.transformFlowToFrontEnd(
          resp.data?.list || [],
          resp.data?.userIdNameMap ?? {},
          resp.data?.flowIdResultMap ?? {},
          resp.data?.flowIdPermissionMap ?? {},
        ),
      ),
    );
  }

  // 获取节点列表
  public static getNodeList(cancelToken?: IRequestCancelToken) {
    return from(getFlowAllSupportNodesAsync({ cancelToken })).pipe(
      takeWhile((resp) => !resp.canceled),
      map((resp) => resp.data || []),
    );
  }

  // 获取flow run列表
  public static getFlowRunList(params: IFlowsRunsQuery, cancelToken?: IRequestCancelToken) {
    return from(queryFlowsRunsAsync({ cancelToken, params })).pipe(
      takeWhile((resp) => !resp.canceled),
      map((resp) => [0, this.transformFlowRunToFrontEnd(resp.data || [])] as [number, IFlowRunList[]]),
    );
  }

  public static runFlow(flowId: string, data: IFlowRunPost, cancelToken?: IRequestCancelToken) {
    return from(runFlowOnceAsync(flowId, data, { cancelToken })).pipe(
      takeWhile((resp) => !resp.canceled),
      map((resp) => resp.data!),
    );
  }

  public static deleteFlow(flowId: string, params: IFlowRevision, cancelToken?: IRequestCancelToken) {
    return from(deleteFlowAsync(flowId, { cancelToken, params })).pipe(
      takeWhile((resp) => !resp.canceled),
      map((resp) => !!resp.data),
    );
  }

  public static deleteBatchFlow(ids: string, cancelToken?: IRequestCancelToken) {
    return from(deleteBatchFlowAsync(ids, { cancelToken })).pipe(takeWhile((resp) => !resp.canceled));
  }

  public static getTableTimedTaskModel(flowId: string): ITableTimedTaskModel {
    return new FlowViewTableTimedTaskModel(flowId);
  }

  public static getFlowEditModel(): IFlowEditModel {
    return new FlowEditModel();
  }

  public static transformFolderToTableData(item: IFolders): IFlowList {
    return {
      taskName: item.currentPath,
      user: '',
      key: '',
      updateTime: item.createTimeDisplay,
      result: ['--', ''],
      revision: NaN,
      flow: {
        id: '',
        revision: NaN,
        create_time: NaN,
        update_time: NaN,
        user_id: NaN,
        flow_type: 'dataflow',
      },
      ...item,
    };
  }

  private static transformFlowToFrontEnd(
    data: IFlow[],
    userIdNameMap: Record<number, string>,
    flowIdResultMap: IRespData['flowIdResultMap'],
    flowIdPermissionMap: Record<string, string[]>,
  ): IFlowList[] {
    const userId = AppController.getInstance().getUserId();
    return _.map(data, (item) => {
      let permissions: Record<string, boolean> = {};
      if (userId === item.user_id) {
        permissions = DEFAULT_PERMISSIONS;
      } else {
        _.forEach(flowIdPermissionMap[item.id], (p) => {
          const key = PermissionKeyMap[p];
          key && (permissions[key] = true);
        });
      }
      const flowRunItem: IFlowRun = flowIdResultMap[item.id] || {};
      return {
        key: item.id,
        taskName: item.title || '',
        user: userIdNameMap[item.user_id] ?? item.user_id.toString(),
        updateTime: flowRunItem.update_time
          ? formateDateWithoutMillisecond(flowRunItem.update_time, DATE_FORMATTER_YYYYDDMM)
          : '--',
        result: this.getFlowRunStatus(flowRunItem.status),
        revision: item.revision,
        flow: item,
        ...permissions,
      };
    });
  }

  private static transformFlowRunToFrontEnd(data: IFlowRun[]): IFlowRunList[] {
    return _.map(data, (item) => ({
      key: item.id,
      startTime: item.create_time ? formateDateWithoutMillisecond(item.create_time, DATE_FORMATTER_YYYYDDMM) : '--',
      endTime: item.update_time ? formateDateWithoutMillisecond(item.update_time, DATE_FORMATTER_YYYYDDMM) : '--',
      consumTime: (item.update_time - item.create_time).toString() + 's',
      // red-700
      result: this.getFlowRunStatus(item.status),
    }));
  }

  // 请求用户名称
  private static async getUserIdNameMap(list: IFlow[], cancelToken?: IRequestCancelToken) {
    return CommonModel.getUserIdNameMap(_.map(list, 'user_id'), cancelToken);
  }

  // 请求flow权限
  private static async getFlowIdPermissionMap(list: IFlow[], cancelToken?: IRequestCancelToken) {
    const result: Record<string, string[]> = {};
    if (_.isEmpty(list)) return result;

    const resp = await queryResourceAsync({
      params: { resource_ids: _.join(_.map(list, 'id'), ',') },
      cancelToken,
    });
    return _.reduce(
      resp.data,
      (prev, curr) => {
        prev[curr.resource_id] = curr.privs;
        return prev;
      },
      result,
    );
  }

  private static getFlowData(params: IFlowsQuery, cancelToken?: IRequestCancelToken) {
    return new Promise<IServerResponse<IRespData>>((resolve) => {
      (async () => {
        const resp = await queryFlowsPaginationAsync({ cancelToken, params: { share: 2, ...(params || {}) } });
        // 如果成功，则在查询用户的名称
        let list: IFlow[] = [];
        let total = 0;
        let userIdNameMap: Record<number, string> = {};
        let flowIdResultMap: Record<string, IFlowRun> = {};
        let flowIdPermissionMap: Record<string, string[]> = {};
        if (resp.success) {
          list = resp.data!.dataResult;
          total = resp.data!.total_count;
          userIdNameMap = await this.getUserIdNameMap(list);
          flowIdResultMap = await this.getFlowIdResultMap(list);
          flowIdPermissionMap = await this.getFlowIdPermissionMap(list);
        }
        resolve({
          success: resp.success,
          canceled: resp.canceled,
          data: { total, list, userIdNameMap, flowIdResultMap, flowIdPermissionMap },
        });
      })();
    });
  }

  private static async getFlowIdResultMap(list: IFlow[], cancelToken?: IRequestCancelToken) {
    const flowIds = _.map(list, ({ id }) => id);
    const params = { flow_ids: _.join(flowIds, ','), latest_record: true };

    const rMap: Record<string, IFlowRun> = {};
    if (flowIds.length) {
      const resp = await queryFlowsRunsAsync({ cancelToken, params });
      _.forEach(resp.data, (it) => {
        rMap[it.flow_id] = it;
      });
    }
    return rMap;
  }

  private static getFlowRunStatus(status: IFlowRunStatus) {
    switch (status) {
      case 'successful':
        return [i18n.chain.dataFactory.etl.success, 'green-700'];
      case 'failed':
        return [i18n.chain.dataFactory.etl.failed, 'red-700'];
      case 'running':
        return [i18n.chain.dataFactory.etl.running, 'blue-700'];
      default:
        return ['--'];
    }
  }
}

export type IFlowHomeModel = typeof FlowHomeModel;
