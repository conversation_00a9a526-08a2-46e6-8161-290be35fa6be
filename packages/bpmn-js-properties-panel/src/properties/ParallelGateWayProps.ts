import {
  CheckboxEntry,
  isCheckboxEntryEdited,
  isSelectEntryEdited,
  isTextFieldEntryEdited,
  SelectEntry,
  TextFieldEntry,
} from '@bpmn-io/properties-panel';
import { useService } from 'bpmn-js-properties-panel';
import { getRelevantBusinessObject, isBpmnParallelGateWay } from '../utils/ElementUtil';
import { getExtensionElement, replaceExtensionElements } from '../utils/ExtensionElementsUtil';

export function ParallelGateWayProps(props: any) {
  const { element } = props;

  if (!isBpmnParallelGateWay(element)) {
    return [];
  }

  return [
    { id: 'completeThreshold', component: CompleteThreshold, isEdited: isTextFieldEntryEdited },
    { id: 'parallelGatewayType', component: ParallelGatewayType, isEdited: isSelectEntryEdited },
    { id: 'terminateIfRejected', component: TerminateIfRejected, isEdited: isCheckboxEntryEdited },
  ];
}

function CompleteThreshold(props: any) {
  const { element, id } = props;
  const bpmnFactory = useService('bpmnFactory');
  const commandStack = useService('commandStack');
  const translate = useService('translate');
  const debounce = useService('debounceInput');
  const businessObject = getRelevantBusinessObject(element);
  const componentKey = 'mdt:CompleteThreshold';

  const getValue = () => {
    const extensionElement = getExtensionElement(businessObject, componentKey);
    return extensionElement?.get('body');
  };

  const setValue = (value: string) => {
    replaceExtensionElements(element, businessObject, bpmnFactory, commandStack, componentKey, { body: value });
  };

  return TextFieldEntry({ element, id, label: translate('Complete Threshold'), getValue, setValue, debounce });
}

function ParallelGatewayType(props: any) {
  const { element, id } = props;
  const bpmnFactory = useService('bpmnFactory');
  const commandStack = useService('commandStack');
  const translate = useService('translate');
  const businessObject = getRelevantBusinessObject(element);
  const componentKey = 'mdt:ParallelGatewayType';

  const getValue = () => {
    const extensionElement = getExtensionElement(businessObject, componentKey);
    let value = extensionElement?.get('body');
    if (value !== 'normal' && value !== 'approval') {
      value = 'normal';
      requestAnimationFrame(() => setValue(value));
    }
    return value;
  };

  const setValue = (value: string) => {
    replaceExtensionElements(element, businessObject, bpmnFactory, commandStack, componentKey, { body: value });
  };

  const getOptions = () => [
    { value: 'normal', label: translate('normal') },
    { value: 'approval', label: translate('approval') },
  ];

  return SelectEntry({ element, id, label: translate('Gateway type'), getValue, setValue, getOptions });
}

function TerminateIfRejected(props: any) {
  const { element, id } = props;
  const bpmnFactory = useService('bpmnFactory');
  const commandStack = useService('commandStack');
  const translate = useService('translate');
  const businessObject = getRelevantBusinessObject(element);
  const componentKey = 'mdt:TerminateIfRejected';

  const getValue = () => {
    const extensionElement = getExtensionElement(businessObject, componentKey);
    let value = extensionElement?.get('body');
    if (value !== false && value !== true) {
      value = true;
      requestAnimationFrame(() => setValue(value));
    }
    return value;
  };

  const setValue = (value: boolean) => {
    replaceExtensionElements(element, businessObject, bpmnFactory, commandStack, componentKey, { body: value });
  };

  if (getExtensionElement(businessObject, 'mdt:ParallelGatewayType')?.get('body') !== 'approval') {
    return null;
  }

  return CheckboxEntry({ element, id, label: translate('Terminate If Rejected'), getValue, setValue });
}
