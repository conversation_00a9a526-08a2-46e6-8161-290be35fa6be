import _ from 'lodash';
import { BehaviorSubject } from 'rxjs';
import { createDate } from '@mdtBsComm/utils/dayUtil';
import { DataNode } from '@mdtDesign/tree';
import { IRoles, IUsers } from '@mdtProComm/interfaces';
import i18n from '../../languages';
import {
  DrawerModifyFormPermissionInfoController,
  FormCompController,
  ILoadDrawerUiDataFunc,
  IModifyDataFunc,
} from '../drawer-modify-form-permission-info';
import { DrawerModifyFormPermissionUserInfoInner } from './DrawerModifyFormPermissionUserInfoInner';

export enum FormDataAttrs {
  NAME = 'name',
  EMAIL = 'email',
  PHONE = 'phone',
  EXPIRE_TIME = 'expireTime',
  ROLE = 'role',
  DISABLE = 'disable',
  ORGANIZATION = 'organization',
}
export interface IControllerOptions<U, V extends IUsers> {
  modifyDataFunc: IModifyDataFunc<IUsers, V>;
  loadDrawerUiDataFunc: ILoadDrawerUiDataFunc<U>;
}
export interface IModalData {
  roleList: IRoles[];
  roleIdNameMap: Record<number, string>;
  appExpireTime: string;
  orgTreeList: DataNode[];
}

class DrawerModifyFormPermissionUserInfoController<V extends IUsers> extends DrawerModifyFormPermissionInfoController<
  IModalData,
  IUsers,
  V
> {
  private isBatch$ = new BehaviorSubject<boolean>(false);
  public constructor(options: IControllerOptions<IModalData, V>) {
    super({
      ...options,
      FormInnerView: DrawerModifyFormPermissionUserInfoInner,
      initInnerControllerFunc: (modalData: IModalData, rest?: V) => this.initInnerController(modalData, rest),
      initDrawerHeaderPropsFunc: () => this.initDrawerHeaderProps(),
    });
  }

  public destroy() {
    super.destroy();
    this.isBatch$.complete();
  }

  public getIsBatch$() {
    return this.isBatch$;
  }

  public openModal(rest?: V, isBatch?: boolean) {
    this.isBatch$.next(!!isBatch);
    return super.openModal(rest);
  }

  // 弹窗头部信息
  private initDrawerHeaderProps = () => {
    const modalRest = this.getModalRest();
    return {
      title: modalRest
        ? i18n.chain.proMicroModules.permission.editUser
        : i18n.chain.proMicroModules.permission.createUser,
    };
  };

  private initInnerController = (modalData: IModalData, rest?: V) => {
    // 默认使用appid的过期时间
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    const defaultValue = { expireTime: createDate(modalData.appExpireTime) } as V;
    // 每次打开弹窗都重新new controller, 快速重置各种状态
    const fc = new FormCompController<V>(rest, defaultValue);
    const isBatch = this.isBatch$.getValue();
    isBatch && fc.changeFormDataError(false);
    fc.changeFormData({
      ...(fc.getFormDataValue() as V),
      organization: _.map(fc.getFormDataValue()?.organization, _.toString),
    });
    return fc as FormCompController<V>;
  };
}

export { DrawerModifyFormPermissionUserInfoController };
