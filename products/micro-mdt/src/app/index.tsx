import { useEffect, useRef } from 'react';
import { useLocation } from 'react-router-dom';
import { ModuleIdEnum } from '@mdtProComm/constants';
import { getRootFontSizeFromUrl } from '@mdtProComm/utils/urlUtil';
import { DatlasApp } from '@mdtProMicroModules/datlas/app';
import { WorkflowPath } from '../_util/product-path';
import { AppController } from './AppController';
import '@mdtProComm/components/bpmn/dark.less';
import './index.less';

const ChangeTitle = () => {
  const { pathname } = useLocation();
  useEffect(() => {
    AppController.getInstance().changeTitle(pathname);
  }, [pathname]);
  return null;
};

// App入口
const App = () => {
  // 只执行一次
  const init = useRef(false);
  if (!init.current) {
    init.current = true;
    const fs = getRootFontSizeFromUrl();
    document.getElementsByTagName('html')[0]!.style.fontSize = `${fs}px`;
  }

  return (
    <>
      <ChangeTitle />
      <DatlasApp
        getController={(history) => AppController.getInstance(history)}
        onInit={(controller) => {
          const url = window.location.href;
          // 表单填报
          if (url.includes(`${ModuleIdEnum.WORKFLOW}/${WorkflowPath.WORKFLOW_APPLY}/`)) {
            controller.adaptaUserWithUnlogin();
          } else {
            controller.autoAuthByToken();
          }
        }}
      />
    </>
  );
};

export default App;
