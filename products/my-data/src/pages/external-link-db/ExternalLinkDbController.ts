import _ from 'lodash';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { DataListCompCardController } from '@mdtBsComponents/data-list-comp-card';
import { IEmotionProps, ModalWithBtnsCompEmotionController } from '@mdtBsComponents/modal-with-btns-comp-emotion';
import { RequestController } from '@mdtBsControllers/request-controller';
import { IDataset, IRequestCancelToken } from '@mdtBsServices/interfaces';
import toast from '@mdtDesign/toast';
import { isOutLink } from '@mdtProComm/utils/datapkgUtil';
import { DialogModifyFormDatasetController } from '@mdtProMicroModules/containers/dialog-modify-form-dataset';
import { getModel } from '../../_util/modelUtil';
import { ITableData as IModifyTableData, ModifyDatasetModel } from '../../_util/ModifyDatasetModel';
import { ModifyDatasetModelBff } from '../../_util/ModifyDatasetModelBff';
import { AppController } from '../../app/AppController';
import i18n from '../../languages';
import { DatabaseDetailController, DrawerDatabaseDetailController } from '../database-detail';
import { DatabaseDetailModel } from '../database-detail/DatabaseDetailModel';
import { DatabaseDetailModelBff } from '../database-detail/DatabaseDetailModelBff';
import { DbItem, EmptyPage } from './ExternalLinkDb';
import { IDatasetInfo, IExternalLinkDbModel } from './ExternalLinkDbModel';

export enum MenuKeyEnum {
  VIEW = 'view',
  EDIT = 'edit',
  DELETE = 'delete',
}

class ExternalLinkDbController extends RequestController {
  private app;
  private Model: IExternalLinkDbModel;
  private dbListCardController: DataListCompCardController<IDatasetInfo, this>;
  private dialogModifyFormDatasetController: DialogModifyFormDatasetController<IModifyTableData>;
  private deleteConnController: ModalWithBtnsCompEmotionController<IDataset>;
  private drawerController: DrawerDatabaseDetailController;
  private modifyDatasetModel = new (getModel(ModifyDatasetModel, ModifyDatasetModelBff))();

  private databasePreviewCache: Record<string, DatabaseDetailController> = {};

  public constructor(app: AppController, Model: IExternalLinkDbModel) {
    super();
    this.app = app;
    this.Model = Model;
    this.dbListCardController = new DataListCompCardController<IDatasetInfo, this>({
      dataListCompControllerOptions: {
        dataListControllerOptions: {
          loadDataListFunc: this.loadDbList,
        },
        compOptions: this.initDbListOptions,
        EmptyView: EmptyPage,
      },
    });

    const cancelToken = this.getCancelToken();
    this.dbListCardController.loadDataList(cancelToken);

    this.dialogModifyFormDatasetController = new DialogModifyFormDatasetController(this.modifyDatasetModel);

    this.drawerController = new DrawerDatabaseDetailController({
      getPreviewControllerFunc: this.getDatapkgController,
    });

    this.deleteConnController = new ModalWithBtnsCompEmotionController<IDataset>({
      modalCompOptions: { modalOptions: this.initDeleteModalOptions },
      clickOkBtnFunc: (originalData?: IDataset) => this.completeDeleteConnFn(originalData),
    });
  }

  public destroy() {
    super.destroy();
    this.dbListCardController.destroy();
    this.dialogModifyFormDatasetController.destroy();
    this.deleteConnController.destroy();
    _.forEach(this.databasePreviewCache, (item) => item.destroy());
    this.databasePreviewCache = {};
    this.app = null!;
    this.Model = null!;
  }

  public getDbListCardController() {
    return this.dbListCardController;
  }

  public getdialogModifyFormDatasetController() {
    return this.dialogModifyFormDatasetController;
  }

  public getDeleteConnController() {
    return this.deleteConnController;
  }

  public getDrawerController() {
    return this.drawerController;
  }

  public openConnFormModal(item?: IDataset) {
    const dbItem = item ? this.modifyDatasetModel.transformToTableData(item) : undefined;
    this.dialogModifyFormDatasetController.openModal(dbItem).subscribe((resp) => {
      if (!resp.success) return;
      const tip = i18n.chain.mydata.link.dbSuccess(
        item ? i18n.chain.proMicroModules.datapkg.edit : i18n.chain.proMicroModules.datapkg.create,
      );
      toast.success(tip);
      this.dbListCardController.loadDataList();
    });
  }

  public entryDb(item: IDataset) {
    this.drawerController.openModal(item.id);
  }

  private getDatapkgController = (dbId: string) => {
    let cache = this.databasePreviewCache[dbId];
    if (!cache) {
      cache = new DatabaseDetailController({ dbId, Model: getModel(DatabaseDetailModel, DatabaseDetailModelBff) });
    }
    this.databasePreviewCache[dbId] = cache;
    return cache;
  };

  private loadDbList = (cancelToken?: IRequestCancelToken): Observable<[number, IDatasetInfo[]]> => {
    return this.Model.queryDatasetList(cancelToken).pipe(
      map((datasets) => {
        this.app.getDatasetsController()!.initDatasets(datasets);
        const externalDb = _.filter(datasets, ({ role }) => isOutLink(role));
        return [0, externalDb];
      }),
    );
  };

  private completeDeleteConnFn = async (item?: IDataset) => {
    const resp = await this.Model.deleteDbConn(item!).toPromise();
    resp.success && toast.success(i18n.chain.mydata.link.delDbSuccess);
    this.dbListCardController.loadDataList();
    return resp;
  };

  private initDbListOptions() {
    return {
      itemGap: 0,
      itemHeight: 75,
      itemWidth: '100%',
      useVirtual: false,
      itemKey: 'id',
      CardItemView: DbItem,
    };
  }

  private initDeleteModalOptions = (): IEmotionProps => {
    const delData = this.deleteConnController.getModalRest();
    return {
      emotion: 'alert',
      title: i18n.chain.mydata.link.delDbConfirm(delData?.name),
      description: i18n.chain.mydata.link.delDbConfirmDesc,
    };
  };
}

export { ExternalLinkDbController };
