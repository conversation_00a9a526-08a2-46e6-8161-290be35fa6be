/* eslint-disable sonarjs/no-duplicate-string */
/* eslint-disable @typescript-eslint/explicit-member-accessibility */
import { registerEditorPlugin } from 'amis-editor-core';
import i18n from '../../languages';
import { FlexPluginBase } from './FlexPluginBase';

export default class Layout_fixed extends FlexPluginBase {
  static id = 'Layout_fixed';
  static scene = ['layout'];

  name = i18n.chain.jsonComp.layout_fixed;
  isBaseComponent = true;
  pluginIcon = 'layout-fixed-plugin';
  description = i18n.chain.jsonComp.layout_fixedDescription;
  order = 0;
  scaffold: any = {
    type: 'container',
    size: 'xs',
    body: [],
    style: {
      position: 'fixed',
      inset: 'auto 50px 50px auto',
      zIndex: 10,
      minWidth: '80px',
      minHeight: '80px',
      display: 'block',
    },
    wrapperBody: false,
    originPosition: 'right-bottom',
  };
  previewSchema: any = {
    type: 'container',
    body: [],
    style: {
      position: 'static',
      display: 'block',
    },
    size: 'none',
    wrapperBody: false,
  };
  panelTitle = i18n.chain.jsonComp.layout_fixed;
}

registerEditorPlugin(Layout_fixed);
