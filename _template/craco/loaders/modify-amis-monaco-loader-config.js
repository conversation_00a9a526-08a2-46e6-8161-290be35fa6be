module.exports = function (source) {
  const key1 = 'if (!window.MonacoEnvironment) {';
  const key2 = "import('monaco-editor').then";
  const insetStr1 = `if (!window.MonacoEnvironment && false) {`;
  const insetStr2 = `import('@monaco-editor/loader').then(function(loader){loader=loader.default||loader;return loader.__getMonacoInstance()||loader.init();}).then`;
  source = source.replace(key1, insetStr1).replace(key2, insetStr2);
  return source;
};
