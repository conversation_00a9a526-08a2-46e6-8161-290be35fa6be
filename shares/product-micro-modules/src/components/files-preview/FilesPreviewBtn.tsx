import _ from 'lodash';
import { FC, useState } from 'react';
import { Button } from '@metroDesign/button';
import { Space } from '@metroDesign/space';
import { useCreation } from 'ahooks';
import { If } from '@mdtBsComm/components/if';
import i18n from '../../languages';
import { downloadFile, IFilesPreviewItem, openFilesPreview } from './util';
import './index.less';

interface IFilesPreviewBtnProps {
  listStr?: string;
  enableDownload?: boolean;
}

export const FilesPreviewBtn: FC<IFilesPreviewBtnProps> = ({ listStr, enableDownload }) => {
  const [downloadLoading, setDownloadLoading] = useState(false);

  const list = useCreation(() => {
    let list: IFilesPreviewItem[] = [];
    try {
      list = _.isString(list) ? JSON.parse(listStr as string) : listStr;
    } catch (e) {
      console.error('FilesPreviewBtn json parse error: ', e);
    }
    return list;
  }, [listStr]);

  if (_.isEmpty(list)) return null;

  const handleClick = async () => {
    openFilesPreview({
      list,
      defaultSelectIndex: 0,
      enableDownload,
    });
  };

  const handleDownload = async () => {
    setDownloadLoading(true);
    for (const file of list) {
      await downloadFile(file);
    }
    setDownloadLoading(false);
  };

  return (
    <Space size={0} onClick={(e) => e.stopPropagation()}>
      <Button className="files-preview-link" onClick={handleClick} size="small" ghost type="primary">
        {i18n.chain.proMicroModules.collaborate.viewDetail}
      </Button>
      <If data={!!enableDownload}>
        <Button
          className="files-preview-link"
          onClick={handleDownload}
          size="small"
          withoutBorder
          ghost
          type="primary"
          loading={downloadLoading}
          style={{ padding: '0.25rem' }}
        >
          {i18n.chain.proMicroModules.collaborate.download}
        </Button>
      </If>
    </Space>
  );
};
