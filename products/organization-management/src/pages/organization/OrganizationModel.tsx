import _ from 'lodash';
import { from, Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { IByRolesPatch, IRequestRequestConfig, IRoleBatch, IUserQuery } from '@mdtApis/interfaces';
import { patchUsersByRoleAsync, postRoleByExcelAsync } from '@mdtBsServices/auth';
import { IPaginationQuery } from '@mdtBsServices/interfaces';
import { IBusinessResult, IOrgs, IUsers } from '@mdtProComm/interfaces';
import { GroupPermissionModel } from '@mdtProMicroModules/models/GroupPermissionModel';

export interface IUserQueryConfig {
  appId: number;
  query?: IUserQuery;
  config?: IRequestRequestConfig<IPaginationQuery>;
  roleIdNameMap?: Record<number, string>;
  orgIdNameMap?: Record<string, IOrgs>;
}

export enum IMoreItemsKey {
  DELETE = 'delete',
  EVICTION = 'eviction',
  EDIT = 'edit',
  RESET_PWD = 'resetPwd',
  DISABLE = 'disable',
  RESTART = 'restart',
  UNLOCK = 'unlock',
}

class OrganizationModel extends GroupPermissionModel {
  public static patchOrgUsers(appId: number, roleId: string, currentKeys: string[]) {
    const body: IByRolesPatch = {
      new_users: currentKeys,
    };
    return from(patchUsersByRoleAsync({ role_ids: roleId, app_id: appId }, body));
  }

  public static removeOrgUsers(roleId: string, data: IByRolesPatch) {
    return from(patchUsersByRoleAsync({ role_ids: roleId }, data));
  }

  // 通过excel批量新建部门
  public static createOrgByExcel(file: File): Observable<IBusinessResult<IOrgs[]>> {
    return from(postRoleByExcelAsync({ role_type: 'organization' }, { file })).pipe(
      map((resp) => {
        const result: IOrgs[] = _.map(resp.data, (org) => this.transformOrgBatchtoOrg(org));
        return { success: resp.success, result };
      }),
    );
  }

  // 将用户数据翻译成导出excel识别的数据
  public static transformUserToExcelData(users: IUsers[]): any[] {
    // TODO 英文下导出，未国际化(因为后端目前只识别中文模板，需要和后端沟通增加英文模板)
    return _.map(users, (user) => {
      return {
        '用户名*': user.name,
        '邮箱*': user.email,
        手机: user.phone || '',
        部门: user.orgDisplay?.join('/'),
        角色: user.roleDisplay?.join('/'),
        过期时间: user.expireTimeDisplay.replace('(已过期)', '') || '',
        是否禁用: user.disable ? '是' : '否',
      };
    });
  }

  // 将导入的部门翻译成可用部门
  private static transformOrgBatchtoOrg(data: IRoleBatch): IOrgs {
    return {
      key: _.toString(data.role_id),
      title: data.role_name,
      pid: _.toString(data.parent_id),
      permission: [],
    };
  }
}
export type IOrganizationModel = typeof OrganizationModel;
export { OrganizationModel };
