import _ from 'lodash';
import { AsyncSubject, BehaviorSubject, of } from 'rxjs';
import { map } from 'rxjs/operators';
import { IUserQuery } from '@mdtApis/interfaces';
import { IBusinessResult } from '@mdtBsComm/interfaces';
import { IEmotionProps, ModalWithBtnsCompEmotionController } from '@mdtBsComponents/modal-with-btns-comp-emotion';
import { RequestController } from '@mdtBsControllers/request-controller';
import { IByRolesPatch } from '@mdtBsServices/interfaces';
import { IPaginationQuery } from '@mdtBsServices/interfaces';
import Button, { LinkButton } from '@mdtDesign/button';
import { Dropmenu, MenuItemProps } from '@mdtDesign/dropdown';
import Tag from '@mdtDesign/tag';
import toastApi from '@mdtDesign/toast';
import { ResourceReceiverEnum } from '@mdtProComm/constants';
import { IOrgs, IOrgTrees, IUsers } from '@mdtProComm/interfaces';
import { getOrgPathWithOrg } from '@mdtProComm/utils/orgUtil';
import { orgsToOrgTrees } from '@mdtProComm/utils/orgUtil';
import { DialogImportAppOrgController } from '@mdtProMicroModules/containers/dialog-import-app-org';
import { DialogImportAppUserController } from '@mdtProMicroModules/containers/dialog-import-app-user';
import { IPermissionModalData } from '@mdtProMicroModules/containers/dialog-modify-from-organization';
import { DialogUserResetPwdController } from '@mdtProMicroModules/containers/dialog-user-reset-pwd';
import {
  DrawerModifyFormPermissionUserInfoController,
  IModalData,
} from '@mdtProMicroModules/containers/drawer-modify-form-permission-user-info';
import {
  OrgManageTreeListController,
  OrgManageTreeListModel,
} from '@mdtProMicroModules/containers/org-manage-tree-list';
import { OrgRoot } from '@mdtProMicroModules/containers/org-manage-tree-list/components';
import {
  IMoreReset,
  IVirtualizedTableProps,
  TableCurdWithSimpleSearchController,
} from '@mdtProMicroModules/containers/table-curd-with-simple-search';
import { UserOrgLazySelectController } from '@mdtProMicroModules/containers/user-org-lazy-select';
import { IUserModel } from '@mdtProMicroModules/models/UserModel';
import { MyResourceShareModel, ResourceShareCreateModel } from '@mdtProMicroModules/pages/resource-share-list';
import { IReceiver } from '@mdtProMicroModules/pages/resource-share-menu';
import { DrawerResouceShareOtherController } from '@mdtProMicroModules/pages/resource-share-other';
import { exportTo } from '../../_util/exportTo';
import { AppController } from '../../app/AppController';
import { DEPLOY_ORIGIN } from '../../config';
import i18n from '../../languages';
import { IMoreItemsKey, IOrganizationModel } from './OrganizationModel';

export enum BatchUserEnum {
  IMPORT = 'import',
  EXPORT = 'export',
  IMPORT_ORG = 'import-org',
  EDIT_USER = 'edit-user',
}

export interface IOrganizationModalData extends IModalData {
  orgIdNameMap: Record<string, IOrgs>;
}
// 防止app的id和部门的id重合，为appId加上前缀
export const APP_KEY_SUFFIX = 'app_';
// 模板路径
const EXCEL_PATH = DEPLOY_ORIGIN + 'static/resources/template.xlsx';
// 部门模板路径
const EXCEL_ORG_PATH = DEPLOY_ORIGIN + 'static/resources/template_org.xlsx';

class OrganizationController extends RequestController {
  private Model: IOrganizationModel;
  private UserModel: IUserModel;
  private app: AppController;

  // app下的用户表格 - 部门用户表格
  private tableController: TableCurdWithSimpleSearchController<IUsers>;
  // 导入用户
  private importUserController: DialogImportAppUserController;
  // 导入部门
  private importOrgController: DialogImportAppOrgController;
  // app下的用户编辑
  private allUserController: DrawerModifyFormPermissionUserInfoController<IUsers>;
  // app下强制修改密码
  private resetPwdController: DialogUserResetPwdController<IUsers>;
  // app下的用户可用状态编辑
  private statusUserController: ModalWithBtnsCompEmotionController<IUsers>;
  // app下的用户删除
  private deleteUserController: ModalWithBtnsCompEmotionController<IUsers>;
  // app下的用户解除锁定
  private unlockUserController: ModalWithBtnsCompEmotionController<IUsers>;
  // 部门移出用户
  private removeRoleUserController: ModalWithBtnsCompEmotionController<IUsers>;
  // 部门新增用户
  private addRoleUserController: UserOrgLazySelectController;
  // 部门
  private orgController: OrgManageTreeListController;
  // 资源
  private resouceShareController: DrawerResouceShareOtherController;

  // 是否为根节点
  private isRoot$ = new BehaviorSubject<boolean>(true);
  // 当前选中的节点
  private currentSelectId$ = new BehaviorSubject<string>('');
  // 部门数据加载状态
  private isOrgLoading$ = new BehaviorSubject<boolean>(false);

  // 当前部门层级展示
  private pidPathDisplay: string;
  // 用户信息表缓存
  private drawerUiDataCache: Partial<IOrganizationModalData> = {};
  // 权限表缓存
  private drawerPermissionsCache: IPermissionModalData = {};

  public constructor(app: AppController, Model: IOrganizationModel, UserModel: IUserModel) {
    super();
    this.app = app;
    this.Model = Model;
    this.UserModel = UserModel;
    this.pidPathDisplay = '--';

    this.allUserController = new DrawerModifyFormPermissionUserInfoController<IUsers>({
      loadDrawerUiDataFunc: this.queryDrawerUiData,
      modifyDataFunc: this.modifyDataToService,
    });

    this.resetPwdController = new DialogUserResetPwdController({
      clickOk: this.resetPwd,
    });

    this.orgController = new OrgManageTreeListController(
      {
        appId: app.getAppId(),
        appName: app.getAppName(),
        enablePermission: app.getUserPermissionController()!.getMenuPermission().enableOrganization,
        queryPermissionUiDataFunc: this.queryDrawerOrgPermissionUiData,
        createOrgFunc: this.createOrgs,
        updateOrgFunc: this.updateOrgs,
        deleteOrgFunc: this.deleteOrgs,
        onOrgSelectFunc: this.onOrgSelect,
        openResourceFunc: this.openResource,
      },
      OrgManageTreeListModel,
    );

    this.importUserController = new DialogImportAppUserController({
      downloadUrl: EXCEL_PATH,
      uploadProps: {
        onDropAccepted: this.onDropCreateUser,
      },
    });

    this.importOrgController = new DialogImportAppOrgController({
      downloadUrl: EXCEL_ORG_PATH,
      uploadProps: {
        onDropAccepted: this.onDropCreateOrg,
      },
    });

    this.deleteUserController = new ModalWithBtnsCompEmotionController<IUsers>({
      clickOkBtnFunc: this.deleteDataToService,
      modalCompOptions: { modalOptions: this.initDeleteModalOptions },
    });

    this.removeRoleUserController = new ModalWithBtnsCompEmotionController<IUsers>({
      clickOkBtnFunc: this.removeRoleUserToService,
      modalCompOptions: { modalOptions: this.initRemoveRoleUserModalOptions },
    });

    this.unlockUserController = new ModalWithBtnsCompEmotionController<IUsers>({
      clickOkBtnFunc: this.unLockUserToService,
      modalCompOptions: { modalOptions: this.initUnlockUserModalOptions },
    });

    this.statusUserController = new ModalWithBtnsCompEmotionController<IUsers>({
      clickOkBtnFunc: this.updateUserStatusToService,
      modalCompOptions: { modalOptions: this.initStatusModalOptions },
      uiOptions: { okBtnProps: { type: 'primary' } },
    });

    this.addRoleUserController = new UserOrgLazySelectController({
      onConmitSelectFunc: this.addRoleUserToService,
      title: i18n.chain.orgAdmin.organization.addUser,
      userIdKey: 'uuid',
      showUserWithoutOrg: true,
    });

    this.resouceShareController = new DrawerResouceShareOtherController();

    this.tableController = new TableCurdWithSimpleSearchController<IUsers>({
      dataListCompTableCurdControllerOptions: {
        dataListControllerOptions: {
          loadDataListFunc: this.queryDataWithTotal,
          loadNextPageDataListFunc: this.queryData,
          getBackendFilterParams: this.getFilterParams,
        },
        curdOptions: this.initCurdOptions,
        tableOptions: this.initTableOptions,
        clickCreateBtnFunc: this.createTableData,
        clickMoreBtnFunc: this.moreItemsTableData,
      },
      headerOptions: this.initHeaderOptions,
    });

    const rootId = this.getRootInfo().key;
    this.currentSelectId$.next(rootId);

    // 先初始化角色和部门数据，然后再加载用户列表
    this.initOrgAndRoleList().subscribe(() => {
      this.tableController.loadDataList();
    });
    this.tableController.listenBackendFilter(this.tableController.getSingleFilter$());
  }

  public destroy() {
    this.tableController.destroy();
    this.importUserController.destroy();
    this.importOrgController.destroy();
    this.allUserController.destroy();
    this.resetPwdController.destroy();
    this.statusUserController.destroy();
    this.deleteUserController.destroy();
    this.unlockUserController.destroy();
    this.removeRoleUserController.destroy();
    this.addRoleUserController.destroy();
    this.orgController.destroy();
    this.resouceShareController.destroy();
    this.isRoot$.complete();
    this.currentSelectId$.complete();
    this.isOrgLoading$.complete();
    this.drawerUiDataCache = {};
    this.drawerPermissionsCache = {};
    this.app = null!;
    this.Model = null!;
    this.UserModel = null!;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  public getTableController(cur?: string) {
    return this.tableController;
  }

  public getOrgController() {
    return this.orgController;
  }

  public getOrgLoading$() {
    return this.isOrgLoading$;
  }

  public getImportUserController() {
    return this.importUserController;
  }

  public getImportOrgController() {
    return this.importOrgController;
  }

  public getAllUserController() {
    return this.allUserController;
  }

  public getDeleteUserController() {
    return this.deleteUserController;
  }

  public getRemoveRoleUserController() {
    return this.removeRoleUserController;
  }

  public getAddRoleUserController() {
    return this.addRoleUserController;
  }

  public getStatusUserController() {
    return this.statusUserController;
  }

  public getResetPwdController() {
    return this.resetPwdController;
  }

  public getUnlockUserController() {
    return this.unlockUserController;
  }

  public getResouceShareController() {
    return this.resouceShareController;
  }

  public getIsRoot$() {
    return this.isRoot$;
  }

  public getCurrentSelectId$() {
    return this.currentSelectId$;
  }

  public getIsOrgLoading$() {
    return this.isOrgLoading$;
  }

  public getRootInfo = () => {
    return {
      title: this.app.getAppName(),
      key: APP_KEY_SUFFIX + this.app.getAppId(),
    };
  };

  public getFooterPermission = () => {
    const { enableOrganization } = this.app!.getUserPermissionController()!.getMenuPermission();
    return {
      disableCreate: !enableOrganization,
      disableManage: !enableOrganization || this.isRoot$.getValue(),
    };
  };

  public onOrgSelect = (id: string, isRoot: boolean, flatData: IOrgs[]) => {
    this.isRoot$.next(!!isRoot);
    this.currentSelectId$.next(id);
    this.pidPathDisplay = getOrgPathWithOrg(id, flatData);

    // 重新加载表格数据
    this.tableController.loadDataList();
  };

  public openResource = (item: IOrgs) => {
    const receiver: IReceiver = {
      type: ResourceReceiverEnum.ORGANIZATION,
      title: _.get(item, 'name') || _.get(item, 'title'),
      key: _.get(item, 'id') || _.get(item, 'key'),
    };
    const options = {
      app: this.app,
      receiverId: receiver.key as string,
      receiverType: receiver.type,
    };
    this.resouceShareController.openModal({
      Model: new MyResourceShareModel(options),
      CreateResourceModel: new ResourceShareCreateModel(options),
      receiver,
    });
    this.orgController.getModifyOrgController()!.closeModal();
  };

  // 渲染表头所需信息
  private initTableOptions = (): IVirtualizedTableProps => {
    return {
      columns: [
        {
          name: i18n.chain.orgAdmin.organization.username,
          code: 'name',
          ellipsis: true,
          render: (value: string, r: IUsers) => <LinkButton onClick={() => this.editTableData(r)}>{value}</LinkButton>,
        },
        {
          name: i18n.chain.orgAdmin.organization.orgs,
          code: 'orgDisplay',
          render: (v: string[]) =>
            this.isRoot$.getValue() ? (
              v?.length ? (
                v.map((v: string) => <Tag key={v} tag={v} />)
              ) : (
                '--'
              )
            ) : (
              <Tag key={this.pidPathDisplay} tag={this.pidPathDisplay} />
            ),
        },
        {
          name: i18n.chain.orgAdmin.organization.roles,
          code: 'roleDisplay',
          render: (v: string[]) => (v?.length ? v.map((v: string) => <Tag key={v} tag={v} />) : '--'),
        },
        {
          name: i18n.chain.orgAdmin.organization.availability,
          code: 'disableDisplay',
          width: 120,
          render: (value: string, r: IUsers) => (
            <Tag key={value} tag={value} color={!r.disable ? 'green-700' : 'red-700'} />
          ),
        },
        { name: i18n.chain.orgAdmin.organization.expireTime, code: 'expireTimeDisplay' },
      ],
      type: 'page-bg',
      primaryKey: 'id',
      emptyContent: i18n.chain.comNoData,
      withVerticalBorder: false,
    };
  };

  private initCurdOptions = () => {
    const { enableDelete, enableCreate, enableEdit, enableResetPwd } =
      this.app!.getUserPermissionController()!.getUserManagePermission();
    const isRoot = this.isRoot$.getValue();
    const moreItems = (item: IUsers) => {
      const menus: MenuItemProps[] = [
        {
          title: i18n.chain.orgAdmin.organization.edit,
          key: IMoreItemsKey.EDIT,
          disabled: !enableEdit,
        },
        {
          title: (item as IUsers).disable
            ? i18n.chain.orgAdmin.organization.activeUser
            : i18n.chain.orgAdmin.organization.freezeUser,
          key: (item as IUsers).disable ? IMoreItemsKey.RESTART : IMoreItemsKey.DISABLE,
          disabled: !enableEdit,
        },
        {
          title: i18n.chain.orgAdmin.organization.forcePassword,
          key: IMoreItemsKey.RESET_PWD,
          disabled: !enableResetPwd,
        },
        {
          title: i18n.chain.orgAdmin.organization.unlockUser,
          key: IMoreItemsKey.UNLOCK,
          disabled: !enableEdit,
        },
        {
          title: i18n.chain.orgAdmin.organization.delUser,
          key: IMoreItemsKey.DELETE,
          disabled: !enableDelete,
          danger: true,
          divider: !isRoot,
        },
      ];
      if (!isRoot) {
        menus.push({
          title: i18n.chain.orgAdmin.organization.removeOrg,
          key: IMoreItemsKey.EVICTION,
          disabled: !enableEdit,
          danger: true,
        });
      }
      return menus;
    };
    return {
      enableCreate: isRoot ? enableCreate : enableEdit,
      enableEdit: false,
      enableSelect: true,
      moreItems,
    };
  };

  private initHeaderOptions = () => {
    const isRoot = this.isRoot$.getValue();
    const { enableImport, enableExport, enableImportOrg, enableEditTime } = this.app
      .getUserPermissionController()
      .getUserBatchManagePermission();
    const enableOrg = this.app.getUserPermissionController()!.getMenuPermission().enableOrganization;
    const batchMenus: any[] = [];
    if (enableImportOrg) {
      batchMenus.push({ title: i18n.chain.orgAdmin.organization.importOrg, key: BatchUserEnum.IMPORT_ORG });
    }
    if (enableImport) {
      batchMenus.push({ title: i18n.chain.orgAdmin.organization.importUser, key: BatchUserEnum.IMPORT });
    }
    if (enableExport) {
      batchMenus.push({ title: i18n.chain.orgAdmin.organization.exportUser, key: BatchUserEnum.EXPORT });
    }
    if (enableEditTime) {
      batchMenus.push({ title: i18n.chain.orgAdmin.organization.batchEditUser, key: BatchUserEnum.EDIT_USER });
    }

    return {
      createBtnLabel: isRoot
        ? i18n.chain.orgAdmin.organization.createUser
        : i18n.chain.orgAdmin.organization.addOrgUser,
      inputPlaceholder: i18n.chain.orgAdmin.organization.searchUser,
      title: isRoot ? i18n.chain.orgAdmin.organization.allUser : i18n.chain.orgAdmin.organization.orgUser,
      renderExtendOthers: () => (
        <>
          <Dropmenu
            className="page_organization-import-user"
            onClickMenuItem={this.batchUserClick}
            noSelected
            menus={batchMenus}
          >
            {i18n.chain.orgAdmin.organization.batchOperation}
          </Dropmenu>
          {!isRoot && (
            <Button
              className="page_organization-org-manage"
              onClick={() => this.orgController.editOrgData()}
              disabled={!enableOrg}
            >
              {i18n.chain.orgAdmin.organization.orgManage}
            </Button>
          )}
        </>
      ),
    };
  };

  // 渲染删除弹窗所需信息
  private initDeleteModalOptions = (): IEmotionProps => {
    const delData = this.deleteUserController.getModalRest();
    return {
      emotion: 'alert',
      title: i18n.chain.orgAdmin.organization.delConfirm(delData?.name || '??'),
      description: i18n.chain.orgAdmin.organization.delConfirmDesc,
    };
  };

  // 渲染部门移出用户弹窗所需信息
  private initRemoveRoleUserModalOptions = (): IEmotionProps => {
    const delData = this.removeRoleUserController.getModalRest();
    return {
      emotion: 'alert',
      title: i18n.chain.orgAdmin.organization.removeConfirm(delData?.name || '??'),
      description: i18n.chain.orgAdmin.organization.removeConfirmDesc,
    };
  };

  // 渲染用户解除锁定弹窗所需信息
  private initUnlockUserModalOptions = (): IEmotionProps => {
    return {
      emotion: 'alert',
      title: i18n.chain.orgAdmin.organization.unlockConfirm,
    };
  };

  // 渲染用户可用状态弹窗所需信息
  private initStatusModalOptions = (): IEmotionProps => {
    const data = this.statusUserController.getModalRest();
    return {
      emotion: 'help-2',
      title: `${data?.disable ? i18n.chain.orgAdmin.organization.active : i18n.chain.orgAdmin.organization.freeze}${
        i18n.chain.orgAdmin.organization.confirm
      }`,
      description: `${data?.disable ? '' : i18n.chain.orgAdmin.organization.freezeUserDesc}`,
    };
  };

  // 加载弹窗user的UI数据
  private queryDrawerUiData = () => {
    if (!this.drawerUiDataCache.appExpireTime) {
      const app = this.app!;
      this.drawerUiDataCache.appExpireTime = app.getAppExpireTime();
    }
    const result: Required<IModalData> = {
      roleList: this.drawerUiDataCache.roleList!,
      roleIdNameMap: this.drawerUiDataCache.roleIdNameMap!,
      appExpireTime: this.drawerUiDataCache.appExpireTime!,
      orgTreeList: this.drawerUiDataCache.orgTreeList!,
    };
    return of(result);
  };

  // 加载部门权限的UI数据
  private queryDrawerOrgPermissionUiData = () => {
    const ps = this.app!.getPermissionController();
    const mdtProduct = this.app!.getUserMdtProduct();
    this.drawerPermissionsCache.permissionOption = this.Model.getGroupPermissions(
      this.app!.getAppPermission()!,
      ps!.getPermissionLabel.bind(ps),
      ps!.getPermissionModule.bind(ps),
      ps!.getPermissionType.bind(ps),
      mdtProduct,
    );
    return of(this.drawerPermissionsCache);
  };
  // 构建查询参数
  private buildQueryParams = (params: IPaginationQuery) => {
    const { enableExport } = this.app!.getUserPermissionController()!.getUserBatchManagePermission();
    const appId = this.app.getAppId();
    const currentId = this.currentSelectId$.getValue();
    const isRoot = this.isRoot$.getValue();
    const { roleIdNameMap, orgIdNameMap } = this.drawerUiDataCache;

    const query: IUserQuery = {
      ...params,
      basic_info: true,
      download: enableExport,
      enable_status: 'all',
    };

    // 如果不是根节点，添加 role_ids 参数
    if (!isRoot) {
      query.role_ids = currentId.toString();
    }

    const searchVal = this.tableController.getSingleFilterValue();
    if (searchVal) {
      query.name_like = searchVal;
    }

    return {
      appId,
      query,
      roleIdNameMap,
      orgIdNameMap,
    };
  };

  // 获取app全部用户
  private queryData = (params: IPaginationQuery) => {
    return this.UserModel.queryUsersPagination(this.buildQueryParams(params));
  };

  private queryDataWithTotal = (params: IPaginationQuery) => {
    return this.UserModel.queryUsersPaginationTotal(this.buildQueryParams(params));
  };

  private batchUserClick = (info: any) => {
    const key = info.key;
    const selectedData = this.tableController.getSelectedDataListValue();
    const data = this.Model.transformUserToExcelData(selectedData);
    // 导入
    if (key === BatchUserEnum.IMPORT) {
      this.importUserController.openModal();
    }
    // 导出
    if (key === BatchUserEnum.EXPORT) {
      if (_.isEmpty(data)) {
        toastApi.warning(i18n.chain.orgAdmin.organization.exportError);
        return;
      }
      try {
        exportTo({ data });
        toastApi.success(i18n.chain.orgAdmin.organization.exportSuccess);
      } catch (error: any) {
        toastApi.error(i18n.chain.orgAdmin.organization.exportFailed(error));
      }
    }
    // 导入部门
    if (key === BatchUserEnum.IMPORT_ORG) {
      this.importOrgController.openModal();
    }
    // 编辑用户
    if (key === BatchUserEnum.EDIT_USER) {
      if (_.isEmpty(data)) {
        toastApi.warning(i18n.chain.orgAdmin.organization.batchError);
        return;
      }
      this.allUserController.openModal(undefined, true);
    }
  };

  // 编辑数据
  private editTableData = (originalData?: IUsers) => {
    const { enableEdit } = this.app!.getUserPermissionController()!.getUserManagePermission();
    if (!enableEdit) {
      let result = new AsyncSubject<IBusinessResult<IUsers>>();
      result.next({ success: true });
      result.complete();
      return result;
    }
    return this.allUserController.openModal(originalData).subscribe((result) => {
      result.success && this.tableController.editDataInList(result.result!);
    });
  };

  // 强制重置密码
  private resetPwd = async (password: string, originalData?: IUsers) => {
    const resp = await this.UserModel.resetPwdUser(password, originalData!).toPromise();
    if (resp.success) {
      toastApi.success(i18n.chain.orgAdmin.organization.forcePasswordSuccess);
    }
    return resp;
  };

  // app下用户向服务端修改数据
  private modifyDataToService = (params: IUsers, originalData?: IUsers) => {
    const selectedData = this.tableController.getSelectedDataListValue();
    const isBatch = !_.isEmpty(selectedData);
    const isEdit = !_.isEmpty(originalData);
    if (isBatch) {
      return this.updateDataBatchToService(params, selectedData);
    }
    if (isEdit) {
      return this.updateDataToService(params, originalData!);
    }
    return this.addDataToService(params);
  };

  // 新增app用户
  private createTableData = () => {
    const isRoot = this.isRoot$.getValue();
    if (isRoot) {
      return this.allUserController.openModal();
    }
    return this.addRoleUserController.openModal();
  };

  // excel新增回调
  private onDropCreateUser = async (files: File[]) => {
    const [file] = files;
    const resp = await this.UserModel.createUserByExcel(
      file,
      this.drawerUiDataCache.roleIdNameMap!,
      this.drawerUiDataCache.orgIdNameMap!,
    ).toPromise();
    const { success } = resp;
    if (success) {
      toastApi.success(i18n.chain.orgAdmin.organization.batchSuccess);
      this.importUserController.closeModal();
      this.tableController.loadDataList();
    }
  };

  // excel新增回调
  private onDropCreateOrg = async (files: File[]) => {
    const [file] = files;
    const resp = await this.Model.createOrgByExcel(file).toPromise();
    const { result = [], success } = resp;
    if (success) {
      toastApi.success(i18n.chain.orgAdmin.organization.batchOrgSuccess);
      this.importOrgController.closeModal();
      this.orgController.update(result);
    }
  };

  // 点击用户的操作
  private moreItemsTableData = (key: string, originalData?: IUsers) => {
    // 编辑用户
    if (key === IMoreItemsKey.EDIT) {
      return this.allUserController.openModal(originalData);
    }
    // 强制修改密码
    if (key === IMoreItemsKey.RESET_PWD) {
      return this.resetPwdController.openModal(originalData);
    }
    // 解除账号锁定
    if (key === IMoreItemsKey.UNLOCK) {
      return this.unlockUserController.openModal(originalData);
    }
    // 删除数据
    if (key === IMoreItemsKey.DELETE) {
      return this.deleteUserController.openModal(originalData);
    }
    // 激活/禁用用户
    if (key === IMoreItemsKey.RESTART || key === IMoreItemsKey.DISABLE) {
      return this.statusUserController.openModal(originalData);
    }
    // 移出部门
    if (key === IMoreItemsKey.EVICTION) {
      return this.removeRoleUserController.openModal(originalData);
    }
    let result = new AsyncSubject<IMoreReset<IUsers>>();
    result.next({ success: true });
    result.complete();
    return result;
  };

  // 更新数据
  private async updateDataToService(data: IUsers, originalData: IUsers) {
    const { roleIdNameMap, orgIdNameMap } = this.drawerUiDataCache;
    const resp = await this.UserModel.updateUser(
      data as IUsers,
      originalData,
      this.app.getAppId(),
      roleIdNameMap!,
      orgIdNameMap!,
    ).toPromise();
    if (resp.success) {
      toastApi.success(i18n.chain.orgAdmin.organization.editUserSuccess);
      // 重新加载表格数据
      this.tableController.loadDataList();
    }
    return resp;
  }

  // 批量更新数据
  private async updateDataBatchToService(data: IUsers, originalData: IUsers[]) {
    const { roleIdNameMap, orgIdNameMap } = this.drawerUiDataCache;
    const resp = await this.UserModel.updateUserBatch(
      data as IUsers,
      this.app.getAppId(),
      originalData,
      roleIdNameMap!,
      orgIdNameMap!,
    ).toPromise();
    if (resp.success) {
      toastApi.success(i18n.chain.orgAdmin.organization.batchEditUserSuccess);
      // 重新加载表格数据
      this.tableController.loadDataList();
      this.tableController.changeSelectedDataList([]);
    }
    return { ...resp, result: undefined };
  }

  // 新建数据
  private async addDataToService(data: IUsers) {
    const { roleIdNameMap, orgIdNameMap } = this.drawerUiDataCache;
    const resp = await this.UserModel.createUser(data, this.app.getAppId(), roleIdNameMap!, orgIdNameMap!).toPromise();
    if (resp.success) {
      toastApi.success(i18n.chain.orgAdmin.organization.createUserSuccess);
      // 重新加载表格数据
      this.tableController.loadDataList();
    }
    return resp;
  }

  // 删除数据
  private deleteDataToService = async (data?: IUsers) => {
    const resp = await this.UserModel.deleteUser(data!, this.app.getAppId(), this.app.getUserId()).toPromise();
    if (resp.success) {
      toastApi.success(i18n.chain.orgAdmin.organization.delUserSuccess);
      // 重新加载表格数据
      this.tableController.loadDataList();
    }
    return resp;
  };

  // 解除锁定
  private unLockUserToService = async (data?: IUsers) => {
    const resp = await this.UserModel.unlockUser(data!.id).toPromise();
    resp.success && toastApi.success(i18n.chain.orgAdmin.organization.unlockSuccess);
    return resp;
  };

  // 修改用户状态
  private updateUserStatusToService = async (data?: IUsers) => {
    const { roleIdNameMap, orgIdNameMap } = this.drawerUiDataCache;
    const newUser = { ...data!, disable: !data!.disable };
    const resp = await this.UserModel.updateUser(
      newUser,
      data!,
      this.app.getAppId(),
      roleIdNameMap!,
      orgIdNameMap!,
    ).toPromise();
    resp.success && toastApi.success(i18n.chain.orgAdmin.organization.editUserStatusSuccess);
    this.tableController.loadDataList();
    return resp;
  };

  // --------- 数据类 - 部门 ---------
  private updateOrgs = (data: IOrgs, treeData: IOrgTrees[], flatData: IOrgs[]) => {
    const { title, key } = this.getRootInfo();
    const currentId = this.currentSelectId$.getValue();
    const orgIdNameMap = {
      ...this.drawerUiDataCache.orgIdNameMap,
      [currentId]: { ...data, key: currentId },
    };
    this.pidPathDisplay = getOrgPathWithOrg(currentId, flatData);
    this.drawerUiDataCache = {
      ...this.drawerUiDataCache,
      orgTreeList: [
        { title: <OrgRoot title={title} className="page_organization-user-root" />, key, disabled: true },
        ...treeData,
      ],
      orgIdNameMap,
    };

    this.tableController.loadDataList();
  };

  private createOrgs = (data: IOrgs, treeData: IOrgTrees[]) => {
    const { title, key } = this.getRootInfo();
    this.drawerUiDataCache.orgTreeList = [
      { title: <OrgRoot title={title} className="page_organization-user-root" />, key, disabled: true },
      ...treeData,
    ];
    this.drawerUiDataCache.orgIdNameMap = { ...this.drawerUiDataCache.orgIdNameMap, [data.key]: data };
  };

  private deleteOrgs = (data: IOrgs, treeData: IOrgTrees[]) => {
    const { title, key } = this.getRootInfo();
    this.drawerUiDataCache.orgTreeList = [
      { title: <OrgRoot title={title} className="page_organization-user-root" />, key, disabled: true },
      ...treeData,
    ];
    this.drawerUiDataCache.orgIdNameMap = { ...this.drawerUiDataCache.orgIdNameMap, [data.key]: data };
    this.currentSelectId$.next(this.getRootInfo().key);
    this.pidPathDisplay = '--';
  };

  // --------- 数据类 - 部门用户 ---------
  // 新增部门用户
  private addRoleUserToService = async (data: any) => {
    const current = this.currentSelectId$.getValue();

    const resp = await this.Model.patchOrgUsers(this.app.getAppId(), current, _.map(data, 'userId')).toPromise();
    if (resp.success) {
      toastApi.success(i18n.chain.orgAdmin.organization.addUserSuccess);
      // 重新加载表格数据
      this.tableController.loadDataList();
    }
    return { success: resp.success };
  };

  // 移出部门用户
  private removeRoleUserToService = async (data?: IUsers) => {
    const current = this.currentSelectId$.getValue();
    const body: IByRolesPatch = {};
    if (data?.uuid) {
      body.del_users = [data.uuid];
    }
    const resp = await this.Model.removeOrgUsers(current, body).toPromise();
    if (resp.success) {
      toastApi.success(i18n.chain.orgAdmin.organization.removeUserSuccess);
      // 重新加载表格数据
      this.tableController.loadDataList();
    }
    return resp;
  };

  // 初始化组织和角色列表
  private initOrgAndRoleList = () => {
    const { title, key: rootId } = this.getRootInfo();
    this.isOrgLoading$.next(true);

    return this.UserModel.queryRolesAndOrgs(this.app.getAppId(), rootId).pipe(
      map((value) => {
        const treeData = orgsToOrgTrees(value.orgList, rootId);
        this.drawerUiDataCache.roleList = value.roleList;
        this.drawerUiDataCache.roleIdNameMap = value.roleIdNameMap;
        this.drawerUiDataCache.orgIdNameMap = value.orgIdNameMap;
        this.drawerPermissionsCache.roleIdPermissionMap = value.roleIdPermissionMap;
        this.drawerUiDataCache.orgTreeList = [
          { title: <OrgRoot title={title} className="page_organization-user-root" />, key: rootId, disabled: true },
          ...treeData,
        ];
        this.orgController.init(value.orgList);
        this.isOrgLoading$.next(false);
        return value;
      }),
    );
  };

  private getFilterParams = (value: any[]) => {
    const [name] = value;
    if (name) {
      return { name_like: name };
    }
    return {};
  };
}

export { OrganizationController };
