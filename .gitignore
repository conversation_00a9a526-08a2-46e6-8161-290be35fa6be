# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
node_modules
.pnp
.pnp.js

# testing
coverage

# production
build
dist
!_template/**/dist

# vscode-extension
out

# misc
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local
.eslintcache

npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# ide
.idea
.vscode
.history
.scannerwork
.nx

# husky
.husky/_/*

# html
index.html

#docs
.umi
.docusaurus
.cache-loader
