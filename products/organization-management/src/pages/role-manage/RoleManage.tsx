import { FC } from 'react';
import { ModalWithBtnsCompEmotion } from '@mdtBsComponents/modal-with-btns-comp-emotion';
import { DrawerModifyFormPermissionRole } from '@mdtProMicroModules/containers/drawer-modify-form-permission-role';
import { TableCurdWithSimpleSearch } from '@mdtProMicroModules/containers/table-curd-with-simple-search';
import { RoleManageController } from './RoleManageController';

// 角色管理页面=======================================================================================
interface IProps {
  controller: RoleManageController;
}
const RoleManage: FC<IProps> = ({ controller }) => {
  return (
    <div style={{ padding: '0 20px 20px 20px', height: '100%', width: '100%' }}>
      <TableCurdWithSimpleSearch controller={controller.getTableController()} />
      <DrawerModifyFormPermissionRole controller={controller.getModifyController()} />
      <ModalWithBtnsCompEmotion controller={controller.getDeleteController()} />
    </div>
  );
};

export { RoleManage };
