import { History } from 'history';
import { initBffCommonService } from '@mdtProComm/bff-services';
import { ProductPrefixEnum } from '@mdtProComm/constants';
import { DatlasAppController } from '@mdtProMicroModules/datlas/app/DatlasAppController';
import { RouterController } from '../_util/RouterController';
import { UserPermissionController } from '../_util/UserPermissionController';
import { AppSideMenuController } from './AppSideMenuController';

// @ts-ignore
class AppController extends DatlasAppController<RouterController, UserPermissionController, AppSideMenuController> {
  private constructor(history: History) {
    super({ ignoreGlobalData: true });
    initBffCommonService(ProductPrefixEnum.DATA_MARKET);
    this.routerController = new RouterController(history, this, true);
  }

  protected async afterAuthSuccess() {
    await super.afterAuthSuccess();
    await super.initRequirementData();
    this.initAppReleation();
  }

  // 构造
  private initAppReleation() {
    const upc = new UserPermissionController(this.getUserPermission()!, this.getPermissionController());
    this.userPermissionController = upc;
    // 初始化完必备信息后，构建用户的权限
    this.routerController!.initRoutes();
  }
}

export { AppController };
