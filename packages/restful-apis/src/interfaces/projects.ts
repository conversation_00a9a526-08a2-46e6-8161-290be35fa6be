import { IOrderQuery, IPaginationQuery } from './comm';

export interface IProjectsQuery extends IOrderQuery, IPaginationQuery {
  project_ids?: string; // 用逗号分隔的id字符串
  fuzzy_name?: string; // 项目名称模糊筛选
  name?: string; // 项目名称精确筛选
  with_status?: boolean;
}

export interface IProject {
  project_id: string;
  app_id: number;
  permissions: string[];
  user_id: number;
  name: string;
  description: string;
  update_time?: number;
  create_time: number;
  // 0: 未发布 1: 已发布 2: 有修改
  project_status?: number;
  extra?: { [key: string]: any };
}
