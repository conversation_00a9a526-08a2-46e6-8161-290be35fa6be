import _ from 'lodash';
import { from } from 'rxjs';
import { map, takeWhile } from 'rxjs/operators';
import { queryDatapkgsAsync, queryDatapkgsPaginationTotalAsync } from '@mdtBsServices/datapkgs';
import { IDatapkg, IDatapkgsQuery, IRequestCancelToken, IServerResponse } from '@mdtProComm/interfaces';
import { CommonModel } from '@mdtProComm/models/CommonModel';
import { IDatapkgItem } from '@mdtProMicroModules/containers/table-card-datapkg-search';
import { DatapkgSearchModel } from '../../_util/DatapkgSearchModel';

interface IRespData {
  total?: number;
  list: IDatapkg[];
  appIdNameMap: Record<number, string>;
  taggroupIdNameMap: Record<string, string>;
}

export class AllPkgSearchModel extends DatapkgSearchModel {
  // 查询下一页数据
  public static queryNextPageTickets(data: IDatapkgsQuery, params: any, cancelToken: IRequestCancelToken) {
    return from(
      new Promise<IServerResponse<IRespData>>((resolve) => {
        const request = async () => {
          const resp = await queryDatapkgsAsync(data, { cancelToken, params });
          // 如果成功，则在查询APP的名称
          let list: IDatapkg[] = [];
          let appIdNameMap: Record<number, string> = {};
          let taggroupIdNameMap: Record<string, string> = {};
          if (resp.success) {
            list = resp.data!;
            appIdNameMap = await this.getAppIdNameMap(list);
          }
          resolve({
            success: resp.success,
            canceled: resp.canceled,
            data: { list, appIdNameMap, taggroupIdNameMap },
          });
        };
        request();
      }),
    ).pipe(
      takeWhile((v) => !v.canceled),
      map((resp) => {
        const { list, appIdNameMap, taggroupIdNameMap } = resp.data!;
        return _.map(list, (it) => {
          const appName = `${appIdNameMap[it.app_id] || it.app_id}`;
          return this.transformToDatapkgItem(it, appName, taggroupIdNameMap);
        });
      }),
    );
  }

  // 查询第一页数据
  public static queryFirstPagePkgs(data: IDatapkgsQuery, params: any, cancelToken: IRequestCancelToken) {
    return from(
      new Promise<IServerResponse<IRespData>>((resolve) => {
        const request = async () => {
          const resp = await queryDatapkgsPaginationTotalAsync(data, {
            cancelToken,
            params,
          });
          // 如果成功，则在查询App的名称
          let total = 0;
          let list: IDatapkg[] = [];
          let appIdNameMap: Record<number, string> = {};
          let taggroupIdNameMap: Record<string, string> = {};
          if (resp.success) {
            const { total_count, dataResult } = resp.data!;
            list = dataResult;
            total = total_count;
            appIdNameMap = await this.getAppIdNameMap(list);
          }
          resolve({
            success: resp.success,
            canceled: resp.canceled,
            data: { total, list, appIdNameMap, taggroupIdNameMap },
          });
        };
        request();
      }),
    ).pipe(
      takeWhile((v) => !v.canceled),
      map((resp) => {
        const { total, list, appIdNameMap, taggroupIdNameMap } = resp.data!;
        const data = _.map(list, (it) => {
          const appName = `${appIdNameMap[it.app_id] || it.app_id}`;
          return this.transformToDatapkgItem(it, appName, taggroupIdNameMap);
        });
        return [total!, data] as [number, IDatapkgItem[]];
      }),
    );
  }

  // 请求App名称
  private static async getAppIdNameMap(list: IDatapkg[], cancelToken?: IRequestCancelToken) {
    return CommonModel.getAppIdNameMap(_.map(list, 'app_id'), cancelToken);
  }
}

export type IAllPkgSearchModel = typeof AllPkgSearchModel;
