import _ from 'lodash';
import { from } from 'rxjs';
import { Observable } from 'rxjs/internal/Observable';
import { map } from 'rxjs/operators';
import { IBusinessResult } from '@mdtBsComm/interfaces';
import {
  IExternalInstitution,
  ITableData,
  ITableExternalInstitutionModel,
} from '@mdtProMicroModules/containers/table-external-institution';
import { AppController } from '../../app/AppController';
import { appBffService, entityBffService } from '../../bff-service';

export class ExternalInstitutionModelBff implements ITableExternalInstitutionModel {
  private appId: number;

  public constructor(app: AppController) {
    this.appId = app.getAppId();
  }

  // 查询外部机构列表
  public queryInstitutionList(): Observable<ITableData[]> {
    const params = {
      resource_type: 'app',
      entity_type: 'app',
      privilege_type: 'read',
      resource_ids: `${this.appId}`,
    };

    return from(appBffService.queryExternalAppList({ params })).pipe(
      map((resp) => {
        return _.map(resp.success ? resp.page_data : [], (app) => ({
          id: app.app_id,
          name: app.name,
        }));
      }),
    );
  }

  // 创建
  public createInstitution(data: IExternalInstitution): Observable<IBusinessResult<ITableData>> {
    return from(
      appBffService.bindExternalApp({
        data: {
          resource_ids: [`${this.appId}`],
          grantee_name_id_pair: [
            {
              grantee_id: data.id,
              grantee_name: data.name,
              grantee_app_id: data.id,
            },
          ],
        },
      }),
    ).pipe(
      map((resp) => {
        return {
          success: resp.success,
          result: resp.success ? data : undefined,
        };
      }),
    );
  }

  // 删除
  public deleteInstitution(data: ITableData): Observable<IBusinessResult<ITableData>> {
    return from(
      appBffService.unbindExternalApp({
        data: {
          resource_ids: [`${this.appId}`],
          grantee_ids: [`${data.id}`],
        },
      }),
    ).pipe(
      map((deleteResp) => {
        return {
          success: deleteResp.success,
          result: deleteResp.success ? data : undefined,
        };
      }),
    );
  }

  // 通过机构id查询名称
  public queryNameById(id: string): Observable<string> {
    return from(entityBffService.queryAppNameByIds({ ids: [id] })).pipe(
      map((resp) => (resp.success ? _.get(resp, 'page_data[0].name') : '')),
    );
  }
}
