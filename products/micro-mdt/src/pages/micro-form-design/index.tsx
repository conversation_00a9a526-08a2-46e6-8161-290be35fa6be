import { FC, memo } from 'react';
import { ModuleIdEnum } from '@mdtProComm/constants';
import { getMicroAppProps, getSubAppName } from '../../_util/util';
import { MICRO_FORM_DESIGN } from '../../config';

const MicroFormDesign: FC<{ product: string }> = memo(({ product }) => {
  const appName = getSubAppName(product, ModuleIdEnum.FORM_DESIGN);
  const props = getMicroAppProps(appName, MICRO_FORM_DESIGN);
  return <micro-app {...props} />;
});

export { MicroFormDesign };
