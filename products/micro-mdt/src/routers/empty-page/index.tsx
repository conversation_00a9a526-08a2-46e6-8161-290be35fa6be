import { EmptyPage as ComEmptyPage } from '@mdtBsComm/components/empty-page';
import { useAppContext } from '../../app/appContext';
import { SSO_LOGOUT_URL } from '../../config';

const EmptyPage = () => {
  const { appController } = useAppContext();

  const goBack = () => {
    SSO_LOGOUT_URL && appController.redirectToSso(SSO_LOGOUT_URL, true);
  };

  return <ComEmptyPage backFunc={goBack} />;
};

export default EmptyPage;
