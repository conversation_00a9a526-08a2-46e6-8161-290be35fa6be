{"extends": ["stylelint-config-standard", "stylelint-config-css-modules", "stylelint-config-rational-order-fix", "stylelint-prettier/recommended"], "customSyntax": "postcss-less", "plugins": ["stylelint-order", "stylelint-config-rational-order-fix/plugin", "stylelint-declaration-strict-value", "stylelint-declaration-block-no-ignored-properties"], "rules": {"scale-unlimited/declaration-strict-value": "color", "plugin/declaration-block-no-ignored-properties": true, "no-empty-source": true, "max-empty-lines": 1, "declaration-empty-line-before": "never", "selector-class-pattern": ["^([a-z][a-z0-9]*)([-_][a-z0-9]+)*", {"message": "Expected class selector to be kebab-case!!!"}], "max-nesting-depth": [5, {"ignorePseudoClasses": ["/^global/", "global"]}], "function-no-unknown": [true, {"ignoreFunctions": ["constant"]}]}}