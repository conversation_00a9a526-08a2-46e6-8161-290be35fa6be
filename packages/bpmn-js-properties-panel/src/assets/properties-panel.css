.bio-properties-panel-button {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 2px;
}

.more-btns-wrap {
  display: flex;
  align-items: center;
}

.bio-properties-panel-button-icon.bpmn-icon-service {
  margin-left: 5px;
  font-size: 24px;
  cursor: pointer;
}

.bio-properties-panel-button-icon.bpmn-icon-data-object {
  font-size: 16px;
  cursor: pointer;
}

.bio-properties-panel-label-with-help {
  display: flex;
  align-items: center;
  margin-bottom: 2px;
}

.bpmn-icon-help {
  width: 24px;
  height: 24px;
}

.bpmn-icon-help::before {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  margin-top: 3px;
  margin-left: 4px;
  font-size: 12px;
  border: 1px solid var(--dmc-text-300-color);
  border-radius: 11px;
  content: '?';
}

/* tooltip start */
.bio-tooltip-wrap {
  position: absolute;
  top: 0;
  left: -1000px;
  z-index: 1060;
}

.bio-tooltip-content {
  position: absolute;
  bottom: 0;
  box-sizing: border-box;
  max-width: 350px;
  transform: translateX(-50%);
  visibility: visible;
}

.bio-tooltip-arrow {
  position: absolute;
  bottom: -12px;
  left: 50%;
  display: block;
  width: 12px;
  height: 12px;
  margin-left: -6px;
  overflow: hidden;
  pointer-events: none;
}

.bio-tooltip-arrow::before {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  display: block;
  width: 8px;
  height: 8px;
  margin: auto;
  background-color: var(--dmc-tooltip-bg-color);
  transform: translateY(-6px) rotate(45deg);
  content: '';
  pointer-events: auto;
}

.bio-tooltip-inner {
  box-sizing: border-box;
  padding: 6px 15px 6px 18px;
  color: var(--dmc-text-white-color);
  font-size: 13px;
  line-height: 18px;
  text-align: left;
  text-decoration: none;
  background-color: var(--dmc-tooltip-bg-color);
  border-radius: 6px;
  box-shadow: none;
}

.bio-properties-panel-input:disabled {
  color: var(--metro-text-2);
  background-color: var(--dmc-bg-color);
  border-color: transparent;
}

/* tooltip end */
