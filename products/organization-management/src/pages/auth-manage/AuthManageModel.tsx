import _ from 'lodash';
import { Dayjs } from 'dayjs';
import { from, Observable, of } from 'rxjs';
import { map, takeWhile } from 'rxjs/operators';
import { IRequestCancelToken, IRole, IRolePut, IUserPut, IUserPutAdmin } from '@mdtApis/interfaces';
import { IBusinessResult } from '@mdtBsComm/interfaces';
import { putRoleAsync, putUserAsync, queryRolesAsync } from '@mdtBsServices/auth';
import { RoleTypeEnum } from '@mdtProComm/constants';
import { IOrgs, IOrgTrees } from '@mdtProComm/interfaces';
import { orgsToOrgTrees } from '@mdtProComm/utils/orgUtil';
import { IPermission } from '@mdtProMicroModules/containers/drawer-modify-form-permission-only';
import { GroupPermissionModel } from '@mdtProMicroModules/models/GroupPermissionModel';

// 通用角色ID
export const COMMON_ROLE_ID = 0;

export interface ITableUserData {
  id: number;
  name: string;
  role: number[];
  expireTime: Dayjs;
  expireTimeDisplay: string;
  permission: number[];
  disable: boolean;
  disableDisplay: string;
  uuid: string;
  organization: number[];
  orgDisplay?: string[];
  roleDisplay?: string[];
}

export interface ITableRoleData {
  id: number;
  name: string;
  description: string;
  permission: number[];
}

export interface IUsersRespData {
  userList: ITableUserData[];
  roleIdPermissionMap: Record<number, number[]>;
  orgCountMap: Record<number, number>;
}

class AuthMangeModel extends GroupPermissionModel {
  // 角色结构翻译
  public static transformToTableRoleData(item: IRole): ITableRoleData {
    return {
      id: item.id,
      name: item.name,
      description: item.description || '',
      permission: item.permission || [],
    };
  }

  // 组织翻译
  public static translateOriginData = (data: IRole[] = [], rootId = ''): IOrgs[] => {
    return _.map(data, ({ id, name, parent_id, permission }) => {
      const result: IOrgs = {
        permission: permission || [],
        key: _.toString(id),
        title: name,
      };
      if (parent_id?.length) {
        result['pid'] = _.toString(parent_id[0]);
      } else {
        result['pid'] = rootId;
      }
      return result;
    });
  };

  public static queryAllRoles(appId: number, cancelToken?: IRequestCancelToken) {
    const cnf = { cancelToken, params: { app_id: appId, role_type: RoleTypeEnum.NORMAL } };
    return from(queryRolesAsync(cnf)).pipe(
      takeWhile((resp) => !resp.canceled),
      map((resp) => {
        return _.map(
          _.filter(resp.data, ({ app_id: appId }) => appId !== COMMON_ROLE_ID),
          (it) => this.transformToTableRoleData(it),
        );
      }),
    );
  }

  public static queryAllOrgs(appId: number, cancelToken?: IRequestCancelToken) {
    const cnf = { cancelToken, params: { app_id: appId, role_type: RoleTypeEnum.ORGANIZATION } };
    return from(queryRolesAsync(cnf)).pipe(
      takeWhile((resp) => !resp.canceled),
      map((resp) => {
        return orgsToOrgTrees(this.translateOriginData(resp.data), '');
      }),
    );
  }

  public static updateUser(
    data: IPermission,
    originalData: ITableUserData,
    appId: number,
  ): Observable<IBusinessResult<ITableUserData>> {
    const postData: IUserPut = { id: originalData.id };
    const putData: IUserPutAdmin = { app_id: appId };
    // 处理权限
    const nps = _.difference(data, originalData.permission);
    const dps = _.difference(originalData.permission, data);
    nps.length && (putData.new_permission = nps);
    dps.length && (putData.del_permission = dps);
    // 判断是否有更改，如果没有则不追加
    _.size(putData) > 1 && (postData.admin = [putData]);
    // 如果未做改变，则直接关闭窗口
    if (_.size(postData) === 1) {
      return of({ success: true, result: originalData });
    }
    return from(putUserAsync(postData)).pipe(
      map((resp) => {
        if (resp.success) {
          return {
            success: true,
            result: { ...originalData, permission: data },
          };
        }
        return { success: false };
      }),
    );
  }

  public static updateRole(
    data: IPermission,
    originalData: ITableRoleData,
  ): Observable<IBusinessResult<ITableRoleData>> {
    const postData: IRolePut = { id: originalData.id, role_type: RoleTypeEnum.NORMAL };
    if (!_.isEqual(data, originalData.permission)) {
      postData.permission = data;
    }
    // 如果未做改变，则直接关闭窗口
    if (_.size(postData) === 1) {
      return of({ success: true, result: originalData });
    }
    return from(putRoleAsync(postData)).pipe(
      map((resp) => {
        if (resp.success) {
          const td = { ...originalData, permission: data };
          return { success: true, result: td };
        }
        return { success: false };
      }),
    );
  }

  public static updateOrg(data: IPermission, originalData: IOrgTrees): Observable<IBusinessResult<IOrgTrees>> {
    const postData: IRolePut = { id: _.toNumber(originalData.key), role_type: RoleTypeEnum.ORGANIZATION };
    if (!_.isEqual(data, originalData.permission)) {
      postData.permission = data;
    }
    // 如果未做改变，则直接关闭窗口
    if (_.size(postData) === 1) {
      return of({ success: true, result: originalData });
    }
    return from(putRoleAsync(postData)).pipe(
      map((resp) => {
        if (resp.success) {
          const td = { ...originalData, permission: data };
          return { success: true, result: td };
        }
        return { success: false };
      }),
    );
  }
}

export { AuthMangeModel };
export type IAuthManageModel = typeof AuthMangeModel;
