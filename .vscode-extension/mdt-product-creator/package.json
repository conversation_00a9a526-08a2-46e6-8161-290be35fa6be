{"name": "mdt-product-creator", "displayName": "MDT Product Creator", "description": "创建MDT新产品的工具", "version": "0.1.2", "publisher": "<PERSON><PERSON><PERSON><PERSON>", "engines": {"vscode": "^1.60.0"}, "categories": ["Other"], "activationEvents": ["*"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "mdt-product-creator.createProduct", "title": "MDT: 创建新产品"}]}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "pretest": "npm run compile && npm run lint", "lint": "eslint src --ext ts", "test": "node ./out/test/runTest.js", "package": "npm run compile && vsce package"}, "repository": {"type": "git", "url": "https://github.com/mdtai/mdt-product-creator.git"}, "keywords": ["mdt", "product", "creator", "frontend", "monorepo"], "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "icon": "images/icon.png", "galleryBanner": {"color": "#1E88E5", "theme": "dark"}, "devDependencies": {"@types/vscode": "^1.60.0", "@types/glob": "^7.1.3", "@types/node": "^14.14.37", "eslint": "^7.19.0", "glob": "^7.1.6", "typescript": "^4.2.4", "vscode-test": "^1.5.2"}}