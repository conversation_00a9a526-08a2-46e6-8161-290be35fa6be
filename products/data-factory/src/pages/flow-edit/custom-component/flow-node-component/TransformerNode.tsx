import React from 'react';
import type { NsGraph } from '@antv/xflow';
import { EditorName } from './EditorName';
import { StatusIcon } from './StatusIcon';

const TransformerNode: NsGraph.INodeRender = ({ data }) => {
  return (
    <div className="node-box">
      <EditorName
        className="transformer-node"
        id={data.id}
        title={data.label}
        enableRun={true}
        isRunNode$={data.isRunNode$}
        editingTitle={data.editingTitle}
      />
      <div className="node-tooltip-icon">
        <StatusIcon status={data.status} statusMsg={data.statusMsg} />
      </div>
    </div>
  );
};

export default TransformerNode;
