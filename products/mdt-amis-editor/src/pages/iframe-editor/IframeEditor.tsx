import React, { FC } from 'react';
import { useLoadFinish } from '../../_util/useLoadFinish';
import { useParentMessage } from '../../_util/useParentMessage';
import { Editor } from '../../containers/editor';
import { IFrameEditorController } from './IFrameEditorController';

export const IframeEditor: FC<{ controller: IFrameEditorController }> = ({ controller }) => {
  useParentMessage(controller);

  useLoadFinish(controller);

  return <Editor key={Math.random()} controller={controller as any} />;
};
