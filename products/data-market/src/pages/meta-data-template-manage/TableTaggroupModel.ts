import _ from 'lodash';
import { from, Observable } from 'rxjs';
import { map, takeWhile } from 'rxjs/operators';
import { IBusinessResult } from '@mdtBsComm/interfaces';
import { ITaggroup, ITaggroupPost } from '@mdtBsServices/interfaces';
import { deleteTaggroupAsync, postTaggroupAsync, putTaggroupAsync, queryTaggroupsAsync } from '@mdtBsServices/taggroup';
import { IFormData } from '@mdtProMicroModules/containers/dialog-modify-form-taggroup';
import { ITableData } from '@mdtProMicroModules/containers/table-taggroup';
import { AppController } from '../../app/AppController';

export class TableTaggroupModel {
  public static queryPermissions() {
    return AppController.getInstance().getUserPermissionController().getTaggroupManagePermission();
  }

  public static queryTaggroupList(): Observable<ITableData[]> {
    const apps = AppController.getInstance().getAppId();
    return from(queryTaggroupsAsync({ params: { apps } })).pipe(
      takeWhile((resp) => !resp.canceled),
      map((resp) => {
        if (!resp.success) return [];
        const dataWithoutDefault = _.reject(resp.data, ['name', '_default_']);
        return _.map(dataWithoutDefault, (item) => this.transformToTableData(item));
      }),
    );
  }

  public static createTaggroup(createData: IFormData): Observable<IBusinessResult<ITableData>> {
    const { name, tags } = createData;
    const postData: ITaggroupPost = { name, choices: tags };
    return from(postTaggroupAsync(postData)).pipe(
      map((resp) => {
        return {
          success: resp.success,
          result: resp.success ? this.transformToTableData(resp.data!) : undefined,
        };
      }),
    );
  }

  public static updateTaggroup(
    updateData: IFormData,
    originalUpdateData: ITableData,
  ): Observable<IBusinessResult<ITableData>> {
    const { name, tags } = updateData;
    const { id } = originalUpdateData;
    const postData = { name, choices: tags };
    return from(putTaggroupAsync(id, postData)).pipe(
      map((res) => {
        return {
          success: res.success,
          result: res.success ? this.transformToTableData(res.data!) : undefined,
        };
      }),
    );
  }

  public static deleteTaggroup(deleteData: ITableData): Observable<IBusinessResult<ITableData>> {
    return from(deleteTaggroupAsync(deleteData.id)).pipe(
      map((resp) => {
        return {
          success: resp.success,
          result: resp.success ? deleteData : undefined,
        };
      }),
    );
  }

  private static transformToTableData(tagGroup: ITaggroup): ITableData {
    return {
      id: tagGroup.id,
      name: tagGroup.name,
      tags: tagGroup.choices,
    };
  }
}
