import { FC } from 'react';
import { useObservableState } from '@mdtBsComm/hooks/use-observable-state';
import { DatapkgDetailFactory } from '@mdtProMicroModules/pages/datapkg-detail-factory';
import { PersonalDatapkgDetailFactoryController } from './PersonalDatapkgDetailFactoryController';

interface IProps {
  controller: PersonalDatapkgDetailFactoryController;
}

const PersonalDatapkgDetailFactory: FC<IProps> = ({ controller }) => {
  const pkgId = useObservableState(controller.getPkgId$());
  return <DatapkgDetailFactory key={pkgId} controller={controller.getDetailFactoryController()} />;
};

export { PersonalDatapkgDetailFactory };
