import _ from 'lodash';
import { DATE_FORMATTER_1, formateDate } from '@mdtBsComm/utils/dayUtil';
import { formatNumberK } from '@mdtBsComm/utils/numberUtil';
import { IDatapkg, IRequestCancelToken, ITaggroupTags } from '@mdtProComm//interfaces';
import { DatapkgPermissionEnum } from '@mdtProComm/constants';
import { CommonModel } from '@mdtProComm/models/CommonModel';
import {
  getDatapkgGeometryTypeColor,
  getDatapkgGeometryTypeIcon,
  getDatapkgGeometryTypeLabel,
  getDatapkgPermissionLabels,
} from '@mdtProComm/utils/datapkgUtil';
import { IDatapkgItem, TableCardDatapkgSearchModel } from '@mdtProMicroModules/containers/table-card-datapkg-search';

export abstract class DatapkgSearchModel extends TableCardDatapkgSearchModel {
  public static transformToDatapkgItem(
    pkg: IDatapkg,
    appName: string,
    taggroupIdNameMap: Record<string, string>,
  ): IDatapkgItem {
    const type = pkg.geometry_type;
    return {
      id: pkg.id,
      name: pkg.name,
      geoTypeDisplay: getDatapkgGeometryTypeLabel(type),
      geoTypeIcon: getDatapkgGeometryTypeIcon(type),
      geoTypeColor: getDatapkgGeometryTypeColor(type),
      updateTimeDisplay: formateDate(pkg.update_time * 1000, DATE_FORMATTER_1),
      count: formatNumberK(pkg.count || 0),
      source: this.getSource(taggroupIdNameMap), // 理论不应该展示，先不显示
      appName,
      permissonLabels: getDatapkgPermissionLabels(_.difference(pkg.permissions, [DatapkgPermissionEnum.UPDATE])),
    };
  }

  // 请求标签组名称
  public static async getTaggroupIdNameMap(list: IDatapkg[], cancelToken?: IRequestCancelToken, apps?: string) {
    return CommonModel.getTaggroupIdNameMap(
      _.flatMap(list, (it) => _.map(it.tags, 'taggroup_id')),
      cancelToken,
      apps,
    );
  }

  private static getSource(taggroupIdNameMap: Record<string, string>, tags?: ITaggroupTags[]) {
    let rslt = '-';
    // 判断是否有供应商标签组
    _.forEach(tags, (it) => {
      const label = taggroupIdNameMap[it.taggroup_id];
      if (label === '数据供应商') {
        rslt = _.get(it.taggroup_tags, '[0]', '-');
      }
    });
    return rslt;
  }
}
