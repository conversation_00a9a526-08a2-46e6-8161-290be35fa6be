import _ from 'lodash';
import { connect, mapProps, mapReadPretty } from '@formily/react';
import { useCreation } from 'ahooks';
import { Observable, of } from 'rxjs';
import { map, skip } from 'rxjs/operators';
import {
  DataListCompTableWithCurd,
  IDynamicOptions,
  IVirtualizedTableCurdProps,
} from '@mdtBsComponents/data-list-comp-table-curd';
import { TableWithCurdFormilyController } from './DatapkgColumnListController';
import './index.less';

type IDataList = any[] | ((params?: any) => Observable<[number, any[]]>);

export interface IDatapkgColumnListProps {
  value?: any[];
  onChange?: (value: any[]) => void;
  className?: string;
  dataList: IDataList;
  tableOptions: IDynamicOptions<IVirtualizedTableCurdProps<any>>;
  prettyTableOptions?: IDynamicOptions<IVirtualizedTableCurdProps<any>>;
}

export const mergeDataListWithValue = (dataList: any[], value?: any[]) => {
  _.forEach(value, (v) => {
    const val = _.find(dataList, (it) => it.id === v.id);
    val && _.assign(val, v);
  });
  return dataList;
};

const mergeDataListFunc = (dataList: IDataList, value?: any[]): Observable<[number, any[]]> => {
  if (!dataList) return of([0, []]);
  if (_.isArray(dataList)) {
    return of([0, mergeDataListWithValue(dataList, value)]);
  }
  return dataList().pipe(
    map((result) => {
      const [total, data] = result;
      return [total, mergeDataListWithValue(data, value)];
    }),
  );
};

const TableWithCurdFormilyPreview = (props: IDatapkgColumnListProps) => {
  const { value, tableOptions, prettyTableOptions, dataList, className } = props;

  return useCreation(() => {
    const controller = new TableWithCurdFormilyController({
      loadDataListFunc: () => mergeDataListFunc(dataList, value),
      tableOptions: prettyTableOptions || tableOptions,
    });
    return <DataListCompTableWithCurd controller={controller} className={className} />;
  }, [dataList]);
};

export const TableWithCurdFormily = connect(
  (props: IDatapkgColumnListProps) => {
    const { value, onChange, tableOptions, dataList, className } = props;

    return useCreation(() => {
      if (!tableOptions) return null;
      const controller = new TableWithCurdFormilyController({
        loadDataListFunc: () => mergeDataListFunc(dataList, value),
        tableOptions,
      });

      controller
        .getDataList$()
        .pipe(skip(1))
        .subscribe(() => {
          onChange?.(controller.getDataListValue());
        });

      return <DataListCompTableWithCurd controller={controller} className={className} />;
    }, [dataList, tableOptions]);
  },
  mapProps({ dataSource: 'dataList' }),
  mapReadPretty(TableWithCurdFormilyPreview),
);
