import _ from 'lodash';
import copy from 'copy-to-clipboard';
import { nanoid } from 'nanoid';
import { BehaviorSubject, of } from 'rxjs';
import toastApi from '@datlas/design/esm/components/toast';
import { RequestController } from '@mdtBsControllers/request-controller';
import { AccessLevelEnum, TaskStatusEnum } from '@mdtProComm/constants';
import {
  CardCurdWithSimpleSearchController,
  ILoadDataListRslt,
} from '@mdtProMicroModules/containers/card-curd-with-simple-search';
import { DrawerCustomLoginCreateController } from '@mdtProMicroModules/containers/drawer-custom-login-create';
import { DrawerCustomLoginFormController } from '@mdtProMicroModules/containers/drawer-cutsom-login-form';
import { removeCustomLoginFromStore, saveCustomLoginToStore } from '@mdtProMicroModules/utils/storageUtil';
import { CreateUploadFileTask } from '@mdtProTasks/CreateUploadFileTask';
import { AppController } from '../../app/AppController';
import { SSO_LOGOUT_URL } from '../../config';
import i18n from '../../languages';
import { CardItemView } from './CustomLogin';
import { ICustomLoginModel } from './CustomLoginModel';

export interface ICustomLogin {
  title: string;
  id?: string;
  config: string;
  fileId?: string;
}

export interface ICustomLoginPreferences {
  template: ICustomLogin[];
  active: string;
}

export enum CreateEnum {
  UPLOAD = 'upload',
  MANUAL = 'manual',
}

export enum OperatorEnum {
  DEFAULT = 'default',
  COPY = 'copy',
  DELETE = 'delete',
  EDIT = 'edit',
  CANCEL_DEFAULT = 'cancelDefault',
}

const OPERATOR_OPTIONS = [
  {
    title: i18n.chain.orgAdmin.customLogin.setDefault,
    key: OperatorEnum.DEFAULT,
    icon: 'checked',
  },
  {
    title: i18n.chain.orgAdmin.customLogin.editTemplate,
    key: OperatorEnum.EDIT,
    icon: 'edit',
  },
  {
    title: i18n.chain.orgAdmin.customLogin.cancelDefault,
    key: OperatorEnum.CANCEL_DEFAULT,
    icon: 'close',
  },
  {
    title: i18n.chain.orgAdmin.customLogin.copyLink,
    key: OperatorEnum.COPY,
    icon: 'page',
    divider: true,
  },
  {
    title: i18n.chain.orgAdmin.customLogin.deleteTemplate,
    key: OperatorEnum.DELETE,
    icon: 'delete-2',
    danger: true,
  },
];

export class CustomLoginController extends RequestController {
  private app: AppController;
  private Model: ICustomLoginModel;
  private lists$ = new BehaviorSubject<ICustomLogin[]>([]);
  private defaultKey$ = new BehaviorSubject<string>('');
  private uploadController: DrawerCustomLoginCreateController;
  private manualController: DrawerCustomLoginFormController;
  private listController: CardCurdWithSimpleSearchController;

  public constructor(app: AppController, Model: ICustomLoginModel) {
    super();
    this.app = app;
    this.Model = Model;

    const preferenceController = app.getPreferencesController();

    this.defaultKey$.next(preferenceController.getCustomLoginActive());
    this.uploadController = new DrawerCustomLoginCreateController({ clickOkBtnFunc: this.clickOkBtnFunc });
    this.manualController = new DrawerCustomLoginFormController({
      ssoUrl: SSO_LOGOUT_URL,
      clickOkBtnFunc: this.formClickOkBtnFunc,
    });
    this.listController = new CardCurdWithSimpleSearchController({
      dataListCompCardCurdControllerOptions: {
        dataListCompControllerOptions: {
          dataListControllerOptions: {
            loadDataListFunc: () => {
              return of([0, preferenceController.getCustomLoginTemplate()]) as ILoadDataListRslt<any>;
            },
          },
          compOptions: this.initCardComOptions(),
        },
        cardItemViewController: () => this,
      },
      headerOptions: this.initCardHeaderOptions(),
    });
    this.listController.loadDataList();
  }

  public destroy(): void {
    this.lists$.complete();
    this.defaultKey$.complete();
    this.uploadController.destroy();
    this.manualController.destroy();
    this.listController.destroy();
    this.app = null!;
    this.Model = null!;
  }

  public getCreateController() {
    return this.uploadController;
  }

  public getManualController() {
    return this.manualController;
  }

  public getListController() {
    return this.listController;
  }

  public getLists$() {
    return this.lists$;
  }

  public getDefaultKey$() {
    return this.defaultKey$;
  }

  public getIsDark() {
    return this.app.getTheme() === 'dark';
  }

  public updateLists(data: ICustomLogin[]) {
    this.lists$.next(data);
  }

  public onCreate = (key: CreateEnum) => {
    if (key === CreateEnum.UPLOAD) {
      this.uploadController.openModal();
    }
    if (key === CreateEnum.MANUAL) {
      this.manualController.openModal();
    }
  };

  public setDefaultKey = async (id: string) => {
    const pfController = this.app.getPreferencesController();
    const pf: ICustomLoginPreferences = { template: pfController.getCustomLoginTemplate(), active: id };
    const resp = await this.Model.putCustomLogin(pf).toPromise();
    if (resp.success) {
      this.updateCustomLoginActiceFunc(id);
      this.defaultKey$.next(id);
    }
  };

  public getOptions = (item: ICustomLogin) => {
    const { id } = item;
    const defaultKey = this.defaultKey$.getValue();
    return _.filter(OPERATOR_OPTIONS, ({ key }) => {
      if (_.includes([OperatorEnum.CANCEL_DEFAULT, OperatorEnum.DEFAULT], key)) {
        return _.isEqual(defaultKey, id)
          ? _.isEqual(key, OperatorEnum.CANCEL_DEFAULT)
          : _.isEqual(key, OperatorEnum.DEFAULT);
      }
      return true;
    });
  };

  public handeOperator = async (key: string, item: ICustomLogin) => {
    switch (key) {
      case OperatorEnum.DEFAULT:
        this.setDefaultKey(item.id!);
        break;
      case OperatorEnum.CANCEL_DEFAULT:
        this.setDefaultKey('');
        break;
      case OperatorEnum.COPY:
        this.copyLink(item.config);
        break;
      case OperatorEnum.DELETE:
        this.removeTemplate(item);
        break;
      case OperatorEnum.EDIT:
        await this.onEdit(item);
        break;
    }
  };

  // 卡片的相关属性
  private initCardComOptions = () => {
    return {
      itemWidth: 196,
      itemHeight: 170,
      itemKey: 'id',
      cardItemViewController: () => this,
      CardItemView: CardItemView,
    };
  };

  private initCardHeaderOptions = () => {
    return {
      createBtnLabel: '',
      hideInput: true,
      title: '',
    };
  };

  private updateCustomLoginActiceFunc(config: string) {
    const pfController = this.app.getPreferencesController();
    pfController.updateCustomLoginActive(config);
    // key变化去更新存储
    const prefConfig = pfController.getDefaultCustomLoginTemplate()?.config;
    prefConfig ? saveCustomLoginToStore(prefConfig) : removeCustomLoginFromStore();
  }

  private removeTemplate = async (item: ICustomLogin, noToast?: boolean) => {
    const { config, id: itemId } = item;
    const pfController = this.app.getPreferencesController();
    const pfActive = pfController.getCustomLoginActive();
    const fileId = config.match(/\/([^\/]+)\./)?.[1];

    const pf: ICustomLoginPreferences = {
      template: _.filter(pfController.getCustomLoginTemplate(), ({ id }) => !_.isEqual(id, itemId)),
      active: _.isEqual(pfActive, itemId) ? '' : pfActive,
    };
    const response = await this.Model.putCustomLogin(pf).toPromise();
    if (response.success) {
      this.app.getPreferencesController().updateCustomLoginTemplate(pf.template);
      this.updateCustomLoginActiceFunc(pf.active);
      !noToast && toastApi.success(i18n.chain.orgAdmin.customLogin.removeSuccess);
    }
    this.listController.loadDataList();
    fileId && this.Model.deleteFile(fileId);
  };

  private copyLink = (config: string) => {
    const pfController = this.app.getPreferencesController();
    const customUrl = pfController.appendCustomLoginParam(SSO_LOGOUT_URL, config);
    copy(this.app.appendForceParam(customUrl));
    toastApi.success(i18n.chain.orgAdmin.customLogin.copy);
  };

  private formClickOkBtnFunc = async (obj: Omit<ICustomLogin, 'config'> & { config: any }, defaultConfig: boolean) => {
    const file = this.jsonToJsFile(obj);
    const isEdit = obj.id;
    if (isEdit) {
      // @ts-ignore
      await this.removeTemplate({ ...obj, config: obj.fileId }, true);
    }
    return this.clickOkBtnFunc(
      { title: obj.title, config: file, id: obj.id },
      defaultConfig,
      i18n.chain.orgAdmin.customLogin.editSuccess,
    );
  };

  private jsonToJsFile = (obj: { title: string; config: any }) => {
    const jsContent = `window.__DM_SSO_CFS = ${JSON.stringify(obj.config, null, 2)};`;
    const blob = new Blob([jsContent], { type: 'text/javascript' });
    return new File([blob], obj.title, { type: 'text/javascript' });
  };

  private onEdit = async (data: ICustomLogin) => {
    const config = await this.Model.getConfigUrlWithPath(data.config).toPromise();
    const pattern = /window\.\__DM_SSO_CFS\s*=\s*({[\s\S]*?});/;
    const matchJson = pattern.exec(config)?.[1];
    // eslint-disable-next-line no-eval
    const jsonData = eval('(' + matchJson + ')');
    this.manualController.openModal({
      ...data,
      config: jsonData,
      fileId: data.config,
      defaultConfig: _.isEqual(data.id, this.defaultKey$.getValue()),
    });
  };

  private clickOkBtnFunc = (
    obj: { title: string; config: File; id?: string },
    defaultConfig: boolean,
    toastMsg = i18n.chain.orgAdmin.customLogin.createSuccess,
  ) => {
    const pfController = this.app.getPreferencesController();
    const newId = obj.id ?? nanoid(10);
    const uploadTask = new CreateUploadFileTask(obj.config, 'code', { accessLevel: AccessLevelEnum.ANYONE });
    uploadTask.taskResp$.subscribe(async (resp) => {
      const status = resp.status;
      if (status === TaskStatusEnum.SUCCESSFUL) {
        const active = defaultConfig ? newId : pfController.getCustomLoginActive();
        const newValue = { id: newId, title: obj.title, config: resp.publicPath };
        const pf: ICustomLoginPreferences = {
          template: _.concat(pfController.getCustomLoginTemplate(), newValue),
          active,
        };
        const response = await this.Model.putCustomLogin(pf).toPromise();
        if (response.success) {
          this.app.getPreferencesController().updateCustomLoginTemplate(pf.template);
          this.updateCustomLoginActiceFunc(pf.active);
          defaultConfig && this.defaultKey$.next(newId);
          toastApi.success(toastMsg);
        }
        this.listController.loadDataList();
      } else if (status === TaskStatusEnum.FAILED) {
        toastApi.info(i18n.chain.orgAdmin.customLogin.fileUploadError);
      }
    });
    return Promise.resolve({ success: true });
  };
}
