import { FC } from 'react';
import { useObservableState } from '@mdtBsComm/hooks/use-observable-state';
import { ModalWithBtnsCompDrawer } from '@mdtBsComponents/modal-with-btns-comp-drawer';
import Button from '@mdtDesign/button';
import { Dropmenu } from '@mdtDesign/dropdown';
import Tag from '@mdtDesign/tag';
import { CardCurdWithSimpleSearch } from '@mdtProMicroModules/containers/card-curd-with-simple-search';
import i18n from '../../languages';
import darkCard from './resources/card-dark.png';
import lightCard from './resources/card-light.png';
import { CreateEnum, CustomLoginController } from './CustomLoginController';
import './index.less';

const SelectTag = () => (
  <Tag className="custom_login_item-select" tag={i18n.chain.orgAdmin.customLogin.active} color="blue-900" />
);

export const CardItemView = ({ item, controller }: any) => {
  const defaultKey = useObservableState(() => controller.getDefaultKey$());
  const isDark = controller.getIsDark();
  const defaultCardImg = isDark ? darkCard : lightCard;

  const isSelect = item.id === defaultKey;

  const onOperator = (value: any) => {
    controller.handeOperator(value.key, item);
  };
  return (
    <div className="custom_login_item">
      <div className="custom_login_item-logo">
        <img src={defaultCardImg} />
        {isSelect && <SelectTag />}
      </div>
      <div className="custom_login_item-desc">
        <div className="custom_login_item-title">{item.title}</div>
        <Dropmenu menus={controller.getOptions(item)} noSelected icon="more" onClickMenuItem={onOperator} />
      </div>
    </div>
  );
};

interface IProps {
  controller: CustomLoginController;
}
const CustomLogin: FC<IProps> = ({ controller }) => {
  return (
    <div className="page-custom-login">
      <div className="page-custom-login-content-title">
        {i18n.chain.orgAdmin.menu.customLogin}
        <Dropmenu
          menus={[
            { title: i18n.chain.orgAdmin.customLogin.uploadTemplate, key: CreateEnum.UPLOAD },
            { title: i18n.chain.orgAdmin.customLogin.manual, key: CreateEnum.MANUAL },
          ]}
          onClickMenuItem={({ key }) => controller.onCreate(key as CreateEnum)}
          noSelected
          onlyText
          dropIcon={false}
        >
          <Button type="primary" rightIcon="arrow-down">
            {i18n.chain.orgAdmin.customLogin.createTemplate}
          </Button>
        </Dropmenu>
      </div>
      <div className="page-custom-login-content-list">
        <CardCurdWithSimpleSearch controller={controller.getListController()} />
      </div>
      <ModalWithBtnsCompDrawer controller={controller.getCreateController()} />
      <ModalWithBtnsCompDrawer controller={controller.getManualController()} />
    </div>
  );
};

export { CustomLogin };
