import { useObservableState } from '@mdtBsComm/hooks/use-observable-state';
import Tree from '@mdtDesign/tree';
import { DataListComp } from '../data-list-comp';
import { useDataListCompTreeContext, useDataListCompTreeProvider } from './dataListCompTreeContext';
import { DataListCompTreeController } from './DataListCompTreeController';

// DataList-tree组件===================================================================================
const AfterLoadingView = () => {
  const { dataListCompTreeController: controller } = useDataListCompTreeContext();
  const data = useObservableState(() => controller.getDataList$());
  const visible = useObservableState(() => controller.getVisibleLoadingNextTip$());
  const { ...resetProps } = controller.getCompOptions();

  const displayData = visible ? [] : data;

  return <Tree {...resetProps} treeData={displayData} />;
};

// tree==============================================================================================
interface IProps {
  controller: DataListCompTreeController;
  className?: string;
}
export function DataListCompTree({ controller, className }: IProps) {
  const Provider = useDataListCompTreeProvider();
  const value = { dataListCompTreeController: controller };

  return (
    <Provider value={value}>
      <DataListComp controller={controller} className={className} AfterLoadingView={AfterLoadingView} />
    </Provider>
  );
}
