import _ from 'lodash';
import { AsyncSubject } from 'rxjs';
import { IPaginationQuery, IUserQuery } from '@mdtApis/interfaces';
import { IEmotionProps, ModalWithBtnsCompEmotionController } from '@mdtBsComponents/modal-with-btns-comp-emotion';
import { RequestController } from '@mdtBsControllers/request-controller';
import Button from '@mdtDesign/button';
import Tag from '@mdtDesign/tag';
import toastApi from '@mdtDesign/toast';
import {
  RoleManageTreeListController,
  RoleManageTreeListModel,
} from '@mdtProMicroModules/containers/role-manage-tree-list';
import {
  IMoreReset,
  IVirtualizedTableProps,
  TableCurdWithSimpleSearchController,
} from '@mdtProMicroModules/containers/table-curd-with-simple-search';
import { UserOrgLazySelectController } from '@mdtProMicroModules/containers/user-org-lazy-select';
import { IUserModel } from '@mdtProMicroModules/models/UserModel';
import { AppController } from '../../app/AppController';
import i18n from '../../languages';
import { IMapRespData, IRoleUserModel, ITableUserData, MoreItemsEnum } from './RoleUserModel';

class RoleUserController extends RequestController {
  private Model: IRoleUserModel;
  private UserModel: IUserModel;
  private app: AppController;

  // 用户列表
  private tableController: TableCurdWithSimpleSearchController<ITableUserData>;
  // 角色新增用户
  private addRoleUserController: UserOrgLazySelectController;
  // 角色移出用户
  private removeRoleUserController: ModalWithBtnsCompEmotionController<ITableUserData>;
  // 角色列表
  private roleManageTreeListController: RoleManageTreeListController;

  // 映射缓存
  private mapCache: IMapRespData;

  public constructor(app: AppController, Model: IRoleUserModel, UserModel: IUserModel) {
    super();
    this.app = app;
    this.Model = Model;
    this.UserModel = UserModel;
    this.mapCache = {};

    this.removeRoleUserController = new ModalWithBtnsCompEmotionController<ITableUserData>({
      clickOkBtnFunc: this.removeRoleUserToService,
      modalCompOptions: { modalOptions: this.initRemoveRoleUserModalOptions },
    });

    this.addRoleUserController = new UserOrgLazySelectController({
      onConmitSelectFunc: this.addRoleUserToService,
      title: i18n.chain.orgAdmin.organization.addUser,
      userIdKey: 'uuid',
      showUserWithoutOrg: true,
    });

    this.roleManageTreeListController = new RoleManageTreeListController(
      {
        app: this.app,
        rolePermissions: this.app!.getUserPermissionController()!.getUserManagePermission(),
        onRoleSelect: () => this.tableController.loadDataList(),
      },
      RoleManageTreeListModel,
    );

    this.tableController = new TableCurdWithSimpleSearchController<ITableUserData>({
      dataListCompTableCurdControllerOptions: {
        dataListControllerOptions: {
          loadDataListFunc: this.queryDataWithTotal,
          loadNextPageDataListFunc: this.queryData,
          getBackendFilterParams: this.getFilterParams,
        },
        curdOptions: this.initCurdOptions,
        tableOptions: this.initTableOptions,
        clickCreateBtnFunc: () => {
          return this.addRoleUserController.openModal();
        },
        clickMoreBtnFunc: this.moreItemsTableData,
      },
      headerOptions: this.initHeaderOptions,
    });
    this.tableController.listenBackendFilter(this.tableController.getSingleFilter$());
    this.initMapAndUserData();
  }

  public destroy() {
    this.tableController.destroy();
    this.removeRoleUserController.destroy();
    this.addRoleUserController.destroy();
    this.roleManageTreeListController.destroy();
    this.mapCache = {};
    this.app = null!;
    this.Model = null!;
    this.UserModel = null!;
  }

  public getRoleManageTreeListController() {
    return this.roleManageTreeListController;
  }

  public getTableController() {
    return this.tableController;
  }

  public getRemoveRoleUserController() {
    return this.removeRoleUserController;
  }

  public getAddRoleUserController() {
    return this.addRoleUserController;
  }

  // 渲染表头所需信息
  private initTableOptions = (): IVirtualizedTableProps => {
    return {
      columns: [
        {
          name: i18n.chain.orgAdmin.organization.username,
          code: 'name',
          width: 180,
          ellipsis: true,
        },
        {
          name: i18n.chain.orgAdmin.organization.orgs,
          code: 'orgDisplay',
          render: (v: string[]) => (v?.length ? v.map((v: string) => <Tag key={v} tag={v} />) : '--'),
          width: 200,
        },
        {
          name: i18n.chain.orgAdmin.organization.availability,
          code: 'disableDisplay',
          width: 80,
          render: (value: string, r: ITableUserData) => (
            <Tag key={value} tag={value} color={!r.disable ? 'green-700' : 'red-700'} />
          ),
        },
        { name: i18n.chain.orgAdmin.organization.expireTime, code: 'expireTimeDisplay', width: 100 },
      ],
      type: 'page-bg',
      primaryKey: 'id',
      emptyContent: i18n.chain.comNoData,
      withVerticalBorder: false,
    };
  };

  private initCurdOptions = () => {
    const { enableEdit } = this.app!.getUserPermissionController()!.getUserManagePermission();
    const moreItems = () => [
      {
        title: i18n.chain.proMicroModules.roleList.removeRole,
        key: MoreItemsEnum.EVICTION,
        disabled: !enableEdit,
      },
    ];
    return {
      enableCreate: enableEdit,
      enableEdit: false,
      moreItems,
    };
  };

  private initHeaderOptions = () => {
    return {
      createBtnLabel: i18n.chain.proMicroModules.roleList.addRoleUser,
      inputPlaceholder: i18n.chain.comPlaceholder.input,
      title: '',
      renderExtendOthers: () => (
        <Button className="page_role-user-resource" onClick={() => this.roleManageTreeListController.editRole()}>
          {i18n.chain.proMicroModules.roleList.manageRole}
        </Button>
      ),
    };
  };

  // 渲染角色移出用户弹窗所需信息
  private initRemoveRoleUserModalOptions = (): IEmotionProps => {
    const delData = this.removeRoleUserController.getModalRest();
    return {
      emotion: 'alert',
      title: i18n.chain.proMicroModules.roleList.removeConfirm(delData?.name || '??'),
      description: i18n.chain.proMicroModules.roleList.removeConfirmDesc,
    };
  };

  // --------- 数据类 - App ---------
  // 初始化映射
  private initMapAndUserData = async () => {
    const roleTreeList = this.roleManageTreeListController.getRoleData$().getValue();
    const resp = await (
      await this.Model.initUserAndMap(this.app.getAppId(), roleTreeList, this.getCancelToken())
    ).toPromise();
    this.mapCache.roleIdNameMap = resp.roleIdNameMap;
    this.mapCache.orgIdNameMap = resp.orgIdNameMap;
    this.mapCache.roleIdPermissionMap = resp.roleIdPermissionMap;
    await this.roleManageTreeListController.start();
    this.tableController.loadDataList();
  };

  // 点击用户的操作
  private moreItemsTableData = (key: string, originalData?: ITableUserData) => {
    // 移出角色
    if (key === MoreItemsEnum.EVICTION) {
      return this.removeRoleUserController.openModal(originalData);
    }
    let result = new AsyncSubject<IMoreReset<ITableUserData>>();
    result.next({ success: true });
    result.complete();
    return result;
  };

  // --------- 数据类 - 角色用户 ---------
  // 新增角色用户
  private addRoleUserToService = async (data: any) => {
    const current = this.roleManageTreeListController.getCurrentSelect$().getValue().id;
    const resp = await this.Model.patchOrgUsers(this.app.getAppId(), current, _.map(data, 'userId')).toPromise();
    if (resp.success) {
      toastApi.success(i18n.chain.orgAdmin.organization.addUserSuccess);
      this.tableController.loadDataList();
    }
  };

  // 移出角色用户
  private removeRoleUserToService = async (data?: ITableUserData) => {
    const current = this.roleManageTreeListController.getCurrentSelect$().getValue().id;
    const resp = await this.Model.removeOrgUsers(this.app.getAppId(), current, data?.uuid as string).toPromise();

    if (resp.success) {
      toastApi.success(i18n.chain.orgAdmin.organization.removeUserSuccess);
      this.tableController.loadDataList();
    }
    return { success: resp.success };
  };
  private buildQueryParams = (params: IPaginationQuery) => {
    const appId = this.app.getAppId();
    const { roleIdNameMap, orgIdNameMap } = this.mapCache;

    const query: IUserQuery = {
      ...params,
      enable_status: 'all',
      basic_info: true,
    };

    // 如果有节点id
    const roleId = this.roleManageTreeListController.getCurrentSelect$().getValue().id;
    if (roleId) {
      query.role_ids = roleId.toString();
    }

    const searchVal = this.tableController.getSingleFilterValue();
    if (searchVal) {
      query.name_like = searchVal;
    }

    return {
      appId,
      query,
      roleIdNameMap,
      orgIdNameMap,
    };
  };

  private queryData = (params: IPaginationQuery) => {
    return this.UserModel.queryUsersPagination(this.buildQueryParams(params));
  };

  private queryDataWithTotal = (params: IPaginationQuery) => {
    return this.UserModel.queryUsersPaginationTotal(this.buildQueryParams(params));
  };

  private getFilterParams = (value: any[]) => {
    const [name] = value;
    if (name) {
      return { name_like: name };
    }
    return {};
  };
}

export { RoleUserController };
