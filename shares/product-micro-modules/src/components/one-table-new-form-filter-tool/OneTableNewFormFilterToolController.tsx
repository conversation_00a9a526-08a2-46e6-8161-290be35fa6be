import _ from 'lodash';
import { Dayjs } from 'dayjs';
import { BehaviorSubject } from 'rxjs';
import { skip } from 'rxjs/operators';
import { IOperatorFilter } from '@mdtApis/interfaces';
import { transformDateToUnix } from '@mdtBsComm/utils/dayUtil';
import { deepClean } from '@mdtBsComm/utils/deepCleanUtil';
import { DebounceTimeEnum } from '@mdtProComm/constants';
import { ONE_TABLE_INFO } from '../../datlas/datlasConfig';

const {
  formEnableAdvancedFilter,
  formEnableDateSearch,
  formEnableNameSearch,
  reportConfig: { advancedFilterSpec },
} = ONE_TABLE_INFO;

// 初始筛选结构
const DEFAULT_FIITER_VALUE = {};

export interface IControllerOptions {
  // 默认筛选值
  defaultFilterValue?: IFilterValue;
  // 筛选值变化回调
  onChange?: (value: ITransformFilterValue, originValue: IFilterValue) => void;
  // 是否启用高级筛选，默认配置项中enableAdvanceFilter
  enableAdvancedFilter?: boolean;
  // 是否启用日期筛选，默认配置项中enableDateSearch
  enableDateSearch?: boolean;
  // 是否启用名称筛选，默认配置项中enableNameSearch
  enableNameSearch?: boolean;
}

export interface IFilterValue {
  createTimeMin?: Dayjs;
  createTimeMax?: Dayjs;
  name?: string;
  metaQuery?: Record<string, any>;
  metaColumnTypes?: Record<string, any>;
}

export interface ITransformFilterValue {
  fuzzy_name?: string;
  meta_query?: IOperatorFilter;
  create_time_min?: number;
  create_time_max?: number;
  meta_column_types?: Record<string, any>;
}

class OneTableNewFormFilterToolController {
  private openAdvancedFilter$ = new BehaviorSubject<boolean>(false);
  private filterValue$ = new BehaviorSubject<IFilterValue>(DEFAULT_FIITER_VALUE);
  private filterDotTip$ = new BehaviorSubject<boolean>(false);
  private enableAdvancedFilter?: boolean;
  private enableDateSearch?: boolean;
  private enableNameSearch?: boolean;
  private filterSpec = advancedFilterSpec || {};
  private transformedFilterValue: ITransformFilterValue = {};
  private debouncedSetName: (name: string) => void;

  public constructor(options?: IControllerOptions) {
    const {
      defaultFilterValue = DEFAULT_FIITER_VALUE,
      onChange,
      enableAdvancedFilter: optionsEnableAdvancedFilter,
      enableDateSearch: optionsEnableDateSearch,
      enableNameSearch: optionsEnableNameSearch,
    } = options || {};
    this.enableAdvancedFilter = optionsEnableAdvancedFilter ?? formEnableAdvancedFilter;
    this.enableDateSearch = optionsEnableDateSearch ?? formEnableDateSearch;
    this.enableNameSearch = optionsEnableNameSearch ?? formEnableNameSearch;
    !_.isEmpty(defaultFilterValue) && this.filterValue$.next(defaultFilterValue);
    this.debouncedSetName = _.debounce((name: string) => {
      const val = this.filterValue$.getValue();
      this.filterValue$.next({ ...val, name });
    }, DebounceTimeEnum.MAX);

    this.listenChange(onChange);
  }

  public destroy() {
    this.openAdvancedFilter$.complete();
    this.filterValue$.complete();
    this.filterDotTip$.complete();
    this.transformedFilterValue = null!;
  }

  public getFilterDotTip$() {
    return this.filterDotTip$;
  }

  public getTransformedFilterValue() {
    return this.transformedFilterValue;
  }

  public getEnableAdvancedFilter() {
    return this.enableAdvancedFilter;
  }

  public getEnableDateSearch() {
    return this.enableDateSearch;
  }

  public getEnableNameSearch() {
    return this.enableNameSearch;
  }

  public getFilterSpec() {
    return this.filterSpec;
  }

  public getOpenAdvancedFilter$() {
    return this.openAdvancedFilter$;
  }

  public setOpenAdvancedFilter(value: boolean) {
    this.openAdvancedFilter$.next(value);
  }

  public getFilterValue$() {
    return this.filterValue$;
  }

  public setMetaQuery(metaQuery: Record<string, any> = {}) {
    const val = this.filterValue$.getValue();
    return this.filterValue$.next({ ...val, metaQuery });
  }

  public setName(name = '') {
    this.debouncedSetName(name);
  }

  public setCreateTimeRange(createTimeMin?: Dayjs, createTimeMax?: Dayjs) {
    const val = this.filterValue$.getValue();
    const result = { ...val };
    if (createTimeMin) {
      result.createTimeMin = createTimeMin;
    } else {
      delete result.createTimeMin;
    }
    if (createTimeMax) {
      result.createTimeMax = createTimeMax;
    } else {
      delete result.createTimeMax;
    }
    this.filterValue$.next(result);
  }

  public resetMetaQuery() {
    const val = this.filterValue$.getValue();
    this.filterValue$.next({
      ...val,
      metaQuery: {},
    });
  }

  public resetAll() {
    this.filterValue$.next(DEFAULT_FIITER_VALUE);
  }

  public transformFilterValueToParams(val: IFilterValue): ITransformFilterValue {
    let result: ITransformFilterValue;

    // key转换
    result = _.mapKeys(val, (_v, k) => {
      if (k === 'name') {
        return 'fuzzy_name';
      }
      if (k === 'createTimeMax' || k === 'createTimeMin' || k === 'metaQuery') {
        return _.snakeCase(k);
      }
      return k;
    });

    // value转换
    _.forIn(result, (value, key) => {
      // 转变为operator_filter
      if (key === 'meta_query') {
        const and = _.map(deepClean(value as IFilterValue['metaQuery']), (queryValue, queryKey) => {
          if (_.isArray(queryValue)) {
            // 这里只处理两种类型，array的int和str
            const isIntArr = _.every(queryValue, _.isInteger);
            const type = isIntArr ? 'array_int' : 'array_str';
            _.set(result, `meta_column_types[${queryKey}]`, type);
            return {
              column: queryKey,
              operator: 'intersect',
              param: queryValue,
            };
          }
          return {
            column: queryKey,
            operator: 'eq',
            param: queryValue,
          };
        });
        !_.isEmpty(and) &&
          (result[key] = {
            $and: and,
          });
      }
      // 时间转换
      if (key === 'create_time_max' || key === 'create_time_min') {
        result[key] = transformDateToUnix(value as Dayjs);
      }
      return value;
    });

    // 空值清除
    return deepClean(result);
  }

  private listenChange(onChange: IControllerOptions['onChange']) {
    this.filterValue$.pipe(skip(1)).subscribe((value) => {
      this.filterDotTip$.next(_.values(value?.metaQuery).some((val) => !_.isEmpty(val)));
      this.transformedFilterValue = this.transformFilterValueToParams(value);
      onChange?.(this.transformedFilterValue, value);
    });
  }
}

export { OneTableNewFormFilterToolController };
