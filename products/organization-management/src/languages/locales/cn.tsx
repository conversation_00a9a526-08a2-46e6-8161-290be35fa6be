import { cn as microCn } from '@mdtProMicroModules/languages';

const cn = {
  ...microCn,
  orgAdmin: {
    menu: {
      userManage: '用户管理',
      userAndOrg: '成员与部门',
      roleManage: '角色管理',
      authManage: '授权管理',
      preferenceSetting: '偏好设置',
      common: '通用',
      customLogin: '自定义登录',
      logo: 'Logo样式',
      appStyle: '机构样式',
      defaultPlay: '默认播放设置',
      dataSetting: '可见数据设置',
      grantedForm: '报表管理授权',
      opsMetrics: '运维统计',
      operationLog: '用户操作日志',
      expired: (date: string) => `${date} 将过期`,
    },
    organization: {
      username: '用户名称',
      orgs: '部门',
      roles: '角色',
      availability: '是否可用',
      expireTime: '过期时间',
      batchOperation: '批量操作',
      importOrg: '导入部门',
      importUser: '导入用户',
      exportUser: '导出用户',
      batchEditUser: '批量编辑用户',
      orgManage: '部门管理',
      createUser: '新建用户',
      addOrgUser: '新增部门用户',
      searchUser: '搜索用户',
      edit: '编辑用户',
      activeUser: '激活用户',
      freezeUser: '禁用用户',
      forcePassword: '强制修改密码',
      unlockUser: '解除账号锁定',
      delUser: '删除用户',
      delConfirm: (user: string) => `确认删除"${user}"用户？`,
      delConfirmDesc: '删除后，该用户将无法再登录使用本产品',
      removeConfirm: (user: string) => `确定将"${user}"用户移出该部门？`,
      removeConfirmDesc: '移出后，用户将不再属于该部门',
      unlockConfirm: '是否确定解除该用户账号锁定状态？',
      addUser: '添加用户',
      removeOrg: '移出部门',
      allUser: '全部用户',
      orgUser: '部门成员',
      active: '激活',
      enable: '可用',
      freeze: '禁用',
      confirm: '确认',
      freezeUserDesc: '禁用账号并不会删除用户，请确认是否禁用',
      exportError: '请选中相应的用户后，再进行“导出用户”的操作',
      exportSuccess: '导出用户成功！',
      exportFailed: (error: string) => `导出用户失败: ${error}`,
      batchError: '请选中相应的用户后，再进行“批量编辑”的操作',
      forcePasswordSuccess: '强制修改密码成功',
      batchSuccess: '批量创建用户成功',
      batchOrgSuccess: '批量创建部门成功',
      editUserSuccess: '更新用户成功',
      batchEditUserSuccess: '批量更新用户成功',
      createUserSuccess: '新建用户成功',
      delUserSuccess: '删除用户成功',
      unlockSuccess: '解除锁定成功',
      editUserStatusSuccess: '更改用户可用状态成功',
      addUserSuccess: '添加用户成功',
      removeUserSuccess: '移出用户成功',
    },
    auth: {
      user: '成员',
      org: '部门',
      role: '角色',
      editAuthSuccess: '权限修改成功',
      roleName: '角色名称',
      orgName: '部门名称',
      orgCount: '部门人数',
      roleDesc: '角色描述',
      auth: '权限',
      resource: '资源',
      authSetting: '权限设置',
      authSettingDesc: '由组织/角色下继承下来的权限是用户无法更改。如需更改，请至组织/角色内修改相关权限',
    },
    logo: {
      product: '产品',
      changeLogo: '替换LOGO',
      reset: '重置',
      resetSuccess: '重置机构Logo成功',
      maxSize: (size: string) => `图片最大不能超过${size}kb`,
      editLogoSuccess: '修改机构Logo成功',
      fileUploadFailed: (name: string) => `文件[${name}]上传失败`,
    },
    appStyle: {
      logo: 'Logo',
      favicon: 'Favicon',
      other: '其他',
      logoName: '网页左上角的标志性大图标',
      faviconName: '浏览器选项卡中的小图标（64*64像素）',
      windowTitleName: '标题',
      windowDescName: '描述',
      helpCenterName: '帮助中心',
      waterMark: '系统水印',
      open: '开启',
      close: '关闭',
      resetAll: '全部重置',
      idleTimeoutName: '空闲超时',
      idleTimeoutDesc: '用户在规定时间段内没有操作，系统自动登出',
      pswSaftyName: '密码安全',
      pswSaftyDesc: '超出规定时间段内，提示用户修改密码',
      tenMinutes: '十分钟',
      halfHour: '半小时',
      oneHour: '一小时',
      twoHours: '两小时',
      fourHours: '四小时',
      eightHours: '八小时',
      sevenDays: '七天',
      fifteenDays: '十五天',
      threeMonth: '三个月',
      halfYear: '半年',
      nineMonth: '九个月',
      oneYear: '一年',
      never: '永不',
      resetAllTips: '确定要重置所有机构样式么？',
      resetAllTipsDesc: '重置后，原有配置将无法保留',
      sizeError: (width: number, height: number) => `图像尺寸不正确，请上传${width}x${height}像素的图标文件。`,
      fileError: '无法加载图像，请确保上传的是有效的图像文件。',
      updateSuccess: '更新成功',
    },
    defaultPlay: {
      defaultPage: '默认播放页',
      desc: '为用户设置首页后，用户进入产品则会默认播放该页面。若需要用户返回系统，请检查用户是否有“返回系统”权限。',
      batchChange: '批量修改',
      resetSuccess: '默认播放重置成功',
      setSuccess: '默认播放设置成功',
    },
    dataSetting: {
      username: '用户名',
      themes: '可见主题库',
      orgs: '可见机构',
      cannotsee: '不可见',
      desc: '当用户不在下面表格中，则默认显示全部数据',
      addShare: '新增分配',
      searchPlaceholder: '搜索用户',
      addTitle: '新建主题库分配表',
      editTitle: '编辑主题库分配表',
      delSuccess: '删除成功',
      del: '删除',
      delDesc: '确认删除已创建的分配表么？',
      editSuccess: '修改成功',
      emptyDesc:
        '默认情况下，本机构用户可以看到所有的主题库。您可以为用户设置“可见主题库”，用户将只能看到这些主题库及里面的数据包',
    },
    operationLog: {
      search: '查询',
      export: '导出',
      operator: '操作',
      resource: '操作资源',
      user: '操作人',
      date: '操作时间',
      object: '操作对象',
      exportTitle: '即将导出当前数据',
      exportDescription: '请注意，由于系统限制，一次最多导出前5000条数据',
      exportCancel: '稍后再说',
      exportOk: '立即导出',
      emptyTitle: '没有查询到相关结果',
      emptyDescription: '请更换条件再次尝试搜索',
      operationTooltip: '在选择【操作资源后】才可以选择对应的操作',
    },
    customLogin: {
      active: '默认',
      title: '自定义登录',
      createTemplate: '新建模板',
      uploadTemplate: '上传模板',
      manual: '手动配置',
      setDefault: '设为默认',
      cancelDefault: '取消默认',
      editTemplate: '编辑模板',
      copyLink: '复制链接',
      deleteTemplate: '删除模板',
      fileUploadError: '文件上传失败，请重试',
      copy: '已复制到粘贴板',
      removeSuccess: '删除模板成功',
      createSuccess: '新建模板成功',
      editSuccess: '编辑模板成功',
    },
  },
};

export type Locale = typeof cn;
export default cn;
