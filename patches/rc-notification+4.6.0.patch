diff --git a/node_modules/rc-notification/es/Notification.js b/node_modules/rc-notification/es/Notification.js
index 6098554..32d8a5e 100644
--- a/node_modules/rc-notification/es/Notification.js
+++ b/node_modules/rc-notification/es/Notification.js
@@ -268,8 +268,9 @@ Notification.newInstance = function newNotificationInstance(properties, callback
       destroy: function destroy() {
         unmount(div);
 
-        if (div.parentNode) {
-          div.parentNode.removeChild(div);
+        if (div) {
+          div.remove();
+          div = null;
         }
       },
       // Hooks
