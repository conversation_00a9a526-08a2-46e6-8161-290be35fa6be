import { from } from 'rxjs';
import { map } from 'rxjs/operators';
import { IRequestCancelToken } from '@mdtBsServices/interfaces';
import { AppController } from '../../../app/AppController';
import { statisticBffService } from '../../../bff-service';

export class GuideMapModelBff {
  // 查询指标数据
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  public static queryData(cancelToken?: IRequestCancelToken) {
    const app = AppController.getInstance();
    const prefC = app.getPreferencesController();
    const hasSetPref = prefC.getHasSetPkgPref();
    const appId = app.getAppId();
    const config = {
      params: {
        appId,
        prefAlbumIds: hasSetPref ? prefC.getThemeIds() : undefined,
        prefAppIds: hasSetPref ? prefC.getGrantedAppIds() : undefined,
      },
    };
    // todo 国际化
    return from(statisticBffService.queryNodeItem(config)).pipe(map((resp) => (resp.success ? resp.page_data : {})));
  }
}

export type IGuideMapModel = typeof GuideMapModelBff;
