import React from 'react';
import type { NsGraph } from '@antv/xflow';
import { EditorName } from './EditorName';
import { StatusIcon } from './StatusIcon';

const LoaderNode: NsGraph.INodeRender = ({ data }) => {
  return (
    <div className="node-box">
      <EditorName
        className="loader-node"
        id={data.id}
        title={data.label}
        isRunNode$={data.isRunNode$}
        editingTitle={data.editingTitle}
      />
      <div className="node-tooltip-icon">
        <StatusIcon status={data.status} statusMsg={data.statusMsg} />
      </div>
    </div>
  );
};

export default LoaderNode;
