import { useController } from '@mdtBsComm/hooks/use-controller';
import { OneTableFormManagementModelBff } from '@mdtProMicroModules/pages/one-table-form-management';
import {
  FormManagement,
  FormManagementController,
} from '@mdtProMicroModules/pages-in-micro/one-table/pages/form-management';

const PageFormManagement = () => {
  const [controller] = useController(() => {
    const ctrl = new FormManagementController({
      Model: OneTableFormManagementModelBff,
    });
    return [ctrl, null];
  });

  return <FormManagement controller={controller} />;
};

export default PageFormManagement;
