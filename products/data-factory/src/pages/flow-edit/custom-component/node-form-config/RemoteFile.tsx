import { observer, RecursionField, useField, useFieldSchema, useForm } from '@formily/react';
import { useUpdateEffect } from 'ahooks';

export const RemoteFile = observer(() => {
  const form = useForm();
  const address = useField().address;
  const allSchemas = useFieldSchema().properties || {};
  const oneOfType = form.values.scheme || '';
  const oneOfSchame = allSchemas[oneOfType];

  useUpdateEffect(() => {
    form.setValues({ scheme: oneOfType }, 'overwrite');
  }, [oneOfType]);

  return <RecursionField key={oneOfType} schema={oneOfSchame} basePath={address} onlyRenderProperties />;
});

export default RemoteFile;
