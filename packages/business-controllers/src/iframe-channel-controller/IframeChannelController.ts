export interface IMainOptions extends ICommOptions {
  iframeId: string | string[]; // 指定嵌套的iframe, id > className ,推荐id查找
}

export interface IIframeOptions extends ICommOptions {
  currentWindow?: Window; // 默认window.parent
}

interface ICommOptions {
  targetOrigin?: string; // 默认 *
}

type IPayloadFunction = (payload: any) => void;

/**
 * 服务于iframe的频道中心
 * 用于主项目和各个iframe之间的双向通信
 */
class IframeChannelController {
  public static sendMessageToIframe(type: string, payload: any, options: IMainOptions) {
    const { targetOrigin = '*', iframeId } = options || {};
    const iframeIds = Array.isArray(iframeId) ? iframeId : [iframeId];
    iframeIds.forEach((id) => {
      const iframe = (document.getElementById(id) as HTMLIFrameElement | null) ?? document.querySelector(id);
      iframe?.contentWindow?.postMessage({ type, payload }, targetOrigin);
    });
  }

  public static sendMessageToParent(type: string, payload: any, options?: IIframeOptions) {
    const { targetOrigin = '*', currentWindow } = options || {};
    const newWindow = currentWindow ?? window.parent;
    newWindow.postMessage({ type, payload }, targetOrigin);
  }

  private listeners: Map<string, Array<IPayloadFunction>>;

  public constructor() {
    this.listeners = new Map();
    this.listenForPostMessage();
  }

  public destroy() {
    this.unsubscribe();
    this.removeListenForPostMessage();
  }

  public subscribe(type: string, callback: IPayloadFunction) {
    const callbacks = this.listeners.get(type) || [];
    if (!callbacks.includes(callback)) {
      callbacks.push(callback);
    }
    this.listeners.set(type, callbacks);
  }

  public unsubscribe(type?: string, callback?: IPayloadFunction) {
    if (type && callback) {
      const callbacks = this.listeners.get(type);
      if (callbacks) {
        const filteredListeners = callbacks.filter((listener) => listener !== callback);
        this.listeners.set(type, filteredListeners);
      }
    } else if (type) {
      this.listeners.delete(type);
    } else {
      this.listeners.clear();
    }
  }

  private handleMessage = (event: MessageEvent) => {
    const eventType = event.data?.type;
    const callbacks = this.listeners.get(eventType);
    if (callbacks) {
      callbacks.forEach((callback) => callback(event.data.payload));
    }
  };

  private listenForPostMessage() {
    window.addEventListener('message', this.handleMessage);
  }

  private removeListenForPostMessage() {
    window.removeEventListener('message', this.handleMessage);
  }
}

export { IframeChannelController };
