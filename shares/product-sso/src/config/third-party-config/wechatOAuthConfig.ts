export let WX_OAUTH_ID = '';
export let WX_OAUTH_STATE = '';
export let WX_OAUTH_SCOPE = '';
export let WX_OAUTH_REDIRECT_URI = '';
export let WX_OAUTH_RESPONSE_TYPE = '';
export let WX_OAUTH_FORCE_POPUP = false;

export const initWechatOAuthConfig = (id: string, options?: any) => {
  const {
    scope = 'snsapi_userinfo',
    redirectUri = '',
    responseType = 'code',
    forcePopup = false,
    state,
  } = options || {};
  WX_OAUTH_ID = id;
  WX_OAUTH_STATE = state;
  WX_OAUTH_SCOPE = scope;
  WX_OAUTH_REDIRECT_URI = redirectUri;
  WX_OAUTH_RESPONSE_TYPE = responseType;
  WX_OAUTH_FORCE_POPUP = forcePopup;
};
