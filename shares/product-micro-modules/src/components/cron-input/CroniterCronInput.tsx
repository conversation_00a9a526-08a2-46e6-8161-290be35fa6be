import _ from 'lodash';
import { useCreation } from 'ahooks';
import { CronInput, ICronInputProps } from './CronInput';

const defaultCronProps = {
  defaultTab: 'day',
  panesShow: { second: false, minute: true, hour: true, day: true, month: true, week: true, year: false },
};

const getValue = (val?: string) => {
  return val || '*';
};

/*
 * croniter的模式是 minute hour day month week second, 不支持?
 * 通用模式是 second minute hour day month week yaer, 支持?
 */
export const CroniterCronInput = ({ value, onChange, cronProps }: ICronInputProps) => {
  // 需要将croniter的模式转换为通用模式
  const formatVal = useCreation(() => {
    const [minuteValue, hourVal, dayVal, monthVal, weekVal, secondVal] = _.split(value, ' ');
    return [
      getValue(secondVal),
      getValue(minuteValue),
      getValue(hourVal),
      getValue(dayVal),
      getValue(monthVal),
      getValue(weekVal),
      getValue(''),
    ].join(' ');
  }, [value]);

  const _onChange = (newVal: string) => {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const [secondVal, minuteValue, hourVal, dayVal, monthVal, weekVal, yearVal] = _.split(newVal, ' ');
    let value = [minuteValue, hourVal, dayVal, monthVal, weekVal].join(' ');
    // 不支持?, 需要做个转换
    // value = value.replace(/\?/g, '*');
    onChange(value);
  };

  return <CronInput value={formatVal} onChange={_onChange} cronProps={{ ...defaultCronProps, ...cronProps }} />;
};
