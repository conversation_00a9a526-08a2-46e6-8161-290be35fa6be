{"compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "commonjs", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "importHelpers": true, "noEmit": false, "jsx": "react-jsx"}, "references": [{"path": "./packages/bpmn-js-properties-panel"}, {"path": "./packages/business-bff-services"}, {"path": "./packages/business-comm"}, {"path": "./packages/business-components"}, {"path": "./packages/business-controllers"}, {"path": "./packages/business-services"}, {"path": "./packages/login"}, {"path": "./packages/restful-apis"}, {"path": "./packages/formily"}, {"path": "./packages/form-design"}, {"path": "./shares/product-comm"}, {"path": "./shares/product-form-editor"}, {"path": "./shares/product-micro-modules"}, {"path": "./shares/product-tasks"}]}