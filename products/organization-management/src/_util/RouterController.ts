import loadable from '@loadable/component';
import { PermissionEnum } from '@mdtProComm/constants/permission';
import { IRoute } from '@mdtProComm/controllers/BaseRouterController';
import { DatlasRouterController } from '@mdtProMicroModules/datlas/comm/DatlasRouterController';
import { RoutePathEnum } from './constants';

const OrganizationView = loadable(() => import('../routers/organization'));
const RoleUserView = loadable(() => import('../routers/role-user'));
const AuthManageView = loadable(() => import('../routers/auth-manage'));
const SettingDatapkgView = loadable(() => import('../routers/setting-datapkg'));
const GrantedOtFormView = loadable(() => import('../routers/granted-ot-form'));
const LogoStyleView = loadable(() => import('../routers/logo-style'));
const AppStyleView = loadable(() => import('../routers/app-style'));
const HomeSettingView = loadable(() => import('../routers/home-setting'));
const OperationLog = loadable(() => import('../routers/operation-log'));
const CustomLogin = loadable(() => import('../routers/custom-login'));

export const allAuthRoutes: IRoute[] = [
  {
    path: RoutePathEnum.ORGANIZATION,
    View: OrganizationView,
    permissionKey: [PermissionEnum.AM_MENU_USER_MANAGE, PermissionEnum.AM_MENU_USER_AUTHORITY_MANAGE],
    // headerLess: true, // 不展示header
    // sideMenuLess: true, // 不展示sideMenu
  },
  {
    path: RoutePathEnum.ROLE_USER,
    View: RoleUserView,
    permissionKey: [PermissionEnum.AM_MENU_USER_MANAGE, PermissionEnum.AM_MENU_USER_ROLE_MANAGE],
  },
  {
    path: RoutePathEnum.AUTH_MANAGE,
    View: AuthManageView,
    permissionKey: [PermissionEnum.AM_MENU_USER_MANAGE, PermissionEnum.AM_MENU_AUTHORITY_MANAGE],
  },
  {
    path: RoutePathEnum.APP_STYLE,
    View: AppStyleView,
    permissionKey: [PermissionEnum.AM_MENU_APPSETTING_MANAGE, PermissionEnum.SYS_SETUP_MENU],
  },
  {
    path: RoutePathEnum.LOGO_STYLE,
    View: LogoStyleView,
    permissionKey: [PermissionEnum.AM_MENU_APPSETTING_MANAGE, PermissionEnum.SYS_SETUP_MENU],
  },
  {
    path: RoutePathEnum.HOME_SETTING,
    View: HomeSettingView,
    permissionKey: [PermissionEnum.AM_MENU_APPSETTING_MANAGE, PermissionEnum.PRODUCT_MENU_DS],
  },
  {
    path: RoutePathEnum.SETTING_DATAPKG,
    View: SettingDatapkgView,
    permissionKey: [PermissionEnum.AM_MENU_APPSETTING_MANAGE, PermissionEnum.PRODUCT_MENU_DM],
  },
  {
    path: RoutePathEnum.GRANTED_OT_FORM,
    View: GrantedOtFormView,
    permissionKey: [PermissionEnum.AM_MENU_APPSETTING_MANAGE, PermissionEnum.PRODUCT_MENU_OT],
  },
  {
    path: RoutePathEnum.CUSTOM_LOGIN,
    View: CustomLogin,
    permissionKey: [PermissionEnum.AM_MENU_APPSETTING_MANAGE, PermissionEnum.CUSTOM_LOGIN],
  },
  {
    path: RoutePathEnum.OPERATION_LOG,
    View: OperationLog,
    permissionKey: [PermissionEnum.MAINTENANCE_STATS, PermissionEnum.USER_HISTORY_MENU],
  },
];

class RouterController extends DatlasRouterController {
  public getAllAuthRoutes(): IRoute[] {
    return allAuthRoutes;
  }

  public gotoOrganization() {
    this.gotoPath(RoutePathEnum.ORGANIZATION);
  }

  public gotoRoleUser() {
    this.gotoPath(RoutePathEnum.ROLE_USER);
  }

  public gotoAuthManage() {
    this.gotoPath(RoutePathEnum.AUTH_MANAGE);
  }

  public gotoSettingDatapkg() {
    this.gotoPath(RoutePathEnum.SETTING_DATAPKG);
  }

  public gotoOperationLog() {
    this.gotoPath(RoutePathEnum.OPERATION_LOG);
  }
}

export { RouterController };
