import microApp from '@micro-zoe/micro-app';
import { getMicroName } from '../_util/util';

const startMicroApp = () => {
  let requestId = 0;
  let insMap: Record<string, boolean> = {};
  // 懒加载微应用
  const waitingMicroApp = (name: any) =>
    new Promise<boolean>((resolve) => {
      const loop = () => {
        if (insMap[name]) {
          window[name] ? resolve(true) : (requestId = window.setTimeout(loop, 4));
        } else {
          resolve(false);
        }
      };
      loop();
    });

  let cacheMap: Record<string, Promise<string>> = {};
  const cacheFetch = ({ cacheKey, url, options }: any) => {
    let resutl = cacheMap[cacheKey];
    if (!resutl) {
      resutl = window.fetch(url, options).then((res) => res.text());
      cacheMap[cacheKey] = resutl;
    }
    return resutl;
  };
  // 微应用启动, hash路由在子组件切换很快的情况下，有概率异常
  microApp.start({
    'disable-memory-router': true,
    'disable-patch-request': true,
    'clear-data': true,
    destroy: true,
    disableScopecss: false,
    fetch(url, options, appName) {
      const fixName = (appName || '').replace('-', '_');
      const fixJsCunk = (text: string) => text.replace(/webpackChunkmdt_datlas/gm, `webpackChunk${fixName}`);

      // 缓存html
      // if (url.includes('/micro.html')) {
      //   return cacheFetch({ cacheKey: fixName, url, options });
      // }
      // 缓存micro_vendor资源
      if (url.includes('micro_vendor')) {
        const name = url.split('/').pop() || '';
        const isCss = name.includes('.css');
        const targetUrl = new URL(`/static/${isCss ? 'css' : 'js'}/${name}`, window.location.href);
        const cf = cacheFetch({ cacheKey: name, url: targetUrl, options });
        return cf.then((text) => {
          if (text.startsWith('<!')) {
            console.log('not exeist', name);
            const nc = window.fetch(url, options).then((res) => res.text());
            cacheMap[name] = nc;
            return nc.then((text) => fixJsCunk(text));
          } else {
            return fixJsCunk(text);
          }
        });
      }
      // 缓存comm_vendor资源
      if (url.includes('comm_vendor')) {
        // 如果是comm, 则表明部分产品通用, 请求后缓存起来
        const name = url.split('/').pop() || '';
        const cf = cacheFetch({ cacheKey: name, url, options });
        return cf.then((text) => fixJsCunk(text));
      }
      // 其他资源
      let rp = window.fetch(url, options).then((res) => res.text());
      // 需要处理jsonpFunction的名字
      if (url.includes('.js')) {
        rp = rp.then((text) => fixJsCunk(text));
      }
      return rp;
    },
    // @ts-ignore
    lifeCycles: {
      mounted: function (e) {
        const name = e ? getMicroName(e.detail.name) : '';
        if (!name || window[name]) return;
        // 延迟加载
        requestId = window.setTimeout(() => {
          insMap[name] = true;
          waitingMicroApp(name).then((rslt) => {
            if (insMap[name] && rslt) {
              // eslint-disable-next-line no-useless-call
              window[name].mount.call(window[name]);
            } else {
              delete window[name];
            }
          });
        }, 4);
      },
      beforeunmount(e: CustomEvent) {
        const name = e ? getMicroName(e.detail.name) : '';
        if (!name || !window[name]) return;
        // eslint-disable-next-line no-useless-call
        window[name].unmount.call(window[name]);
      },
      unmount: function (e) {
        requestId && clearTimeout(requestId);
        const name = e ? getMicroName(e.detail.name) : '';
        insMap[name] = false;
        delete window[name];
      },
    },
  });
};

export { startMicroApp };
