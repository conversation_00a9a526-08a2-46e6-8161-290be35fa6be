import { useContext } from 'react';
import { createNameContext } from '@mdtBsComm/utils/contextUtil';
import { LuckysheetDatapkgDataEditController } from './LuckysheetDatapkgDataEditController';

export interface IContext {
  luckysheetDatapkgDataEditController: LuckysheetDatapkgDataEditController;
}

const context = createNameContext<IContext>('LuckysheetDatapkgDataEditController');

export const useLuckysheetDatapkgDataEditProvider = () => {
  return context.Provider;
};

export const useLuckysheetDatapkgDataEditContext = () => {
  return useContext(context);
};
