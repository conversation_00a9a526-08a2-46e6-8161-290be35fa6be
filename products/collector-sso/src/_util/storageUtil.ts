import { PRODUCT_NAME } from '../config';

// store
const STORE_LANGUAGE_KEY = `${PRODUCT_NAME}_language`;
export const getLanguageFromStore = () => {
  return window.localStorage.getItem(STORE_LANGUAGE_KEY) || '';
};
export const saveLanguageToStore = (value: string) => {
  return window.localStorage.setItem(STORE_LANGUAGE_KEY, value);
};

const STORE_THEME_KEY = `${PRODUCT_NAME}_theme`;
export const getThemeFromStore = () => {
  return window.localStorage.getItem(STORE_THEME_KEY) || '';
};
export const saveThemeToStore = (value: string) => {
  return window.localStorage.setItem(STORE_THEME_KEY, value);
};

const STORE_TAB_KEY = `${PRODUCT_NAME}_tab`;
export const getTabFromStore = () => {
  return window.localStorage.getItem(STORE_TAB_KEY) || '';
};
export const saveTabToStore = (value: string) => {
  return window.localStorage.setItem(STORE_TAB_KEY, value);
};

const STORE_TOKEN_KEY = `${PRODUCT_NAME}_token`;
export const getTokenFromStore = () => {
  return window.localStorage.getItem(STORE_TOKEN_KEY) || '';
};
export const saveTokenToStore = (value: string) => {
  return window.localStorage.setItem(STORE_TOKEN_KEY, value);
};
export const removeTokenFromStore = () => {
  return window.localStorage.removeItem(STORE_TOKEN_KEY);
};

const STORE_PRIVACY_KEY = `${PRODUCT_NAME}_privacy`;
export const getPrivacyFromStore = () => {
  return window.localStorage.getItem(STORE_PRIVACY_KEY) || '';
};
export const savePrivacyToStore = (value: string) => {
  return window.localStorage.setItem(STORE_PRIVACY_KEY, value);
};
export const removePrivacyFromStore = () => {
  return window.localStorage.removeItem(STORE_PRIVACY_KEY);
};

const SESSION_DIALOG_KEY = 'dialog';
export const getDialogFromSessionStore = () => {
  return window.sessionStorage.getItem(SESSION_DIALOG_KEY);
};

export const setDialogFromSessionStore = () => {
  return window.sessionStorage.setItem(SESSION_DIALOG_KEY, 'true');
};
