import { IPaginationQuery, IRowValue } from './comm';

export interface IFlowsQuery extends IPaginationQuery {
  name?: string;
  id?: string[];
  create_time?: string[];
  update_time?: string[];
  folder?: string;
  nofolder?: boolean;
  user_id?: number;
  // 1表示筛选别人分享给我的Flows, 0表示仅筛选我自己的Flows，2表示同时筛选我的和别人分享给我的Flows，default: 0
  share?: 0 | 1 | 2;
  permission?: string;
  name_like?: string;
}

export type IFlowSharePermissionType = 'read' | 'update' | 'execute' | 'delete';

export type IFlowScheduleTriggerType = 'cron' | 'interval' | 'manual';

export interface IFlowCronParams {
  week?: object;
  hour?: object;
  minute?: object;
  year?: object;
  day?: object;
  day_of_week?: object;
  second?: object;
  month?: object;
}

export interface IFlowIntervalParams {
  second: number;
}
export interface IFlowManualParams {
  second: number;
}

export interface IFlowScheduleTrigger<T> {
  end?: number;
  params: T;
  start?: number;
}

export interface IFlowCronScheduleTrigger extends IFlowScheduleTrigger<IFlowCronParams> {
  type: 'cron';
}

export interface IFlowIntervalScheduleTrigger extends IFlowScheduleTrigger<IFlowIntervalParams> {
  type: 'interval';
}

export interface IFlowManualScheduleTrigger extends IFlowScheduleTrigger<IFlowManualParams> {
  type: 'manual';
}

export type IFlowType = 'dataflow';

export type IFlowLinkTriggerRule = 'success' | 'failure' | 'finish' | 'manual';

export type IJunctionName = '_default_' | 'right' | 'others';

export type IJunctionType = 'list' | 'node';

export interface IJunction {
  name: IJunctionName; // 默认 _default_
  type: IJunctionType; // 默认 node
  index?: number;
}

export interface IFlowLink {
  downstream: string;
  upstream: string;
  junction?: IJunction;
  key: string;
  trigger_rule?: IFlowLinkTriggerRule;
}

export interface IFlowNode {
  description?: string;
  node_type: string;
  title?: string;
  version?: string;
  key: string;
  params?: object;
}

export interface IFlowPost {
  is_schedule_active?: boolean;
  description?: string;
  schedule?: IFlowCronScheduleTrigger | IFlowIntervalScheduleTrigger | IFlowManualScheduleTrigger;
  decisive_nodes?: string[];
  tags?: string[];
  params?: {
    schema: Record<string, IRowValue>;
  };
  title?: string;
  version?: string;
  flow_type: IFlowType;
  key?: string;
  links?: IFlowLink[];
  nodes?: IFlowNode[];
  _fparams_?: object;
}

export interface IFlow extends IFlowPost {
  name?: string;
  id: string;
  revision: number;
  create_time: number;
  update_time: number;
  user_id: number;
}

export interface IFlowRevision {
  revision: number;
}

export type IFlowPatch = Partial<IFlowPost>;

export type IFlowRunAction = 'lock' | 'offline' | 'online' | 'backfill' | 'stop_backfill';

export interface IFlowRunActionQuery {
  action: IFlowRunAction;
}

export type IFlowRunStatus = 'idle' | 'ready' | 'running' | 'transient_failed' | 'failed' | 'successful' | 'canceled';

export interface IFlowRun {
  flow_id: string;
  name?: string;
  id: string;
  status: IFlowRunStatus;
  task_id: string;
  revision?: string;
  params: IFlowRunPost;
  version: string;
  schedule_start_time: number;
  create_time: number;
  update_time: number;
  manual: boolean;
  user_id: number;
}

export interface IFlowRunsQuery extends IPaginationQuery {
  name?: string;
  id?: string;
  status?: IFlowRunStatus;
  schedule_start_time?: string;
  create_time?: string;
  update_time?: string;
  manual?: boolean;
  user_id?: number;
  order_by?: string;
}

export interface IFlowRunConfig {
  dump_how?: 'maximum' | 'column_names' | 'only_columns';
  breakpoints?: string[]; // array<断点位置，使用Node key指定>
  dump_data?: boolean;
}

export interface IFlowRunPost {
  manual?: boolean;
  flow_run_config?: IFlowRunConfig;
  mode?: 'normal' | 'debug' | 'dryrun';
  limit?: number;
  loglevel?: 'CRITICAL' | 'FATAL' | 'ERROR' | 'WARN' | 'WARNING' | 'INFO' | 'DEBUG' | 'NOTSET';
  override_params?: Array<{
    node_key: string;
    params: Array<{
      argument: string;
      parameter: IRowValue;
    }>;
  }>;
}

export interface IFlowSupportEnvResource {
  type: string;
  name: string;
  value: any;
  value_type?: any;
  is_secure?: boolean;
}

export interface IFlowSupportEnv {
  id: string;
  title: string;
  description?: string;
  is_default: boolean;
  resources: IFlowSupportEnvResource[];
}

export interface IFlowSupportNode {
  description: string;
  node_type: string;
  tags?: string[];
  params?: Record<string, IRowValue>;
  title: string;
  version: string;
  subgroup: string;
  group: string;
}

export interface IFlowsRunsQuery extends IFlowRunsQuery {
  flow_id?: string;
  // string[]
  flow_ids?: string;
  latest_record?: boolean;
}

export interface IFlowRunResultData {
  columns: string[];
  column_types: string[];
  data: any[][];
}

export interface IFlowRunResult {
  current_node: string | null;
  status: string;
  error?: string;
  data?: IFlowRunResultData;
}

export interface ITemporaryExecuteFlow {
  limit?: number; // 设置执行flow时，提取数据的flow node的提取数据量上限
  flow_run_config?: IFlowRunConfig;
  flow_id?: string;
  flow?: IFlowPost;
}

export interface ITemporaryExecuteFlowResult {
  flow_run_id: string;
  task_id: string;
}
