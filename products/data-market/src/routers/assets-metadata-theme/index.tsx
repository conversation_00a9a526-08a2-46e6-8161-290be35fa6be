import { useController } from '@mdtBsComm/hooks/use-controller';
import { AppController } from '../../app/AppController';
import {
  MetaDataTemplateManage,
  MetaDataTemplateManageController,
  TagsPage,
} from '../../pages/meta-data-template-manage';
import { MetaDataTemplateManageModel } from '../../pages/meta-data-template-manage/MetaDataTemplateManageModel';

const MetaDataTemplateManageView = () => {
  const [controller] = useController(() => {
    const ctrl = new MetaDataTemplateManageController(
      AppController.getInstance(),
      MetaDataTemplateManageModel,
      TagsPage.THEME,
    );
    return [ctrl, null];
  });

  return <MetaDataTemplateManage controller={controller} />;
};

export default MetaDataTemplateManageView;
