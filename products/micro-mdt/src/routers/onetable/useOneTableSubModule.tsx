import loadable from '@loadable/component';
import { OneTableH5RoutePathEnum } from '@mdtProComm/constants/route';
import { useMicroPath } from '@mdtProComm/utils/useMicroPath';

const PageSetting = loadable(() => import('./page-setting'));
const PageDashboardWithMenu = loadable(() => import('./page-dashboard/PageWithMenu'));
const PageDashboard = loadable(() => import('./page-dashboard'));
const PageFormManagementWithMenu = loadable(() => import('./page-form-management/PageWithMenu'));
const PageFormManagement = loadable(() => import('./page-form-management'));
const PageMissionCenterFormWithMenu = loadable(() => import('./page-mission-center-form/PageWithMenu'));
const PageMissionCenterForm = loadable(() => import('./page-mission-center-form'));
const PageMissionCenterReviewWithMenu = loadable(() => import('./page-mission-center-review/PageWithMenu'));
const PageMissionCenterReview = loadable(() => import('./page-mission-center-review'));
const PageMissionCenterTaskWithMenu = loadable(() => import('./page-mission-center-task/PageWithMenu'));
const PageMissionCenterTask = loadable(() => import('./page-mission-center-task'));
const PageMissionDetail = loadable(() => import('./page-mission-detail'));

export const useOneTableSubModule = () => {
  const pathname = useMicroPath();

  if (pathname.includes(OneTableH5RoutePathEnum.MICRO_SETTING)) {
    return <PageSetting />;
  }

  if (pathname.includes(OneTableH5RoutePathEnum.MICRO_DASHBOARD_H5_MENU)) {
    return <PageDashboardWithMenu />;
  }

  if (pathname.includes(OneTableH5RoutePathEnum.MICRO_DASHBOARD_H5)) {
    return <PageDashboard />;
  }

  if (pathname.includes(OneTableH5RoutePathEnum.MICRO_FORM_MANAGEMENT_H5_MENU)) {
    return <PageFormManagementWithMenu />;
  }

  if (pathname.includes(OneTableH5RoutePathEnum.MICRO_FORM_MANAGEMENT_H5)) {
    return <PageFormManagement />;
  }

  if (pathname.includes(OneTableH5RoutePathEnum.MICRO_MISSION_CENTER_FORM_H5_MENU)) {
    return <PageMissionCenterFormWithMenu />;
  }

  if (pathname.includes(OneTableH5RoutePathEnum.MICRO_MISSION_DETAIL)) {
    return <PageMissionDetail />;
  }

  if (pathname.includes(OneTableH5RoutePathEnum.MICRO_MISSION_CENTER_FORM_H5)) {
    return <PageMissionCenterForm />;
  }

  if (pathname.includes(OneTableH5RoutePathEnum.MICRO_MISSION_CENTER_REVIEW_H5_MENU)) {
    return <PageMissionCenterReviewWithMenu />;
  }

  if (pathname.includes(OneTableH5RoutePathEnum.MICRO_MISSION_CENTER_REVIEW_H5)) {
    return <PageMissionCenterReview />;
  }

  if (pathname.includes(OneTableH5RoutePathEnum.MICRO_MISSION_CENTER_TASK_H5_MENU)) {
    return <PageMissionCenterTaskWithMenu />;
  }

  if (pathname.includes(OneTableH5RoutePathEnum.MICRO_MISSION_CENTER_TASK_H5)) {
    return <PageMissionCenterTask />;
  }

  return null;
};
