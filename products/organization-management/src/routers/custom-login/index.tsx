import { useController } from '@mdtBsComm/hooks/use-controller';
import { AppController } from '../../app/AppController';
import { CustomLogin, CustomLoginController, CustomLoginModel } from '../../pages/custom-login';

const CustomLoginView = () => {
  const [controller] = useController(() => {
    const ctrl = new CustomLoginController(AppController.getInstance(), CustomLoginModel);
    return [ctrl, null];
  });

  return <CustomLogin controller={controller} />;
};

export default CustomLoginView;
