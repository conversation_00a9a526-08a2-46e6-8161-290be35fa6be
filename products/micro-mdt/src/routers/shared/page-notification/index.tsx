import { useController } from '@mdtBsComm/hooks/use-controller';
import { NotificationModel } from '@mdtProMicroModules/models/NotificationModel';
import {
  NotificationPage,
  NotificationPageController,
} from '@mdtProMicroModules/pages-in-micro/shared/pages/notification';

const PageNotification = () => {
  const [controller] = useController(() => {
    const ctrl = new NotificationPageController(NotificationModel);
    return [ctrl, null];
  });

  return <NotificationPage controller={controller} />;
};

export default PageNotification;
