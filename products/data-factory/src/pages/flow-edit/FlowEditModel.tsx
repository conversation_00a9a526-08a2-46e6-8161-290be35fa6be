import { Observable } from 'rxjs';
import { IRequestCancelToken } from '@mdtApis/interfaces/request';
import {
  IDataset,
  IFlow,
  IFlowPost,
  IFlowRevision,
  IFlowRun,
  IFlowRunPost,
  IFlowSupportNode,
  ITemporaryExecuteFlow,
  ITemporaryExecuteFlowResult,
} from '@mdtBsServices/interfaces';
import { NodeGroupEnum } from './_util/enum';

export type INodeList = Record<NodeGroupEnum, IFlowSupportNode[]>;

export interface IFlowEditModel {
  groupNodeList: (data: IFlowSupportNode[]) => INodeList;

  // 获取节点列表
  getNodeList: (cancelToken?: IRequestCancelToken) => Observable<INodeList>;

  // 获取节点列表Select
  getAlreadyHaveFlowList: (cancelToken?: IRequestCancelToken) => Observable<any>;

  // 执行临时FLOW
  runTemporaryFlow: (
    data: ITemporaryExecuteFlow,
    cancelToken?: IRequestCancelToken,
  ) => Observable<ITemporaryExecuteFlowResult>;

  runFlow: (flowId: string, data: IFlowRunPost, cancelToken?: IRequestCancelToken) => Observable<IFlowRun>;

  // 更新FLOW
  putFlow: (
    flowId: string,
    data: IFlowPost,
    params: IFlowRevision,
    cancelToken?: IRequestCancelToken,
  ) => Observable<IFlow | undefined>;

  // 新增FLOW
  postFlow: (data: IFlowPost, cancelToken?: IRequestCancelToken) => Observable<IFlow | undefined>;

  // 获取数据源
  getDatasets: (cancelToken?: IRequestCancelToken) => Observable<IDataset[]>;
}
