import { IAfterSubmitConfig } from './properties/AfterSubmitProps';
import { FormSpecTypeEnum } from './properties/FormSpecProps';
import type { TimerTypeEnum } from './properties/TimerProps';

export default function MdtExternalProvider() {}

export interface IBpmnCustomerNode {
  id: string;
  name?: string;
}

MdtExternalProvider.prototype.getServiceNameOptions = function () {
  return [];
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
MdtExternalProvider.prototype.getFunctionNameOptions = function (serviceName: string) {
  return [];
};

export interface IClickFunctionParamsOptions {
  serviceName: string;
  functionName: string;
  functionLabel: string;
  value: string;
  callback: (newVal: string) => void;
}
// eslint-disable-next-line @typescript-eslint/no-unused-vars
MdtExternalProvider.prototype.handleClickFunctionParams = function (options: IClickFunctionParamsOptions) {
  return {};
};

MdtExternalProvider.prototype.handleClickPkgSelector =
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  function (node: IBpmnCustomerNode, value: string, callback: (newVal: string) => void) {};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
MdtExternalProvider.prototype.handleClickAssignment = function (value: string, callback: (newVal: string) => void) {};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
MdtExternalProvider.prototype.handleClickStarter = function (value: string, callback: (newVal: string) => void) {};

MdtExternalProvider.prototype.handleClickFormily =
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  function (node: IBpmnCustomerNode, value: string, callback: (newVal: string) => void) {};

MdtExternalProvider.prototype.handleConfigDynamicAssignees =
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  function (value: string, callback: (newVal: string, script: string) => void) {};

MdtExternalProvider.prototype.handleConfigScriptGlobalVars =
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  function (value: string, callback: (newVal: string, script: string) => void) {};

MdtExternalProvider.prototype.handleConfigGlobalVariables =
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  function (value: string, callback: (newVal: string) => void) {};

export interface IFormSpecValue {
  specType: FormSpecTypeEnum;
  spec: string;
  defaultValueConfig: string;
}
MdtExternalProvider.prototype.handleConfigFormDefaultValue =
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  function (node: IBpmnCustomerNode, value: IFormSpecValue, callback: (newVal: string) => void) {};

MdtExternalProvider.prototype.handleConfigTimerValue =
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  function (timerType: TimerTypeEnum, value: string, callback: (newVal: string) => void) {};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
MdtExternalProvider.prototype.handleClickExpression = function (value: string, callback: (newVal: string) => void) {};

MdtExternalProvider.prototype.handleConfigWorkflowTmpl =
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  function (title: string, value: string, callback: (newVal: string) => void) {};

MdtExternalProvider.prototype.handleConfigWorkflowDetail =
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  function (value: string, callback: (newVal: string) => void) {};

MdtExternalProvider.prototype.handleConfigUserTaskDetail =
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  function (value: string, callback: (newVal: string) => void) {};

MdtExternalProvider.prototype.handleConfigAfterSubmit =
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  function (value: string, callback: (newVal: IAfterSubmitConfig) => void) {};

MdtExternalProvider.prototype.handleConfigServiceConfig =
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  function (value: string, callback: (newVal: string) => void) {};

MdtExternalProvider.prototype.handleConfigMultiInstance =
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  function (value: string, callback: (newVal: string) => void) {};

MdtExternalProvider.prototype.handleConfigCallActivity =
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  function (value: string, callback: (newVal: string) => void) {};

MdtExternalProvider.prototype.handleConfigIOMapping =
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  function (value: string, callback: (newVal: string) => void) {};

MdtExternalProvider.prototype.handleConfigStandardLoop =
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  function (value: string, callback: (newVal: string) => void) {};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
MdtExternalProvider.prototype.hasSpecialFunctionConfig = function (serviceFunctionName: string): boolean {
  return false;
};
