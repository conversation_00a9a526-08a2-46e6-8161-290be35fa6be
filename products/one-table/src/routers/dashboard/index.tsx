import { useController } from '@mdtBsComm/hooks/use-controller';
import {
  OneTableDashbord,
  OneTableDashbordController,
  OneTableDashbordModelBff,
} from '@mdtProMicroModules/pages/one-table-dashbord';

const DashboardView = () => {
  const [controller] = useController(() => {
    return [new OneTableDashbordController({ Model: OneTableDashbordModelBff }), null];
  }, []);

  return <OneTableDashbord controller={controller} />;
};

export default DashboardView;
