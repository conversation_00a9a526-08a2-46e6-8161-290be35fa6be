import { useEffect, useMemo, useState } from 'react';
import { decompressFromEncodedURIComponent } from 'lz-string';

function useDecompress(value: string) {
  const initialOutput = useMemo(() => {
    if (!value) return value;
    try {
      const decompressed = decompressFromEncodedURIComponent(value);
      return decompressed === null ? value : decompressed;
    } catch {
      return value;
    }
  }, [value]);

  const [output, setOutput] = useState(initialOutput);

  useEffect(() => {
    if (initialOutput !== value) {
      setOutput(initialOutput);
    }
  }, [value, initialOutput]);

  return output;
}

export default useDecompress;
