import React from 'react';
import { useController } from '@mdtBsComm/hooks/use-controller';
import { AppController } from '../app/AppController';
import { IframeEditor, IFrameEditorController } from '../pages/iframe-editor';

const IframeEditorView = () => {
  const [controller] = useController(() => {
    const ctrl = new IFrameEditorController(AppController.getInstance());
    return [ctrl, null];
  });
  return <IframeEditor controller={controller} />;
};

export default IframeEditorView;
