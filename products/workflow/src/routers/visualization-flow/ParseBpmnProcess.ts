import _ from 'lodash';
import { INode } from './BuildBpmnUtil';
import { ParseBpmn } from './ParseBpmn';

interface ITask {
  children: string[];
  data: Record<string, any>;
  id: string;
  internal_data: Record<string, any>;
  parent: string;
  task_spec: string;
  typename: string;
}

export class ParseBpmnProcess extends ParseBpmn<ITask> {
  protected getNodes(et: any) {
    return et['tasks'] as Record<string, ITask>;
  }

  // 解析Start节点
  protected parseStartNode(tasks: Record<string, ITask>, removeIds: string[], existsMap: Record<string, boolean>) {
    const start = _.find(tasks, (it) => it.task_spec === 'Start')!;
    removeIds.push(start.id);
    const startNodeIds = start.children;
    return _.map(startNodeIds, (id) => {
      // 统计可视化节点，用于连线判断
      existsMap[id] = true;
      removeIds.push(id);
      const node = this.transformTaskToNode(tasks[id]);
      node.inputs = [];
      return node;
    });
  }

  protected parseBoundaryNodes(tasks: Record<string, ITask>, removeIds: string[], existsMap: Record<string, boolean>) {
    const boundaryNodes: INode[] = [];
    const boundarys = _.filter(tasks, (it) => _.split(it.task_spec, '.').length > 1);
    _.forEach(boundarys, (boundary) => {
      removeIds.push(boundary.id, ...boundary.children);
      if (!boundary.task_spec.includes('BoundaryEventSplit')) return;
      const splitNodeId = boundary.id;
      const activeNodeId = boundary.children[0];
      if (!activeNodeId) return;
      const spliteNode = tasks[splitNodeId];
      const activeNode = tasks[activeNodeId]!;
      const children = _.reject(spliteNode.children, (it) => it === activeNodeId);
      // // 统计可视化节点，用于连线判断
      existsMap[activeNodeId] = true;
      const childrenNodes = _.map(children, (child) => {
        existsMap[child] = true;
        const childNode = this.transformTaskToNode(tasks[child]);
        childNode.outputs = _.reject(childNode.outputs, (it) => {
          const isJoin = tasks[it].task_spec.includes('BoundaryEventJoin');
          isJoin && removeIds.push(it);
          return isJoin;
        });
        return childNode;
      });
      const boundaryNode = this.transformTaskToNode(activeNode);
      boundaryNode.children = children;
      boundaryNode.childrenNodes = childrenNodes;
      boundaryNode.inputs = [activeNode.parent];
      boundaryNode.outputs = _.reject(activeNode.children, (it) => {
        const isJoin = tasks[it].task_spec.includes('BoundaryEventJoin');
        isJoin && removeIds.push(it);
        return isJoin;
      });
      boundaryNodes.push(boundaryNode);
    });
    return boundaryNodes;
  }

  protected parseEndNode(tasks: Record<string, ITask>, removeIds: string[], existsMap: Record<string, boolean>) {
    const ends = _.filter(tasks, (it) => it.task_spec === 'EndEvent');
    return _.map(ends, (end) => {
      const id = end.id;
      existsMap[id] = true;
      removeIds.push(id);
      const node = this.transformTaskToNode(tasks[id]);
      node.outputs = [];
      return node;
    });
  }

  // 解析剩余节点
  protected parseLeftNodes(tasks: Record<string, ITask>, removeIds: string[], existsMap: Record<string, boolean>) {
    const leftNodes: Record<string, ITask> = _.omit(tasks, removeIds);
    return _.map(leftNodes, (it) => {
      // 统计可视化节点，用于连线判断
      existsMap[it.id] = true;
      return this.transformTaskToNode(it);
    });
  }

  private transformTaskToNode(task: ITask): INode {
    const spec = this.engineContext.spec.task_specs[task.task_spec];
    return {
      id: task.id,
      label: spec.bpmn_name || '',
      inputs: task.parent ? [task.parent] : [],
      outputs: task.children,
      typename: spec.typename,
      defaultTaskSpec: spec.default_task_spec,
      eventDefinition: spec.event_definition,
    };
  }
}
