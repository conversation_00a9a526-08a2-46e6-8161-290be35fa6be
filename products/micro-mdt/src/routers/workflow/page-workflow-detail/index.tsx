import { useEffect, useRef, useState } from 'react';
import { useParams } from 'react-router-dom';
import { toastApi } from '@metroDesign/toast';
import { AppLoading } from '@mdtBsComm/components/app-loading';
import { getWorkflowSpecifiedRootXmlIdFromUrl, getWorkflowUserTaskIdFromUrl } from '@mdtProComm/utils/urlUtil';
import { WINDOW_TITLE } from '@mdtProMicroModules/datlas/datlasConfig';
import {
  WorkflowDetail,
  WorkflowDetailController,
  WorkflowDetailModelBff,
} from '@mdtProMicroModules/pages/workflow-detail';
import { WorkFlowFormModel } from '@mdtProMicroModules/pages-in-micro/workflow/_util/WorkFlowFormModel';
import { useAppContext } from '../../../app/appContext';
import i18n from '../../../languages';
import './index.less';

// 在流程详情页面，有两类接口，第一类是表单中请求数据源的接口，第二类是流程详情和流程实例相关的接口（node list,task list）
// 第二类接口无需通过bff代理模拟登录，因为他们只是和流程相关的，不涉及权限问题（就算加上模拟登录也无影响，只是多请求了一次模拟登录接口）
const WorkflowDetailView = () => {
  const { appController } = useAppContext();
  const [loading, setLoading] = useState(true);
  const { wfSpecId, wfId, userTaskXmlId } = useParams<Record<string, string>>();
  const userTaskId = getWorkflowUserTaskIdFromUrl();
  const specifiedRootXmlId = getWorkflowSpecifiedRootXmlIdFromUrl();
  const controllerRef = useRef<WorkflowDetailController | null>(null);

  useEffect(() => {
    const request = async () => {
      const wfDetail = await WorkFlowFormModel.getStartWorkflowDetailInfo(wfSpecId, { quiet: true }, wfId);
      wfDetail.active && (await appController.initDataSourceRequestProxy(wfDetail.appId!, wfDetail.userId!, false));
      controllerRef.current = new WorkflowDetailController(
        {
          Model: WorkflowDetailModelBff,
          workflowId: wfId,
          userTaskXmlId,
          userTaskId,
          specifiedRootXmlId,
        },
        () => {
          toastApi.success(i18n.chain.comTip.optSuccess);
          setTimeout(() => {
            window.location.reload();
          }, 500);
        },
      );
      setLoading(false);
    };
    request();
    return () => {
      controllerRef.current?.destroy();
      controllerRef.current = null;
    };
  }, []);

  return loading ? (
    <AppLoading title={WINDOW_TITLE} />
  ) : (
    <WorkflowDetail key={Math.random()} controller={controllerRef.current!} />
  );
};

export default WorkflowDetailView;
