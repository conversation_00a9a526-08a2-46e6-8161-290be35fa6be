import _ from 'lodash';
import { from } from 'rxjs';
import { map, takeWhile } from 'rxjs/operators';
import {
  getAlbumAllTagsAsync,
  queryAlbumDatapkgsAsyncWrap,
  queryAlbumDatapkgsPaginationTotalAsyncWrap,
} from '@mdtBsServices/albums';
import { IAlbumDatapkgIdsQuery, IDatapkg, IRequestCancelToken, IServerResponse } from '@mdtProComm/interfaces';
import { CommonModel } from '@mdtProComm/models/CommonModel';
import { IGroupListUnitData } from '@mdtProMicroModules/containers/collapse-taggroups-list';
import { IDatapkgItem } from '@mdtProMicroModules/containers/table-card-datapkg-search';
import { DatapkgSearchModel } from '../../_util/DatapkgSearchModel';
import i18n from '../../languages';

interface IRespData {
  total?: number;
  list: IDatapkg[];
  taggroupIdNameMap: Record<string, string>;
}

const APP_NAME = i18n.chain.datamarket.pkgSearch.ownerApp;

export class AlbumPkgSearchModel extends DatapkgSearchModel {
  // 查询下一页数据
  public static queryNextPageTickets(
    albumId: string,
    params: IAlbumDatapkgIdsQuery,
    cancelToken: IRequestCancelToken,
    apps?: string,
  ) {
    return from(
      new Promise<IServerResponse<IRespData>>((resolve) => {
        const request = async () => {
          const resp = await queryAlbumDatapkgsAsyncWrap(albumId, params, { cancelToken }, true);
          // 如果成功，则在查询标签的名称
          let list: IDatapkg[] = [];
          let taggroupIdNameMap: Record<string, string> = {};
          if (resp.success) {
            list = resp.data!;
            taggroupIdNameMap = await this.getTaggroupIdNameMap(list, cancelToken, apps);
          }
          resolve({
            success: resp.success,
            canceled: resp.canceled,
            data: { list, taggroupIdNameMap },
          });
        };
        request();
      }),
    ).pipe(
      takeWhile((v) => !v.canceled),
      map((resp) => {
        const { list, taggroupIdNameMap } = resp.data!;
        return _.map(list, (it) => this.transformToDatapkgItem(it, APP_NAME, taggroupIdNameMap));
      }),
    );
  }

  // 查询第一页数据
  public static queryFirstPagePkgs(
    albumId: string,
    params: IAlbumDatapkgIdsQuery,
    cancelToken: IRequestCancelToken,
    apps?: string,
  ) {
    return from(
      new Promise<IServerResponse<IRespData>>((resolve) => {
        const request = async () => {
          const resp = await queryAlbumDatapkgsPaginationTotalAsyncWrap(albumId, params, { cancelToken }, true);

          let total = 0;
          let list: IDatapkg[] = [];
          let taggroupIdNameMap: Record<string, string> = {};
          if (resp.success) {
            const { total_count, dataResult } = resp.data!;
            list = dataResult;
            total = total_count;
            taggroupIdNameMap = await this.getTaggroupIdNameMap(list, cancelToken, apps);
          }
          resolve({
            success: resp.success,
            canceled: resp.canceled,
            data: { total, list, taggroupIdNameMap },
          });
        };
        request();
      }),
    ).pipe(
      takeWhile((v) => !v.canceled),
      map((resp) => {
        const { total, list, taggroupIdNameMap } = resp.data!;
        const data = _.map(list, (it) => this.transformToDatapkgItem(it, APP_NAME, taggroupIdNameMap));
        return [total!, data] as [number, IDatapkgItem[]];
      }),
    );
  }

  // 查询主题库的标签
  public static queryAllTags(albumId: string, cancelToken: IRequestCancelToken, apps?: string) {
    return from(
      new Promise<IServerResponse<[number, IGroupListUnitData[]]>>((resolve) => {
        const request = async () => {
          const resp = await getAlbumAllTagsAsync(albumId, { cancelToken });
          const tags = resp.success ? resp.data!.tags : [];
          const tgIds = _.map(tags, 'taggroup_id');
          let taggroupIdNameMap: Record<string, string> = {};
          if (tgIds.length) {
            taggroupIdNameMap = await CommonModel.getTaggroupIdNameMap(tgIds, cancelToken, apps);
          }
          const data = _.compact(
            _.map(tags, ({ taggroup_id, taggroup_tags }) => {
              const label = taggroupIdNameMap[taggroup_id];
              return label ? { group: label, groupId: taggroup_id, data: taggroup_tags } : undefined;
            }),
          );
          resolve({
            success: resp.success,
            canceled: resp.canceled,
            data: [0, data] as [number, IGroupListUnitData[]],
          });
        };
        request();
      }),
    ).pipe(
      takeWhile((v) => !v.canceled),
      map((resp) => resp.data!),
    );
  }
}

export type IAlbumPkgSearchModel = typeof AlbumPkgSearchModel;
