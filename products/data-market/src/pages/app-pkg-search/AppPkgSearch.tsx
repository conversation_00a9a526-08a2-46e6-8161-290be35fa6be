import { FC } from 'react';
import { TableCardDatapkgSearch } from '@mdtProMicroModules/containers/table-card-datapkg-search';
import { DrawerDatapkgDetailPreview } from '@mdtProMicroModules/pages/datapkg-detail-preview';
import { AppPkgSearchController } from './AppPkgSearchController';

// App数据包检索页面==================================================================================
interface IProps {
  controller: AppPkgSearchController;
}
const AppPkgSearch: FC<IProps> = ({ controller }) => {
  return (
    <>
      <TableCardDatapkgSearch controller={controller.getTableCardController()} />
      <DrawerDatapkgDetailPreview controller={controller.getDrawerController()} />
    </>
  );
};

export { AppPkgSearch };
