import _ from 'lodash';
import microApp from '@micro-zoe/micro-app';
import { History } from 'history';
import { IRequestOwner, IRequestRequestConfig } from '@mdtApis/interfaces';
import { ApolloService, IBffRequestConfig } from '@mdtBsBffServices/ApolloService';
import { saveCompressValueToUrl } from '@mdtBsComm/utils/urlUtil';
import { authByTokenAsync } from '@mdtBsServices/auth';
import { initBffCommonService } from '@mdtProComm/bff-services';
import {
  MicroMyDataNotifyType,
  ModuleIdEnum,
  moduleIdToProductPrefixMap,
  ProductPrefixEnum,
} from '@mdtProComm/constants';
import { OrgShareRoutePathEnum } from '@mdtProComm/constants';
import { getModuleIds, getProductIds, getProductName } from '@mdtProComm/utils/commonUtil';
import { getDmCloudFlagFromUrl, getQFromUrl } from '@mdtProComm/utils/urlUtil';
import { DatlasAppController } from '@mdtProMicroModules/datlas/app/DatlasAppController';
import { WorkflowPath } from '../_util/product-path';
import { RouterController } from '../_util/RouterController';
import { UserPermissionController } from '../_util/UserPermissionController';
import { API_URL } from '../config';
import i18n from '../languages';
import { AppLayout } from './AppLayout';
import { AppSideMenuController } from './AppSideMenuController';

/**
 * 使用单例模式
 */
class AppController extends DatlasAppController<RouterController, UserPermissionController, AppSideMenuController> {
  protected removeDataSourceRequestProxy?: () => void;
  private defaultRequestOwner?: IRequestOwner;

  private constructor(history: History) {
    super({ i18n });
    initBffCommonService(this.getProductPrefix(history));
    this.initSubData({ urlQ: getQFromUrl() });
    this.routerController = new RouterController(history, this);
  }

  public destroy() {
    super.destroy();
    microApp.clearGlobalDataListener();
    this.defaultRequestOwner = null!;
    this.removeDataSourceRequestProxy?.();
    this.removeDataSourceRequestProxy = undefined;
  }

  public adaptaUserWithUnlogin() {
    const tk = this.getUserToken();
    const itk = this.getImpersonateToken();
    this.initRequestToken(tk, itk);
    this.initInnerRequireController();
    const upc = new UserPermissionController(this.getUserPermission()!, this.getPermissionController());
    this.userPermissionController = upc;
    this.routerController?.initRoutes();
    this.getAuth$().next(true);
    this.getVisible$().next(true);
  }

  public setDefaultRequestOwner(owner: IRequestOwner) {
    this.defaultRequestOwner = owner;
  }

  // 初始化数据源请求需要代理的情况
  // 用于填报发起页面和流程详情页面（已登录）
  // 填报发起页面有需要登录和无需登录的情况，用户未登录 流程需要登录填报时接口会返回401去登录页面
  // eslint-disable-next-line sonarjs/cognitive-complexity
  public async initDataSourceRequestProxy(appId: number, userId: number, isPublic?: boolean) {
    this.defaultRequestOwner = { appId, userId };
    if (!isPublic && !this.getUserId()) {
      const resp = await authByTokenAsync({ quiet: true });
      if (resp.success) {
        this.setLoginUser(resp.data!);
      }
    }
    // 任何人都可以访问问卷时，如果用户登录了，是否按照未登录用户处理?(目前按照未登录处理)
    this.removeBffProxy?.(); // 先删除全局enablebffproxy
    const ins = this.getRequest()!.getIns().interceptors;
    // 请求拦截
    const reqIns = ins.request.use((config: IRequestRequestConfig) => {
      config.disableBffProxy = config.disableBffProxy ?? false;
      return config.disableBffProxy
        ? config
        : this.redirectToBffPublicFlowork({ ...config, owner: this.defaultRequestOwner });
    });
    const respIns = ins.response.use((resp) => {
      const cf = (resp.config || {}) as IRequestRequestConfig;
      if (cf.disableBffProxy) return resp;
      // bff的数据格式转换
      return { ...resp, data: resp.data?.page_data };
    });

    const bffIns = ApolloService.getAxiosIns().interceptors;
    const bffReqIns = bffIns.request.use((config: IBffRequestConfig) => {
      config.disableBffProxy = config.disableBffProxy ?? false;
      return config.disableBffProxy
        ? config
        : this.redirectToBffInnerGraphql({ ...config, owner: this.defaultRequestOwner });
    });
    const bffRespIns = bffIns.response.use((resp) => {
      const cf = (resp.config || {}) as IBffRequestConfig;
      if (cf.disableBffProxy) return resp;
      return { ...resp, data: { data: _.get(resp, 'data.page_data.data') } };
    });
    this.removeDataSourceRequestProxy = () => {
      ins.request.eject(reqIns);
      ins.response.eject(respIns);
      bffIns.request.eject(bffReqIns);
      bffIns.response.eject(bffRespIns);
    };
  }

  public changeTitle(pathname: string) {
    // 如果设置了偏好或者是问卷界面，不需要修改title
    if (this.getModifyTitleByPref() || _.includes(pathname, WorkflowPath.WORKFLOW_APPLY)) return;
    const products = getProductIds();
    const productIndex = _.findIndex(products, (product) => _.includes(pathname, product));
    if (productIndex === -1) return;
    const path = [getProductName(products[productIndex])];

    const modules = getModuleIds();
    const modulesIndex = _.findIndex(modules, (module) => _.includes(pathname, module));
    if (modulesIndex > -1) {
      path.push(getProductName(modules[modulesIndex]));
    }
    document.title = _.join(path, '-');
  }

  public getCustomAppLayout() {
    return AppLayout;
  }

  public jumpToProductDataFactory() {
    if (this.isCurrentModule(ModuleIdEnum.DATA_FACTORY)) return;
    this.getRouterController().gotoDataFactory();
  }

  public jumpToProductDataMarket() {
    if (this.isCurrentModule(ModuleIdEnum.DATA_MARKET)) return;
    this.getRouterController().gotoDataMarket();
  }

  public jumpToProductWorkflow() {
    if (this.isCurrentModule(ModuleIdEnum.WORKFLOW)) return;
    this.getRouterController().gotoWorkflow();
  }

  public jumpToProductOneTable() {
    if (this.isCurrentModule(ModuleIdEnum.ONE_TABLE)) return;
    this.getRouterController().gotoOneTable();
  }

  public jumpToProductMyData() {
    const home = this.getAppHeaderController().getCurrentProductValue();
    this.getRouterController().gotoMyData(home);
  }

  public jumpToOrganizationManagement(params?: Record<string, any>) {
    const home = this.getAppHeaderController().getCurrentProductValue();
    this.getRouterController().gotoOrganizationManagement(home, params?.sub);
  }

  public jumpToProductResourceShare() {
    const home = this.getAppHeaderController().getCurrentProductValue();
    this.getRouterController().gotoResourceShare(home);
  }

  public clickHeaderLogo() {
    this.jumpToHome();
  }

  public jumpToHome() {
    const home = this.getAppHeaderController().getCurrentProductValue();
    if (home === ModuleIdEnum.DATA_FACTORY) {
      this.jumpToProductDataFactory();
    } else if (home === ModuleIdEnum.DATA_MARKET) {
      this.jumpToProductDataMarket();
    } else if (home === ModuleIdEnum.WORKFLOW) {
      this.jumpToProductWorkflow();
    } else if (home === ModuleIdEnum.ONE_TABLE) {
      this.jumpToProductOneTable();
    }
  }

  protected async afterAuthSuccess() {
    const dmCloudFlag = getDmCloudFlagFromUrl();
    const rds = await super.afterAuthSuccess(dmCloudFlag);
    // 处理偏好事件
    this.handlePreferences();
    // 注册子模块数据
    this.initSubData({
      user: this.getOrginUser(),
      itk: this.getImpersonateToken(),
      theme: this.getTheme(),
      requirementData: rds,
    });
    this.initAppReleation();
  }

  private isCurrentModule(moduleId: ModuleIdEnum) {
    const home = this.getAppHeaderController().getCurrentProductValue();
    const module = this.getAppHeaderController().getCurrentModuleValue();
    return home === moduleId && module === ModuleIdEnum.HOME;
  }

  // 构造
  private initAppReleation() {
    // 初始化完必备信息后，构建用户的权限
    const upc = new UserPermissionController(this.getUserPermission()!, this.getPermissionController());
    this.userPermissionController = upc;
    // 设置router
    this.routerController!.initRoutes();
    this.appHeaderController = this.initAppHeader({
      defaultProduct: this.routerController!.getRouterOfHome().slice(1),
      dmCloudFlag: getDmCloudFlagFromUrl(),
    });
  }

  private microRunEvent = (data: any) => {
    const type = _.get(data, 'type');
    const extra = _.get(data, 'extra');
    if (type === MicroMyDataNotifyType.LOGIN_SUCCESS) {
      this.login(data.data, '');
    } else if (type === MicroMyDataNotifyType.REDIRECT_TO_DATA_FACTORY) {
      this.jumpToProductDataFactory();
    } else if (type === MicroMyDataNotifyType.REDIRECT_TO_ORG_AUTH) {
      const query = _.isObject(extra) ? `?q=${saveCompressValueToUrl(extra)}` : '';
      this.jumpToOrganizationManagement({
        sub: `${OrgShareRoutePathEnum.AUTH_MANAGE.substring(1)}${query}`,
      });
    }
  };

  // 通知子应用
  private initSubData(data?: Record<string, any>) {
    const val = { ...(data || {}), backendApiUrl: API_URL, microRunEvent: this.microRunEvent };
    microApp.setGlobalData(val);
  }

  private getProductPrefix(history: History) {
    const moduleId = _.split(history.location.pathname, '/')[1];
    return moduleIdToProductPrefixMap[moduleId] || ProductPrefixEnum.WORKFLOW;
  }
}

export { AppController };
