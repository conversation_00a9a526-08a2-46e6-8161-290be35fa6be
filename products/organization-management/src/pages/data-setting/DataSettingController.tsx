import _ from 'lodash';
import { Fragment } from 'react';
import { BehaviorSubject, forkJoin, of } from 'rxjs';
import { IEmotionProps, ModalWithBtnsCompEmotionController } from '@mdtBsComponents/modal-with-btns-comp-emotion';
import { TreeLabelValueItemInterface } from '@mdtDesign/check-list';
import { Icon } from '@mdtDesign/icon';
import Tag from '@mdtDesign/tag';
import toastApi from '@mdtDesign/toast';
import Tooltip from '@mdtDesign/tooltip';
import { DbColumnTypeEnum } from '@mdtProComm/constants';
import { TransformIdToName } from '@mdtProMicroModules/components/transform-id-to-name';
import { DrawerModifyFormThemeShareController } from '@mdtProMicroModules/containers/drawer-modify-form-themes-share';
import {
  IVirtualizedTableProps,
  TableCurdWithSimpleSearchController,
} from '@mdtProMicroModules/containers/table-curd-with-simple-search';
import { AppController } from '../../app/AppController';
import i18n from '../../languages';
import { EmptyContent } from './components';
import { IDataSettingModel, ITableData } from './DataSettingModel';

class DataSettingController {
  private app: AppController;
  private Model: IDataSettingModel;
  private tableController: TableCurdWithSimpleSearchController<ITableData>;
  private modifyController: DrawerModifyFormThemeShareController;
  private deleteController: ModalWithBtnsCompEmotionController<ITableData>;
  private loading$ = new BehaviorSubject(true);
  private currentEditIndex?: number;

  public constructor(app: AppController, Model: IDataSettingModel) {
    this.Model = Model;
    this.app = app;
    this.tableController = new TableCurdWithSimpleSearchController<ITableData>({
      dataListCompTableCurdControllerOptions: {
        dataListControllerOptions: {
          loadDataListFunc: this.queryList,
          // filterItemFunc: (item, search) => {
          //   return _.some(item.users, ({ title }) => {
          //     return _.includes(title, search);
          //   });
          // },
          equalItemFunc: (item: ITableData, newItem: ITableData) => {
            return _.isEqual(item, newItem);
          },
        },
        tableOptions: this.initTableOptions,
        curdOptions: this.app.getUserPermissionController()!.getDataSettingPermission(),
        clickCreateBtnFunc: this.createTableData,
        clickEditBtnFunc: this.editTableData,
        clickDeleteBtnFunc: this.deleteTableData,
      },
      headerOptions: this.initHeaderOptions,
    });
    this.modifyController = new DrawerModifyFormThemeShareController({
      modifyDataFunc: this.modifyDataToService,
      initDrawerHeaderPropsFunc: this.initDrawerHeaderProps,
      appId: this.app.getAppId(),
    });
    this.deleteController = new ModalWithBtnsCompEmotionController<ITableData>({
      clickOkBtnFunc: this.deleteDataToService,
      modalCompOptions: { modalOptions: this.initDeleteModalOptions },
    });
    this.init();
  }

  public destroy() {
    this.tableController.destroy();
    this.modifyController.destroy();
    this.deleteController.destroy();
    this.loading$.complete();
    this.app = null!;
    this.Model = null!;
  }

  public getTableController() {
    return this.tableController;
  }

  public getModifyController() {
    return this.modifyController;
  }

  public getDeleteController() {
    return this.deleteController;
  }

  public getLoading$() {
    return this.loading$;
  }

  // 渲染表头所需信息
  private initTableOptions = (): IVirtualizedTableProps => {
    return {
      columns: [
        {
          name: i18n.chain.orgAdmin.dataSetting.username,
          code: 'users',
          render: (result: number[]) =>
            _.map(result, (id, index) => (
              <Fragment key={id + index}>
                <TransformIdToName id={id} type={DbColumnTypeEnum.USER_ID} />
                {!_.isEqual(index, result.length - 1) && '、'}
              </Fragment>
            )),
        },
        {
          name: i18n.chain.orgAdmin.dataSetting.themes,
          code: 'themes',
          render: (result: TreeLabelValueItemInterface[]) => (
            <div>
              {result?.length ? (
                _.map(result, ({ key, title }) => <Tag key={key} tag={title} color="blue-700" />)
              ) : (
                <Tag key="displayNone" tag={i18n.chain.orgAdmin.dataSetting.cannotsee} color="red-700" />
              )}
            </div>
          ),
        },
        {
          name: i18n.chain.orgAdmin.dataSetting.orgs,
          code: 'apps',
          render: (result: TreeLabelValueItemInterface[]) => (
            <div>
              {result?.length ? (
                _.map(result, ({ key, title }) => <Tag key={key} tag={title} color="green-700" />)
              ) : (
                <Tag key="displayNone" tag={i18n.chain.orgAdmin.dataSetting.cannotsee} color="red-700" />
              )}
            </div>
          ),
        },
      ],
      type: 'page-bg',
      withVerticalBorder: false,
      emptyContent: <EmptyContent />,
    };
  };

  // 渲染头部所需信息
  private initHeaderOptions = () => {
    return {
      createBtnLabel: i18n.chain.orgAdmin.dataSetting.addShare,
      inputPlaceholder: i18n.chain.orgAdmin.dataSetting.searchPlaceholder,
      hideInput: true,
      title: (
        <div style={{ display: 'flex' }}>
          {i18n.chain.orgAdmin.menu.dataSetting}
          <Tooltip title={i18n.chain.orgAdmin.dataSetting.desc} placement="bottom">
            <Icon icon="help-2" size={16} style={{ color: '#a7afb7', marginLeft: '5px' }} />
          </Tooltip>
        </div>
      ),
    };
  };

  // 弹窗头部信息
  private initDrawerHeaderProps = () => {
    const modalRest = this.modifyController?.getModalRest();
    return { title: modalRest ? i18n.chain.orgAdmin.dataSetting.editTitle : i18n.chain.orgAdmin.dataSetting.addTitle };
  };

  private createTableData = () => {
    this.currentEditIndex = -1;
    return this.modifyController.openModal();
  };

  // 编辑数据
  private editTableData = (orginalData?: ITableData) => {
    this.currentEditIndex = _.findIndex(this.tableController.getDataListValue(), orginalData);
    return this.modifyController.openModal(orginalData);
  };

  // 删除数据打开
  private deleteTableData = (orginalData?: ITableData) => {
    return this.deleteController.openModal(orginalData);
  };

  // 删除数据
  private deleteDataToService = async (orginalData?: ITableData) => {
    const dataList = _.cloneDeep(this.tableController.getDataListValue());
    _.remove(dataList, orginalData);
    const resp = await this.Model.updateCustomizeThemeList(dataList).toPromise();
    if (resp.success) {
      toastApi.success(i18n.chain.orgAdmin.dataSetting.delSuccess);
    }
    return { ...resp, result: orginalData };
  };

  // 渲染删除弹窗所需信息
  private initDeleteModalOptions = (): IEmotionProps => {
    return {
      emotion: 'alert',
      title: i18n.chain.orgAdmin.dataSetting.del,
      description: i18n.chain.orgAdmin.dataSetting.delDesc,
    };
  };

  private queryList = () => {
    return of(this.Model.getCustomizeThemeList());
  };

  private modifyDataToService = async (originData?: ITableData) => {
    let dataList: ITableData[] = _.cloneDeep(this.tableController.getDataListValue());
    const isNew = this.currentEditIndex! < 0;
    if (isNew) {
      dataList = _.concat(dataList, [originData as ITableData]);
    } else {
      dataList[this.currentEditIndex!] = originData as ITableData;
    }

    const resp = await this.Model.updateCustomizeThemeList(dataList).toPromise();
    if (resp.success) {
      toastApi.success(i18n.chain.orgAdmin.dataSetting.editSuccess);
      !isNew && this.tableController.changeDataList(dataList);
    }
    return { ...resp, result: originData };
  };

  // 初始化
  private async init() {
    forkJoin(this.Model.queryGrantedApps(), this.Model.queryAppAlbums(this.app.getAppId())).subscribe(
      ([appResp, themeResp]) => {
        this.modifyController.initCompOptions({
          themes: themeResp.data,
          apps: appResp.result || [],
        });
        this.tableController.listenFrontFilter();
        this.tableController.loadDataList();
        this.loading$.next(false);
      },
    );
  }
}

export { DataSettingController };
