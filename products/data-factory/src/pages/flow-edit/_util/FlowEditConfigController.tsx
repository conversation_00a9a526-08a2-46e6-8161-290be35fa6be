import _ from 'lodash';
import React from 'react';
import type { Node } from '@antv/x6';
import type { IEvent, IGraphCommandService, NsEdgeCmd, NsGraphCmd, NsNodeCmd } from '@antv/xflow';
import {
  createCmdConfig,
  createGraphConfig,
  createKeybindingConfig,
  DisposableCollection,
  MODELS,
  NsGraph,
  uuidv4,
  XFlowEdgeCommands,
  XFlowGraphCommands,
  XFlowNodeCommands,
} from '@antv/xflow';
import { BehaviorSubject } from 'rxjs';
import { IFlowSupportNode } from '@mdtBsServices/interfaces';
import { ICollapsePanel, IPanelNode } from '@mdtProMicroModules/components/canvas-collapse-panel/interface';
import { ConnectEdge, GRAPH_SHAPE_CONNECTOR_NAME_KEY } from '@mdtProMicroModules/containers/xflow-canvas';
import i18n from '../../../languages';
import {
  CreateNode,
  ExecuteNode,
  ExtractorNode,
  LoaderNode,
  TransformerNode,
} from '../custom-component/flow-node-component';
import { DragNodeItem, NodeClass, NodeItem } from '../custom-component/node-menu';
import FlowEditController from '../FlowEditController';
import { INodeList } from '../FlowEditModel';
import {
  EDGE_COMMON_PROPS,
  EDGE_LINE_NORMAL,
  GRAPH_ATTR_PORT_GROUP_KEY,
  NODE_COMMON_PROPS,
  NODE_RENAME_INFO_MAP,
  PORT_CONNECTED,
} from './constants';
import {
  GraphEventEnum,
  GraphPortGroupEnum,
  NodeGroupEnum,
  NodeRenderKeyEnum,
  NodeStatusEnum,
  NodeTypeEnum,
} from './enum';
import { IEdgeRelation, INodeData } from './interfaces';
import { getPortsConfig } from './portsConfig';

interface ISelectNode {
  node?: Node;
  noSave?: boolean;
}
interface INodeItem {
  id: string;
  title: string;
  list: IFlowSupportNode[];
}

const TRANSFORMER_NODE_NAME = i18n.chain.dataFactory.flow.dataHandle;
const PROCESSING_LIST_NODE = [
  {
    id: NodeRenderKeyEnum.TRANSFORMER_NODE,
    title: `${TRANSFORMER_NODE_NAME}--${i18n.chain.dataFactory.flow.filterData}`,
    list: ['filter', 'only_columns', 'drop_duplicates', 'drop_columns'],
  },
  {
    id: NodeRenderKeyEnum.TRANSFORMER_NODE,
    title: `${TRANSFORMER_NODE_NAME}--${i18n.chain.dataFactory.flow.replaceData}`,
    list: ['null_if', 'replace', 'value_mapping', 'strip'],
  },
  {
    id: NodeRenderKeyEnum.TRANSFORMER_NODE,
    title: `${TRANSFORMER_NODE_NAME}--${i18n.chain.dataFactory.flow.addRow}`,
    list: ['insert_const_column', 'insert_sequence_column', 'copy_column'],
  },
  {
    id: NodeRenderKeyEnum.TRANSFORMER_NODE,
    title: `${TRANSFORMER_NODE_NAME}--${i18n.chain.dataFactory.flow.mergedData}`,
    list: ['join', 'append', 'sjoin', 'upsert'],
  },
  {
    id: NodeRenderKeyEnum.TRANSFORMER_NODE,
    title: `${TRANSFORMER_NODE_NAME}--${i18n.chain.dataFactory.flow.geoHandler}`,
    list: ['geocode', 'lnglat_to_geometry', 'convert_geometry'],
  },
];

class FlowEditConfigController {
  private selecteNode$ = new BehaviorSubject<ISelectNode>({ node: undefined });
  private pitchNodeIdList?: string[] = [];
  private flowNodeId$ = new BehaviorSubject('');
  private parent: FlowEditController;

  public constructor(parent: FlowEditController) {
    this.parent = parent;

    // 自动保存node表单参数
    this.selecteNode$.subscribe(({ noSave }) => {
      if (noSave) return;
      const nodeFormController = this.parent.getNodeFormController();
      nodeFormController.onSave();
    });
  }

  public destroy() {
    this.selecteNode$.complete();
    this.selecteNode$.next(null!);
    this.flowNodeId$.complete();
    this.pitchNodeIdList = undefined;
    this.parent = null!;
  }

  public getSelectNode() {
    return this.selecteNode$;
  }

  public getNodeId() {
    return this.flowNodeId$;
  }

  public getPitchNodeIdList() {
    return this.pitchNodeIdList;
  }

  public getSelectNodeValue() {
    return this.selecteNode$.getValue().node;
  }

  public manualTriggerrParamsSave() {
    const selectNode = this.selecteNode$.getValue().node;
    selectNode && this.selecteNode$.next({ node: selectNode });
  }

  // 节点拖拽菜单放下节点的函数
  public async onNodeDrop(nodeConfig: IPanelNode, commandService: IGraphCommandService) {
    commandService.executeCommand<NsNodeCmd.AddNode.IArgs>(XFlowNodeCommands.ADD_NODE.id, {
      // 确保同一个节点可以被多次拖入画布
      nodeConfig: { ...nodeConfig, id: uuidv4() },
    });
  }

  // 节点拖拽菜单查询函数
  public async getSearchService(panels: ICollapsePanel[] = [], keyword: string): Promise<ICollapsePanel[]> {
    let list: ICollapsePanel[] = [];
    _.forEach(panels, (panel) => {
      const listItem = _.clone(panel);
      const panelNodes = _.filter(panel.children, ({ label = '' }) => _.includes(label, keyword));
      if (_.size(panelNodes)) {
        listItem.children = panelNodes;
        list.push(listItem);
      }
    });
    return list;
  }

  // 节点拖拽菜单数据源
  public async getNodeDataService(graphMeta: MODELS.GRAPH_META.IState): Promise<ICollapsePanel[]> {
    const nodeList = graphMeta.nodeList as INodeList;
    const nt: Record<string, IFlowSupportNode> = {};
    _.forEach(nodeList.transformer, (it: any) => {
      nt[it.node_type] = it;
    });
    const tnl: INodeItem[] = _.map(PROCESSING_LIST_NODE, ({ id, list, title }) => {
      const nl = _.compact(
        _.map(list, (it) => {
          const vt = nt[it];
          delete nt[it];
          return vt;
        }),
      );
      return { id, title, list: nl };
    });
    tnl.push({
      id: NodeRenderKeyEnum.TRANSFORMER_NODE,
      title: `${TRANSFORMER_NODE_NAME}--${i18n.chain.dataFactory.flow.other}`,
      list: _.map(nt),
    });

    const data: INodeItem[] = [
      {
        id: NodeRenderKeyEnum.EXTRACTOR_NODE,
        title: i18n.chain.dataFactory.flow.inputSource,
        list: nodeList.extractor,
      },
      ...tnl,
      { id: NodeRenderKeyEnum.EXECUTE_NODE, title: i18n.chain.dataFactory.flow.play, list: nodeList.execute },
      { id: NodeRenderKeyEnum.CREATE_NODE, title: i18n.chain.dataFactory.flow.create, list: nodeList.create },
      { id: NodeRenderKeyEnum.LOADER_NODE, title: i18n.chain.dataFactory.flow.outputSource, list: nodeList.loader },
    ];

    return _.map(data, ({ id, title, list }) => {
      return {
        id: uuidv4(),
        header: (<NodeClass id={id} header={title} />) as unknown as React.ComponentType<ICollapsePanel>,
        children: _.map(list, (node) => {
          const nodeInfo = NODE_RENAME_INFO_MAP[node.node_type as NodeTypeEnum] || {};
          return {
            id: uuidv4(),
            label: nodeInfo.name || node.title,
            type: node.node_type,
            feGroup: node.group,
            renderKey: id,
            popoverContent: <div> {nodeInfo.tip || node.description} </div>,
            renderComponent: (props) => <NodeItem {...props} />,
            dragComponent: DragNodeItem,
          };
        }),
      };
    });
  }

  // 获取画布配置
  public getGraphConfig = () => {
    return createGraphConfig((config) => {
      /** 设置画布配置项，会覆盖XFlow默认画布配置项 */

      config.setX6Config({
        grid: {
          size: 20,
          visible: true,
        },
        background: {
          color: '#f6f7f9',
        },
        scaling: { min: 0.5, max: 2 },
        /** 画布滚轮缩放 */
        mousewheel: {
          enabled: true,
          modifiers: ['ctrl', 'meta'],
          /** 将鼠标位置作为中心缩放 */
          zoomAtMousePosition: true,
        },

        connecting: {
          // 自动吸附
          snap: { radius: 16 },
          allowBlank: false,
          allowMulti: false,
          allowLoop: false,
          allowNode: false,
          allowEdge: false,
          allowPort: true,
          highlight: true,
          connector: GRAPH_SHAPE_CONNECTOR_NAME_KEY,
          sourceAnchor: 'bottom',
          targetAnchor: 'top',
          connectionPoint: 'anchor',
          createEdge: () => new ConnectEdge(),
          validateMagnet: ({ magnet, e }) => {
            e.preventDefault();
            e.stopPropagation();
            // GRAPH_ATTR_PORT_GROUP_KEY：'port-group' 是magnet元素上的属性
            return magnet.getAttribute(GRAPH_ATTR_PORT_GROUP_KEY) === GraphPortGroupEnum.OUT;
          },
          validateConnection(this, { targetCell, targetPort, targetMagnet }) {
            if (!targetMagnet || !targetCell || !targetPort) return false;
            // 只能连接输入桩
            if (targetMagnet.getAttribute(GRAPH_ATTR_PORT_GROUP_KEY) !== GraphPortGroupEnum.IN) return false;
            // 根据 port 上的 connected 判断目标链接桩是否可连接
            const node = targetCell as Node;
            const port = node.getPort(targetPort);
            return !port?.[PORT_CONNECTED];
          },
        },
      });

      /** 设置画布需要渲染的React节点、连线上的React内容 */
      config.setNodeRender(NodeRenderKeyEnum.EXTRACTOR_NODE, ExtractorNode);
      config.setNodeRender(NodeRenderKeyEnum.TRANSFORMER_NODE, TransformerNode);
      config.setNodeRender(NodeRenderKeyEnum.LOADER_NODE, LoaderNode);
      config.setNodeRender(NodeRenderKeyEnum.CREATE_NODE, CreateNode);
      config.setNodeRender(NodeRenderKeyEnum.EXECUTE_NODE, ExecuteNode);

      /**  绑定画布相关事件  */
      config.setEvents(this.getEventsConfig());
    });
  };

  // 获取全局命令Hook配置
  public getCmdConfig = () => {
    return createCmdConfig((config) => {
      config.setRegisterHookFn((hooks) => {
        const list = [
          hooks.addNode.registerHook({
            name: 'addNode',
            handler: async (args) => {
              const graphContrroller = this.parent.getGraphController();
              const nodeConfig = args.nodeConfig;
              const id = nodeConfig.id ?? uuidv4();
              const nodeParams = graphContrroller.getFlowParams('nodeParams', id);
              graphContrroller.saveFlowParams('nodeParams', id, nodeParams);

              const { type = '', feGroup: group = '' } = nodeConfig;
              // 前端group
              const feGroup = this.getNodeGroup(type, group);

              // 对新添加的节点做处理 ports
              const ports = nodeConfig.ports ?? this.renderPorts(group, type);
              const status = nodeConfig.status ?? NodeStatusEnum.INFO;

              const nodeData: INodeData = {
                ...NODE_COMMON_PROPS,
                ...nodeConfig,
                id,
                type: type as NodeTypeEnum,
                ports,
                feGroup,
                status,
                isRunNode$: graphContrroller.getIsRunNode$(),
              };
              args.nodeConfig = nodeData;
            },
          }),
          // hooks.selectNode.registerHook({
          //   name: 'selectNode',
          //   handler: async (args) => {
          //     const selectNode = await MODELS.SELECTED_NODE.useValue(args.modelService!);
          //     console.log('selectNode: ', selectNode);
          //     this.selecteNode$.next({ node: selectNode });
          //   },
          // }),
          hooks.delNode.registerHook({
            name: 'delNode',
            handler: async (args) => {
              const graphContrroller = this.parent.getGraphController();
              const nodeConfig = args.nodeConfig;
              const id = nodeConfig.id;
              graphContrroller.removeFlowParams('nodeParams', id);
            },
          }),
          hooks.addEdge.registerHook({
            name: 'addEdge',
            handler: async (args) => {
              const edgeConfig = args.edgeConfig;
              args.edgeConfig = {
                ...EDGE_COMMON_PROPS,
                ...edgeConfig,
              };
            },
          }),
          hooks.delEdge.registerHook({
            name: 'delEdge',
            handler: async (args) => {
              const graphContrroller = this.parent.getGraphController();
              const edgeConfig = args.edgeConfig;
              const id = edgeConfig?.id;
              id && graphContrroller.removeFlowParams('edgeRelation', id);
            },
          }),
        ];
        const toDispose = new DisposableCollection();
        toDispose.pushAll(list);
        return toDispose;
      });
    })();
  };

  // 获取按键绑定快捷键
  public getKeybindingConfig() {
    return createKeybindingConfig((config) => {
      config.setKeybindingFunc((regsitry) => {
        return regsitry.registerKeybinding([
          // 删除
          {
            id: i18n.chain.dataFactory.flow.delDomOrBorder,
            keybinding: ['backspace', 'Delete'],
            callback: async function (item, modelService, cmd) {
              const cells = await MODELS.SELECTED_CELLS.useValue(modelService);
              // 先删除edges
              await Promise.all(
                _.map(cells, (cell) => {
                  if (cell.isEdge()) {
                    const cellData = cell.getData();
                    return cmd.executeCommand<NsEdgeCmd.DelEdge.IArgs>(XFlowEdgeCommands.DEL_EDGE.id, {
                      edgeConfig: { ...cellData, id: cell.id },
                    });
                  }
                }),
              );
              // 先删除nodes
              await Promise.all(
                _.map(cells, (cell) => {
                  if (cell.isNode()) {
                    return cmd.executeCommand<NsNodeCmd.DelNode.IArgs>(XFlowNodeCommands.DEL_NODE.id, {
                      nodeConfig: {
                        ...cell.getData(),
                        id: cell.id,
                      },
                    });
                  }
                }),
              );
            },
          },
          // 复制
          {
            id: 'copy',
            keybinding: ['command+c', 'ctrl+c'],
            callback: async function (item, modelService, cmd, e) {
              e.preventDefault();
              cmd.executeCommand<NsGraphCmd.GraphCopySelection.IArgs>(XFlowGraphCommands.GRAPH_COPY.id, {});
            },
          },
          // 粘贴
          {
            id: 'paste',
            keybinding: ['command+v', 'ctrl+v'],
            callback: async function (item, ctx, cmd, e) {
              e.preventDefault();
              cmd.executeCommand<NsGraphCmd.GraphPasteSelection.IArgs>(XFlowGraphCommands.GRAPH_PASTE.id, {});
            },
          },
          // 撤销
          {
            id: 'undo',
            keybinding: ['command+z', 'ctrl+z'],
            callback: async function (item, ctx, cmd, e) {
              e.preventDefault();
              cmd.executeCommand<NsGraphCmd.UndoCmd.IArgs>(XFlowGraphCommands.UNDO_CMD.id, {});
            },
          },
          // 重做
          {
            id: 'redo',
            keybinding: ['command+shift+z', 'ctrl+shift+z'],
            callback: async function (item, ctx, cmd, e) {
              e.preventDefault();
              cmd.executeCommand<NsGraphCmd.RedoCmd.IArgs>(XFlowGraphCommands.REDO_CMD.id, {});
            },
          },
        ]);
      });
    })();
  }

  // 根据renderKey 渲染链接桩
  public renderPorts(key: string, nodeType?: NodeTypeEnum): NsGraph.INodePortMeta | undefined {
    return getPortsConfig(key, nodeType);
  }

  // 获取所有事件绑定函数
  private getEventsConfig() {
    const nodeAddedEvent: IEvent<GraphEventEnum.NODE_ADDED> = {
      eventName: GraphEventEnum.NODE_ADDED,
      callback: (e, commandService) => {
        const { node } = e;
        this.selecteNode$.next({ node });
        commandService.executeCommand(XFlowNodeCommands.SELECT_NODE.id, { nodeIds: [node.id], resetSelection: true });
      },
    };

    // 节点点击事件
    const nodeClickEvent: IEvent<GraphEventEnum.NODE_CLICK> = {
      eventName: GraphEventEnum.NODE_CLICK,
      callback: async (x6Event) => {
        const { node } = x6Event;
        this.flowNodeId$.next(node.id);
        this.parent.getNodeFormController().getPitchNodeData(node);
        this.parent.cheageTableIsNone(true);

        const selectNode = this.selecteNode$.getValue().node;
        // 连续点击同一个节点不触发参数保存
        node.id !== selectNode?.id && this.selecteNode$.next({ node });
      },
    };

    // 节点双击事件
    const nodedbClickEvent = {
      eventName: GraphEventEnum.NODE_DBLCLICK,
      callback: async () => {
        const selectNode = this.selecteNode$.getValue().node;
        if (!selectNode) return;
        selectNode.setData({ ...selectNode.getData(), editingTitle: true });
      },
    };

    // 点击空白画布事件
    const blankClickEvent: IEvent<GraphEventEnum.BLANK_CLICK> = {
      eventName: GraphEventEnum.BLANK_CLICK,
      callback: async () => {
        const selectNode = this.selecteNode$.getValue().node;
        this.parent.cheageTableIsNone(false);

        // 触发参数保存
        selectNode !== undefined && this.selecteNode$.next({ node: undefined });
      },
    };

    // 边连接事件
    const edgeConnectedEvent: IEvent<GraphEventEnum.EDGE_CONNECTED> = {
      eventName: GraphEventEnum.EDGE_CONNECTED,
      callback: (e) => {
        const graphContrroller = this.parent.getGraphController();
        const { edge, currentCell, currentPort, view } = e;
        const source = view.sourceView?.cell;
        const souceData = source?.getData<IPanelNode>();
        const sourcePorts = souceData?.ports as NsGraph.INodePortMeta;
        // 连线成功后更改线为实线
        edge.attr({ line: { ...EDGE_LINE_NORMAL } });
        // 连线成功后更改 port connected 属性
        if (currentCell && currentPort) {
          const node = currentCell! as Node;
          const nodeData = node.getData<IPanelNode>();
          node.setPortProp(currentPort, PORT_CONNECTED, true);

          const sourceId = source?.id || '';
          const targetId = currentCell.id;
          // 输出只会有一个 放心使用
          const sourcePortId = sourcePorts.items[0].id || '';
          const targetPortId = currentPort;
          const upNodeType = souceData?.type || '';
          const downNodeType = nodeData.type || '';

          const junction = graphContrroller.dealEdgeJunction(downNodeType, targetId, targetPortId);
          const edgeRelation: IEdgeRelation = {
            id: edge.id,
            upNodeId: sourceId,
            upNodeType,
            upPortId: sourcePortId,
            downNodeId: targetId,
            downNodeType,
            downPortId: targetPortId,
            junction,
          };
          // graphContrroller.saveEdgeRelation(edge.id);
          graphContrroller.saveFlowParams('edgeRelation', edge.id, edgeRelation);

          // 更新锚点
          if (downNodeType === NodeTypeEnum.APPEND || downNodeType === NodeTypeEnum.PIPE) {
            graphContrroller.updateAppendNodeInports(targetId);
          }

          // 更新边的data
          edge.setData<NsGraph.IEdgeConfig>({
            ...(edge.getData() || {}),
            source: sourceId,
            sourcePortId,
            target: targetId,
            targetPortId,
          });
        }
      },
    };

    // 边点击事件
    const edgeClickEvent: IEvent<GraphEventEnum.EDGE_CLICK> = {
      eventName: GraphEventEnum.EDGE_CLICK,
      callback: async (e, commandService) => {
        const selectNode = this.selecteNode$.getValue().node;
        // 触发参数保存
        selectNode !== undefined && this.selecteNode$.next({ node: undefined });
        commandService.executeCommand(XFlowEdgeCommands.HIGHLIGHT_EDGE.id, {
          edgeId: e.edge.id,
          strokeColor: '#3D7FE9',
          strokeWidth: 2,
        });
      },
    };

    // 边移除事件
    const edgeRemovedEvent: IEvent<GraphEventEnum.EDGE_REMOVED> = {
      eventName: GraphEventEnum.EDGE_REMOVED,
      callback: ({ edge }, commandService, modelService, graph) => {
        // 如果没有连接，那么target targetPortId可能为空值
        const edgeData = edge.getData();
        const target = edgeData?.target;
        const targetPortId = edgeData?.targetPortId;
        if (!target || !targetPortId) return;
        const node = graph.getCellById(target) as Node;
        node.setPortProp(targetPortId, PORT_CONNECTED, false);
      },
    };

    return [
      nodeAddedEvent,
      nodeClickEvent,
      nodedbClickEvent,
      blankClickEvent,
      edgeConnectedEvent,
      edgeClickEvent,
      edgeRemovedEvent,
    ];
  }

  // 根据节点类型获取前端Group
  private getNodeGroup(nodeType: string, nodeGroup: string) {
    switch (nodeType) {
      case NodeTypeEnum.CREATE_TABLE:
        return NodeGroupEnum.CREATE;
      case NodeTypeEnum.EXECUTE_SQL:
        return NodeGroupEnum.EXECUTE;
      default:
        return nodeGroup as NodeGroupEnum;
    }
  }
}

export default FlowEditConfigController;
