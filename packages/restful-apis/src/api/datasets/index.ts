import { DATASETS_URL } from '../../config';
import {
  IApiResponse,
  IDatapkgId,
  IDatapkgIds,
  IDataset,
  IDatasetDatapkgFromEmptyPost,
  IDatasetDatapkgFromFilePost,
  IDatasetDatapkgFromSqlPost,
  IDatasetDatapkgFromTablePost,
  IDatasetDatapkgIdByNameQuery,
  IDatasetDatapkgQuery,
  IDatasetId,
  IDatasetIds,
  IDatasetPatch,
  IDatasetPingPost,
  IDatasetPost,
  IDatasetRows,
  IDatasetRowsDialectQuery,
  IDatasetStats,
  IDatasetTabel,
  IPaginationQuery,
  IRequestPromise,
  IRequestRequestConfig,
  ITags,
  ITaskId,
} from '../../interfaces';
import request from '../../request';

// 获取所有的dataset
export const getAllDatasets = (config?: IRequestRequestConfig) => {
  return request({
    ...(config || {}),
    method: 'get',
    url: DATASETS_URL,
  }) as IRequestPromise<IApiResponse<IDataset[]>>;
};

// 添加新的dataset
export const postDataset = (data: IDatasetPost, config?: IRequestRequestConfig) => {
  return request({
    ...(config || {}),
    method: 'post',
    url: DATASETS_URL,
    data,
  }) as IRequestPromise<IApiResponse<IDataset>>;
};

// 获取某个dataset的信息
export const getDataset = (datasetId: string, config?: IRequestRequestConfig) => {
  return request({
    ...(config || {}),
    method: 'get',
    url: `${DATASETS_URL}/${datasetId}`,
  }) as IRequestPromise<IApiResponse<IDataset>>;
};

// 修改dataset的部分属性
export const patchDataset = (datasetId: string, data: IDatasetPatch, config?: IRequestRequestConfig) => {
  return request({
    ...(config || {}),
    method: 'patch',
    url: `${DATASETS_URL}/${datasetId}`,
    data,
  }) as IRequestPromise<IApiResponse<IDataset>>;
};

// 删除dataset
export const deleteDataset = (datasetId: string, config?: IRequestRequestConfig) => {
  return request({
    ...(config || {}),
    method: 'delete',
    url: `${DATASETS_URL}/${datasetId}`,
  }) as IRequestPromise<IApiResponse<IDatasetId>>;
};

// 测试dataset是否可以正常链接
export const pingDataset = (datasetId: string, data?: IDatasetPatch, config?: IRequestRequestConfig) => {
  return request({
    ...(config || {}),
    method: 'post',
    url: `${DATASETS_URL}/${datasetId}/actions/ping`,
    data,
  }) as IRequestPromise<IApiResponse<boolean>>;
};

// 删除数据集下对应的数据包
export const deleteDatapkgIdsFromDataset = (datasetId: string, data: IDatapkgIds, config?: IRequestRequestConfig) => {
  return request({
    ...(config || {}),
    method: 'delete',
    url: `${DATASETS_URL}/${datasetId}/datapkgs`,
    data,
  }) as IRequestPromise<IApiResponse<IDatapkgIds>>;
};

// 创建空数据包
export const postDatapkgToDatasetFromEmpty = (
  datasetId: string,
  data: IDatasetDatapkgFromEmptyPost,
  config?: IRequestRequestConfig,
) => {
  return request({
    ...(config || {}),
    method: 'post',
    url: `${DATASETS_URL}/${datasetId}/datapkgs/create/from_empty`,
    data,
  }) as IRequestPromise<IApiResponse<ITaskId>>;
};

// 从文件上传新建数据包
export const postDatapkgToDatasetFromFile = (
  datasetId: string,
  data: IDatasetDatapkgFromFilePost,
  config?: IRequestRequestConfig,
) => {
  return request({
    ...(config || {}),
    method: 'post',
    url: `${DATASETS_URL}/${datasetId}/datapkgs/create/from_file`,
    data,
  }) as IRequestPromise<IApiResponse<IDatapkgId>>;
};

// 从sql新建数据包
export const postDatapkgToDatasetFromSql = (
  datasetId: string,
  data: IDatasetDatapkgFromSqlPost,
  config?: IRequestRequestConfig,
) => {
  return request({
    ...(config || {}),
    method: 'post',
    url: `${DATASETS_URL}/${datasetId}/datapkgs/create/from_sql`,
    data,
  }) as IRequestPromise<IApiResponse<IDatapkgId>>;
};

// 从table新建数据包
export const postDatapkgToDatasetFromTable = (
  datasetId: string,
  data: IDatasetDatapkgFromTablePost,
  config?: IRequestRequestConfig,
) => {
  return request({
    ...(config || {}),
    method: 'post',
    url: `${DATASETS_URL}/${datasetId}/datapkgs/create/from_table`,
    data,
  }) as IRequestPromise<IApiResponse<IDatapkgId>>;
};

// 在dataset中通过name查找数据包
export const queryDatasetDatapkgIdByName = (
  datasetId: string,
  config: IRequestRequestConfig<IDatasetDatapkgIdByNameQuery>,
) => {
  return request({
    ...(config || {}),
    method: 'get',
    url: `${DATASETS_URL}/${datasetId}/datapkgs/query`,
  }) as IRequestPromise<IApiResponse<IDatapkgId>>;
};

// 查询数据集下的数据包
export const queryDatasetDatapkgIds = (
  datasetId: string,
  data: IDatasetDatapkgQuery,
  config?: IRequestRequestConfig,
) => {
  return request({
    ...(config || {}),
    method: 'post',
    url: `${DATASETS_URL}/${datasetId}/datapkgs/query`,
    data,
  }) as IRequestPromise<IApiResponse<IDatapkgIds>>;
};

// 查询数据集下的全部标签
export const getDatasetAllTags = (datasetId: string, config?: IRequestRequestConfig) => {
  return request({
    ...(config || {}),
    method: 'get',
    url: `${DATASETS_URL}/${datasetId}/datapkgs/tags`,
  }) as IRequestPromise<IApiResponse<ITags>>;
};

// 数据集自带的查询语句查询数据
export const queryDatasetRowsByDialect = (
  datasetId: string,
  data: IDatasetRowsDialectQuery,
  config?: IRequestRequestConfig,
) => {
  return request({
    ...(config || {}),
    method: 'post',
    url: `${DATASETS_URL}/${datasetId}/query/dialect`,
    data,
  }) as IRequestPromise<IApiResponse<IDatasetRows>>;
};

// 查询数据集下的全部schema
export const getDatasetAllSchemas = (datasetId: string, config?: IRequestRequestConfig) => {
  return request({
    ...(config || {}),
    method: 'get',
    url: `${DATASETS_URL}/${datasetId}/schemas`,
  }) as IRequestPromise<IApiResponse<string[]>>;
};

// 获取数据集某一Schema下所有的数据库表格
export const getDatasetAllTablesBySchema = (datasetId: string, schema: string, config?: IRequestRequestConfig) => {
  return request({
    ...(config || {}),
    method: 'get',
    url: `${DATASETS_URL}/${datasetId}/schemas/${schema}/tables`,
  }) as IRequestPromise<IApiResponse<IDatasetTabel[]>>;
};

// 获取数据集某个Schema下某个数据表格的
export const queryDatasetRowsBySchemaTable = (
  datasetId: string,
  schema: string,
  table: string,
  config?: IRequestRequestConfig<IPaginationQuery>,
) => {
  return request({
    ...(config || {}),
    method: 'get',
    url: `${DATASETS_URL}/${datasetId}/schemas/${schema}/tables/${table}/rows`,
  }) as IRequestPromise<IApiResponse<IDatasetRows>>;
};

// 统计单个dataset信息
export const statsDataset = (datasetId: string, config?: IRequestRequestConfig) => {
  return request({
    ...(config || {}),
    method: 'get',
    url: `${DATASETS_URL}/${datasetId}/stats`,
  }) as IRequestPromise<IApiResponse<IDatasetStats>>;
};

// 获取数据集所有Schema下的所有表格
export const getDatasetAllTables = (datasetId: string, config?: IRequestRequestConfig) => {
  return request({
    ...(config || {}),
    method: 'get',
    url: `${DATASETS_URL}/${datasetId}/tables`,
  }) as IRequestPromise<IApiResponse<IDatasetTabel[]>>;
};

// 测试未保存的dataset是否可以正常链接
export const pingUnsaveDataset = (data: IDatasetPingPost, config?: IRequestRequestConfig) => {
  return request({
    ...(config || {}),
    method: 'post',
    url: `${DATASETS_URL}/actions/ping`,
    data,
  }) as IRequestPromise<IApiResponse<boolean>>;
};

// 统计多个dataset信息
export const statsDatasets = (data: IDatasetIds, config?: IRequestRequestConfig) => {
  return request({
    ...(config || {}),
    method: 'post',
    url: `${DATASETS_URL}/batch/stats`,
    data,
  }) as IRequestPromise<IApiResponse<IDatasetStats[]>>;
};
