import { ChangeEvent, FC, useState } from 'react';
import { useMemoizedFn } from 'ahooks';
import Input from '@mdtDesign/input';
import './index.less';

type IValue = string | number;

export interface ITextEditorProps {
  value: IValue;
  onChange?: (val: IValue) => void;
  onComplete?: (val: IValue) => void;
  disabledEdit?: boolean;
}

const TextInput: FC<ITextEditorProps> = ({ value, onChange, onComplete, ...resetProps }) => {
  const [val, setVal] = useState(value);

  const handleChange = useMemoizedFn((e: ChangeEvent<HTMLInputElement>) => {
    const v = e.target.value;
    setVal(v);
    onChange?.(v);
  });

  const handleBlur = useMemoizedFn(() => {
    onComplete?.(val);
  });

  return <Input value={val} onChange={handleChange} onBlur={handleBlur} autoFocus allowClear={false} {...resetProps} />;
};

const TextEditor: FC<ITextEditorProps> = ({ value, onChange, onComplete, disabledEdit, ...resetProps }) => {
  const [showInput, setShowInput] = useState(false);

  const handleComplete = useMemoizedFn((val: IValue) => {
    onComplete?.(val);
    setShowInput(false);
  });

  const content = showInput ? (
    <TextInput value={value} onChange={onChange} onComplete={handleComplete} {...resetProps} />
  ) : (
    <div className="editor-display" onClick={disabledEdit ? undefined : () => setShowInput(true)}>
      {value}
    </div>
  );

  return <div className={`com_text-editor-wrapper ${showInput ? 'active' : ''}`}>{content}</div>;
};

export { TextEditor };
