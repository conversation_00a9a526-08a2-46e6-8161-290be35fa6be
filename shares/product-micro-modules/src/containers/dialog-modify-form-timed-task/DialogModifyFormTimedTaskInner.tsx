import { memo } from 'react';
import { FormField } from '@mdtBsComm/components/form';
import { useObservableState } from '@mdtBsComm/hooks/use-observable-state';
import { lessThanOrSameNow } from '@mdtBsComm/utils/dayUtil';
import { FormComp } from '@mdtBsComponents/form-comp';
import DatePicker from '@mdtDesign/date-picker';
import Input, { InputNumber } from '@mdtDesign/input';
import { RadioGroup } from '@mdtDesign/radio';
import Select from '@mdtDesign/select';
import TimePicker from '@mdtDesign/time-picker';
import i18n from '../../languages';
import {
  CronFrequencyEnum,
  DialogModifyFormTimedTaskController,
  FormDataAttrs,
  frequencyOptions,
  intervalTypeOptions,
  monthOptions,
  TriggerEnum,
  triggerOptions,
  weekOptions,
} from './DialogModifyFormTimedTaskController';
import './index.less';

const RangePicker = DatePicker.RangePicker;

interface IProps {
  controller?: DialogModifyFormTimedTaskController<any>;
}

const DialogModifyFormTaggroupInner = memo((props: IProps) => {
  const controller = props.controller!;
  const formCompController = controller.getFormCompController();
  const value = useObservableState(formCompController.getFormData$());
  const isInterval = value?.triggerType === TriggerEnum.INTERVAL;

  const intervalFields = (
    <div className="interval-wrap">
      <FormField
        label={i18n.chain.proMicroModules.timeTask.interval}
        name={FormDataAttrs.INTERVAL}
        required
        rules={[{ required: true, message: i18n.chain.proMicroModules.timeTask.numberPlaceholder }]}
      >
        <InputNumber placeholder={i18n.chain.proMicroModules.timeTask.numberPlaceholder} />
      </FormField>
      <FormField label="" name={FormDataAttrs.INTERVALTYPE} className="field-interval-type">
        <Select options={intervalTypeOptions} />
      </FormField>
    </div>
  );

  const cronFields = (
    <>
      <FormField label={i18n.chain.proMicroModules.timeTask.frequency} name={FormDataAttrs.FREQUENCY}>
        <RadioGroup options={frequencyOptions} />
      </FormField>
      <FormField
        label={i18n.chain.proMicroModules.timeTask.time}
        name={FormDataAttrs.TIME}
        required
        rules={[{ required: true, message: i18n.chain.proMicroModules.timeTask.timePlaceholder }]}
      >
        <TimePicker format="HH:mm:ss" className="dialog-timed-task_date-picker" />
      </FormField>
      {value?.frequency === CronFrequencyEnum.WEEK && (
        <FormField
          label={i18n.chain.proMicroModules.timeTask.chooseWeek}
          name={FormDataAttrs.WEEKTIME}
          required
          rules={[{ required: true, message: i18n.chain.comPlaceholder.input }]}
        >
          <Select options={weekOptions} mode="multiple" />
        </FormField>
      )}
      {value?.frequency === CronFrequencyEnum.MONTH && (
        <FormField
          label={i18n.chain.proMicroModules.timeTask.chooseMonth}
          name={FormDataAttrs.MONTHTIME}
          required
          rules={[{ required: true, message: i18n.chain.comPlaceholder.input }]}
        >
          <Select options={monthOptions} mode="multiple" />
        </FormField>
      )}
    </>
  );

  return (
    <div className="module_dialog-modify-form-timed-task">
      <FormComp controller={formCompController}>
        <FormField
          label={i18n.chain.proMicroModules.timeTask.name}
          name={FormDataAttrs.NAME}
          required
          rules={[{ required: true, message: i18n.chain.proMicroModules.timeTask.nameEmptyError }]}
        >
          <Input placeholder={i18n.chain.proMicroModules.timeTask.namePlaceholder} block allowClear={false} />
        </FormField>
        <FormField label={i18n.chain.proMicroModules.timeTask.timingWay} name={FormDataAttrs.TRIGGER_TYPE}>
          <RadioGroup options={triggerOptions} />
        </FormField>
        {isInterval ? intervalFields : cronFields}
        <FormField label={i18n.chain.proMicroModules.timeTask.startEndDate} name={FormDataAttrs.STARTENDTIME}>
          <RangePicker
            className="dialog-timed-task_date-picker"
            showTime
            disabledDate={(date) => lessThanOrSameNow(date)}
          />
        </FormField>
      </FormComp>
    </div>
  );
});

export { DialogModifyFormTaggroupInner };
