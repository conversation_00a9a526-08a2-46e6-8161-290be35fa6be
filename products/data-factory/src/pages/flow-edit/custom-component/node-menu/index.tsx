import React, { FC, useState } from 'react';
import { Icon } from '@mdtDesign/icon';
import { Add, Download, PlayCircle, Renew, Upload } from '@mdtDesign/icons';
import { TooltipText } from '@mdtDesign/tooltip';
import { ICollapsePanel, IPanelNode } from '@mdtProMicroModules/components/canvas-collapse-panel/interface';
import { NodeRenderKeyEnum } from '../../_util/enum';
import './index.less';

const NODETYPE_ICON_MAP: Record<NodeRenderKeyEnum, any> = {
  [NodeRenderKeyEnum.EXTRACTOR_NODE]: <Download size={16} color="#2BB480" />,
  [NodeRenderKeyEnum.TRANSFORMER_NODE]: <Renew size={16} color="#3D7FE9" />,
  [NodeRenderKeyEnum.LOADER_NODE]: <Upload size={16} color="#AC60CA" />,
  [NodeRenderKeyEnum.EXECUTE_NODE]: <PlayCircle size={16} color="#F2A70D" />,
  [NodeRenderKeyEnum.CREATE_NODE]: <Add size={16} color="#6389BC" />,
};

export const NodeClass = ({ id, header }: ICollapsePanel) => {
  const [expand, setExpand] = useState(true);

  return (
    <div className="nodeClass" onClick={() => setExpand(!expand)}>
      <div className="left">
        {NODETYPE_ICON_MAP[id as NodeRenderKeyEnum]}
        <span>{header}</span>
      </div>
      <Icon icon={expand ? 'chevron-up-2' : 'chevron-down-2'} className="rightIcon" />
    </div>
  );
};

export const NodeItem: FC<{
  data: IPanelNode;
  isNodePanel: boolean;
}> = ({ data }) => {
  return (
    <div className="nodeItem">
      <TooltipText text={data.label || ''} />
    </div>
  );
};

export const DragNodeItem: FC<{
  data: IPanelNode;
  isNodePanel: boolean;
}> = ({ data }) => {
  return (
    <div className="node-container">
      <div className="node-label">
        <TooltipText text={data.label ?? ''} />
      </div>
    </div>
  );
};
