import _ from 'lodash';
import {
  ArrayCards,
  ArrayTable,
  AutoComplete,
  Cascader,
  Checkbox,
  DatePicker,
  Input,
  Radio,
  Rate,
  Switch,
  Upload,
} from '@mdtProFormEditor/metro-form-design/components';
import { MetroSettingProp } from '@mdtProFormEditor/metro-form-design/shared';
import { GeometryInput } from '../../../components/geometry-input/GeometryInputDesign';
import { UserSelector } from '../../../containers/formily-user-selector/UserSelectorDesign';
import { componentPropsPath } from './_constants';

// 题型使用的组件与组件属性的映射
// tip: 无需配置Select Select.Multiple, 因为下拉组件不属于题型的直接组件。
export const componentNamePropsMap: Record<string, any> = {
  'Radio.Group': _.get(Radio, componentPropsPath),
  'Checkbox.Group': _.get(Checkbox, componentPropsPath),
  DatePicker: _.get(DatePicker, componentPropsPath),
  Input: _.get(Input, componentPropsPath),
  GeometryInput: _.get(GeometryInput, componentPropsPath),
  Upload: _.get(Upload, componentPropsPath),
  Switch: _.get(Switch, componentPropsPath),
  UserSelector: _.get(UserSelector, componentPropsPath),
  Rate: _.get(Rate, componentPropsPath),
  ArrayCards: _.get(ArrayCards, componentPropsPath),
  ArrayTable: _.get(ArrayTable, componentPropsPath),
  Cascader: _.get(Cascader, componentPropsPath),
  AutoComplete: _.get(AutoComplete, componentPropsPath),
};

const MetroSettingPropList = _.values(MetroSettingProp);

/**
 * nodeProps 属性设置中的原始formily属性
 * metroValues 属性设置中的自定义属性
 * settingProps 把属性设置中的自定义属性转换为formily属性后的值
 */
export const groupSettingValues = (values: any): any => {
  const nodeProps = _.omit(values, MetroSettingPropList);
  const metroValues = _.pick(values, MetroSettingPropList);
  let settingProps: any = _.pick(metroValues, [MetroSettingProp.addOtherOption, MetroSettingProp.otherOptionLabel]);

  return [nodeProps, settingProps, metroValues];
};
