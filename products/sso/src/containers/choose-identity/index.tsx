import _ from 'lodash';
import { FC, useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import classnames from 'classnames';
import { Checkbox } from '@mdtLogin/components/checkbox';
import { Spin } from '@mdtLogin/components/spin';
import Toast from '@mdtLogin/components/toast';
import { LoginTitle } from '@mdtLogin/containers/login-title';
import { afterLoginSuccess, fetchRequest, getTokenHeader, requestError } from '../../_util/fetchUtil';
import { ILoginUser } from '../../interface';
import i18n from '../../languages';
import './index.less';

// api请求结果处理
const receiveResponse = async (response: any, callBack: Function, failCallback?: Function) => {
  const status = response.status;
  const _response = await response.json();
  const { rc, result } = _response;
  if (rc === 0) {
    return callBack(result);
  } else {
    Toast.error(requestError(_response, status, i18n));
    return failCallback?.(_response);
  }
};

export interface ISelect {
  onSelect: (id: string) => Promise<void>;
}
export interface IAppData {
  enable: boolean;
  id: string;
  name: string;
  orgName: string;
  defaultIdentity?: boolean;
  expired?: boolean;
}

export interface IAppMap {
  [key: string]: IAppData[];
}

const IDentityCell: FC<IAppData & ISelect> = ({ id, name, orgName, enable, expired, defaultIdentity, onSelect }) => {
  const onCellClick = () => {
    enable && onSelect(id);
  };
  const defaultText = defaultIdentity ? i18n.chain.default : '';
  const extraText = defaultText + (!enable ? (expired ? i18n.chain.expired : i18n.chain.disabled) : '');
  return (
    <div
      className={classnames('mdt-login-choose-indentity-cell', {
        'mdt-login-choose-indentity-cell-disabled': !enable,
      })}
      onClick={onCellClick}
    >
      <div className="mdt-login-choose-indentity-cell-icon">{(name || orgName).substring(0, 1)}</div>
      <div>
        <div className="mdt-login-choose-indentity-cell-org-name">{name + extraText}</div>
        {orgName ? <div className="mdt-login-choose-indentity-cell-name">{`@${orgName}`}</div> : null}
      </div>
      <svg width="18" height="18" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M19.998 12l-2.83 2.83 9.17 9.17-9.17 9.17 2.83 2.83 12-12-12-12z" fill="currentColor" />
      </svg>
    </div>
  );
};

const AppCell: FC<{ data: IAppData[]; name: string; onSelect: ISelect['onSelect'] }> = ({
  data = [],
  name,
  onSelect,
}) => {
  return (
    <div className="mdt-login-choose-indentity-app">
      <div className="mdt-login-choose-indentity-primary-tag">{name}</div>
      {_.map(data, (item, index) => (
        <IDentityCell key={index} {...item} onSelect={onSelect} />
      ))}
    </div>
  );
};

const transformIdentities = (identities: ILoginUser['identities'] = []): IAppMap => {
  return _.reduce(
    identities,
    (acc: IAppMap, identity) => {
      const originalName = _.get(identity, 'nickname', '') || _.get(identity, 'name', '');
      const [name = '', orgName] = originalName.split('|');

      const appData: IAppData = {
        enable: _.get(identity, 'enable', false),
        id: _.get(identity, 'id', ''),
        name,
        orgName,
        expired: _.get(identity, 'expired', false),
        defaultIdentity: _.get(identity, 'is_main', false),
      };

      const appName = _.get(identity, 'app_name', '');
      if (!_.has(acc, appName)) {
        acc[appName] = [];
      }

      acc[appName].push(appData);

      return acc;
    },
    {},
  );
};

const goBack = () => {
  window.history.back();
};

const getLoginInfo = (token?: string, callback?: (value: ILoginUser) => void) => {
  // 查找顺序: 优先本地存储 > 其次token获取
  const sessionKey = 'mdt-login-identities';
  const localIdentities = sessionStorage.getItem(sessionKey);
  if (localIdentities) {
    // rc 不为零的时候，无法通过bytoken获取登录信息，所以需要构造一个不完整的登录对象
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    const generateLoginInfo = { auth: token, identities: JSON.parse(localIdentities) } as ILoginUser;
    sessionStorage.removeItem(sessionKey);
    callback?.(generateLoginInfo);
    return;
  }
  if (token) {
    const headers = getTokenHeader(token);
    fetchRequest(`auth/v2/user/bytoken`, { headers })
      .then(async (response) => {
        const _response = await response.json();
        const { result, rc } = _response;
        if (rc !== 0) {
          throw new Error();
        } else {
          callback?.(result);
        }
      })
      .catch(() => {
        goBack();
      });
  }
};

const ChooseIdentity: FC = () => {
  const { token } = useParams<{ token: string }>();
  const [loginInfo, setLoginInfo] = useState<ILoginUser | undefined>(undefined);
  const [checked, setChecked] = useState<boolean>(false);

  useEffect(() => {
    getLoginInfo(token, setLoginInfo);
  }, [token]);

  const identityMap = transformIdentities(_.get(loginInfo, 'identities', []));
  const appList = _.keys(identityMap) || [];

  // Extract default identities across all apps
  const defaultIdentities: { appName: string; identity: IAppData }[] = [];
  const filteredIdentityMap: IAppMap = {};

  // Separate default identities and create filtered map
  _.forEach(appList, (appName) => {
    const appIdentities = _.get(identityMap, appName, []);
    filteredIdentityMap[appName] = [];

    _.forEach(appIdentities, (identity) => {
      if (_.get(identity, 'defaultIdentity', false)) {
        defaultIdentities.push({ appName, identity });
      } else {
        filteredIdentityMap[appName].push(identity);
      }
    });
  });

  const onSelectIdentity = async (identityId?: string) => {
    const headers = getTokenHeader(token);
    const prefOptions = {
      method: 'PUT',
      headers,
      body: JSON.stringify({ value: checked }),
    };

    const queue = [
      fetchRequest(`datamap/v2/preferences/pref.user_choose_identity_auto`, prefOptions),
      fetchRequest(`datamap/v2/preferences/pref.user_switch_to_main_identity`, prefOptions),
    ];

    if (checked && !_.isEmpty(identityId)) {
      queue.push(
        fetchRequest(`auth/v2/user/identities/main_identity`, {
          method: 'PUT',
          headers,
          body: { identity_uuid: identityId },
        }),
      );
    }

    try {
      await Promise.all(queue);
    } catch (error) {
      console.error(error);
      return;
    }

    if (_.isEmpty(identityId) && loginInfo) {
      afterLoginSuccess(loginInfo, false);
      return;
    }

    fetchRequest(`auth/v2/user/identities`, {
      method: 'POST',
      headers,
      body: JSON.stringify({ user_id: identityId }),
    }).then(async (response) => {
      return receiveResponse(response, (result: ILoginUser) => {
        afterLoginSuccess(result, false);
      });
    });
  };

  return (
    <div className="mdt-login-choose-indentity-container">
      {loginInfo ? (
        <div className="mdt-login-choose-indentity-content">
          <div className="mdt-login-choose-indentity-goback" onSelect={goBack}>
            <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M11.5575 5.5575L10.5 4.5L6 9L10.5 13.5L11.5575 12.4425L8.1225 9L11.5575 5.5575Z"
                fill="#202939"
              />
            </svg>
          </div>
          <LoginTitle
            title={
              <div>
                <div className="mdt-login-title">{i18n.chain.chooseIdentityTitle}</div>
                <div className="mdt-login-desc">{i18n.chain.chooseIdentityDesc}</div>
              </div>
            }
          />
          <div className="mdt-login-choose-indentity-list-wrap">
            {!_.isEmpty(defaultIdentities) && (
              <div className="mdt-login-choose-indentity-app">
                {_.map(defaultIdentities, (item, index) => (
                  <>
                    <div className="mdt-login-choose-indentity-primary-tag">{item.appName}</div>
                    <IDentityCell key={index} {...item.identity} onSelect={onSelectIdentity} />
                  </>
                ))}
              </div>
            )}

            {_.map(appList, (appName) => {
              const appData = _.get(filteredIdentityMap, appName, []);
              return !_.isEmpty(appData) ? (
                <AppCell key={appName} name={appName} data={appData} onSelect={onSelectIdentity} />
              ) : null;
            })}
          </div>
          <div className="mdt-login-choose-indentity-pref">
            <Checkbox title={i18n.chain.chooseIdentityPrefTxt} onChange={setChecked} checked={checked} />
          </div>
        </div>
      ) : (
        <Spin />
      )}
    </div>
  );
};

ChooseIdentity.displayName = 'ChooseIdentity';
export { ChooseIdentity };
