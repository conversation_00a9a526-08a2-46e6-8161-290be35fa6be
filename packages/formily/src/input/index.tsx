import React from 'react';
import { connect, mapProps, mapReadPretty } from '@formily/react';
import { Input as MdtInput, InputProps, Textarea, TextareaProps } from '@mdtDesign/input';
import Spin from '@mdtDesign/spin';
import PreviewText from '../preview-text';

type ComposedInput = React.FC<React.PropsWithChildren<InputProps>> & {
  TextArea?: React.FC<React.PropsWithChildren<TextareaProps>>;
};

export const Input: ComposedInput = connect(
  MdtInput,
  mapProps({ value: true, initialValue: 'defaultValue' }, (props, field) => {
    return {
      block: true,
      ...props,
      // @ts-ignore
      suffix: <span>{field?.['loading'] || field?.['validating'] ? <Spin size="small" /> : props.suffix}</span>,
    };
  }),
  mapReadPretty(PreviewText.Input),
);

Input.TextArea = connect(Textarea, mapReadPretty(PreviewText.Input));

export default Input;
