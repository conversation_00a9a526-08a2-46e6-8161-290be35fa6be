import { FC, useMemo } from 'react';
import { observer } from '@formily/react';
import { Tabs } from '@metroDesign/tabs';
import i18n from '../../../../languages';
import { ApiSetting } from './ApiSetting';
import { RequestSetting } from './RequestSetting';

export const SettingSchema: FC = observer(() => {
  const items = useMemo(() => {
    return [
      { key: '1', label: i18n.chain.commonSetting, children: <ApiSetting /> },
      { key: '2', label: i18n.chain.advanceSetting, children: <RequestSetting /> },
    ];
  }, []);
  return <Tabs className="mdt-editor-setting-tabs" defaultActiveKey="1" centered items={items} />;
});
