import _ from 'lodash';
import { from, of } from 'rxjs';
import { Observable } from 'rxjs/internal/Observable';
import { map, takeWhile } from 'rxjs/operators';
import { IBusinessResult } from '@mdtBsComm/interfaces';
import { deleteRolesAsync, postRoleAsync, putRoleAsync, queryRolesAsync } from '@mdtBsServices/auth';
import { IRequestCancelToken, IRole, IRolePut } from '@mdtBsServices/interfaces';
import { RoleTypeEnum } from '@mdtProComm/constants';
import { IFormData } from '@mdtProMicroModules/containers/drawer-modify-form-permission-role';
import { GroupPermissionModel } from '@mdtProMicroModules/models/GroupPermissionModel';

// 通用角色ID
export const COMMON_ROLE_ID = 0;

export interface ITableData {
  id: number;
  name: string;
  description: string;
  permission: number[];
}

export enum IMoreItemsKey {
  DELETE = 'delete',
}

export class RoleManageModel extends GroupPermissionModel {
  public static transformToTableData(item: IRole): ITableData {
    return {
      id: item.id,
      name: item.name,
      description: item.description || '',
      permission: item.permission || [],
    };
  }

  public static queryAllRoles(appId: number, cancelToken?: IRequestCancelToken) {
    const cnf = { cancelToken, params: { app_id: appId } };
    return from(queryRolesAsync(cnf)).pipe(
      takeWhile((resp) => !resp.canceled),
      map((resp) => {
        return _.map(
          _.filter(
            resp.data,
            ({ app_id: appId, role_type: type }) => appId !== COMMON_ROLE_ID && type === RoleTypeEnum.NORMAL,
          ),
          (it) => this.transformToTableData(it),
        );
      }),
    );
  }

  public static updateRole(data: IFormData, originalData: ITableData): Observable<IBusinessResult<ITableData>> {
    const postData: IRolePut = { id: originalData.id, role_type: RoleTypeEnum.NORMAL };
    data.name !== originalData.name && (postData.name = data.name);
    data.description !== originalData.description && (postData.description = data.description);
    if (!_.isEqual(data.permission, originalData.permission)) {
      postData.permission = data.permission;
    }
    // 如果未做改变，则直接关闭窗口
    if (_.size(postData) === 1) {
      return of({ success: true, result: originalData });
    }
    return from(putRoleAsync(postData)).pipe(
      map((resp) => {
        if (resp.success) {
          const td = { ...data, id: originalData.id };
          return { success: true, result: td };
        }
        return { success: false };
      }),
    );
  }

  public static createRole(data: IFormData, appId: number): Observable<IBusinessResult<ITableData>> {
    const postData = { ...data, app_id: appId };
    return from(postRoleAsync(postData)).pipe(
      map((resp) => {
        if (resp.success) {
          const td = {
            id: resp.data!,
            name: data.name,
            description: data.description || '',
            permission: data.permission || [],
          };
          return { success: true, result: td };
        }
        return { success: false };
      }),
    );
  }

  public static deleteRole(data: ITableData): Observable<IBusinessResult<ITableData>> {
    return from(deleteRolesAsync(`${data.id}`)).pipe(
      map((resp) => ({ success: resp.success, result: data, actionType: IMoreItemsKey.DELETE })),
    );
  }
}

export type IRoleManageModel = typeof RoleManageModel;
