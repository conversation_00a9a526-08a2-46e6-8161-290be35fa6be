import _ from 'lodash';
import { ReactNode } from 'react';
import type { RuleCondition } from '@metro/rule-form';
import type { SelectProps } from '@metroDesign/select';
import { BehaviorSubject } from 'rxjs';
import { skip } from 'rxjs/operators';
import { ICondition, IDatapkgRowsQuery, IDatapkgRowsQueryOrderBy, IOperatorFilter } from '@mdtApis/interfaces';
import i18n from '../../languages';
import {
  getDefaultSortBy,
  getDetailDataOperatorFilter,
  getDetailDataStaticsOperatorFilter,
  getDownstreamSubmitedUsers,
  getFastViewOperatorFilter,
  getFillDataOperatorFilter,
  IQueryDownstreamUsersOptions,
} from '../../utils/oneTableNewFilterUtil';
import {
  COLUMN_ACTION,
  COLUMN_FLAG,
  COLUMN_STATUS,
  DataStatusEnum,
  DataTableTypeEnum,
} from '../../utils/oneTableNewUtil';
import { IControllerOptions as IParentOptions, OneTableNewDataTableController } from '../one-table-new-data-table';

interface IStaticsInfo {
  total: number;
  add: number;
  update: number;
  untouch: number;
  del: number;
}

export interface IControllerOptions extends IParentOptions {
  filterLinkStatics?: boolean;
  defaultFastVal?: IFastSelectFilterValue;
  hideDataStatistic?: boolean;
  staticsExtraBodyParams?: Record<string, any>;
  queryDownstreamUsersOptions?: IQueryDownstreamUsersOptions;
}

export interface IFastSelectFilterValue {
  realVal?: IOperatorFilter | ICondition;
  extraParams?: Record<string, any>;
  value?: string;
  label?: string;
}

class OneTableNewDataTableStaticsController extends OneTableNewDataTableController {
  protected staticsInfo$ = new BehaviorSubject<IStaticsInfo>({ total: 0, add: 0, update: 0, untouch: 0, del: 0 });
  protected userIds$ = new BehaviorSubject<number[]>([]);
  private fastSelectFilterValue$: BehaviorSubject<IFastSelectFilterValue>;
  private loadStaticsDepence$ = new BehaviorSubject<number>(0);

  private showStatics?: boolean;
  private filterLinkStatics?: boolean;
  private hideDataStatistic?: boolean;
  private staticsExtraBodyParams?: Record<string, any>;
  private queryDownstreamUsersOptions?: IQueryDownstreamUsersOptions;

  public constructor({
    filterLinkStatics,
    defaultFastVal,
    hideDataStatistic,
    staticsExtraBodyParams,
    queryDownstreamUsersOptions,
    ...resetOptions
  }: IControllerOptions) {
    super({
      actionOptions: [
        { value: DataStatusEnum.Insert, label: i18n.chain.dataActionStatus.insert },
        { value: DataStatusEnum.Update, label: i18n.chain.dataActionStatus.update },
        { value: DataStatusEnum.Delete, label: i18n.chain.dataActionStatus.delete },
        { value: DataStatusEnum.NoDelete, label: i18n.chain.dataActionStatus.noDelete },
        { value: DataStatusEnum.ACTION_NULL, label: i18n.chain.dataActionStatus.empty },
        { value: DataStatusEnum.FLAG_NULL, label: i18n.chain.dataActionStatus.empty, disabled: true },
      ],
      ...resetOptions,
      dataPreviewOptions: {
        pagination: true,
        paginationAtBefore: true,
        paginationProps: { defaultPageSize: 50 },
        modifyFilterParams: (params) => this.innerStaticsModifyFilterParams(params),
        ruleFormOptions: () => this.initRuleFormProps(),
        ...resetOptions.dataPreviewOptions,
      },
    });
    this.staticsExtraBodyParams = staticsExtraBodyParams;
    this.queryDownstreamUsersOptions = queryDownstreamUsersOptions;

    this.filterLinkStatics = filterLinkStatics;
    this.fastSelectFilterValue$ = new BehaviorSubject(
      defaultFastVal || this.getFastSelectFilterProps().defaultValue || {},
    );
    this.hideDataStatistic = hideDataStatistic;
    this.initReletion();
  }

  public destroy() {
    super.destroy();
    this.staticsExtraBodyParams = undefined;
    this.loadStaticsDepence$.complete();
    this.staticsInfo$.complete();
    this.userIds$.complete();
    this.userIds$.next([]);
    this.fastSelectFilterValue$.complete();
    this.fastSelectFilterValue$.next({});
  }

  public getFastSelectFilterValue$() {
    return this.fastSelectFilterValue$;
  }

  public changeFastSelectFilterValue(val: IFastSelectFilterValue) {
    this.fastSelectFilterValue$.next(val);
  }

  public getFastSelectFilterProps(): Omit<SelectProps, 'value' | 'onChange'> {
    return {};
  }

  public fastSearchData(key: string) {
    const filterFunc = getFastViewOperatorFilter(key);
    const val = (filterFunc?.() || {}) as IOperatorFilter;
    this.setHighFilterChange(val, true);
  }

  public getTableTotalLabel() {
    return i18n.chain.proMicroModules.oneTable.tip.tableDataTotal;
  }

  public getStaticsInfo$() {
    return this.staticsInfo$;
  }

  public getStaticsPrefix(): string | undefined {
    return undefined;
  }

  public getStaticsBtnText(): ReactNode {
    return null;
  }

  public getStaticsStrVal() {
    const info = this.staticsInfo$.getValue();
    return i18n.chain.proMicroModules.oneTable.tip.staticsDataStatus(
      info as unknown as Record<string, number>,
      this.getStaticsPrefix(),
    );
  }

  public getShowStatics() {
    return this.showStatics;
  }

  public getUserIds() {
    return this.userIds$.getValue();
  }

  public loadDataList(params?: any) {
    this.changeDataListLoading(true);
    const { rootWfId, assignWfId } = this.itemData;
    getDownstreamSubmitedUsers(rootWfId, assignWfId, this.getCancelToken(), this.queryDownstreamUsersOptions).then(
      (users) => {
        this.userIds$.next(users);
        super.loadDataList(params);
      },
    );
  }

  private loadDetailStatics = (userIds: number[]) => {
    const { operator_filter = undefined } = this.filterLinkStatics ? this.getFilterParams() : {};
    getDetailDataStaticsOperatorFilter({
      users: userIds,
      pkgId: this.itemData.pkgId,
      extraFilter: operator_filter,
      extraBodyParams: this.staticsExtraBodyParams,
    }).then((data) => {
      this.staticsInfo$.next(data);
    });
  };

  private innerStaticsModifyFilterParams = (params: IDatapkgRowsQuery) => {
    const userIds = this.getUserIds();
    const cb = _.flatten([params.orderby, getDefaultSortBy()]).filter(Boolean);
    const ob = _.map(cb as IDatapkgRowsQueryOrderBy[], (it) => {
      it.nulls_position = 'last';
      return it;
    });
    params.orderby = ob;

    const { realVal, extraParams = {} } = this.fastSelectFilterValue$.getValue();
    const { operator_filter: extraOperatorFilter } = extraParams;
    const operatorFilter: (IDatapkgRowsQuery['operator_filter'] | null)[] = [params.operator_filter, realVal];
    if (this.dataTableType === DataTableTypeEnum.FILL) {
      operatorFilter.push(getFillDataOperatorFilter(userIds));
    } else {
      operatorFilter.push(getDetailDataOperatorFilter(userIds));
    }

    if (extraOperatorFilter) {
      params.operator_filter = extraOperatorFilter;
    } else {
      const vof = operatorFilter.filter(Boolean);
      !_.isEmpty(vof) && (params.operator_filter = { $and: vof });
    }
    const mergedParams = _.merge({}, { ...params, ...(this.getRequestParams() || {}) }, extraParams);
    return _.omitBy(mergedParams, (v) => _.isObject(v) && _.isEmpty(v));
  };

  private initReletion() {
    const tt = this.dataTableType;
    this.showStatics = !this.hideDataStatistic && tt !== DataTableTypeEnum.FILL && tt !== DataTableTypeEnum.ISSUE;
    if (this.showStatics) {
      this.userIds$.pipe(skip(1)).subscribe(this.loadDetailStatics);
    }
    this.fastSelectFilterValue$.pipe(skip(1)).subscribe(() => {
      this.setHighFilterChange({}, false);
      this.loadDataList();
    });
  }

  private setHighFilterChange(val: IOperatorFilter, search?: boolean) {
    const filterCtrl = this.getFilerController().getFilterRuleFormController();
    if (!filterCtrl) return;
    filterCtrl.onTransformChange(val);
    search && filterCtrl.onSubmit();
  }

  private initRuleFormProps() {
    return {
      transformToValueCondition: (node: RuleCondition) => {
        let name = node.column;
        let value = node.param;
        let type = node.type;

        const flagArr = [DataStatusEnum.NoDelete, DataStatusEnum.Delete];
        const statusArr = [DataStatusEnum.UnSubmitted, DataStatusEnum.Submitted, DataStatusEnum.Rejected];

        if (name === COLUMN_ACTION) {
          type = COLUMN_ACTION;
          value = _.eq(value, null) ? DataStatusEnum.ACTION_NULL : value;
        }

        if (name === COLUMN_FLAG) {
          value = flagArr[value] ?? (_.eq(value, null) ? DataStatusEnum.FLAG_NULL : value);
          name = COLUMN_ACTION;
          type = COLUMN_ACTION;
        }

        if (name === COLUMN_STATUS) {
          value = statusArr[value] ?? value;
          name = COLUMN_ACTION;
          type = COLUMN_ACTION;
        }

        return { value, operator: node.operator, name, type };
      },
    };
  }
}

export { OneTableNewDataTableStaticsController };
