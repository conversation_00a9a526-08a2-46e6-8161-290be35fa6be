import { jsx, jsxs } from '@bpmn-io/properties-panel/preact/jsx-runtime';
import { useService } from 'bpmn-js-properties-panel';
import { addToolTip } from '../utils/tooltipUtil';

export function PkgButtonEntry(props: any) {
  const translate = useService('translate');
  const { onClickPkg, onClickConfig, children } = props;

  return jsxs('div', {
    class: 'bio-properties-panel-button',
    children: [
      children,
      jsx('div', {
        class: 'more-btns-wrap',
        children: [
          jsx('span', {
            class: 'bpmn-icon-data-object bio-properties-panel-button-icon',
            onClick: onClickPkg,
            ...addToolTip({ tip: translate('fromSource') }),
          }),
          jsx('span', {
            class: 'bpmn-icon-service bio-properties-panel-button-icon',
            onClick: onClickConfig,
            ...addToolTip({ tip: translate('config') }),
          }),
        ],
      }),
    ],
  });
}
