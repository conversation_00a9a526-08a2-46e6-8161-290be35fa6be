import _ from 'lodash';
import { BehaviorSubject } from 'rxjs';
import { ILabelValue } from '@mdtBsComm/interfaces';
import { FormCompController } from '@mdtBsComponents/form-comp';
import { IMyDataFilterModel } from './MyDataFilterModel';

export interface IFilterForm {
  geoType?: string;
  packageType?: string;
  datasource?: string;
  otherApp?: string;
}

export enum FilterFormAttrs {
  DATASOURCE = 'datasource',
  GEO_TYPE = 'geoType',
  PACKAGE_TYPE = 'packageType',
  OTHER_APP = 'otherApp',
}

export enum MyDataMenuEnum {
  PERSONAL = 'personal',
  APP = 'app',
  OTHER = 'other',
  GLOBAL = 'global',
}

const hasPackageTypeMenus = [MyDataMenuEnum.PERSONAL, MyDataMenuEnum.APP];
const hasDatasourceMenus = [MyDataMenuEnum.APP];
const hasOtherAppMenus = [MyDataMenuEnum.OTHER];

interface IControllerOptions {
  menu: MyDataMenuEnum;
  Model: IMyDataFilterModel;
}

class MyDataFilterController {
  private Model: IMyDataFilterModel;
  private formController = new FormCompController<IFilterForm>();
  private enableFilterPackageType = false;
  private enableFilterDatasource = false;
  private enableFilterOtherApp = false;
  private dsOptions$ = new BehaviorSubject<ILabelValue[]>([]);

  public constructor({ menu, Model }: IControllerOptions) {
    this.Model = Model;
    if (_.includes(hasPackageTypeMenus, menu)) {
      this.enableFilterPackageType = true;
    }
    if (_.includes(hasDatasourceMenus, menu)) {
      this.enableFilterDatasource = true;
      this.Model.queryDsOptions().subscribe((options) => {
        this.dsOptions$.next(options);
      });
    }
    if (_.includes(hasOtherAppMenus, menu)) {
      this.enableFilterOtherApp = true;
    }
  }

  public destroy() {
    this.formController.destroy();
    this.dsOptions$.complete();
    this.dsOptions$.next([]);
    this.Model = null!;
  }

  public getFilterData$() {
    return this.formController.getFormData$();
  }

  public getFilterDataValue() {
    return this.formController.getFormData$().getValue();
  }

  public getEnableFilterPackageType() {
    return this.enableFilterPackageType;
  }

  public getEnableFilterDatasource() {
    return this.enableFilterDatasource;
  }

  public getEnableFilterOtherApp() {
    return this.enableFilterOtherApp;
  }

  public getFormController() {
    return this.formController;
  }

  public getDsOptions$() {
    return this.dsOptions$;
  }
}

export { MyDataFilterController };
