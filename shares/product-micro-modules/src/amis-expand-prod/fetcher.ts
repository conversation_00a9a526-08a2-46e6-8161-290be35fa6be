import { attachmentAdpator } from 'amis-core';
import axios from 'axios';
import {
  FILE_ID_FLAG,
  isMdtApi,
  mdtApiProxyFetcher,
  mdtFileUrlProxyFetch,
  mdtUploadFile,
} from '../amis-expand/mdtApiProxy';

const getCancelToken = (cancelExecutor: any) => {
  if (!cancelExecutor) return;
  const source = axios.CancelToken.source();
  cancelExecutor(() => {
    source.cancel('Request canceled by executor');
  });
  return source;
};

export const mdtFetcher = {
  transformFileUrl: (url: string) => mdtFileUrlProxyFetch(url),
  // eslint-disable-next-line sonarjs/cognitive-complexity
  fetcher: async (api: any) => {
    if (isMdtApi(api.url)) {
      return mdtApiProxyFetcher(api);
    }

    if (api.url.includes('/upload')) {
      const file = api.data.get('file');
      if (!file) return { status: '1', ok: false, msg: 'file not found' };
      const cancelToken = getCancelToken(api.config.cancelExecutor);
      return mdtUploadFile(file, cancelToken, api.config.onUploadProgress).then((result) => {
        if (!result.ok) return result;
        const { data } = result;
        const fileUrl = `${FILE_ID_FLAG}${data.value.value}`;
        if (api.context?.type === 'pdf-viewer') {
          data.url = fileUrl;
          return result;
        }
        return mdtFileUrlProxyFetch(fileUrl).then((url) => {
          data.url = url;
          return result;
        });
      });
    }

    let { url, method, data, config, headers } = api;
    config = config || {};
    config.url = url;
    config.withCredentials = !url.includes('Signature'); // 含有签名，则不需要
    config.canceltoken = getCancelToken(config.cancelExecutor)?.token;

    config.headers = headers ? { ...config.headers, ...headers } : config.headers ?? {};
    config.method = method;
    config.data = data;

    if (method === 'get' && data) {
      config.params = data;
    } else if (data && data instanceof FormData) {
      // config.headers['Content-Type'] = 'multipart/form-data';
    } else if (data && typeof data !== 'string' && !(data instanceof Blob) && !(data instanceof ArrayBuffer)) {
      data = JSON.stringify(data);
      config.headers['Content-Type'] = 'application/json';
    }

    let response = await axios(config);
    response = await attachmentAdpator(response, (msg: string) => msg, api);
    return response;
  },
  isCancel: (value: any) => (axios as any).isCancel(value),
};
