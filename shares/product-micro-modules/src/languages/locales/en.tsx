/* eslint-disable sonarjs/no-duplicate-string */
import { en as proTasksEn } from '@mdtProTasks/languages';
import { Locale } from './cn';

export const en: Locale = {
  ...proTasksEn,
  proMicroModules: {
    userInactivityLogout: (time: number) => `Your account will be logged out after ${time} minutes of inactivity`,
    crontab: {
      cronExp: 'Cron Expression',
      hot: 'Popular',
      custom: 'Custom',
      exampl1: 'Every 30 seconds',
      exampl2: 'Every 5 minutes',
      exampl3: 'Every 10 hours',
      exampl4: 'At 1 AM every day',
      exampl5: 'At 12 PM every day',
      exampl6: 'At 11 PM on the last day of every month',
      exampl7: 'At 1 AM every Sunday',
      onetable: {
        exampl1: 'Executed at 00:00:00 every day',
        exampl2: 'Executed every Sunday at 00:00:00',
        exampl3: 'Executed at 00:00:00 on the 1st of each month',
        exampl4: 'Executed at 00:00:00 on the last day of the month',
        exampl5: 'Executed at 00:00:00 on the first day of the first month of each quarter',
        exampl6: 'Executed at 00:00:00 on January 1 and July 1',
        exampl7: 'Executed at 00:00:00 on January 1 every year',
      },
      simpleSetting: 'Simple',
      highSetting: 'High',
      notSimpleExp: 'It is not generated by the shortcut settings and cannot be parsed correctly',
      notHighExp: 'It is not generated by the high settings and cannot be parsed correctly',
      viewExecuteBtn: 'View execute time',
      viewLastTime: 'The execution time of the last 5 times',
      viewPeriodic: 'The last 5 times of the execution period',
      language: {
        paneTitle: {
          second: 'Second',
          minute: 'Minute',
          hour: 'Hour',
          day: 'Day',
          month: 'Month',
          week: 'Week',
          year: 'Year',
        },
        assign: 'Assign',
        donTAssign: "Don't Assign",
        okBtnText: 'Generate',
        everyTime: {
          second: 'Every second',
          minute: 'Every minute',
          hour: 'Every hour',
          day: 'Every day',
          month: 'Every month',
          week: 'Every week',
          year: 'Every year',
        },
        week: {
          sun: 'Sunday',
          mon: 'Monday',
          tue: 'Tuesday',
          wed: 'Wednesday',
          thu: 'Thursday',
          fri: 'Friday',
          sat: 'Saturday',
        },
        aTob: {
          second: (AInput: string, BInput: string) => (
            <span>
              From {AInput}-{BInput} seconds, every second
            </span>
          ),
          minute: (AInput: string, BInput: string) => (
            <span>
              From {AInput}-{BInput} minutes, every minute
            </span>
          ),
          hour: (AInput: string, BInput: string) => (
            <span>
              From {AInput}-{BInput} hours, every hour
            </span>
          ),
          day: (AInput: string, BInput: string) => (
            <span>
              From {AInput}-{BInput} days, every day
            </span>
          ),
          month: (AInput: string, BInput: string) => (
            <span>
              From {AInput}-{BInput} months, every month
            </span>
          ),
          week: (AInput: string, BInput: string) => (
            <span>
              From {AInput}-{BInput} every week
            </span>
          ),
          year: (AInput: string, BInput: string) => (
            <span>
              From {AInput}-{BInput} years, every year
            </span>
          ),
        },
        aStartTob: {
          second: (AInput: string, BInput: string) => (
            <span>
              From {AInput} seconds onwards, every {BInput} seconds
            </span>
          ),
          minute: (AInput: string, BInput: string) => (
            <span>
              From {AInput} minutes onwards, every {BInput} minutes
            </span>
          ),
          hour: (AInput: string, BInput: string) => (
            <span>
              From {AInput} hour onwards, every {BInput} hours
            </span>
          ),
          day: (AInput: string, BInput: string) => (
            <span>
              From {AInput} day onwards, every {BInput} days
            </span>
          ),
          workday: (AInput: string) => <span>On the nearest workday to the {AInput}th day of every month</span>,
          lastday: () => <span>On the last day of every month</span>,
          month: (AInput: string, BInput: string) => (
            <span>
              From {AInput} month onwards, every {BInput} months
            </span>
          ),
          week: (AInput: string, BInput: string) => (
            <span>
              On {BInput} of the {AInput}th week of every month
            </span>
          ),
          week2: (AInput: string) => <span>On the last {AInput} of every month</span>,
          year: (AInput: string, BInput: string) => (
            <span>
              From {AInput} year onwards, every {BInput} years
            </span>
          ),
        },
      },
    },
    customLogin: {
      createTemplate: 'Create New Template',
      createTitleDesc: 'Customize the name of the login template',
      createDropTitle: 'Click or drag the "Login Configuration Item" to this area',
      createDropDesc: (accept: string) => `Supports ${accept} file format`,
      createDefault: 'Set as default login configuration',
      defaultTemplateName: 'Custom Template',
      giveupTitle: 'Give up?',
      giveupDesc: 'After giving up, your changes will not be recoverable.',
      titleRequired: 'Title required',
      commonConfigTab: 'General Configuration',
      deployWindowTitle: 'Page Title',
      deployWindowDesc: 'Page Description',
      deployFavicon: 'Page Icon',
      productTitle: 'Title',
      productDesc: 'Description',
      productFooterVisible: 'Footer',
      customLogoRadioTitle: 'Display Logo',
      defaultLogo: 'Default Logo',
      customLogo: 'Custom Logo',
      productShowLanguage: 'Support Language Switching',
      languageSimplifiedChinese: 'Simplified Chinese',
      languageEnglish: 'English',
      productTabsTitle: 'Login Methods',
      loginMethodAccount: 'Account Login',
      loginMethodPhone: 'SMS Login',
      loginMethodWechat: 'WeChat Login',
      loginMethodDingtalk: 'DingTalk Login',
      loginMethodZZDing: 'ZZDing',
      productPrivacyVisibleCheckedText: 'Privacy Agreement Enabled',
      productPrivacyVisibleUncheckedText: 'Privacy Agreement Disabled',
      productPrivacyCheckChildren: 'Default Checked',
      productPrivacyNameTitle: 'Agreement Title',
      productPrivacyLinkTitle: 'Redirect Link',
      productPrivacyAdditionTitle: 'Add Privacy Agreement',
      productShowPasswordForgetTitle: 'Forgot Password',
      productShowPasswordForgetDesc: 'Can be retrieved via phone or email',
      productRegisterVisibleTitle: 'Register Button',
      productRegisterVisibleDesc: 'After enabling, you can configure the registration redirect link',
      productRegisterLinkTitle: 'Registration Redirect Address',
      productRegisterJumpTargetTitle: 'Redirect Method',
      productRegisterJumpTargetSelf: 'Internal Redirect',
      productRegisterJumpTargetBlank: 'Open in New Tab',
      pageStyleTab: 'Interface Style',
      productThemeTitle: 'Theme Color',
      lightTheme: 'Light Theme',
      darkTheme: 'Dark Theme',
      displayLayoutTitle: 'Layout Method',
      layoutLeft: 'Login Box Left',
      layoutRight: 'Login Box Right',
      layoutCenter: 'Login Box Center',
      loginBgStyleTitle: 'Login Box Style Configuration',
      productBgIsVideoTitle: 'Video Playback',
      productBgIsVideoDesc: 'Video size must not exceed 1MB',
      productBgUrlTitle: 'Background Image',
      productTitleCenterTitle: 'Title Style',
      alignLeft: 'Align Left',
      alignCenter: 'Align Center',
      productTitleSizeTitle: 'Title Font Size',
      customGridMediaRadioTitle: 'Material Box Background',
      defaultBackground: 'Default Background',
      customBackground: 'Custom Background',
      productGridIsVideoTitle: 'Video Playback',
      productGridIsVideoDesc: 'Video size must not exceed 1MB',
      advancedConfigTab: 'Advanced',
      productVerificationTitle: 'Slide Authentication Enabled',
      productVerificationDesc: 'Drag the slider with mouse for safer authentication',
      productChooseApp: 'Choose App',
      productChooseAppDesc: 'Select any authorized App after login',
      productRedirectUrlTitle: 'Specify Login Success Redirect URL',
      productRedirectUrlPlaceholder: "Default to use sso's redirect parameter",
    },
    card: {
      searchTag: 'Search tags',
      delTags: 'Delete tag group',
      createTag: 'Create new tag',
      createTagSuccess: 'Tag created successfully',
      delTagSuccess: 'Tag deleted successfully',
    },
    products: {
      myData: 'My Data(Common)',
      org: 'Organization（Common）',
      other: 'Other Common Modules',
      datlas: 'Data App',
      workflow: 'Workflow',
      datamarket: 'Data Market',
      datafactory: 'Data Factory',
      onetable: 'One Table',
    },
    header: {
      workBench: 'Workbench',
      helpCenter: 'Help center',
      settings: 'Settings',
      goAdmin: 'Admin System',
      impersonate: 'Impersonate login',
      exitImpersonate: 'Exit Impersonate login',
      changeImpersonateUser: 'Change Impersonate user',
      imperError: 'The current simulation APP is unavailable or has expired.',
      orderLastest: 'Recent Impersonate Login',
      orderDefault: 'Default Order',
      starSub: 'Starred on Top',
      people: 'users',
      logout: 'Logout',
      expired: 'Expired',
      clearCacheSuccess: 'Cache already cleared',
      editServerWorkerSuccess: 'Successfully modified Service Worker status, please refresh the page again',
      appForCloud: (product: string, appName: string) => `Use【${product}】Account from【${appName}】to apply data`,
      preferenceSetting: 'Preferences',
      notificationSetting: 'Notifications',
      notificationSettingDesc:
        'It is used to manage the status and notification method of the built-in notification event in the system',
      themeName: 'Theme',
      dark: 'Dark Mode',
      light: 'Light Mode',
      systemLanguage: 'Language',
      loginSetting: 'Login Setting',
      otherSetting: 'Other Setting',
      autoLoginApp: 'Automatically log in to recently used APP',
      autoLoginIdentity: 'Automatically log in to main identity',
      autoLoginIdentitySwitchMainIdentity: 'Switch identity and specify main identity',
      disableServiceWorker: 'Disable Service Worker',
      disableServiceWorkerDesc: 'Disabling it may affect the user experience.',
      notificationType: 'Notification way',
      notificationTypeIn: 'System',
      notificationTypeEmail: 'Email',
      readed: 'Read',
      del: 'Delete',
      allNotifications: 'All notifications',
      readedNotifications: 'Read notifications',
      unreadNotifications: 'Unread notifications',
      allReaded: 'Mark all as read',
      allDel: 'Delete all',
      personal: 'Personal',
      username: 'Username',
      nickname: 'Nickname',
      changeIdentities: 'Change Identities',
      systemNotificationEvent: 'System Notification Event',
      status: 'Status',
      statusOn: 'On',
      statusOff: 'Off',
      notificationCustom: 'User attention event',
      notificationExpired: 'Account expiration reminder',
      notificationApproval: 'Data approval result',
      notificationResource: 'Resource Authorization Notification',
      notificationFlow: 'Process pending',
      org: 'Organization',
      phone: 'Phone',
      email: 'Email',
      password: 'Password',
      dingtalk: 'Dingtalk',
      wechat: 'Wechat',
      copyId: 'Copy ID',
      change: 'Change',
      verify: 'To verify',
      noVerify: '(No verify)',
      bind: 'Bind',
      unBind: 'Unbind',
      unBindConfirm: 'Are you sure you want to unbind?',
      unBindSuccess: 'The unbinding is successful',
      copyIdSuccess: 'The institution ID has been copied to the clipboard',
      role: 'Roles',
      searchApp: 'Search APP',
      changeThemeSuccess: 'Change Theme successfully',
      autoLoginSuccess: 'Change Automatically login successfully',
      changeLanguageSuccess: 'Change Language successfully',
      notificationNomal: 'Nomal',
      notificationSystem: 'System',
      notificationTask: 'Backend Task',
      notificationSubscribe: 'Subscribe',
      notificationAnnouncement: 'Announcement',
      productList: 'Product List',
      default: '(Default)',
      switchMainIdentitySuccess: 'Set main identity successfully',
      switchMainIdentity: 'Switch main identity',
    },
    theme: {
      name: 'Theme',
      delTagDesc:
        'If the user has set only part of the theme library to be visible, after deleting the theme library tab, the user will not be able to view the data in that theme library.',
      themeName: 'Theme Name',
      pkgStats: 'Datapkg Count',
      search: 'Search Theme',
      create: 'Create Theme',
    },
    taggroup: {
      name: 'Taggroup',
      tag: 'Tag',
      tagName: 'Tag Name',
      createTip: 'Can be added in batches by space spacing for ',
      create: 'Create Taggroup',
      update: 'Update Taggroup',
      taggroupName: 'Taggroup Name',
      delDesc: 'After deletion, the tags within the tag group will be deleted at the same time.',
      searchTag: 'Search Tag',
      addTagDesc: 'Batch tagging by space spacing.',
      dataTag: 'Data Tag',
    },
    mdTmpl: {
      name: 'Datapkg document',
      tmplName: 'Name',
      tmplType: 'Type',
      update: 'Update',
      updateTime: 'Update Time',
      assign: 'Assign',
      preview: 'Preview',
      tmplPreview: 'Template Preview',
      copyId: 'Copy Id',
      delete: 'Delete Template',
      tmplContent: 'Content',
      selectPkg: 'Select Datapkg',
      source: 'Source',
      pkg: 'Datapkg',
      assignField: 'Assign Fields',
      pkgDesc: 'Datapkg Description',
      upload: 'Template Upload',
      assignSuccess: 'Assign Success',
      theme: 'By Theme',
    },
    approval: {
      selpApp: 'Self Inst',
      crossApp: 'Cross Inst',
      appData: 'Inst Data',
      use: 'Use',
      search: 'Search',
      download: 'Download',
      publish: 'Publish',
      toApproval: 'To Approval',
      finished: 'Approvaled',
      searchApply: 'Search Application',
      pkgName: 'Datapkg Name',
      applier: 'Applier',
      applyDate: 'Apply Date',
      permission: 'Permission Type',
      approver: 'Approver',
      status: 'Status',
      applyApp: 'Apply Inst',
      expireDate: 'Expire Date',
      approved: 'Passed',
      rejected: 'Rejected',
      cancelled: 'Cancelled',
      other: 'Other',
      expireTitle: 'Expire Setting',
      expireDesc: 'Deadline',
      downloadTimeOutsid: 'Available download time must be greater than five minutes.',
      applyFinished: (name: string) => `${name}'s application is completed`,
    },
    dataSearch: {
      allData: 'All Data',
      selfApp: 'Current Inst',
      byName: 'By Name',
      byTag: 'By Tag',
      byField: 'By Field',
      search: 'Search Datapkg',
      dataTag: 'Data Tag',
      searchTag: 'Search Tag',
      dataPermission: 'Data Permission',
      dataGeoType: 'Geometry Type',
      pkg: 'Datapkg',
      dataType: 'Data Type',
      dataTypeEmptyError: 'Data Type cannot be empty',
      permission: 'Permission',
      updateDate: 'Update Date',
      count: 'Count',
      mutiGeoType: 'Multiple Geometric Types',
      searchBoxPlachode: 'Search for the datapkg you want',
      searchBoxHistory: 'History',
      searchBoxTotal: (total: number) => `Total of ${total} results`,
      searchBoxDescEmpty: 'Nothing to leave',
      searchBoxColums: 'Colums',
      searchBoxSetting: 'Setting',
      searchBoxSettingTitle: 'Search scope',
      searchBoxSettingTheme: 'Theme',
      searchBoxSettingApp: 'Inst',
      searchBoxSettingSubmitText: 'Save',
      searchBoxSettingResetText: 'Reset',
    },
    pkgDetail: {
      apply: 'Data Apply',
      count: 'Data Count',
      updateDate: 'Update',
      basic: 'Basic Overview',
      tablePreview: 'Table Preview',
      field: 'Field Properties',
      desc: 'Data Package Description',
      genelogy: 'Lineage Chart',
      use: 'Usage Permission',
      search: 'Search Permission',
      download: 'Download Permission',
      applyBtn: 'Apply',
      aggined: 'Already Obtained',
      share: 'Public Link Sharing',
      shareBtn: 'Copy Link',
    },
    orgList: {
      searchOrg: 'Search organization',
      createChildOrg: 'Add child',
      addOrg: 'Add organization',
      orgManage: 'Manage',
      addChildOrg: 'Add child',
      delOrg: 'Delete',
      orgAuth: 'Permission',
      orgResource: 'Resource',
      authManage: 'Permission',
      resourceManage: 'Resource',
      orgName: 'Organization Name',
      orgNameDesc: "Organization can't be empty",
      pOrg: 'Parent Organization',
      delConfirm: 'Delete Confirmation',
      delConfirmText:
        'Deleting the organization will delete the organization and its subordinate organizations. Are you sure you want to delete it?',
      createOrgSuccess: 'Organization created successfully',
      delOrgSuccess: 'Organization deleted successfully',
      editOrgSuccess: 'Organization updated successfully',
      importOrg: 'Import organization',
      importUser: 'Import User',
      downloadImportTemp: 'Download import template',
      downloadImportTempTitle: 'You can download an ',
      downloadImportTempDesc: 'empty template',
      downloadImportTempDesc2: ', fill in the department information, and then upload it to create departments in bulk',
      dropTitle: 'Click or drag file here',
      dropDesc:
        'After downloading and completing the template, you can directly upload the file. .xlsx format files are supported.',
      userDropTitle: 'Complete the table content according to the prompts',
      userDropDesc: 'Download an empty template table',
    },
    roleList: {
      createRole: 'Create role',
      manageRole: 'Manage',
      delRole: 'Delete Role',
      empty: 'No roles available',
      removeRole: 'Remove from Role',
      addRoleUser: 'Add Role Users',
      removeConfirm: (user: string) => `Are you sure you want to remove user "${user}" from this role?`,
      removeConfirmDesc: 'After removal, the user will no longer belong to this role',
      delConfirm: 'Delete Confirmation',
      delConfirmDesc: 'Please confirm whether to delete this role',
      editRoleSuccess: 'Update role successful',
      createRoleSuccess: 'Create role successful',
      delRoleSuccess: 'Delete role successful',
      editRoleAuthSuccess: 'Update role permission successful',
    },
    permission: {
      basic: 'Basic info',
      authManage: 'Permission',
      editError: 'An unexpected error occurred while editing. Please try refreshing your browser.',
      editUser: 'Edit User',
      createUser: 'Create User',
      username: 'Username',
      userNameEmptyError: 'Username cannot be empty',
      emailDesc: 'Email (initial password will be sent via email, it is recommended to fill in)',
      emailEmptyError: 'Email cannot be empty',
      emailInvalid: 'Invalid email format: <EMAIL>',
      phone: 'Phone Number',
      fromOrgs: 'Belongs to Organizations',
      fromRoles: 'Belongs to Roles',
      fromRolesDesc: 'You can select one or more roles',
      expireTime: 'Expiration Time',
      expireTimeEmptyError: 'Expiration time cannot be empty',
      isFreeze: 'Is Disabled',
      freeze: 'Disabled',
    },
    resetPwd: {
      forceResetPwd: 'Force Password Change',
      newPwd: 'New Password',
      pwdReg:
        'At least 8 characters, including at least 3 of the following: uppercase letters, lowercase letters, numbers, special characters',
      pwdConfirm: 'Confirm New Password',
      pwdError:
        'Password must be at least 8 characters and include at least 3 of the following: uppercase letters, lowercase letters, numbers, special characters',
      pwdDiff: 'The two passwords entered do not match',
      placeholder: 'Please input',
    },
    role: {
      roleManage: 'Manage Roles',
      roleCreate: 'Create Role',
      roleName: 'Role Name',
      roleNameEmptyError: 'Role name cannot be empty',
      roleDesc: 'Role Description',
      roleAuth: 'Role Permission',
      authSetting: 'Permission',
      roleResource: 'Role Resource',
      resourceSetting: 'Resource',
      editRole: 'Edit Role',
      group: {
        groupManage: 'Group Management',
        groupCreate: 'Create Group',
        groupName: 'Group Name',
        groupNameEmptyError: 'Group name can not be empty',
        groupDesc: 'Group Description',
        userName: 'Username',
      },
    },
    defaultPlay: {
      title: 'Default Play Setting',
      page: 'Default Play Page',
      add: 'Add',
      chooseTitle: 'Choose Page',
      project: 'Project',
      reset: 'Reset',
      all: 'All',
      pages: 'Pages',
      searchByProj: (value: string) => `Search by "${value}"`,
      searchByPage: (value: string) => `Search by "${value}"`,
    },
    themeShare: {
      editUser: 'Edit User',
      username: 'Username',
      usernameEmptyError: 'Username cannot be empty',
      editTheme: 'Edit Theme Library',
      themeVisible: 'Visible Theme Libraries',
      editOrgs: 'Edit Organizations',
      orgsVisible: 'Visible Organizations',
      editDetail: 'Edit Details',
    },
    genealogy: {
      delBorder: 'Delete edge',
      delDom: 'Delete node',
      arrange: 'Arrange',
      scaleScreen: 'Zoom to fit the screen',
      switchSelection: 'Open/close frame selection',
      searchDatapkg: 'Search data package',
      saveSuccess: 'Saved successfully',
      noChange: 'No modification',
      inputPile: 'Input Pile',
      outputPile: 'Output Pile',
      dataPreview: 'Geospatial Data Preview',
      edit: 'Edit Lineage',
      delDomOrBorder: 'Delete Node or Edge',
    },
    markdownPreview: {
      editDesc: 'Edit Description',
      fileUploadFailed: (name: string) => `File ${name} upload Failed`,
    },
    timeTask: {
      delSuccess: 'Delete successful',
      operatorSucces: 'Operation succeeded',
      name: 'Name',
      timingWay: 'Timing way',
      expired: 'Valid until',
      nextRunTime: 'Next run time',
      avtive: 'Enable',
      delConfirm: (name?: string) => `Confirm to delete "${name}"?`,
      delComfirmDesc: 'After deletion, the tags in the scheduled task will be deleted at the same time. ',
      addTiming: 'Add Timing',
      timingJob: 'timing task',
      updateTimingSuccess: 'Update timing task succeeded',
      createTimingSuccess: 'Create a new scheduled task successfully',
      createTiming: 'New Timing Task',
      editTiming: 'Modify the timing task',
      nailTime: 'Fixed interval',
      nailDate: 'Fixed date',
      interval: 'Interval',
      numberPlaceholder: 'Enter a value',
      frequency: 'Frequency',
      time: 'Time',
      timePlaceholder: 'Please select a Time',
      chooseWeek: 'Choose week',
      chooseMonth: 'Choose month',
      nameEmptyError: 'The name cannot be empty',
      namePlaceholder: 'Please enter a name',
      startEndDate: 'Date',
    },
    datapkg: {
      createZoneTip: 'Copy files from local. Click or drag files to this area',
      createZoneDesc: 'Support .csv/.xlsx (single sheet) file upload',
      emptyDatapkg: 'Empty data package',
      qe: 'QE creation',
      tableExtraction: 'Extract entire table',
      sql: 'SQL creation',
      createPkg: 'Create new data package',
      createEmptyPkg: 'Create empty data package',
      limitFileSize: (size: string) => `File size cannot exceed ${size}M`,
      create: 'Generate',
      update: 'Update',
      operateMessage: (name: string, operate: string) => `Data package 【${name}】${operate} successfully`,
      viewPkg: 'View data package',
      dataPreview: 'Data preview',
      dataSearch: 'Data search',
      blurSearchTip: 'Support text column fuzzy search',
      geoPreview: 'Geographic data preview',
      noSearchDesc: 'You do not have search permission. Only preview up to the first 10 data',
      saving: 'Saving...',
      addField: 'Add field',
      edit: 'Edit',
      editFieldDesc:
        'Modifying field types may cause incompatibility with existing data, causing data to be unusable or even locked. Please modify with caution!',
      keyNoRepeat: 'Key columns cannot be repeated',
      name: 'Field name',
      nameEmptyError: 'Field name cannot be empty',
      type: 'Field type',
      typeEmptyError: 'Field type cannot be empty',
      displayFormatter: 'Display format',
      unique: 'Unique',
      nullable: 'Nullable',
      index: 'Index',
      keyType: 'Key field type',
      nameDesc: 'Field description',
      updatePkg: 'Update data package',
      generatePkg: 'Generate data package',
      chooseGeoType: 'Please select the geographic type',
      chooseDataType: 'Please select the data type',
      datapkgName: 'Data package name',
      datapkgNameDesc: 'Please enter data package name',
      datapkgNameEmptyError: 'Data package name cannot be empty',
      schemaEmptyError: 'Schema cannot be empty',
      tableEmptyError: 'Table cannot be empty',
      addFormDataset: 'Add from data source',
      refreshTimer: 'Periodically flush the packet cache',
      nextRefreshTime: 'The next refresh cache time',
      nextRefreshTimeTip:
        'If not specified, the calculation is automatically performed based on the [Periodically flush the packet cache] configuration and the current time',
      lastRefreshTime: 'Last refresh cache time',
      datapkgExist:
        'A data package with the same name already exists. Please choose a different name for the package and try again.',
      cleatFilter: 'Clear',
      noAuth: 'No Permission',
      typeDesc: 'Description',
      tableEdit: 'Table edit',
      mapEdit: 'Map edit',
      geoTypeDesc:
        'This data is non-geographic and needs to be switched to geographic data before entering the editing page.',
      validateError: 'Both the topic library and the institution must retain at least one option',
      share: {
        pkgEmpty: 'Select the data package to share on the left',
        selectedInputPlaceholder: 'Search for selected data',
        selected: 'Selected',
        clear: 'Clear',
        startSharing: 'Start sharing',
        shareApplyTitle: 'External sharing approval has been initiated',
        shareApplyTitleDesc: (count: number) =>
          `External sharing approval has been initiated for the selected "${count}" data packages. Please wait for the administrator to process it.`,
        followOrgDate: 'Follow Organization Validity Period',
        custom: 'Custom',
        shareSetting: 'Share Setting',
        endDate: 'End Time',
        endDatePlaceholder: 'Please select an end time',
        otherApp: 'External Organization',
        otherAppPlaceholder: 'Please select an external organization',
        applyAuth: 'Apply for Permission',
        applyAuthPlaceholder: 'Please Select Permission',
        date: 'Validity Period',
        dateEmptyError: 'Please Select Validity Period',
        applyConfirm: 'Confirm the permission to apply',
        authEmptyTip: 'Permissions cannot be empty',
        btnConfirmText: 'Sure',
        permissionAcquired: 'Permissions already owne',
        permissionToApproval: 'Pending permissions',
        permissionToApprovaling: 'Permissions to be requested',
        permissionToApprovalingTip:
          'Permission requests will be limited to items that have been selected, and permissions that are not selected will not trigger the request',
        applyDesc: 'Request datapkg permissions',
        applyResult: 'Apply result',
        applySuccess: (pkg: string, ps: string) =>
          `The [${ps}] permission application for datapkg "${pkg}" is successful`,
        applyFaild: (pkg: string, ps: string) => `The [${ps}] permission application for datapkg "${pkg}" is failed`,
        appDiffTip: "The current agency doesn't match the external agency you choose",
        useCurrentBtn: 'Use current',
      },
      datapkgSql: {
        play: 'Run',
        format: 'Format',
        noDataset: 'No data source?',
        noDatasetTip:
          'Contact your administrator to help you enable the custom data source menu or have him add a data source',
        toCreate: 'Create one',
        otherDataSet: 'External data source',
        ownership: 'Ownership',
        ownershipEmptyError: 'Ownership cannot be empty',
        dataset: 'Data source',
        datasetEmptyError: 'Data source cannot be empty',
        geoType: 'Geotype',
        geoTypeEmptyError: 'Geotype cannot be empty',
        previewLoading: 'Generating preview…',
        chooseDatasetEmpty: 'Please select a data source on the left first',
        edit: 'Edit SQL',
        confirmEdit: 'Confirm changes',
        createDb: 'Create database',
        createError: 'Please run SQL first',
        emptyError: 'Please write a SQL statement',
        pkgExistError: 'Data package already exists',
      },
      description: {
        title: 'Data Package Description Modification',
        customDesc: 'Custom Description',
        chooseTemp: 'Choose Template',
        desc: 'Description',
        contentEmptyError: 'Content can not be empty',
      },
      download: {
        exportResult: 'Export Result',
        exportGeo: 'Export Geographical Fields',
        startDownload: 'Start Download',
        datapkgDownload: 'Data Package Download',
        columnTips: 'Please select columns to download',
      },
      subscribe: {
        subscribeNotification: 'Subscribe Notification',
        datapkgUpdate: 'Data Package Update',
        qualityMonitorTaskResult: 'Quality Monitor Task Result',
        subscribeAll: 'Subscribe All',
        success: 'Subscribe successfully',
        cancelSuccess: 'Cancel Subscribe successfully',
      },
    },
    datapkgDetail: {
      applyingPublish: 'Publishing application in progress',
      publishWithPkg: 'Publish as organizational data package',
      updateData: 'Update data',
      delData: 'Delete data package',
      fillData: 'Fill data',
      basicInfo: 'Basic information',
      emptyPkgError: 'Data package does not exist or cannot be requested due to other reasons',
      pkgLoading: 'Data package details loading',
      nameUpdateFailed: 'Name update failed',
      nameUpdateSuccess: 'Name update successful',
      updateFailed: 'Meta update failed',
      updateSuccess: 'Meta update successful',
      personalDataToOrg: 'Are you sure you want to publish personal data as organizational data?',
      personalDataToOrgDesc:
        'Once personal data is converted to organizational data, it cannot be converted back to personal data.',
      submitApplay: 'Application submitted',
      dataUpdateFailed: 'Data update failed',
      dataUpdateSuccess: 'Data updated successfully',
      delPkg: (name: string) => `Data package "${name}" has been deleted successfully`,
      delPkgConfirm: (name?: string) => `Confirm to delete "${name}" data package`,
      delPkgConfirmDesc:
        'After deletion, it may cause charts, maps, Lab files, and low-code ETL files made with this data to become invalid.',
      tablePreview: 'Table preview',
      filedPreview: 'Field properties',
      pkgDesc: 'Data package description',
      pkgMonitor: 'Quality monitoring',
      collaborateEdit: 'Collaborative editing',
      fieldDesensitize: 'Field desensitization',
      genealogy: 'Lineage',
      theme: 'Theme library',
      userName: 'User',
      description: 'Descripiton',
      orgName: 'App name',
      geoType: 'Geographic type',
      hasPermission: 'Has permission',
      tags: 'Tags',
      count: 'Number of rows',
      columnCount: 'Number of columns',
      checkLog: 'Quality inspection results',
      updateTime: 'Update time',
      createTime: 'Creation time',
      uuid: 'UUID',
      dataset: 'Data source',
      storageType: 'Storage type',
      tableName: 'Table name',
      displayStatus: 'Display status',
      display: 'Displayed',
      unDisplay: 'Not displayed',
      copy: 'Copy',
      copySuccess: 'Copy successful',
      startPos: 'Starting position:',
      desensitizeLength: 'Desensitization length:',
      reg: 'Regular expression:',
      desensitizeStyle: 'Desensitization style:',
      qualified: 'Qualified',
      unqualified: 'Unqualified',
    },
    user: {
      privateSetting: 'Private Setting',
      active: 'Enable',
      disable: 'Disable',
      expired: '(Expired)',
      noExpire: 'Unlimit expire',
      verify: 'Verify',
      bind: 'Bind',
      email: 'Email ',
      phone: 'Phone ',
      captchaEmptyError: 'Verification code cannot be empty',
      captchaPlaceholder: 'Please enter the verification code',
      sendCaptcha: 'Send code',
      reSendCaptcha: (s: number) => `${s}s to refresh`,
      sendTo: `Verification code has been sent to:`,
      cannotReceive: 'Cannot receive verification code?',
      use: 'Use ',
      emptyError: 'cannot be empty',
      formatError: 'format is incorrect',
      enterRight: 'Please enter the correct',
      captchaSendSuccess: 'Verification code send successfully',
      bindSuccess: 'Bind successfully',
      thirdPartyBind: 'Third-party binding',
      oldPassword: 'Old password',
      newPassword: 'New password',
      passwordFormat:
        'At least 8 characters, including at least 3 of the following: uppercase letter, lowercase letter, digit, and special character',
      confirmNewPassword: 'Confirm new password',
      changePassword: 'Change password',
      passwordError: 'Incorrect password, please re-enter',
      passwordFormatError: 'Less than 8 characters or less than 3 types of characters',
      passwordDiffError: 'Passwords entered twice are inconsistent',
      updatePasswordSuccess: 'Password changed successfully',
      expireTip: {
        title: 'Personal Account Expiration Reminder',
        titleDesc: (appName: string, userName: string, date: string) =>
          `Your user account for 【${userName}】 under [${appName}] is set to expire on ${date}. If you would like to extend your usage, please contact your organization administrator.`,
        noTip: 'Do not remind me again',
      },
      pswTip: {
        title: 'Password Change Reminder',
        titleDesc: (appName: string, userName: string, date: number) =>
          `Your user account [${userName}] under [${appName}] has not been changed for more than ${date} days. For the security of your account, please change your password as soon as possible.`,
        known: 'Got it',
      },
    },
    collaborate: {
      addCollaborate: 'Add collaboration',
      putCollaborate: 'Initiate collaboration',
      editCollaborate: 'Modify collaboration',
      generateCollaborate: 'Create collaboration',
      conditions: 'Data conditions',
      permission: 'Permission',
      user: 'User',
      confirmDelConditions: 'Confirm to delete the conditions',
      createCollaborateSuccess: 'Collaboration created successfully',
      updateCollaborateSuccess: 'Collaboration updated successfully',
      read: 'Read',
      viewDetail: 'View',
      download: 'Download',
      batchDownload: 'Batch download',
      filePreview: 'File preview',
      batchDownloading: 'Batch downloading...',
      downloadSuccess: 'Download completed',
      update: 'Update rows',
      insert: 'Insert rows',
      delete: 'Delete rows',
      chooseDataRow: 'Select data rows',
      chooseDataRowDesc:
        '"By default, set permissions for all data. After adding filter conditions, set permissions for some data rows',
      dataView: 'Data view',
      choosePermission: 'Choose permission',
      chooseUserOrTag: 'Choose user or user tag',
    },
    resource: {
      myShare: 'My shares',
      shareMe: 'Shares to me',
      emptyTitle: 'No content yet',
      emptyDesc: 'Please select a user/group in the left sidebar to share resources',
      shareTime: 'Share time',
      auth: 'Permission',
      noAuth: 'Cannot authorize',
      fileName: 'File name',
      fileType: 'File type',
      creator: 'Creator',
      addResource: 'Add resource',
      searchResource: 'Search resource',
      resourceType: 'Resource type',
      selected: 'Selected:',
      clear: 'Clear',
      authEmpty: 'Permission cannot be empty',
      shareSuccess: 'Shared successfully',
      chooseUser: 'User',
      chooseGroup: 'Group',
      groupManage: 'Group Management',
      delGroup: 'Delete Group',
      createGroup: 'Create Group',
      delGroupSuccess: 'Group deletion successful',
      delGroupTilte: (name?: string) => `Confirm deletion of "${name}?"`,
      delGroupDesc: 'Deleting a group will revoke authorized permissions. Please confirm whether to delete the group.',
      share: 'Resource Sharing',
      authManage: 'Permission',
      authManageFailed: (name?: string) => `Please grant at least one permission or revoke the resource "${name}".`,
      removeResource: 'Revoke Resource',
      selectResourceEmpty: 'Please select resources',
      operateSuccess: 'Operation Successful',
      removeConfirm: 'Revoke Confirmation',
      removeConfirmDesc: 'Please confirm whether to revoke',
      batchUpdateAuth: 'Modify permissions in bulk',
    },
    sqlDataTable: {
      sqlNoChange: 'No change to SQL',
      sqlSaveSuccess: 'SQL saved successfully',
      play: 'Run',
      format: 'Format',
      generateDatapkg: 'Generate data package',
      sqlEmptyError: 'Please write SQL statement first',
      addDatapkgSuccess: 'Data package added successfully',
      generateDatapkgFailed: 'Data package generation failed',
    },
    luckySheet: {
      addCol: 'Add a column',
      colName: 'Column Name',
      colNameEmptyError: 'Column name can not be empty',
      colNamePlaceholder: 'Please enter a column name',
      type: 'Type',
      typeEmptyError: 'Type can not be empty',
      typePlaceholder: 'Please select a column type',
      name: 'Spreadsheet',
    },
    dataset: {
      uploadSuccess: 'Upload successful',
      connectSuccess: 'Connection successful',
      connectFailed: 'Connection failed',
      connecting: 'Connecting',
      editConnect: 'Edit connection',
      createConnect: 'Create connection',
      testConnect: 'Test connection',
      connectType: 'Type',
      connectTypeEmptyError: 'Connection type cannot be empty',
      name: 'Name',
      nameEmptyError: 'Name cannot be empty',
      host: 'Host',
      hostEmptyError: 'Host cannot be empty',
      port: 'Port',
      portEmptyError: 'Port cannot be empty',
      db: 'DB',
      dbEmptyError: 'DB name cannot be empty',
      username: 'Username',
      usernameEmptyError: 'Username cannot be empty',
      password: 'Password',
      passwordEmptyError: 'Password cannot be empty',
      connectBySsh: 'Connect via SSH',
      privateKey: 'Private key',
      choosePrivateFile: 'Choose private key file',
    },
    desensitize: {
      editDesensitize: 'Modify desensitization',
      createDesensitize: 'Create desensitization',
      startPos: 'Starting position',
      startPosPlaceholder: 'Please enter the starting position of encryption',
      desensitizeLength: 'Desensitization length',
      desensitizeLengthPlaceholder: 'Please enter the length of the range to be hidden',
      reg: 'Regular expression',
      regPlaceholder: 'Please enter the information to be replaced',
      desensitizeStyle: 'Desensitization style',
      replacePlaceholder: 'Please enter the content to replace with',
      desensitizeType: 'Desensitization type',
      desensitizeTypePlaceholder: 'Please select a desensitization method',
      noFigma: 'Not available in the design file, to be confirmed',
      typeEmptyError: 'Type cannot be empty',
      addDesensitize: 'Add Desensitization Condition',
      activeDesensitize: 'Activate Desensitization',
      activeSuccess: 'Successfully Activated',
      activeFailed: 'Deactivated',
      field: 'Field',
      desensitizeWay: 'Desensitization Method',
      desensitizeInfo: 'Desensitization Information',
      delDesensitizeCol: (name?: string) => `Confirm deletion of the desensitization condition for column ${name}`,
      desensitizeRange: 'Desensitization Range',
      md5Desensitize: 'MD5 Desensitization',
      regDesensitize: 'Regular Expression Desensitization',
    },
    institution: {
      cannotAddOwnerApp: 'Cannot add the current app',
      idNotExist: 'ID does not exist',
      idNotMatchApp: 'App ID does not match the name',
      editApp: 'Edit app',
      addApp: 'Add App',
      appId: 'App ID',
      appIdEmptyError: 'App ID cannot be empty',
      appIdPlaceholder: 'Please enter app ID',
      name: 'Name',
      nameEmptyError: 'Name cannot be empty',
      namePlaceholder: 'Please enter app name',
      appName: 'App Name',
      createApp: 'New App',
      appSetting: 'App Settings',
      createAppSuccess: 'App Successfully Created',
      delAppSuccess: 'App Successfully Deleted',
      delAppTitle: (name?: string) => `Confirm deletion of app "${name}"`,
    },
    monitor: {
      editMonitor: 'Edit Monitor',
      addMonitor: 'Create Monitor',
      create: 'New',
      name: 'Name',
      nameEmptyError: 'Name can not be empty',
      monitorType: 'Monitor Type',
      monitorTypeEmptyError: 'Monitor type can not be empty',
      monitorField: 'Monitor Field',
      monitorFieldEmptyError: 'Monitor field can not be empty',
      relationship: 'Relationship',
      relationshipFieldEmptyError: 'Relationship can not be empty',
      monitorCondition: 'Monitor Condition',
      monitorConditionEmptyError: 'Monitor condition can not be empty',
      monitorConditionHasEmptyError: 'Monitor condition can not be empty',
      formula: 'Formula Expression',
      formulaEmptyError: 'Formula expression can not be empty',
      formulaDesc1:
        '1. Please write judgment statements here, which consist of Column Calculation Formula, Judgment Symbol and Judgment Value.',
      formulaDesc2: '2. The execution result of the judgment statement can only be true or false.',
      formulaDesc3: '3. Example of a judgment statement: t.unitPrice * t.quantity > 1000.',
      formulaDesc4:
        '4. When monitoring, the formula will be used to judge each row of the data package. If the execution results of all rows are true, the quality status is "Qualified", otherwise it is "Unqualified".',
      formulaDesc5: '5. Input t. to select a column.',
      sql: 'SQL Statement',
      sqlEmptyError: 'SQL statement can not be empty',
      sqlDesc1: '1. Please write SQL code here, which must include the judgment statement.',
      sqlDesc2: '2. Judgment statements consist of Calculation Formula, Judgment Symbol and Judgment Value.',
      sqlDesc3:
        '3. The execution result of the judgment statement can only be true or false. If the execution result is true, the quality status is "Qualified", otherwise it is "Unqualified".',
      sqlDesc4:
        '4. Example of a judgment statement: select sum(t.unitPrice * t.quantity) > 100 from m.pkg_uuid as t where t.city="Shanghai".',
      sqlDesc5: '5. Input m. to select a data package, input t. to select a column.',
      sqlDesc6: '6. Multiple data packages can be operated in the SQL statement.',
      desc: 'Description',
      descPlaceholder: 'Please briefly describe the monitoring content.',
      startMonitor: 'Run Monitoring',
      timingJob: 'Timing Task',
      createMonitor: 'Create Monitoring',
      monitorResult: 'Recent Monitoring Results:',
      monitorTIme: 'Recent Monitoring Time:',
      type: 'Type',
      condition: 'Condition',
      result: 'Result',
      delMonitorSuccess: 'Successfully Deleted Monitoring',
      delMonitorTitle: (name?: string) => `Confirm deletion of monitoring "${name}"`,
      delMonitorDesc: 'Please be careful as deleting monitoring will weaken your data quality monitoring!',
    },
    chooseUser: {
      user: 'By User',
      role: 'By Role',
      org: 'By Organization',
      addUser: 'Add Member',
      searchRole: 'Search Role',
      searchOrg: 'Search Organization',
      orgLeader: 'Organization leader',
      orgOnetableAdmin: 'Organization onetable admin',
      sub: 'Sub',
      people: 'User',
      clear: 'Clear',
      selected: 'Selected:',
      group: 'By Group',
      addPeople: 'Add User',
      addConnector: 'Add User',
      searchUserOrOrg: 'Search user/organization/user organization (space as separator)',
      searchUserOrOrgTip:
        'Search user/organization/user organization (space as separator), support fuzzy search, press Enter to search',
      showCountTip: (count: number) => `Selected users: ${count}`,
      userResource: 'Resource',
      me: 'Me',
      myOrgList: 'My organization list',
      selectOrg: 'Select',
      myOrg: 'My organization',
      setPermission: 'Set Permission',
    },
    filter: {
      advancedFilter: 'Advanced Filter',
      and: 'and',
      true: 'True',
      false: 'False',
      fieldPlaceholder: 'Please select a field',
      operatorPlaceholder: 'Select an operator',
      clear: 'Clear',
      startSearch: 'Start Search',
      addFilter: 'Add Filter',
      setFilter: 'Set Filter',
    },
    pkgBoundWfspec: {
      formName: 'Form Name',
      creator: 'Creator',
      createForm: 'Create Form',
      searchForm: 'Search Form',
      open: 'Open',
      fillData: 'Fill Data',
    },
    fillDesign: {
      generateFlow: 'Generate And Open',
      fillDesign: 'Fill Design',
      propertyConfig: 'Property Config',
      configTip: 'Please complete property config',
      allTextCompTip: 'The form cannot only have the [Caption] question type',
      columnNameRepeatTip: 'repeat column name',
      columnExist: 'auto store column exist',
      columnFieldEmptyTip: 'Form field cannot be empty:',
      columnFieldRepeatTip: 'Form field cannot repeat:',
      columnEmptyTip: 'Form cannot empty',
      saveTip: 'Create success',
      insertData: 'Insert Data',
      approval: 'Approval',
      updateTip: 'Saving will overwrite the changes made in the Workflow!',
      fieldTyle: 'Field Type',
      dragFieldToForm: (form: string) => `Please drag child field to${form}`,
      colsNotExistedInPkg: (cols: string) => `These fields not exist in datapkg：${cols}`,
      invalidFieldTypes: (cols: string) => `These field's type are incorrect：${cols}`,
    },
    wfSpecPkgProperyConfig: {
      wfName: 'Workflow Name',
      wfInitiator: 'Workflow Initiator',
      wfNameTmpl: 'Workflow name template',
      wfNameTmplPlaceholder: 'eg：The application of {{__initiator_name}}',
      wfDescTmpl: 'Workflow description template',
      wfDescTmplPlaceholder: 'eg：Holiday start time:{{startTime}}; Holiday end time:{{endTime}}',
      approvers: 'Approvers',
      isApproval: 'Does it need to be approved?',
      golbalVariables: 'Global variables',
      starterId: 'Initiator ID',
      starterApp: 'Initiator App',
      starterName: 'Initiator Name',
      isAnonymity: 'Is Anonymity',
      isAutoStoreInfo: 'Is auto store fill info?',
      appUsers: 'Self App Users',
      loginUsers: 'All Datlas Users',
      linkUsers: 'All Has Link Users',
      assignUsers: 'Assign Users',
      fillInfo: 'Fill Info',
      storedColumn: 'Column Of Datapkg to Store',
    },
    wfSpecCreate: {
      selectType: 'Select Type',
      pkg: 'Datapkg Form',
      pkgDesc: () => [
        'Datapkg Form can be used to initiate simple form fill type processes.',
        "You don't need to use a complex process editor, just design the fill-in form by dragging and dropping components like building blocks and select packets to store the data.",
      ],
      bpmnDesc: () => [
        'BPMN is a business-based process map that consists of a series of graphical elements, such as rectangles for activities, diamonds for conditions, and so on.',
        'Graphics simplify model development and make complex operations easy to understand.',
      ],
      selectPkg: 'Select Datapkg',
    },
    tagInput: {
      insert: 'Select',
    },
    shortEmpty: 'None',
    mediaJsonError: 'The content of the file does not meet expectations and the parsing fails',
    formView: {
      addDesc: 'Add Desc',
      addTitle: 'Add title',
      fillForm: 'Fill form',
      defaultExcelName: 'Data form',
      geometryInputSearch: 'Search address',
      redrawBtn: 'Redraw',
      retargeting: 'Retargeting',
      drawTitle: 'Draw geography',
      uploadFile: 'Upload file',
      startTime: 'Start time',
      endTime: 'End time',
      geometry: 'Geometry',
      connector: 'Connector',
      annex: 'Annex',
      downloadFileError: 'Failed to obtain the file url',
      uploadBtn: 'Upload',
      changeBtn: 'Change',
      fileSizeLimit: 'File size need to less than',
      uploading: 'Uploading',
      uploadFailed: 'Upload failed',
      mobileCarema: 'Mobile Camera',
      watermarkSetting: 'Watermark Settings',
      watermarkUserId: 'Add Username and ID',
      watermarkUserIdTip: 'Only effective when logged in',
      watermarkDate: 'Date',
      minUploadCount: 'Minimum File Count',
      maxUploadCount: 'Maximum File Count',
      formCheckError: 'Form validation failed, please check the input content',
    },
    formEditor: {
      and: 'And',
      or: 'Or',
      not: 'Not',
      fieldPlaceholder: 'Please select a field',
      operatorPlaceholder: 'Select an operator',
      clear: 'Clear',
      startSearch: 'Start Search',
      addFilter: 'Add Filter',
      setFilter: 'Set Filter',
      editableInputEmptyError: 'Please enter a value',
      overrideCurrent: 'Override current value',
      preserveValueOnEmptyDependency: 'Preserve value on empty dependency',
      ruleInt: 'Please enter an integer',
      ruleFloat: 'Please enter a decimal place',
      ruleMaxFloat: (count: number) => `A maximum of ${count} decimal places is allowed`,
      ruleLessThen: 'Value cannot be less than',
      ruleMoreThen: 'Value cannot be greater than',
      ruleContentLessThen: 'Content length cannot be less than',
      ruleContentMoreThen: 'Content length cannot be greater than',
      isPhoneError: 'Please enter a valid phone number',
      isIdCardError: 'Please enter a valid ID number',
      isEmailError: 'Please enter a valid email address',
      isUrlError: 'Please enter a valid URL',
      ruleInDataSource: 'In the data source',
      ruleNotInDataSource: 'Not in the data source',
      fileCountLessThen: 'Number of files cannot be less than',
      fileCountMoreThen: 'Number of files cannot be greater than',
    },
    wfStartWorkflow: {
      noInitiate: 'no data',
      stopFill: 'From had already stop visit',
      requestError: 'From had not exist or deleted',
      noAuth: 'No Auth',
      instanceName: 'Instance Name',
      startSuccess: 'submit success',
      startInstance: 'Submit',
      fillAgain: 'fill again',
      submitSuccess: 'submit success',
      submitThanks: 'Thank you for your patience',
      chooseDom: 'Select Process Node',
      chooseDomDesc:
        '(If the previous report content is empty, the content of the following node will be used as the default value)',
      nodeChooseError: 'Please select at least one workflow node',
      nodeNameEmptyError: 'Node name cannot be empty',
      getFromNode: 'Get from workflow node',
    },
    grantedOtForm: {
      whoTip: (ele: any, scope: any) => (
        <div className="user-label">
          Who can {ele} {scope} all report
        </div>
      ),
      manageForm: 'manage and delegate',
      scopeApp: 'self app',
      scopeAppName: 'All reports of the app',
      sureOk: 'Sure',
      noGranted: 'You have not been granted access',
    },
    oneTable: {
      h5: {
        startEndData: 'Start and end dates',
        to: 'To',
        searchReportName: 'Search Report Name',
        unhandledTitle: 'Unhandled',
        all: 'All >',
        myForm: 'My Form',
        myTask: 'My Task',
        filling: 'Filling',
        finished: 'Finished',
        unhandled: 'Unhandled',
        handled: 'Handled',
      },
      menu: {
        dashbord: 'Dashbord',
        formManagement: 'Form Manage',
        formCreateManagement: 'Form Create Manage',
        missionCenter: 'Mission Center',
        missionCenterTask: 'Take Task',
        missionCenterForm: 'Fill Form',
        missionCenterReview: 'Form Review',
        grantedForm: 'Form overview',
        grantedOrgForm: 'Report authorization',
        h5Home: 'Home',
        h5MyTask: 'My Task',
        missionCenterReviewSubmit: 'Review and Submit Task',
        settingError: 'Configuration Error',
        formDatapkg: 'Form Datapkg',
        setting: 'Setting',
      },
      uploadExcelValid: {
        clickCopy: 'Click copy',
        copyed: 'Copyed',
        rightTip1: 'View right drowdown data',
        rightTip2: 'Select from right drowdown',
        valueTip1: 'Delete it from Excel',
        valueTip2: 'Copy to Excel',
        errorLimitTip: 'Data upload verification warning',
        errorLimitDescription:
          'The data in the following columns exceeds the limit, if you click "Continue", the data will be ignored and not imported into the current form (unknown column)',
        unkonwnCol: 'Unknown column',
        errorWinTitle: 'The data upload verification error is incorrect',
        errorWinBodyTip: 'Follow the tips below to review your data and modify it',
        emailErrorTip: 'The email address is in the wrong format',
        emailHowModify: 'For example: <EMAIL>',
        urlErrorTip: 'The URL address is in the wrong format',
        urlHowModify: 'For example: https://example.com',
        phoneErrorTip: 'The mobile phone number is in the wrong format',
        phoneHowModify: 'Please enter a valid 11-digit number',
        idcardErrorTip: 'The ID card is in the wrong format',
        idcardHowModify: 'The number of digits is 15 or 18 digits, and only 18 digits support the last digit is X',
        numberErrorTip: 'The content needs to be numeric',
        numberHowModify: 'For example: 1, 1.5',
        integerErrorTip: 'The content needs to be integer',
        integerHowModify: 'For example: 1, 2',
        stringErrorTip: 'The content needs to be string',
        stringHowModify: 'For example: abc, 中文',
        booleanErrorTip: 'The content needs to be boolean',
        booleanHowModify: 'For example: TRUE, FALSE',
        arrayErrorTip: 'The content needs to be array',
        arrayHowModify: 'For example: [1,2], ["a","b"]',
        objectErrorTip: 'The content needs to be object',
        objectHowModify: 'For example: {"a":"b"}',
        additionalPropertiesErrorTip: (val: string) => `Presence of redundant questions or attributes (${val})`,
        additionalPropertiesHowModify: (val: string) => `Remove redundant questions or attributes (${val})`,
        datetimeErrorTip: 'The date format is incorrect',
        datetimeHowModify: (val: string) => `For example: ${val}`,
        userSelectErrorTip: 'The contact format is incorrect',
        userSelectHowModify: (val: string) => `For example: ${val}`,
        uploadErrorTip: 'The file format is incorrect',
        uploadHowModify: 'For example: [{"id": "xx", "type": "image", value: "xxxx", name: "example.png"}]',
        arrayCardOrTableErrorTip: 'The self-incrementing card or table is incorrectly formatted',
        arrayCardOrTableHowModify: '',
        missingPropertyErrorTip: 'The input cannot be empty',
        missingPropertyHowModify: 'Please fill in the appropriate content for this question',
        valueNeedInDataSourceErrorTip: 'The input is out of the list allowed',
        valueNeedInDataSourceHowModify: '',
        valueCanNotInDataSourceErrorTip: 'The input is prohibited from the list',
        valueCanNotInDataSourceHowModify: '',
        maxDecimalCountErrorTip: (count: string) => `The number of decimal places cannot exceed ${count} digits`,
        maxDecimalCountHowModify: 'Reduce the number of decimal places',
        minLengthErrorTip: 'The content length is too short',
        minLengthHowModify: (count: string) => `The length of the extension content must be at least ${count}`,
        maxLengthErrorTip: (count: string) => `The content length exceeds ${count} bits`,
        maxLengthHowModify: (count: string) => `Reduce the length of the content to within ${count} bits`,
        maximumErrorTip: (limit: number) => `The number cannot be greater than ${limit}`,
        maximumHowModify: (limit: number) => `Adjustment number less than or equal to ${limit}`,
        minimumErrorTip: (limit: number) => `The number cannot be less than ${limit}`,
        minimumHowModify: (limit: number) => `Adjustment figure greater than or equal to ${limit}`,
        multipleOfErrorTip: (multipleOf: number) => `The number is not a multiple of ${multipleOf}`,
        multipleOfHowModify: (multipleOf: number) => `Adjust the number so that it is divisible by ${multipleOf}`,
        maxItemsErrorTip: (limit: number) => `There are more than ${limit} elements in the array`,
        maxItemsHowModify: (limit: number) => `Reduce the number of elements in the array to no more than ${limit}`,
        minItemsErrorTip: 'The number of elements in the array is too small',
        minItemsHowModify: (limit: number) =>
          `Add elements to the array so that the number of them is at least ${limit}`,
      },
      featureFlags: {
        enableInsertData: 'Enable insert data',
        enableUpdateData: 'Enable update data',
        enableDeleteData: 'Enable delete data',
        enableDownloadData: 'Enable download data',
        enableSendDown: 'Enable send down',
        enableTransfer: 'Enable transfer',
        enableReinforce: 'Enable reinforce',
        enableRefuse: 'Enable refuse',
        enableModifyDataAfterSubmit: 'Enable modify data after submit',
      },
      onlyDealUnassignData: 'Only not assign',
      fastSelectLabel: {
        all: 'All data',
        untouch: 'Only untouch',
        add: 'Only add',
        update: 'Only update',
        delete: 'Only delete',
        synced: 'Only synced',
        unsync: 'Only unsync',
        rejected: 'Only rejected',
        approved: 'Only approved',
        all2: 'All data',
        add2: 'Only add and unsync',
        update2: 'Only update and unsync',
        delete2: 'Only delete and unsync',
        untouch2: 'Only untouch and unsync',
        unsync2: 'Only unsync',
      },
      syncDataPrefixTip: 'Total of data',
      submitDataPrefixTip: 'Total of data',
      fastView: {
        all: 'View all',
        add: 'View add',
        update: 'View update',
        delete: 'View delete',
        untouch: 'View untouch',
        allWithSubordinates: 'View all with subordinates',
      },
      tableColumns: {
        reportName: 'Report name',
        reportTag: 'Report tag',
        startDepartment: 'Start department',
        taskStatus: 'Status',
        startTime: 'Start time',
        endTime: 'End time',
        fillDepartment: 'Fill department',
        initiator: 'Initiator',
        filler: 'Filler',
        dealTime: 'Deal time',
        issuedTime: 'Issued time',
        departName: 'Depart name',
        userName: 'User name',
        dataDeliveryInfo: 'Branch statistics',
        deliveryDataCount: (count: number, percent: string) => `${count} (${percent})`,
        finishStatus: 'Status',
        dealType: 'Deal Type',
        approvalStatus: 'Status',
        reportDesc: 'Description',
        periodicStatus: 'Finish status',
        periodic: 'Periodic',
        rowIndex: 'Index',
        question: 'Question',
        errorTip: 'Error tip',
        howModify: 'Modify the proposal',
      },
      myIssued: 'My issued',
      uploadExcelError: 'The uploaded Excel template does not match or has no data',
      uploadExcelMaxRowError:
        'Currently, data upload with more than 1,000 rows is not supported, you can try to split a large file into multiple files with less than 1,000 rows before continuing to upload',
      uploadExcelDataError: 'The data verification fails, please correct it and upload it again',
      uploadExcelBtn: 'Upload excel',
      excelDescIndex: (id: any) =>
        `You can copy the downloaded data, including [${id}], to this table. When [${id}] exists, it will be modified and updated, and when [${id}] does not exist, it will be added. [Please do not delete, fill, modify ${id}]`,
      noDataModify: 'The data has not changed, and the window will close',
      columnIndex: 'Index',
      columnOpt: 'Operate',
      systemColumn: 'System',
      rowAddBtn: 'Add row',
      modifyData: 'Modify data',
      statusProcessing: 'Processing',
      statusNotStart: 'Not Start',
      statusUnDeal: 'Undeal',
      firstDeal: 'First Deal',
      statusRefuse: 'Refused',
      statusDoing: 'Doing',
      statusUnFill: 'Unfill',
      firstFill: 'First Fill',
      firstApproval: 'First Approval',
      statusApproval: 'Approval',
      statusDone: 'Done',
      btnViewHistory: 'View history',
      btnGet: 'Direct report',
      btnForword: 'Forword',
      btnSubmitJob: 'Submit',
      btnSubmitJob3: 'Submit',
      btnSubmitJob4: 'Submit',
      btnSubmitJob2: 'Submit again',
      btnSubmitJob5: 'Submit again',
      btnRevoke: 'Revoke',
      btnIssued: 'Issued',
      btnTransferForm: 'Transfer',
      btnTransferFormTip:
        'When you are no longer responsible for report management, you can transfer it to someone else',
      btnTransferFormTip2: "Transfer a report to someone else and you won't have access to any information about it",
      clearReinforce: 'Clear reinforce',
      btnCollectorTooltip: 'Add users to assist you in completing the task report together.',
      btnCollectorTooltip2: 'Add users to help you co-manage your reports',
      editCollectorEqualTip: 'The collaborator has not changed and does not need to be modified',
      submitTooltip: 'Submit the data for review and approval by a superior.',
      btnForword4Tooltip:
        'Delegate the task to someone else to complete; after delegation, you will no longer have access to any information about this task.',
      btnRevokeTooltip:
        'If the task is not within your scope of responsibility, you can return the task to a superior.',
      btnCancelIssued: 'Cancel issued',
      cancelIssuedDesc: 'They will no longer be able to see the task',
      btnAftercancel: 'Operate data',
      btnIssueData: 'Assign data',
      btnIssued2: 'Issued',
      btnForword2: 'Assign',
      fillForm: 'Fill Form',
      btnStartFill: 'Start fill',
      btnBatchFill: 'Batch fill',
      btnRepeatFill: 'Refill',
      btnDetail: 'View',
      btnPass: 'Pass',
      btnRefuse: 'Refuse',
      btnSubmit: 'Submit',
      btnSubmitFinished: 'Submit Finished',
      btnSubmitFinishedConfirm: 'Confirm Submit Finished',
      btnFinish: 'Finish',
      btnCleanWf: 'Clean',
      btnFinishConfirm: 'Confirm Finish',
      btnUpdate: 'Fill',
      btnReApproval: 'Distribute and Review',
      btnCreateReport: 'Create',
      btnConfirmAndFill: 'Confirm and Fill',
      btnConfirmAndApproval: 'Confirm and Approval',
      btnPublish: 'Start',
      btnCopyCreate: 'Use this to create',
      btnForword3: 'Forword',
      btnForword4: 'Forword task',
      issuedUsers: 'Add users',
      addUsers: 'Add users',
      fillType: 'Fill type',
      fillTypeIndividual: 'Individual',
      fillTypeIndividualDesc: 'The issuing personnel shall fill in and submit it separately',
      fillTypeReinfoce: 'Reinfoce',
      fillTypeReinfoceDesc:
        'The issuing personnel jointly assist in filling in the report, and the data is shared among the collaborative personnel',
      reinfoceMain: 'Co-lead',
      self: '(Yourself)',
      issuedDataType: 'Filter data type',
      issuedAll: 'All data',
      issuedFilter: 'Filter data',
      issuedSelect: 'Select data',
      needIssuedData: 'Data that needs to be processed',
      issuedDataStatue: 'Assignment status',
      issuedDoing: 'Unassigned',
      issuedOk: 'Assigned',
      issueOkAndNext: 'Ok and Next',
      seletedDataView: 'View data',
      issueTaskEmptyError: 'There are required items that are not filled in the delivery configuration',
      issueDataEmptyError: 'Empty data cannot be allocated',
      notIssueyet: 'No data issue yet',
      reasonTip: (reason?: string) => `Reason: ${reason}`,
      seletedTotal: (total: number) => `${total} data has been selectd`,
      issuedType: 'Issued type',
      btnIssuedNoData: 'Issued without data',
      btnIssuedWithData: 'Issued with data',
      btnBack: 'Back',
      btnOnlySave: 'Save only',
      btnOnlySave2: 'Save only',
      btnSureSubmit: 'Submit',
      btnSureSubmit2: 'Submit',
      submitDataTip: (add: number, update: number, del: number) =>
        `In this submission, a total of ${add} entries are added, ${update} entries are modified, and ${del} entries are deleted`,
      submitNoData: "No data changed and don't do this",
      taskIdEmpty: "Task Id can't fetch",
      cancelFlowTip: 'Confirm the end of the report',
      cancelFlowDesc: 'After the completion of the application, the incomplete application will not be continued.',
      currentPeriodic: 'End current period',
      oldestPeriodic: 'End all period',
      cancelPeriodic:
        'Please select the period type to be ended, note that ending the current period will automatically enter the nearest period',
      saveForm: 'Save',
      saveEditForm: 'Finish',
      publishJob: 'Start',
      deleteForm: 'Delete',
      scheduledJob: 'Schedule Start',
      published: 'Published',
      draft: 'Draft',
      filling: 'Filling',
      filled: 'Filled',
      canceled: 'Canceled',
      createForm: 'Create Form',
      createFormEmpty: 'Create Empty',
      createFromPkg: 'Create from datapkg',
      btnAddRow: 'Add Row',
      issuedDetail: 'Issued detail',
      fillDataDetail: 'Data detail',
      rejectReasons: 'Reject reasons',
      dataDeliveryOverview: 'Branch statistics (including uncommitted)',
      dataTotalCount: (count: number, percent: string) => `Data Volume: ${count} entries (Percentage: ${percent}%)`,
      userTotalCount: (count: number) => `Personnel Count: ${count} people`,
      organizationLevel: (level: number) => `Distribution level: ${level}`,
      latestUpdateTime: 'Latest Data Update Time',
      earliestUpdateTime: 'First Data Update Time',
      daysAgo: (days: number) => `${days} days ago`,
      refusedReasons: 'Refused reasons',
      btnSureReject: 'Reject',
      btnApproval: 'Approval',
      btnUnsubmit: 'Unsubmit',
      userAdmin: 'Admin',
      collectorUser: 'Collector User',
      manageCollector: 'Manage Collector',
      btnApprovalAgain: 'Approval again',
      editData: 'Edit Data',
      exportToPdf: 'Export PDF',
      btnAddOther: 'Add Other',
      btnAddDispatch: 'Add dispatch',
      btnAddDispatch2: 'Add dispatch',
      btnAddCollector: 'Add collaboration',
      btnEditCollector: 'Edit collaboration',
      btnCollector: 'Collaboration',
      btnUseTemplate: 'Use Template',
      searchEndTime: 'Termination date range',
      searchTag: 'Search tag',
      searchRegion: 'Search Region',
      searchLevel: 'Search Level',
      searchFrequency: 'Search Reporting Frequency',
      searchDepartment: 'Search Reporting Department',
      searchLevelMuti: 'Search Reporting Hierarchy',
      searchRange: 'Search Reporting Range',
      searchTaskType: 'Search Task Type',
      searchName: 'Search name',
      todoTaskDoing: 'Doing',
      todoTaskDone: 'Done',
      todoLabel: 'TODO',
      todoDoingReportLabel: 'Doing Report',
      todoDoingTaskLabel: 'Doing Task',
      statisticLabel: 'Statistics',
      statisticDays: 'last 30 days',
      lastedUser: 'Created User',
      lastedTime: 'Created Time',
      dataStatus: 'Data status',
      emptyListTip: '【Note】No data has been entered.',
      formDetail: 'Form Detail',
      individual: 'Individual',
      reinfoce: 'Reinfoce',
      reinfoceByOrg: 'Reinfoce by org',
      allData: 'All data',
      editDateTip: 'Click Edit to edit it on the right',
      uploadExcelTip: 'Please download the template first, and then upload the data according to the template format',
      uploadExcelFileTip: 'Support XLSL, XLS formats',
      editFormSuccess: 'Refresh form successfully',
      submitOnce: 'Only allow to submit once, after submit, the task will be finished automatically',
      publishTip:
        'Once initiated, the non-periodic reports cannot be edited (report configurations can be changed). Please ensure that the report design is correct before starting the task.',
      publishFlow: 'Publish',
      downloadData: 'Download data',
      dataFormat: 'Data format',
      flowDetail: 'Flow details',
      publishTextTip: 'You can start filling out the report after initiation',
      finishJob: 'End task',
      editReport: 'Edit report',
      editFormConfig: 'Edit report configuration',
      editForm: 'Edit report form',
      editFormWaring: 'This report has been initiated and new or deleted questions are not supported yet!!!',
      exitCreateConfirm: 'Exit Confirmation?',
      exitCreateConfirmDesc: 'Are you sure you want to exit the current page?',
      confirmCancelTask: {
        title: 'Are you sure to cancel the delivery?',
        content: 'Please enter a reason for cancellation for your convenience',
        okText: 'Sure',
      },
      confirmRevokeTask: {
        title: 'Confirm Return Task?',
        content:
          'Please input the return reason, after returning, this task will not appear in your task center. The upper level can view the details in the issuing management.',
        okText: 'Return Task',
      },
      confirmCancelSubmit: {
        title: 'Are you sure cencel submit？',
        content:
          'After you withdraw your submission, the data you submitted will also be withdrawn and you will need to resubmit it in the future',
        okText: 'Sure',
      },
      confirmDeleteDraft: {
        title: 'Confirm Delete Draft?',
        content: 'This action cannot be undone. Please proceed with caution.',
        okText: 'Confirm Delete',
      },
      confirmDeleteWorkFlow: {
        title: 'Confirm Delete Report?',
        content:
          'Deleting the report will also delete the collected data, and it cannot be recovered. Please proceed with caution.',
        okText: 'Confirm Delete',
      },
      flowType: {
        usual: 'Usual',
        periodic: 'Periodic',
        endless: 'Continuous',
        collaborate: 'Collaborative',
      },
      dateStatus: {
        start: 'Start with',
        create: 'Created',
        publish: 'Published',
      },
      statistics: {
        finishedReport: 'Finished report',
        finishedOrder: 'Finished order',
        finishedForm: 'Finished form',
        finishedReview: 'Finished review',
      },
      detail: {
        tabInfoLabel: 'Report Info',
        tabFlowLabel: 'Flow Info',
        tabDataLabel: 'Form Data',
      },
      tip: {
        tableDataTotal: 'Total',
        staticsPrefix2: 'This task has total data',
        staticsDataStatus: (info: Record<string, number>, staticsPrefix = 'Up to now, a total of data') =>
          `${staticsPrefix}: ${info.total} (data status added: ${info.add}; Data status update: ${info.update} entries; Data status deleted: ${info.del} entries; Unmanipulated data: ${info.untouch})`,
        confirmFinish: 'Are you sure to complete?',
        taskFinished: 'Finished',
        publishReport: (name: string) => `Are you sure to publish "${name}"`,
        deleteReport: (name: string) => `Are you sure to delete "${name}"`,
        noPkgInfo: 'No datapkg info',
        confirmSubmit: (name: string) => `Are you sure to submit "${name}"`,
        confirmReApproval: 'Please select operator',
        confirmAccept:
          'Confirm the report submission (After confirmation, further issuance and reassignment will not be possible)',
        confirmEdit: 'Edit',
        publishFail: 'Release failed, process not initiated',
        fillReportInfo: 'Please complete the report information first',
        reportInfoError: 'There are required fields not filled in the report configuration',
        periodicMinInterval: (minInterval: number) => `Interval cannot be less than ${minInterval} minutes`,
        reportConfigError: 'There are required fields not filled in the report public configuration',
        residentInfoCount: 'Add up to one citizen information component',
        residentIdCard: 'Please tick the ID card option in the Citizen Information component',
        noSearchTip: 'No data collected or completed at this time',
        deleteData: 'Delete data',
        confirmDeleteData: 'Are you sure to delete this row data?',
        dataFail: 'Data acquisition failure',
        flowEmpty: 'No flow data',
        dispatchRepeat: (names: string) => `No duplication of additional assignments allowed: [${names}]`,
        reassignTip:
          'Note: After choosing to reassign, the primary responsible department will be changed to the corresponding department, and your department will no longer have permission to access this task. If you need to continue managing this task, please choose "Delegate".',
        reassignTip2:
          'Note: After choosing to reassign, the primary responsible department will be changed to the corresponding department, and your department will no longer have permission to access this task. If you need to continue managing this task, please choose "Add dispatch" or "Add collaboration".',
      },
      reportInfo: 'Report Info',
      reportDesign: 'Report Design',
      reportEdit: 'Report Edit',
      publishSetting: 'Publish Setting',
      fillSetting: 'Fill Setting',
      fillDate: 'Fill Date:',
      endFillDate: 'End Date',
      flowCycletimer: 'Cycle timer',
      flowSetting: 'Flow Configuration',
      needApproval: 'Need Superior Approval',
      inheritData: 'Inherit Data from Previous Cycle',
      completeWorkflowAfterNextPeriod:
        'When the next cycle is started, the task of the previous cycle is automatically terminated',
      completeWorkflowAfterNextPeriodTip:
        'Applicants will not be able to continue to complete tasks in the completed cycle',
      showDataAfterSubmit: 'Data is visible to superiors only after submission',
      finishAfterSubmit: 'Finish task after data submission',
      reportConfig: 'Report Config',
      collaborate: 'Collaborate',
      residentInfo: 'Resident Info',
      periodConfig: 'Periodic setting',
      reportStartDate: 'Report Start Date',
      reportEndDate: 'Report End Date',
      dispatchConfig: 'Dispatch Department Setup',
      infoName: 'Name',
      infoOrg: 'Org',
      infoFormOrg: 'Auth Data Org',
      ownership: 'Form Org',
      infoTag: 'Tag',
      infoDesc: 'Desc',
      reportFillTask: 'Report fill task',
      finisheProcessing: 'Degree of completion',
      firstDispatch: 'First assignment',
      firstDispatch2: 'Received new task',
      firstDispatch3: 'First start',
      endFill: 'End flow',
      rejectDispatch: 'Reject Assignment',
      startEndDate: 'Starting and ending Date',
      startDate: 'Starting Date',
      endDate: 'Ending Date',
      reportTemplate: 'Report Template',
      downloadTemplate: 'Download Template',
      reportCreate: 'Create Report',
      municipalProject: 'Municipal Project',
      districtProject: 'District Project',
      streetProject: 'Street Project',
      dataCollection: 'Data Collection',
      dataVerify: 'Data Verification',
      startSpecError: 'Configuration file lacks the ID for initiating the workflow',
      flowSpecError: 'Configuration file lacks the ID for the flow process',
      ownershipPkgIdError: 'Configuration file lacks the ID for the data package of the form org',
      expedite: 'Expedite',
      expediteAll: 'Expedite all',
      viewFlowMap: 'View map',
      viewDeliveryCancelOrRejectList: 'View delivery cancelled/refused list',
      hiddenDeliveryCancelOrRejectList: 'Hide delivery cancelled/refused list',
      viewPeriodicHistory: 'View periodic history',
      switchVersion: 'Switch this',
      currentVersionTip: "It's",
      hadIsuuedTip: 'Isuued',
      viewAll: 'View all',
      visit: 'Visit',
      taskType: 'Task Type',
      regionTitle: 'Form Region',
      levelTitle: 'Form Level',
      levelMutiTitle: 'Reporting Hierarchy',
      frequencyTitle: 'Reporting Frequency',
      rangeTitle: 'Reporting Scope',
      ownerTitle: 'Reporting Owner',
      ownerPhoneTitle: 'Reporting Owner Contact Information',
      publishSuccessTitle: 'Publish Successfully!',
      publishSuccessTip: 'The current report has been published and you can start filling it out.',
      publishSuccessDividerTip: 'Next, you can start',
      region: {
        person: 'Person',
        house: 'House',
        company: 'Company',
        matter: 'Matter',
        thing: 'Thing',
      },
      level: {
        country: 'national level',
        province: 'provincial level',
        city: 'municipal level',
        district: 'district level',
        street: 'street/town level',
      },
      frequency: {
        day: 'Daily',
        week: 'Weekly',
        month: 'Monthly',
        quarter: 'Quarterly',
        year: 'Yearly',
        halfYear: 'Half Year',
        demand: 'On-demand',
        temporary: 'Temporary',
      },
      levelMuti: {
        district: 'District',
        street: 'Street',
        community: 'Community',
      },
      range: {
        hangzhou: 'Hangzhou',
        shangcheng: 'Shangcheng District',
        gongsu: 'Gongshu District',
        xihu: 'Xihu District',
        binjiang: 'Binjiang District',
        xiaoshan: 'Xiaoshan District',
        yuhang: 'Yuhang District',
        linping: 'Linping District',
        qiantang: 'Qiantang District',
        fuyang: 'Fuyang District',
        linan: 'Linan District',
        tonglu: 'Tonglu County',
        chunan: 'Chunan County',
        jiande: 'Jiande City',
        xihu2: 'Xihu Scenic Area',
      },
      advanceFilter: {
        title: 'Advance Filter',
        reset: 'Reset',
      },
      formPlaceholder: 'Search form name',
      taskPlaceholder: 'Search task name',
    },
    resourceShare: {
      title: 'Share',
      allShared: 'All Shared Users',
      selected: (num: number) => `Selected ${num} items`,
      addUser: 'Add User',
      allSelect: 'All Select',
      cancel: 'Cancel',
      confirm: 'Confirm',
      delete: 'Remove User',
      editPermission: 'Edit Permission',
      noContent: () => (
        <span>
          Currently no shared users, click the top right corner &quot;
          <span style={{ color: 'var(--metro-primary-default)' }}>+Add User</span>&quot; to share
        </span>
      ),
    },
    share: {
      shareWorkflow: 'Share workflow',
      shareLink: 'Share link',
      copyBtn: 'Copy',
      previewBtn: 'Preview',
      qrcode: 'Qrcode',
      miniCode: 'Mini Program Qrcode',
      qrFileName: (spName: string, type: string) => `${spName}_${type}_${Math.random().toString(16).slice(2)}`,
      fillParamLabel: 'Predefined parameters',
      fillParamBtn: 'Go setting',
      fillParamTip:
        'You can control the selection and filling of some questions in the questionnaire by attaching some parameters to the questionnaire link',
      fillParamSuccessTip: 'Share this link, which already contains pre-set parameters',
      sharePlateform: 'Share to the app',
      shareToPf: (pf?: string) => `Share on ${pf}`,
      redbook: 'Redbook',
      weibo: 'Weibo',
      douyin: 'Douyin',
      qq: 'QQ',
    },
    workflow: {
      menu: {
        workflow: 'Workflows',
        myInitiate: 'Initiated by me',
        waitHandle: 'Processed by me',
        myApproval: 'My Approval',
        formResources: 'Flow form',
      },
      form: {
        createForm: 'Create form',
        searchForm: 'Search form',
        columnName: 'Form name',
        columnDesc: 'Form description',
        columnCreateTime: 'Create time',
      },
      addRequiredParams: 'Auto fill required parameters',
      noRequiredParams: 'No required parameters',
      useSelf: 'Self',
      startSuccess: 'Initiated successfully',
      approved: 'Approved',
      rejected: 'Rejected',
      inputInfo: 'Fill in information',
      workflowImage: 'Workflow diagram',
      instance: 'Instance',
      theWorkflowImage: '',
      viewInitiateInfo: 'View information',
      viewEngineError: 'View engine error',
      tempDataGetFailed: 'Failed to retrieve workflow data',
      inputFormInfo: 'Fill in form information',
      operateSuccess: 'Operation successful',
      noInitiate: 'No initiation content',
      copyLinkSuccess: 'Link copied successfully',
      createLinkPage: 'Generate link page',
      createTime: 'Initiation time',
      byWorkflow: 'By workflow',
      byInstance: 'By instance',
      byTask: 'By task',
      byStatus: 'By status',
      byResult: 'By result',
      searchPlaceholder: 'Search instance name',
      searchTaskPlaceholder: 'Search task name',
      moreFilterBtnLabel: 'More search',
      moreFilter: {
        chooseSpec: 'Please select a workflow first',
        specTip: 'For different workflow, you can perform advanced searches on specific fields',
        specLabel: 'Workflow: ',
        emptyTip: 'There are no global variables that support searching',
        clearBtn: 'Clear',
        okBtn: 'Search',
      },
      initiateStatusDesc: 'Select initiation status',
      cancel: 'Cancel',
      detail: 'Details',
      flowName: 'Name',
      flowId: 'ID',
      title: 'Title',
      taskName: 'Task Name',
      wfName: 'Workflow Name',
      wfSpecName: 'Workflow Spec',
      taskDate: 'Date',
      taskStatus: 'Status',
      desc: 'Description',
      applyTime: 'Apply time',
      runResult: 'Execution result',
      searchFlow: 'Search workflow',
      cancelConfirm: 'Cancel confirmation',
      cancelConfirmDesc: 'Confirm whether to cancel, and provide a reason',
      cancelRequired: 'Reason is required',
      initiate: 'Initiated',
      initiateInfo: 'Initiation information',
      circulationInfo: 'Circulation information',
      instanceStatusDesc: 'Select instance status',
      userName: 'Applier',
      searchResource: 'Search resource name',
      instanceName: 'Instance name:',
      startInstance: 'Initiate instance',
      copyStartInstanceLink: 'Copy initiate instance link',
      editFlow: 'Edit workflow',
      tempDataError: 'Failed to obtain workflow data',
      copyXml: 'Copy XML',
      replaceTemp: 'Import from File',
      replaceTempFromCopy: 'Import from clipboard',
      clipboardError: 'The contents of the clipboard are not process XML',
      noname: 'Unnamed',
      nameOrConfigEmptyError: 'Name and configuration cannot be empty!',
      tempOperateSuccess: (name?: string, isAdd?: boolean) =>
        `${name} workflow ${isAdd ? 'updated' : 'added'} successfully`,
      copyed: 'Copied to clipboard',
      userOrAdmin: 'User/Creator',
      lastRunTime: 'Last execution time',
      lastRunResult: 'Last execution result',
      uploadTemp: 'Upload file',
      uploadCompressTip:
        'After activation, if the image exceeds the size limit, the system will compress the image to below the limit size. Please note that if the size of the uploaded image exceeds the limit size by a large margin, the compression time will increase.',
      createFlow: 'Create new workflow',
      share: 'Share',
      downloadTemp: 'Download file',
      delTemp: 'Delete workflow',
      delTempSuccess: (name?: string) => `Workflow ${name} deleted successfully`,
      delTempConfirm: (name?: string) => `Confirm to delete ${name} workflow?`,
      delTempConfirmDesc: 'After deletion, the records and scheduled tasks that have been executed cannot be viewed.',
      h5: {
        initiateUser: 'Initiator: ',
        applyUser: 'Applicant: ',
        applyTime: 'Start Time: ',
        completeTime: 'Finish Time:',
      },
      status: {
        idle: 'Suspended',
        ready: 'In process',
        completed: 'Approved',
        rejected: 'Rejected',
        cancelled: 'Cancelled',
        waiting: 'In process',
        processing: 'Processing',
        processed: 'Processed',
        done: 'Completed',
        finishDone: 'Done',
        reject: 'Rejected',
        noRun: 'Not Running',
        finished: 'Finished',
        pending: 'Pending',
        ongoing: 'Ongoing',
        engine_error: 'Engine Error',
        approval: 'Approval',
      },
      keybord: {
        function: 'Function',
        hotKey: 'Hot Key',
        undo: 'Undo',
        redo: 'Redo',
        copy: 'Copy',
        cut: 'Cut',
        paste: 'Paste',
        find: 'Find',
        drag: 'Drag',
        selectBox: 'Select box',
        connect: 'Connect',
        editLabel: 'Edit label',
        selectAll: 'Select all',
        delete: 'Delete',
        zoomin: 'Zoom in',
        zoomout: 'Zoom out',
        nozoom: 'No zoom',
        move: 'Move',
      },
      notSupportForm:
        'Recommend using the function parameter configuration tool, the system will give priority to the parameter value configured by the function',
      active: 'Active',
      pkg: 'Datapkg',
      mappingTable: 'Field Mapping Table',
      mappingTableTip: 'Please fill in the mapping between the packet fields and the form fields',
      paramsName: 'Parameter Name',
      paramsNameTip: 'Please fill in the parameter name',
      required: '(Required)',
      visualParamsConfig: 'Function Configuration',
      traditionalParamsConfig: 'Function Parameter Configuration(Old)',
      saveWithVisualParams: 'Save Function Configuration',
      saveWithTraditionalParams: 'Save Function Parameter Configuration(Old)',
      specialParamsConfigTip:
        'This function has a special configuration method. You can use the configuration above, or you can edit the parameters directly here',
      manualParamsConfigTip:
        'You can edit the parameters directly here, but it is recommended to use the configuration above',
      updateDatapkgDataTip: 'The datapkg must have only one key',
      fieldName: 'Datapkg Field',
      fieldId: 'Form Field ID',
      pkgIdTip: 'Datapkg ID(Hidden properties, do not delete!)',
      insertData: 'Insert Data',
      selectPkg: 'Select Datapkg',
      generateForm: 'Generate Form',
      wfSpecList: {
        type: 'Spec Type',
        status: 'Spec Status',
        enable: 'Enable',
        disable: 'Disable',
      },
      reFill: 'Re-fill',
      fillThanks: 'Thank you for your patience in filling it out ',

      anonymous: 'Anonymous',
      refuse: 'Reject',
      applyInfo: 'Application Information',
      flowInfo: 'Flow Information',
      anonymousNode: 'Anonymous Node',
      fillStart: 'Initiate Report',
      flowEnd: 'End',
      cancelled: 'Cancelled',
      cancelledReason: 'Cancelled Reason',
      workflowNotExist: 'Workflow does not exist',
      edit: {
        datasource1: 'Option 1',
        datasource11: 'Option 1-1',
        datasource111: 'Option 1-1-1',
        datasource112: 'Option 1-1-2',
        datasource2: 'Option 2',
        datasource3: 'Option 3',
        datasourceLabel: 'Display Value',
        datasourceValue: 'Stored Value',
        single: 'Single-choice Question',
        multiple: 'Multiple-choice Question',
        datePicker: 'Date and Time',
        input: 'Question & Answer',
        geometryInput: 'Geography',
        geometryInputDesc: 'Click the icon on the right to input',
        emptyContent: 'No options available',
        switch: 'Switch',
        upload: 'Attachment Question',
        userSelector: 'Contact Person',
        autoComplete: 'Auto Complete',
        rate: 'Rating Question',
        arrayCards: 'Incremental Card Question',
        arrayCardsTitle: 'Card Title',
        arrayTable: 'Incremental Table Question',
        colName: 'Column Name',
        cascader: 'Cascading Selection Question',
        datasourceStep1: '① Data Source Setting',
        datasourceStep2: '② Data Processing',
        datasourceStep3: '③ Response Result',
        datasourceEmpty: 'This question type does not require data configuration',
        datasourceTypeStatic: 'Static Data',
        datasourceTypeApiOut: 'External API',
        datasourceTypeApiIn: 'Internal API',
        datasourceTypeApiInDesc: 'Please select an internal API',
        dataAuthFollow: 'Data Permissions Follow',
        datasourceTypeQLang: 'QLang',
        datasourceTypeLowCodeQLang: 'Low-Code QLang',
        datasourceType: 'Data Source Type',
        datasourceTypeDesc: 'Please select the data type',
        fillEdit: 'Full Screen Edit',
        unsupportObj: 'Editing complex objects is not supported',
        addOne: 'Add a row',
        changePreviewEdit: 'Switch to Visual Editing',
        changeCodeEdit: 'Switch to Code Editing',
        requestType: 'Request Method',
        requestTypeDesc: 'Please select the request method',
        urlDesc: 'Please enter the URL',
        serviceProxy: 'Server Proxy Request',
        serviceProxyDesc: 'Check if access is not possible due to cross-domain issues',
        cookie: 'Cookie Required',
        cookieDesc: 'Use when proxy is not selected and cookies need to be retrieved',
        apiDep: 'API Dependency',
        pkgColInfo: 'Data Package Column Information',
        pkgRowInfo: 'Data Package Row Data',
        curOrgUserList: 'Current Organization User List',
        curOrgList: 'Current Organization Departments List',
        creator: 'Creator',
        infoUser: 'Reporter',
        allUser: 'All Users',
        orgUser: 'Organization Users',
        specificUser: 'Specific Users',
        allOrg: 'All Departments',
        specificOrg: 'Specific Departments',
        equalOrg: 'Equal-Level Departments',
        childOrg: 'Subordinate Departments',
        allChildOrg: 'All Subordinate Departments',
        currentOrg: 'Current Departments',
        currentAndLowerOrg: 'Current Department and Subordinate Departments',
        currentAndLowerAllOrg: 'Current Department and All Subordinate Departments',
        settings: 'Option Settings',
        userRange: 'User Range',
        orgRange: 'Organization Range',
        sqlError: 'SQL cannot be empty and must be syntactically correct',
        runSql: 'Run SQL',
        alloc: 'Format',
        previewModeEdit: 'Preview Mode Edit',
        sqlDev: 'SQL Dependency',
        orgDev: 'Organization Dependency',
        objMode: 'Object Mode',
        lowCodeQe: 'QLang Low-Code Editor',
        lowCodeSql: 'Low-Code SQL',
        noDep: 'No Dependencies',
        addCol: 'Add a Column',
        delCol: 'Delete a Column',
        cascaderTitleDesc: 'Cascading Options',
        mdtAI: 'Mdt AI Assistant',
        AI: 'AI Assistant',
        mdtAIDesc: 'Don’t know how to code? Try the Mdt AI Assistant.',
        handler: 'Handler',
        createHandler: 'Create Handler',
        addGlobalHandler: 'Add Global Handler',
        delHandler: 'Are you sure you want to delete this handler?',
        delHandlerDesc: 'Deleting it may cause the data source result to be incorrect',
        codeEmptyOrGrammerError: 'Code cannot be empty or have syntax errors',
        codeEdit: 'Code Edit',
        formatDataDesc:
          'When the data source return result does not meet the format requirement, you can process it here, such as sorting, filtering, converting, etc.',
        dataTransform: 'Data Transformation',
        dataFilter: 'Transformation & Filtering',
        dataSort: 'Transformation & Sorting',
        AIDesc: 'Hello, I am the Mdt AI Assistant. I can help you complete the coding for data processing',
        dataRespContent: (type, str) =>
          `The data type is ${type}, and a portion of the data is as follows:\n\n${str}\n\n`,
        AIPlaceholder: 'Please describe your requirements',
        AIAwaitPrevAnswer: 'Please wait for the previous response to finish before continuing',
        AINeedDatasource: 'Please configure the data source first, and ensure the data source is configured correctly',
        AICodeFormat: 'Code Requirements',
        AIAcceptThis: 'Adopt this response',
        AIQuickReply1: '1. The data might be empty, add some prechecks',
        AIQuickReply2: '2. Use ES6, keeping the code concise',
        AIQuickReply3: '3. Filter out entries with empty values in the transformed data, retaining only 5 entries',
        AIQuickReply4: '3. Sort the transformed data by value in descending order',
        matching: 'Matching',
        matchSuccess: 'Match Successful',
        matchFailed: 'Match Failed',
        field: 'Field',
        map: 'Mapping',
        status: 'Status',
        fieldMap: 'Field Mapping',
        viewFormat: 'View Format Requirements',
        dataResultTip: 'The final result after the first 2 steps is displayed below',
        editableParam: 'Parameter Name (Editable)',
        formatTransform: 'Format Conversion Requirements',
        defaultValueFormatDesc1: 'To ensure field format correctness, we',
        defaultValueFormatDesc2: 'agree',
        defaultValueFormatDesc3: 'to process the data in the following ways, for example:',
        defaultValueFormatDesc4:
          '1. The field accepting [integer] type will automatically parse the data as an integer;',
        defaultValueFormatDesc5:
          '2. The field accepting [floating-point number] type will automatically parse the data as a float;',
        defaultValueFormatDesc6:
          '3. The field accepting [array] type, receiving a comma-separated (",") string can convert it into an array, for example, "1,2,3" will be converted to ["1","2","3"]',
        defaultValueMatchTitle:
          'By default, it will match based on the field names. If you need manual field mapping, please enable the manual field mapping feature.',
        defaultValueMatchClose: 'Do you want to close field mapping?',
        defaultValueMatchCloseDesc:
          'After closing, your configuration cannot be saved, and parameters will automatically match based on field names.',
        defaultValueManualMatch: 'Manual Field Mapping',
        defaultValueManualMatchDesc:
          'After enabling, it can accurately filter and fill the parameters obtained through URL, or map and fill.',
        defaultValueStep1: '① Default Value Setting',
        defaultValueStep2: '② Data Processing',
        defaultValueStep3: '③ Preview',
        defaultValue: 'Default Value',
        delDefaultValueTitle: 'Are you sure you want to delete this default value?',
        delDefaultValueTitleDesc: 'Deleting it may cause some data to not be filled by default',
        defaultValueSource: 'Default Value Source:',
        defaultValueType: 'Please select the default value type',
        defaultValueSortDesc:
          'The sorting here will affect the final default value result, and by default, it will combine from top to bottom in sequence.',
        createDefaultValue: 'Create Default Value',
        setted: 'Set',
        unset: 'Unset',
        fieldDep: 'Dependent Field',
        operator: 'Operator',
        depSetting: 'Dependency Configuration',
        depSettingDesc: 'Reference variables via variable names, starting with a colon (:), for example, :h2gIMlc2GT',
        addDepField: 'Add Dependency Field',
        sourceSetting: 'Source Configuration',
        dataHandle: 'Data Processing',
        finally: 'Finally',
        runScript: 'Run Script',
        visible: 'Display Condition',
        notJson: 'Not a standard JSON string: ',
        needSql: 'Please enter SQL',
        needUrl: 'Please enter URL',
        chooseApi: 'Please select an API',
        serviceError: 'Server error, please try again later',
        datasourceRequestSetError: 'Data source setting request error:',
        filedNeedManual: (str) => `Field [${str}] not successfully matched, set mapping or handle with code`,
        dataHandleError:
          'After processing with the handler, the data is NULL, please disable the handler or check the code',
        dataHandleDataError: 'Data is incorrect, the type should be:',
        dataHandleFormatError:
          'Data format does not meet the format requirements, you may have forgotten to handle it, or try field mapping',
        filterError: (name, message) => `Filter [${name}] execution error: ${message}`,
        dataValueSettingError: 'Form value setting request error:',
        chooseDatapkg: 'Please select a data package',
        datapkgEmpty: 'This data package has no data yet',
        needOrg: 'Please select a department',
        needUser: 'Please select a user',
        netError: 'Network error',
        downloadSuccess: 'Download successful',
        formatError: 'Incorrect format',
        nodeName: 'Node Name',
        addNode: 'Add Node',
        question: 'Question',
        formFieldName: 'Field Name',
        fieldNoEmpty: 'Variable name cannot be duplicated',
        addCondition: 'Add Condition',
        addRule: 'Add Rule',
        strType: 'String Type',
        numType: 'Number Type',
        boolType: 'Boolean Type',
        literalValue: 'Static Value',
        globalVar: 'Global Variable',
        pythonExpr: 'Python Expression',
        template: 'Template',
        value: 'Value',
        dataTypeTransform: 'Convert Data Type To',
        userType: 'User type',
        varCategory: 'Variable Category',
        configAfterSubmit: 'Display Settings After Submission',
        configServiceConfig: 'Request Configuration',
        specOwner: 'Workflow Creator',
        initiator: 'Filler',
        taskExecutor: 'Task Executor',
        serviceConfigType: 'Specify Request User Identity',
        taskExecutorId: 'Task Executor Task ID',
        allowMissing: 'Use Creator When User Identity Does Not Exist',
        allowMissingTooltip: 'Closing will result in an error prompt',
        fromWfSpec: 'Get from existing workflow',
        fromVars: 'Get from variables',
        childWf: 'Subprocess',
        propagateAllParentVariables: 'Propagate all parameters from parent workflow to subprocess',
        childWfInputVars: 'Input parameters for subprocess instances',
        inputVars: 'Input Parameters',
        childWfAcceptVars: 'Variables accepted by subprocess',
        propagateAllChildVariables: 'Propagate all parameters from subprocess to parent workflow',
        childWfOutputVars: 'Output parameters of subprocess instances',
        outputVars: 'Output Parameters',
        parentWfAcceptVars: 'Variables accepted by parent workflow',
        waitChildCompleted: 'Wait for subprocess to finish',
        childWfName: 'Subprocess Instance Name',
        childDescription: 'Subprocess Instance Description',
        asigneeVisible: 'Display subprocess in the frontend pending tasks list',
        assignUsers: 'Users who need to be displayed on the frontend pending tasks',
        userGrouped: 'User Group',
        allowAssigneeCancelChild: 'Allow assignee to cancel subprocess',
        childInitiator: 'Subprocess Initiator',
        childWfSource: 'Subprocess Source',
        childWfStartTip: 'The initiator of the subprocess instance allows at most one person',
        exprType: 'Expression Type',
        exprPlaceholder: 'Please enter an expression',
        conditionSetting: 'Condition Expression Setting',
        executorId: 'Node Executor Source',
        targetId: 'Target Node',
        target: 'Node',
        configNodeExecutor: 'Dynamic Setting for Node Executor',
        fromNodeAndVars: 'Get from workflow nodes and global variables',
        tipSetForm: 'Please set the form first',
        fieldMapList: 'Mapping relationship with current node form fields',
        nodeAndVarsField: 'Node and Global Variable Field',
        currentNodeField: 'Current Node Field',
        chooseOtherField: 'Choose other workflow nodes',
        defaultType: 'Default Value Type',
        isUsePrev: 'Use previous report content',
        defaultValTitleFull:
          'Mapping relationship with current node form fields (if the previous content is empty, the content of the following node will be used as the default value)',
        defaultValTitle: 'Mapping relationship with current node form fields',
        configFormDefault: 'Form Default Value Setting',
        pkg: 'Data Package',
        wfNode: 'Workflow Node',
        usedNode: 'Applied Node',
        formSource: 'Specify Form Source',
        tipVarName: 'Variable name cannot be empty',
        tipVarType: (name) => `${name} variable type cannot be empty`,
        tipVarId: (name) => `${name} variable ID cannot be empty`,
        tipVarDefault: (name) => `Default value type of ${name} does not match`,
        tipVarIdFormat: 'Variable ID does not conform to the standard format',
        tipVarIdFormatContent: (name) =>
          `The variable ID (${name}) does not conform to the naming convention, ID must start with a letter/Chinese character, and can only contain letters, Chinese characters, numbers, and underscores.`,
        tipVarIdExist: (name) => `The following variable IDs already exist in the workflow, please modify: ${name}`,
        sysGlobalVar: 'System Global Variable',
        varName: 'Variable Name',
        varType: 'Variable Type',
        insertSysVar: 'Insert Built-in Variable',
        varID: 'Variable ID',
        customGlobalVars: 'Custom Global Variables',
        selectField: 'Select Node Field',
        customGlobalVarTip: `
          <div>
            <div>Default Value Description</div>
            <div>1. For numeric and text types, please enter directly, for example:</div>
            <div>Numeric: 12345</div>
            <div>Text: abcde</div>
            <div>2. For array-type variables, use comma (,) to separate and enclose with brackets, for example:</div>
            <div>Text Array: ["aaaa", "bbb"]</div>
            <div>Integer Array: [1, 2]</div>
            <div>File Type: [{JSON.stringify({ name: 'xxx.jpg' })}]</div>
            <div>3. Supported values for Boolean types are: true, false</div>
          </div>
        `,
        varDefault: 'Variable Default Value',
        varDesc: 'Variable Description',
        supportSearch: 'Support Search',
        inForm: 'In Form',
        questionName: 'Question Name',
        formVar: 'Form Variable',
        configVar: 'Variable Management',
        tipLoopDataInputRef: 'The type of "Data Set for Loop Creation" must be an array',
        tipLoopDataOutputRef: 'The type of "Data Set for Collecting All Instance Output Values" must be an array',
        byLoopCount: 'By Loop Count',
        byDatasource: 'By Data Set',
        normalTimer: 'Normal setting',
        mdtVarRef: 'Reference variables',
        loopCount: 'Loop Count',
        loopDataInputRef: 'Data Set for Loop Creation',
        inputDataItem: 'Variable for Each Instance to Accept Loop Value',
        outputDataItem: 'Output Variable for Each Instance',
        loopDataOutputRef: 'Data Set for Collecting All Instance Output Values',
        completionCondition: 'Completion Condition',
        multiInstance: 'Multi-instance',
        tipCustomVar: 'The assigned variable can only be a custom variable.',
        tipExpr:
          "Separate multiple assignment statements with a semicolon (;), and enclose strings with single quotes (')",
        configGlobalVar: 'Global Variable Assignment',
        tipLoopCountCondition: 'Specify at least one of [Maximum Loop Count] and [Loop Condition]',
        loopMaximum: 'Maximum Loop Count',
        loopCondition: 'Loop Condition(second)',
        loopInterval: 'Interval',
        recordLoop: 'Record the execution history of all loops',
        recordLoopTip:
          'By default, only the last execution is recorded, if the switch is turned on, when the number of cycles is too large, it may cause the process instance to occupy too much memory, affecting the process efficiency',
        testBefore: 'Check Loop Condition First',
        standardLoop: 'Standard Loop',
        cycleTime: 'Cycle time',
        date: 'Date',
        configTimer: 'Timer Value Setting',
        userAssign: 'User Assignment',
        startFormField: 'Initial Form Field',
        configWfTemplate: 'Workflow Instance Template Setting',
        configDetailPage: 'Settings on the details page',
        tipFieldFormat: (name) =>
          `Field name (${name}) does not meet the naming convention, and the variable name must start with a letter/Chinese character, and can only contain letters, Chinese characters, numbers, and underscores.`,
        tipFieldExist: (name) =>
          `The following question field names already exist in the workflow, please modify: ${name}`,
        tipAddField: 'Please add field mapping',
        tipFieldRepeat: 'Form fields cannot be duplicated',
        tipPkgFieldRepeat: 'Data package fields cannot be duplicated',
        formField: 'Initial Form Field',
        mappingField: 'Mapping Field',
        returnColumns: 'Returned Columns',
        returnRows: 'Return Top Rows',
        filterSet: 'Set Filter Condition',
        tipFilterSet: 'If the condition value is a variable, please enclose it with braces, e.g., {{__initiator}}',
        text: 'Text',
        link: 'Link',
        textContent: 'Text Content',
        linkTitle: 'Link Title',
        linkContent: 'Link Content',
        linkUrl: 'URL to Redirect on Click',
        linkTip: 'The link address must use the HTTPS protocol, e.g., https://linkaddress',
        linkImg: 'Link Image',
        linkImgTip: 'The image address must use the HTTPS protocol, e.g., https://picaddress.jpg',
        markdownTitle: 'First Screen Conversation Display Content',
        markdownText: 'Markdown Format Message',
        webhook: 'Webhook URL',
        webhookMsgType: 'Message Type',
        detailPageType: 'Page Type',
        detailPageTypeDefault: 'Default',
        detailPageTypeDatlas: 'Datlas Screen',
        detailPageTypeAmis: 'Amis JSON',
        detailPageTypeUrl: 'Custom URL',
        detailPageTypeUrlPlacehold: 'Please enter URL (supports internal relative paths)',
        detailPageTypeAmisPlacehold: 'Please enter JSON',
        detailPageTypeDatlasPlacehold: 'Please enter Screen ID',
      },
      config: {
        question: 'Question',
        pkgField: 'Data Package Field',
        insertField: 'Insert Form Field',
        detailPage: 'Workflow Detail Page',
        defaultPage: 'Default Completion Page',
        outerPage: 'Redirect to External Page',
        outerPc: 'PC',
        outerMobile: 'Mobile',
        innerPage: 'Redirect to Internal Page',
        refresh: 'Refresh Original Page',
        blank: 'Open in a New Tab in the Browser',
        submitDisplay: 'Display After Submission',
        allowMult: 'Allow Multiple Submissions',
        urlAfterSubmit: 'Please Enter the Target Page URL',
        formDataTip:
          'Supports using form variables in URL paths and query parameters. Variable format: {{variable}}, for example, ?id={{userId}} or /api/{{endpoint}}.',
        jumpType: 'Redirect After Submission',
        storeLocation: 'Storage Location After Submission',
        selectPkg: 'Select Data Package',
        storePkgMap: 'Mapping Relationship between Questions and Data Package Fields',
        baseConfig: 'Basic Settings',
        fillConfig: 'Fill Settings',
        storeConfig: 'Storage Settings',
        submitConfig: 'Settings After Submission',
      },
      startApply: 'Initiated Applications',
      searchTitle: 'Search Title',
    },
    forwarder: {
      noContent: 'No Content',
      noContentDesc: 'Sorry, the page you visited does not exist.',
    },
    comment: {
      replyToPlaceholder: 'Input comment...',
      commentTitle: 'Comment:',
      noComment: 'No Comment',
      deleteSuccess: 'Comment deleted successfully',
      more: 'Expand',
      less: 'Collapse',
      addCommitSuccess: 'Comment added successfully',
      loadMore: 'View More',
      deleteComment: 'Delete Comment',
      reply: 'Reply',
      comment: 'Comment',
      refreshSuccess: 'Refresh successfully',
    },
    pkg: {
      appSetting: 'Settings',
      shareDesc: 'Only allowing sharing of data within the institution to outside institutions.',
      dataSize: 'Size',
      lastUpdate: 'Update',
      copyLink: 'Copy Link',
      copyLinkSuccess: 'Link Successfully Copied',
      publicLinkShare: 'Share Public Link',
      noAuth: 'No Permission',
      dataApply: 'Data Apply',
      pkgEmptyError: 'Data package does not exist or could not be requested for other reasons.',
      pkgLoading: 'Loading data package details',
      basic: 'Basic Overview',
      data: 'Table Preview',
      column: 'Column Attributes',
      desc: 'Data Package Description',
      genealogy: 'Lineage Graph',
      read: 'Use Permission',
      readDesc: 'Use this data to create graphs and maps',
      viewDetail: 'Query Permission',
      viewDetailDesc: 'Sort and filter data or use it as input for low-code ETL',
      download: 'Download Permission',
      downloadDesc: 'Download data to local machine.',
      description: 'Description:',
    },
    folder: {
      outermost: 'Outermost',
      home: 'Home',
      create: 'Create New Folder',
      createTip:
        'When you choose to create a new folder in the outermost layer, a new folder will be created at that location by default. If you have selected a specific folder, the new folder will be created within the currently selected folder.',
      rename: 'Rename Folder',
      moveSuccess: 'Moved successfully',
      createSuccess: 'Created successfully',
      deleteSuccess: 'Deleted successfully',
      moveToOutermost: 'Move to outermost',
      delete: 'Delete Folder',
      deleteTip:
        'Are you sure you want to delete this folder? Please note that the resources within the folder will be released to the root directory.',
      deleteCheck: 'After checking, all the file resources within the folder will be deleted together.',
    },
    selectDatapkg: {
      appData: 'Inst data',
      personalData: 'Personal data',
    },
    submitStyleConfig: {
      sizeTitle: 'Size',
      fillTitle: 'Fill',
      borderTitle: 'Border',
      fontTitle: 'Font',
    },
    uploadExcelCheck: {
      column: 'Column',
      columnEmpty: (emptyColumns: string) => `Column [${emptyColumns}] cannot be empty`,
      columnNotExist: (notExistColumns: string) =>
        `[${notExistColumns}] does not exist in the template, please delete them`,
      columnNotMatch: (notMatchColumns: string) =>
        `The ${notMatchColumns} column is not the same as the corresponding column name in the template`,
    },
    oneTableFormTypeItems: {
      normal: 'Normal',
      normalDesc:
        'Completes the corresponding task, the status of the task will change from "pending" to "processed", indicating that the reporter has completed the task. However, it can still continue to operate, such as viewing details or further processing.',
      endless: 'Endless',
      endlessDesc:
        'The reporter needs to fill in the report continuously at any time, and the corresponding task status is always in the "pending" state.',
      periodic: 'Periodic',
      periodicDesc:
        'Recurring reports recur at predetermined intervals, and the person concerned needs to complete this task in each cycle. Each time a cycle arrives, the task reverts to the Pending state.',
    },
    apply: 'Apply',
    hasApply: 'Applied',
    applying: 'Applying',
    emptyContent: 'App does not exist or is not authorized',
    qeBuilder: 'Qe Editor',
    fillFormData: 'Fill form',
    editFormData: 'Edit form',
    previousRow: 'Previous',
    nextRow: 'Next',
    saveAndAdd: 'Save & Add new',
    doFinished: 'Done',
    notSaveTip: 'Data had changed but not save',
    notSaveTipText: 'Unsaved content detected. Do you want to save the changes to the current page?',
    noSave: 'No save',
    saveedTip: 'Data had saved',
    btnReverDel: 'Rever delete',
    editCurrentDataId: 'Current edit data ID:',
    previewCurrentDataId: 'Current preview data ID:',
    displayByTable: 'Display use table',
    displayByTree: 'Display use tree',
    editRegeonTip: 'The row has been selected, please edit it in the area on the right.',
    closeForm: 'Close Form',
    h5Setting: {
      menu: 'Setting',
      hello: 'Hello',
      logout: 'Logout',
      userInfo: 'User Information',
      userName: 'User Name',
      changeIdentity: 'Change Identity',
      appName: 'App Name',
      perferenceSetting: 'Preference Setting',
      darkMode: 'Dark Mode',
      systemSetting: 'System Function',
    },
    h5Grid: {
      myNotify: 'My Notifications',
      myHandle: 'To Be Handled by Me',
      myStart: 'Initiated by Me',
    },
    bpmn: {
      executeTime: 'Execution Time',
      executeUserId: 'Executor ID',
      globalVars: 'System Global Variables',
      globalCustomVars: 'Custom Global Variables',
      nodeExecuteUserId: 'Node Executor ID',
      nodeExecuteTime: 'Node Execution Time',
      startNode: 'Start Node',
      inlineScript: 'Inline Script',
      dynamicAssignees: 'Dynamic Node Executor Setting',
      globalVariablesSetting: 'Global Variable Assignment',
      globalVarSetting: 'Global Variable Setting',
      globalVarCalResult: 'Global Variable Calculation Result',
    },
    ruleForm: {
      addCondition: 'Add Condition',
      addRule: 'Add Rule',
      copy: 'Copy',
      delete: 'Delete',
      multipleEmpty: '+ Please enter the query content',
    },
  },
};
