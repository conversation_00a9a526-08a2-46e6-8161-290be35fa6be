import * as fs from 'fs';
import * as path from 'path';

/**
 * 更新 datlasConfig.ts 文件
 * @param rootPath 项目根路径
 * @param productId 产品/模块ID
 * @param productName 产品/模块中文名称
 * @param isProduct 是否是产品（而非模块）
 */
export function updateDatlasConfig(
  rootPath: string,
  productId: string,
  productName: string,
  isProduct: boolean = true,
): void {
  const datlasConfigPath = path.join(rootPath, 'shares', 'product-micro-modules', 'src', 'datlas', 'datlasConfig.ts');

  if (!fs.existsSync(datlasConfigPath)) {
    throw new Error('无法找到 datlasConfig.ts 文件');
  }

  // 读取现有配置
  let configContent = fs.readFileSync(datlasConfigPath, 'utf8');

  // 检查产品是否已在配置中
  if (configContent.includes(`'${productId}'`)) {
    // 产品已存在，不需要更新
    return;
  }

  // 根据是否是产品进行不同的更新
  if (isProduct) {
    // 更新产品配置
    updateProductConfig(datlasConfigPath, configContent, productId, productName);
  } else {
    // 更新模块配置
    updateModuleConfig(datlasConfigPath, configContent, productId, productName);
  }

  // 更新菜单配置
  updateDatlasMenu(rootPath, productId, productName, isProduct);
}

/**
 * 更新产品配置
 * @param configPath 配置文件路径
 * @param content 配置文件内容
 * @param productId 产品ID
 * @param productName 产品中文名称
 */
function updateProductConfig(configPath: string, content: string, productId: string, productName: string): void {
  // 查找产品映射部分
  const productMapIndex = content.indexOf('const PRODUCT_PATH_MAP: Record<string, string> = {');
  if (productMapIndex === -1) {
    return;
  }

  // 查找产品映射结束位置
  const productMapEnd = content.indexOf('};', productMapIndex);
  if (productMapEnd === -1) {
    return;
  }

  // 添加新产品映射
  const productPathFormatted = productId.replace(/-/g, '');
  const newProductMapLine = `  '${productId}': '${productPathFormatted}',\n`;

  // 构建新内容
  const newContent = content.substring(0, productMapEnd) + newProductMapLine + content.substring(productMapEnd);

  // 更新产品跳转URL常量
  const constName = `JUMP_${productId.toUpperCase().replace(/-/g, '_')}_URL`;
  if (!newContent.includes(`export let ${constName} = '';`)) {
    // 找到最后一个JUMP_开头的导出常量
    const lastJumpExportIndex = findLastIndex(newContent, /export let JUMP_[A-Z_]+_URL = '';/);

    if (lastJumpExportIndex !== -1) {
      const insertIndex = newContent.indexOf(';', lastJumpExportIndex) + 1;
      if (insertIndex !== 0) {
        // 插入新的产品URL常量
        const exportLine = `\nexport let ${constName} = '';`;
        const updatedContent = newContent.substring(0, insertIndex) + exportLine + newContent.substring(insertIndex);

        fs.writeFileSync(configPath, updatedContent);
        return;
      }
    }
  }

  // 如果没有找到合适的位置或已存在，直接保存
  fs.writeFileSync(configPath, newContent);
}

/**
 * 更新模块配置
 * @param configPath 配置文件路径
 * @param content 配置文件内容
 * @param moduleId 模块ID
 * @param moduleName 模块中文名称
 */
function updateModuleConfig(configPath: string, content: string, moduleId: string, moduleName: string): void {
  // 对于模块，我们需要更新不同的部分
  // 根据你的项目具体情况实现模块配置更新逻辑

  // 保存更新后的配置
  fs.writeFileSync(configPath, content);
}

/**
 * 更新 datlas 菜单配置
 * @param rootPath 项目根路径
 * @param itemId 产品/模块ID
 * @param itemName 产品/模块中文名称
 * @param isProduct 是否是产品
 */
export function updateDatlasMenu(rootPath: string, itemId: string, itemName: string, isProduct: boolean = true): void {
  const appSideMenuPath = path.join(
    rootPath,
    'shares',
    'product-micro-modules',
    'src',
    'datlas',
    'app-side-menu',
    'DatlasAppSideMenu.tsx',
  );

  if (!fs.existsSync(appSideMenuPath)) {
    // 如果菜单文件不存在，不进行更新
    return;
  }

  // 读取现有菜单
  let menuContent = fs.readFileSync(appSideMenuPath, 'utf8');

  // 检查产品/模块是否已在菜单中
  if (menuContent.includes(`'${itemId}'`)) {
    // 产品/模块已在菜单中，不需要更新
    return;
  }

  // 根据是否是产品不同，更新不同部分的菜单
  // 这里只是示例，实际上需要根据你的项目具体情况修改

  // 保存更新后的菜单
  fs.writeFileSync(appSideMenuPath, menuContent);
}

/**
 * 从后向前查找字符串中匹配正则表达式的位置
 * @param str 要搜索的字符串
 * @param regex 正则表达式
 * @returns 匹配位置的索引，如果没找到返回-1
 */
function findLastIndex(str: string, regex: RegExp): number {
  // 将正则转为全局模式
  const globalRegex = new RegExp(regex.source, 'g');

  let lastIndex = -1;
  let match;

  // 找出所有匹配位置
  while ((match = globalRegex.exec(str)) !== null) {
    lastIndex = match.index;
  }

  return lastIndex;
}

/**
 * 创建模板文件
 * @param targetPath 目标路径
 * @param templateContent 模板内容
 * @param replaceMap 替换映射
 */
export function createFileFromTemplate(
  targetPath: string,
  templateContent: string,
  replaceMap: Record<string, string>,
): void {
  let content = templateContent;

  // 替换所有映射的内容
  Object.entries(replaceMap).forEach(([key, value]) => {
    content = content.replace(new RegExp(key, 'g'), value);
  });

  // 确保目标目录存在
  const dirPath = path.dirname(targetPath);
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
  }

  // 写入文件
  fs.writeFileSync(targetPath, content);
}

/**
 * 从文件模板创建新文件
 * @param rootPath 项目根路径
 * @param templatePath 模板文件路径
 * @param targetPath 目标文件路径
 * @param replaceMap 替换映射
 */
export function createFileFromFileTemplate(
  rootPath: string,
  templatePath: string,
  targetPath: string,
  replaceMap: Record<string, string>,
): void {
  const fullTemplatePath = path.join(rootPath, templatePath);
  const fullTargetPath = path.join(rootPath, targetPath);

  if (!fs.existsSync(fullTemplatePath)) {
    throw new Error(`模板文件 ${templatePath} 不存在`);
  }

  // 读取模板文件
  const templateContent = fs.readFileSync(fullTemplatePath, 'utf8');

  // 创建文件
  createFileFromTemplate(fullTargetPath, templateContent, replaceMap);
}
