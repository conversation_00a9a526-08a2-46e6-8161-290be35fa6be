import { join } from 'path';

const { resolve } = require('path');
const CopyWebpackPlugin = require('copy-webpack-plugin');

const FAVICON_NAME = 'favicon.ico';
const ICO_PATH = resolve(__dirname, '../resources', FAVICON_NAME);

const modifyWebpackConfig = (config) => {
  config.plugin('copy').use(CopyWebpackPlugin, [
    {
      patterns: [{ from: ICO_PATH, to: FAVICON_NAME }],
    },
  ]);
};

export const loadDocConfig = () => {
  return {
    logo: FAVICON_NAME,
    chainWebpack: modifyWebpackConfig,
  };
};
