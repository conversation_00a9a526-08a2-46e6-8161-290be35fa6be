import { FC } from 'react';
import { <PERSON>, ObjectField, observer } from '@formily/react';
import { Scrollbar } from '@metroDesign/scrollbar';
import { RequestTypeEnum } from '@mdtProMicroModules/amis-expand/constants';
import i18n from '../../../../languages';
import { CustomApi } from './CustomApi';
import { DataPkg } from './DataPkg';
import { DataQlang } from './DataQlang';
import { LowCodeQlang } from './LowCodeQlang';

export const FILED_REQUEST_TYPE = 'request_type';

export const ApiSetting: FC = observer(() => {
  return (
    <div className="request-type-setting">
      <Scrollbar style={{ height: '100%' }}>
        <Field
          name={FILED_REQUEST_TYPE}
          title={i18n.chain.requestType}
          dataSource={[
            { label: i18n.chain.qlang, value: RequestTypeEnum.QLANG },
            { label: i18n.chain.lowcodeQlang, value: RequestTypeEnum.LOW_CODE_QLANG },
            { label: i18n.chain.datapkg, value: RequestTypeEnum.DATAPKG },
            { label: i18n.chain.custom, value: RequestTypeEnum.CUSTOM },
          ]}
          decorator={['FormItem']}
          component={['Select', { placeholder: i18n.chain.requestPlaceholder }]}
        />
        {/* 数据源配置 */}
        <ObjectField name="config">
          <DataPkg parentName={FILED_REQUEST_TYPE} />
          <LowCodeQlang parentName={FILED_REQUEST_TYPE} />
          <DataQlang parentName={FILED_REQUEST_TYPE} />
          <CustomApi parentName={FILED_REQUEST_TYPE} />
        </ObjectField>
      </Scrollbar>
    </div>
  );
});
