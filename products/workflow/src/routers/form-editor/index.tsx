import { useController } from '@mdtBsComm/hooks/use-controller';
import { FormEditor, FormEditorController } from '@mdtProMicroModules/pages/form-editor';
import { AppController } from '../../app/AppController';

const FormEditorView = () => {
  const [controller] = useController(() => {
    const app = AppController.getInstance();
    const ctrl = new FormEditorController(
      {
        theme: app.getTheme(),
      },
      (data: any) => {
        console.log('formEditor values=', data);
      },
    );
    return [ctrl, null];
  });

  return <FormEditor controller={controller} />;
};

export default FormEditorView;
