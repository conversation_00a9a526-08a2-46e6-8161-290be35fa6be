import type { Observable } from 'rxjs';
import type { IBusinessResult, ILabelValue } from '@mdtBsComm/interfaces';
import type { IRowValue } from '@mdtBsServices/interfaces';
import type { OwnershipEnum } from '@mdtProComm/constants';
import type { IModelRefreshTimer } from '../../ascatter-expand/datapkg-refresh-timer/constants';
import type { IPkgForm } from './CreateDatapkgFromTableController';

export interface IGeneratePkgOptions extends IModelRefreshTimer {
  dbId: string;
  schema: string;
  tableName: string;
  values: IPkgForm;
  keyColumns: Record<string, string>;
  ownership?: OwnershipEnum;
}

export interface ICreateDatapkgFromTableModel {
  queryDatasourceOptions: () => Observable<ILabelValue[]>;
  querySchemaOptions: (datasourceId: string) => Observable<ILabelValue[]>;
  queryTableOptions: (datasrouceId: string, schemaId: string) => Observable<ILabelValue[]>;
  queryGeometryOptions: () => Observable<ILabelValue[]>;
  queryTableInfo: (
    dbId: string,
    schema: string,
    table: string,
  ) => Observable<{
    columnType: Record<string, string>;
    rows: Record<string, IRowValue>[];
    keyColumn: object;
  }>;
  generatePkg: (data: IGeneratePkgOptions) => Observable<IBusinessResult<void>>;
  checkNameExistFunc: (dbId: string, name: string) => Observable<boolean>;
}
