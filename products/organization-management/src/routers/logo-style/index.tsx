import { useController } from '@mdtBsComm/hooks/use-controller';
import { AppController } from '../../app/AppController';
import { LogoStyle, LogoStyleController, LogoStyleModel } from '../../pages/logo-style';

const LogoStyleView = () => {
  const [controller] = useController(() => {
    const ctrl = new LogoStyleController(AppController.getInstance(), LogoStyleModel);
    return [ctrl, null];
  });

  return <LogoStyle controller={controller} />;
};

export default LogoStyleView;
