import { UserSelectorBffService } from '@mdtProComm/bff-services/UserSelectorBffService';
import { preloadOrganizationData, preloadUsersData } from '@mdtProComm/utils/preloadUtil';
import { ENABLE_PRELOAD_LARGER_DATA } from '@mdtProMicroModules/datlas/datlasConfig';
import { AppController } from '../app/AppController';

export const preloadData = (app: AppController) => {
  if (!ENABLE_PRELOAD_LARGER_DATA) return;
  requestIdleCallback(() => {
    UserSelectorBffService.queryUsersRolesOrgsGroups(app.getAppId(), undefined, true, true);
    preloadOrganizationData(app.getAppId(), true);
    preloadUsersData(app.getAppId(), false, undefined, true);
  });
};
