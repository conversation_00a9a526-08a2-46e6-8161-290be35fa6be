import { Observable } from 'rxjs';
import {
  DataListCompTableCurdController,
  IDynamicOptions,
  IVirtualizedTableCurdProps,
} from '@mdtBsComponents/data-list-comp-table-curd';

interface IControllerOptions {
  loadDataListFunc?: (params?: any) => Observable<[number, any[]]>;
  tableOptions: IDynamicOptions<IVirtualizedTableCurdProps<any>>;
}

export class TableWithCurdFormilyController extends DataListCompTableCurdController {
  public constructor({ loadDataListFunc, tableOptions }: IControllerOptions) {
    super({
      dataListControllerOptions: {
        loadDataListFunc,
      },
      tableOptions,
      curdOptions: () => ({
        columnWidth: 80,
      }),
    });

    this.loadDataList();
  }

  public destroy() {}
}
