import _ from 'lodash';
import {
  createDesigner,
  Engine,
  FromNodeEvent,
  ITreeNode,
  KeyCode,
  RemoveNodeEvent,
  Shortcut,
  TreeNode,
} from '@designable/core';
import { IFormilySchema, transformToTreeNode } from '@designable/formily-transformer';
import { transformToSchema } from '@designable/formily-transformer';
import type { ISchema } from '@formily/json-schema';
import { autorun } from '@formily/reactive';
import { BehaviorSubject } from 'rxjs';
import { debounceTime, skip } from 'rxjs/operators';
import type { ILabelValue } from '@mdtBsComm/interfaces';
import { DebounceTimeEnum, OTHER_OPTION_KEY_PREFIX } from '@mdtProComm/constants';
import { isFormHiddenKey } from '@mdtProComm/utils/questionUtil';
import { getAvaliableNodeId, MetroSettingProp } from '@mdtProFormEditor/metro-form-design/shared';
import { getOtherOptionI<PERSON><PERSON><PERSON> } from '../../components/form-view/util';
import i18n from '../../languages';
import type { IOnPageCancelCb, IOnPageSuccessCb } from '../drawer-within-page';
import { DataValue } from './service/data-value';
import { defaultConfig, formDefaultConfig } from './default-config';
import { ICustomerDefaultSettingFunc } from './interface';
import { DataSource, saveSchema } from './service';
import { generateNewQuestionSchema } from './transformer';
import {
  ArrayBaseCompEnum,
  arrayBaseComps,
  ensureSameStyle,
  FORM_NODE_ID,
  getNewFieldSchema,
  getNewFormSchema,
  getNodeAndChildrenIds,
  hasDataSource,
  isArrayBaseComp,
  isCompAddOtherOption,
  mergeNodePropsWithSetting,
  modifySavedFormilySchemaAndSentting,
  NODE_COMP,
  NODE_PROPS_ID,
  sureNodeNameAndOtherNameSame,
  transformAllSettingValuesToSave,
  transformFormilySchemaToSave,
  transformSavedAllSettingValues,
  transformSavedFormilySchema,
} from './util';

export interface IFormEditorControllerProps {
  theme?: string;
  editorSchema?: IFormEditorSchema;
  optionsMap?: Record<string, ILabelValue[]>;
  validate?: (values: IFormEditorSchema) => string | undefined;
  defaultSettingFunc?: ICustomerDefaultSettingFunc;
  onSuccessCb?: IOnPageSuccessCb;
  onCancelCb?: IOnPageCancelCb;
}

export interface IFormEditorSchema {
  formilySchema: IFormilySchema;
  allSettingValues: Record<string, ISchema>;
  dataSource?: DataSource;
}

export class FormEditorController {
  public currentNodeData$: BehaviorSubject<any> = new BehaviorSubject(null);
  public onSuccessCb?: IOnPageSuccessCb;
  public onCancelCb?: IOnPageCancelCb;
  private _engine: Engine;
  private _theme: string;
  private _initSchemaUpdate$ = new BehaviorSubject('N');
  private _optionsMap?: Record<string, ILabelValue[]>;
  private allSettingValues: Record<string, ISchema> = {};
  // 移除手动维护的 formilySchema，改为从 engine 实时获取
  // private formilySchema: IFormilySchema = {};
  // 字段节点实例
  private nodeFiledMap: Record<string, any> = {};
  private nodeSetting$ = new BehaviorSubject<any>(null);
  private dataSource: DataSource;
  private dataValue: DataValue;
  private customerValidate: (values: IFormEditorSchema) => string | undefined;
  private customerDefaultSettingFunc?: ICustomerDefaultSettingFunc;

  public constructor(props?: IFormEditorControllerProps, onSuccessCb?: IOnPageSuccessCb, onCancelCb?: IOnPageCancelCb) {
    const {
      theme = 'light',
      editorSchema,
      optionsMap,
      validate = this.defaultCustomerValidate,
      defaultSettingFunc,
      onSuccessCb: fbOnSuccessCb,
      onCancelCb: fbOnCancelCb,
    } = props || {};
    this.onSuccessCb = onSuccessCb || fbOnSuccessCb;
    this.onCancelCb = onCancelCb || fbOnCancelCb;
    this.customerValidate = validate;
    this.customerDefaultSettingFunc = defaultSettingFunc;
    defaultConfig['Form'] = _.cloneDeep(formDefaultConfig);
    this._theme = theme;
    this._engine = createDesigner({
      shortcuts: [
        new Shortcut({
          codes: [
            [KeyCode.Meta, KeyCode.S],
            [KeyCode.Control, KeyCode.S],
          ],
          handler(ctx) {
            saveSchema(ctx.engine);
          },
        }),
      ],
      rootComponentName: 'Form',
    });
    this.dataSource = new DataSource(this._engine);
    this.dataValue = new DataValue(this._engine);
    // 补充默认值
    this._engine.subscribeTo(FromNodeEvent, (event) => {
      this.saveNodeFiledMap(event.data.target);
      this.fillDefaultValue(event.data.source, event.data.target);
    });
    // 删除节点时，需要将删除的节点的设置值也删除
    this._engine.subscribeTo(RemoveNodeEvent, (event) => {
      this.saveNodeFiledMap(event.data.target, true);
      this.removeEditorNode(event.data.target);
    });
    this.listenAddNode();
    this.listenNodeSettingChange();
    if (editorSchema) {
      requestAnimationFrame(() => {
        this.initSchema(editorSchema, optionsMap);
      });
    }
  }

  public destroy() {
    this.currentNodeData$.complete();
    this.currentNodeData$.next(null);
    this.onSuccessCb = undefined;
    this.onCancelCb = undefined;
    this._engine = null!;
    this._initSchemaUpdate$.complete();
    this._optionsMap = undefined;
    this.allSettingValues = {};
    this.formilySchema = {};
    this.nodeFiledMap = {};
    this.nodeSetting$.complete();
    this.nodeSetting$.next(null);
    this.dataSource.destroy();
    this.dataValue.destroy();
    this.customerValidate = this.defaultCustomerValidate;
  }

  public getDataSource() {
    return this.dataSource;
  }

  public getDataValue() {
    return this.dataValue;
  }

  public get engine() {
    return this._engine;
  }

  public get theme() {
    return this._theme;
  }

  public get initSchemaUpdate$() {
    return this._initSchemaUpdate$;
  }

  public get optionsMap() {
    return this._optionsMap;
  }

  // 初始化editor schema
  public initSchema(editorSchema?: IFormEditorSchema, optionsMap?: Record<string, ILabelValue[]>) {
    this._optionsMap = optionsMap;
    let val = editorSchema || { formilySchema: {}, allSettingValues: {} };
    val = modifySavedFormilySchemaAndSentting(val);
    this.formilySchema = transformSavedFormilySchema(val.formilySchema);
    this.allSettingValues = transformSavedAllSettingValues(val.allSettingValues);
    this._engine.setCurrentTree(transformToTreeNode(this.formilySchema));
    this.initSchemaUpdate$.next('Y');
  }

  // 预览时无需触发外部校验
  public getEditorSchemaToSave(disableCustomerValidate = false): IFormEditorSchema | string {
    const errMsg = this.validate();
    if (errMsg) return errMsg;

    const data = {
      formilySchema: transformFormilySchemaToSave(this.formilySchema, this._engine),
      allSettingValues: transformAllSettingValuesToSave(this.allSettingValues, this.formilySchema),
    };

    ensureSameStyle(data);

    const customerErrorMsg = !disableCustomerValidate ? this.customerValidate(data) : '';
    if (customerErrorMsg) return customerErrorMsg;

    return data;
  }

  public getAllSettingValues() {
    return this.allSettingValues;
  }

  // 属性修改后刷新node tree
  public refreshNodeTree(node: TreeNode, setting: any) {
    this.nodeSetting$.next({ node, setting });
  }

  // 修改题型后替换node
  public replaceNodeType(node: TreeNode, newComponentName: string) {
    const defaultSettingValue = defaultConfig[newComponentName] || {};
    // const designer = this._engine;
    const properties: Record<string, any> = this.formilySchema.schema!.properties! as any;
    const nodeId = node.id;
    const property = properties[nodeId];
    properties[nodeId] = {
      ...generateNewQuestionSchema(newComponentName, defaultSettingValue),
      ..._.pick(property, ['x-index', NODE_PROPS_ID, 'title', 'name', 'dataSource', 'x-decorator-props']),
    };
    sureNodeNameAndOtherNameSame(properties, nodeId);
    const oldSettingValueToStore = _.pick(this.allSettingValues[nodeId], [MetroSettingProp.datasource]);
    const mergedSettingValue = {
      ...defaultSettingValue,
      title: property.title,
      name: property.name,
      // 装饰器的配置直接赋值
      'x-decorator-props': property['x-decorator-props'],
      ...oldSettingValueToStore,
    };
    this.allSettingValues[node.id] = mergedSettingValue;
    sureNodeNameAndOtherNameSame(this.allSettingValues, nodeId);
    // designer.setCurrentTree(transformToTreeNode(this.formilySchema));
    node.props = properties[nodeId];
    if (hasDataSource(newComponentName, _.get(mergedSettingValue, MetroSettingProp.datasource))) {
      this.dataSource.addNodeDataSource(node.id, mergedSettingValue);
    }
  }

  public initNodeForm(nodeId: string, values: any) {
    const ds = this.dataSource.getData(nodeId);
    const hasDataSource = ds && values?.[MetroSettingProp.datasource];
    const nodeData = hasDataSource ? this.dataSource.addNodeDataSource(nodeId, values, true) : null;
    this.currentNodeData$.next(nodeData);
  }

  public getDefaultValueFiledMap() {
    const defaultValueList: any[] = [];
    const editorSchema = this.getEditorSchemaToSave();
    if (typeof editorSchema !== 'string') {
      const filterKeys = ['form_submitter__BPMN_ROOT_NODE_KEY'];
      _.forEach(_.values(editorSchema.formilySchema.schema?.properties), (item: any) => {
        if (_.includes(filterKeys, item.name)) return;
        defaultValueList.push({ label: item.name, value: item.name, select: true });
      });
    }
    return defaultValueList;
  }

  // 属性修改后或添加新的node后更新editorSchema
  private updateEditorSchema(node: ITreeNode, values: any) {
    let formilySchema = this.formilySchema;
    const isForm = node.componentName === 'Form';
    this.formilySchema = isForm
      ? getNewFormSchema(formilySchema, node, values)
      : getNewFieldSchema(formilySchema, node, values);
    const nodeID = node.id!;
    this.allSettingValues[nodeID] = values;
    sureNodeNameAndOtherNameSame(this.allSettingValues, nodeID);
  }

  private removeEditorNode(nodes: TreeNode | TreeNode[]) {
    const { formilySchema, allSettingValues } = this;
    const properties: any = formilySchema.schema?.properties || {};
    let nodeIdsToDelete: string[] = [];
    if (_.isArray(nodes)) {
      _.forEach(nodes, (n) => {
        nodeIdsToDelete.push(...getNodeAndChildrenIds(n));
      });
    } else {
      nodeIdsToDelete.push(...getNodeAndChildrenIds(nodes));
    }
    _.forEach(nodeIdsToDelete, (id) => {
      delete allSettingValues[id];
      delete properties[id];
    });
  }

  // 为node添加默认属性
  private fillDefaultValue(node: ITreeNode, targetNode: TreeNode) {
    const isForm = node.componentName === 'Form';
    if (isForm) {
      node.id = FORM_NODE_ID;

      const formDefaultValue = defaultConfig['Form'];
      formDefaultValue['x-metro-default-value'].defaultConfig.map.value = this.getDefaultValueFiledMap();

      const mergedFormSettingValue = _.merge(formDefaultValue, this.allSettingValues[FORM_NODE_ID]);
      this.updateEditorSchema(node, mergedFormSettingValue);
      node.props = this.formilySchema.form;
      return;
    }

    const noNeedTitle = _.get(targetNode, 'parent.props.x-component') === 'ArrayTable.Column';
    const compOfTableComlumnTitle = noNeedTitle ? { title: '' } : {};

    const nodePropsId = node.props?.[NODE_PROPS_ID];
    const isClone = nodePropsId && node.id !== nodePropsId;
    const compName = node.props?.[NODE_COMP];
    const defaultValue = defaultConfig[compName] || {};
    const settingValue = isClone
      ? { ...this.allSettingValues[nodePropsId], name: undefined }
      : this.getSettingValue(node, targetNode);
    const mergedSettingValue = {
      ...defaultValue,
      ...settingValue,
      ...this.getChildrenNodeLayout(targetNode),
      ...compOfTableComlumnTitle,
    };
    // 如果复制的是其他选项，需要特殊处理
    if (isClone && _.startsWith(nodePropsId, OTHER_OPTION_KEY_PREFIX)) {
      const parent = targetNode.parent;
      const nId = getOtherOptionInnerKey(parent.id);
      mergedSettingValue.name = getOtherOptionInnerKey(parent.props!.name);
      targetNode.id = nId;
      node.id = nId;
    } else {
      const isNameUseInputComp = !this.optionsMap?.name;
      isNameUseInputComp && (mergedSettingValue.name = mergedSettingValue.name || getAvaliableNodeId(node.id!));
    }
    this.updateEditorSchema(node, mergedSettingValue);
    node.props = mergeNodePropsWithSetting(node, mergedSettingValue);
    // 如果有数据源设置，则创建数据源对象
    if (hasDataSource(compName, _.get(mergedSettingValue, MetroSettingProp.datasource))) {
      this.dataSource.addNodeDataSource(node.id!, mergedSettingValue);
    }
  }

  private getSettingValue = (node: ITreeNode, targetNode: TreeNode) => {
    const parentNode = targetNode.parent;
    const settingValue = this.allSettingValues[node.id!];
    if (!settingValue) {
      const pprops = parentNode.props as Record<string, any>;
      if (isCompAddOtherOption(pprops)) {
        // other选项需要保证和parent一致
        return { title: '', name: pprops.name ? getOtherOptionInnerKey(pprops.name) : node.id };
      }
      return this.customerDefaultSettingFunc?.(node, targetNode);
    }
    return settingValue;
  };

  private getChildrenNodeLayout(node: TreeNode) {
    return node.depth > 1
      ? {
          'x-decorator-props': {
            layout: _.get(this.allSettingValues, 'form-node-id.layout'),
            colon: _.get(this.allSettingValues, 'form-node-id.colon'),
          },
        }
      : {};
  }

  // 存储字段节点实例, 方便后续操作
  private saveNodeFiledMap(node: TreeNode | TreeNode[], remove?: boolean) {
    const nodes = Array.isArray(node) ? node : [node];
    if (remove) {
      for (const d of nodes) {
        delete this.nodeFiledMap[d.id];
      }
    } else {
      for (const d of nodes) {
        this.nodeFiledMap[d.id] = node;
      }
    }
  }

  private listenAddNode() {
    autorun(() => {
      const nodes = this._engine.getCurrentTree()?.children || [];
      const propertyIdIndexMap = _.reduce(
        nodes,
        (map, node, index) => {
          map[node.id] = index;
          return map;
        },
        {} as any,
      );
      if (nodes.length) {
        const properties = _.values(this.formilySchema.schema?.properties);
        _.forEach(properties, (it: any) => {
          it['x-index'] = propertyIdIndexMap[it[NODE_PROPS_ID]];
        });
      }
    });
  }

  private listenNodeSettingChange() {
    this.nodeSetting$.pipe(skip(1), debounceTime(DebounceTimeEnum.NORMAL)).subscribe((val) => {
      if (!val) return;
      const { node, setting } = val;
      this.updateEditorSchema(node, setting);
      const isForm = node.componentName === 'Form';
      const newProps = isForm ? this.formilySchema.form : (this.formilySchema.schema!.properties as any)[node.id];
      node.props = newProps;
      if (isForm) {
        node.eachChildren((subNode: TreeNode) => {
          const decoProps = _.get(subNode, 'props.x-decorator-props');
          if (!decoProps) return;
          if (decoProps.layout === newProps.layout) return false;
          decoProps.layout = newProps.layout;
        });
      }
    });
  }

  private validate() {
    const baseErrMsg = this.baseValidate();
    if (baseErrMsg) {
      console.log('base validate error', baseErrMsg, this.formilySchema, this.allSettingValues);
      return baseErrMsg;
    }

    const arrayBaseErrMsg = this.validateArrayBaseComps();
    if (arrayBaseErrMsg) {
      console.log('array base validate error', arrayBaseErrMsg, this.formilySchema, this.allSettingValues);
      return arrayBaseErrMsg;
    }
  }

  // 字段校验
  private baseValidate() {
    const prefixOfNoNeedNamesComs = _.map(arrayBaseComps, (comp) => `${comp}.`);
    const obj = _.values(this.formilySchema.schema?.properties);
    const properties = _.filter(
      obj,
      (it: any) => !_.some(prefixOfNoNeedNamesComs, (prefix) => _.startsWith(it[NODE_COMP], prefix)),
    );
    const validFields: string[] = [];
    const repeatedNames: string[] = [];
    const noNameFields: string[] = [];
    const existNameMap: Record<string, true> = {};
    _.forEach(properties, (it: any) => {
      const compName = it[NODE_COMP];
      const name = it.name;
      const title = it.title;
      const nodeId = it[NODE_PROPS_ID];
      if (!compName || isFormHiddenKey(nodeId)) return;
      if (name) {
        validFields.push(name);
        if (existNameMap[name]) {
          repeatedNames.push(title || 'name');
        } else {
          existNameMap[name] = true;
        }
      } else {
        noNameFields.push(title);
      }
    });
    if (!_.isEmpty(noNameFields))
      return `${i18n.chain.proMicroModules.fillDesign.columnFieldEmptyTip} ${_.join(noNameFields, ', ')}`;
    if (!_.isEmpty(repeatedNames))
      return `${i18n.chain.proMicroModules.fillDesign.columnFieldRepeatTip} ${_.join(repeatedNames, ', ')}`;
    if (_.isEmpty(validFields)) return i18n.chain.proMicroModules.fillDesign.columnEmptyTip;
  }

  private validateArrayBaseComps() {
    let result = '';
    const schema = transformToSchema(this.engine.getCurrentTree());
    const list = _.values(schema.schema?.properties);
    const arrayBaseProperties = _.filter(list, isArrayBaseComp);
    _.forEach(arrayBaseProperties, (abItem: any) => {
      const comp = abItem[NODE_COMP];
      const children = _.get(abItem, 'items.properties');
      let hasChildren = true;
      if (comp === ArrayBaseCompEnum.ARRAY_CARDS) {
        hasChildren = _.some(children, (child) => !_.startsWith(child[NODE_COMP], ArrayBaseCompEnum.ARRAY_CARDS));
      } else if (comp === ArrayBaseCompEnum.ARRAY_TABLE) {
        hasChildren = _.some(children, (col) => !!_.size(col.properties));
      }
      if (!hasChildren) {
        result = i18n.chain.proMicroModules.fillDesign.dragFieldToForm(abItem.title || abItem[NODE_PROPS_ID]);
        return false;
      }
    });
    return result;
  }

  private defaultCustomerValidate() {
    return undefined;
  }
}
