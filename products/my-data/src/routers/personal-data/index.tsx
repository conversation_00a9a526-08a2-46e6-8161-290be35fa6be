import { useController } from '@mdtBsComm/hooks/use-controller';
import { getModel } from '../../_util/modelUtil';
import { useAppContext } from '../../app/appContext';
import { PersonalDatapkg, PersonalDatapkgController, PersonalDatapkgModel } from '../../pages/personal-datapkg';
import { PersonalDatapkgModelBff } from '../../pages/personal-datapkg/PersonalDatapkgModelBff';

// 个人数据==========================================================================================
const PersonalData = () => {
  const { appController } = useAppContext();

  const [controller] = useController(() => {
    // TODO 后续可以考虑缓存到App Controller, 加速打开速度
    const ctrl = new PersonalDatapkgController(appController, getModel(PersonalDatapkgModel, PersonalDatapkgModelBff));
    return [ctrl, null];
  });

  return <PersonalDatapkg controller={controller} />;
};

export default PersonalData;
