import { useController } from '@mdtBsComm/hooks/use-controller';
import {
  OneTableMissionCenterReview,
  OneTableMissionCenterReviewController,
  OneTableMissionCenterReviewModelBff,
} from '@mdtProMicroModules/pages/one-table-mission-center-review';

const OneTableMissionCenterReviewView = () => {
  const [controller] = useController(() => {
    return [
      new OneTableMissionCenterReviewController({
        Model: OneTableMissionCenterReviewModelBff,
      }),
      null,
    ];
  }, []);

  return <OneTableMissionCenterReview controller={controller} />;
};

export default OneTableMissionCenterReviewView;
