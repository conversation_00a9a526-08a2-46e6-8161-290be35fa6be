import {
  IDatapkg,
  <PERSON>E<PERSON>ty,
  IFloworkTask,
  IFloworkTaskCompletePost,
  IFloworkTaskGet,
  IFloworkTasksQuery,
  IOneTableRootDispatchWorkflow,
  IPaginationQuery,
} from '@mdtApis/interfaces';
import { ICancelTokenOption } from '../interfaces';
import { IWorkflowModel } from '../workflow';

export interface IFloworkTaskModel extends IFloworkTask {
  workflow?: IWorkflowModel;
  executorObj?: IEntity;
}

export interface IOneTableRootDispatchWorkflowModel extends IOneTableRootDispatchWorkflow {
  datapkg: IDatapkg;
  workflow: IWorkflowModel;
}

export interface IQueryOneTableRootDispatchWorkflowOptions extends ICancelTokenOption {
  params?: IPaginationQuery;
  respData: string;
}

export interface IQueryFloworkTasksOptions extends ICancelTokenOption {
  data: IFloworkTasksQuery;
  params?: IPaginationQuery;
  respData: string;
}

export interface IQueryFloworkTaskDetailOptions extends ICancelTokenOption {
  id: string;
  params?: IFloworkTaskGet;
  respData: string;
}

export interface ICompleteFloworkTaskOptions extends ICancelTokenOption {
  id: string;
  data: IFloworkTaskCompletePost;
  respData: string;
}

export interface IQueryOneTableFlowTasksOptions extends ICancelTokenOption {
  resourceId: string;
  params: {
    resource_type: 'workflow' | 'task';
  };
  respData: string;
}

export interface IOneTableFlowTaskModel {
  stage: string;
  status: string;
  workflow_id: string;
  task_info: IFloworkTask;
  executor: number;
  name: string;
  bpmn_name: string;
  update_time: number;
  form_spec?: Record<string, any>;

  workflow?: IWorkflowModel;
  executorObj?: IEntity;
}
