/**
 * 统计登录的pathname，目前无实际作用
 */
export enum LoginPathnameEnum {
  ACCOUNT = '/auth/login/byname',
  PHONE = '/auth/login/byphone',
  WECHAT = '/auth/login/bywechat',
  DINGTALK = '/auth/login/bydingtalk',
  ZZDING = '/auth/login/bydingtalkauthcode',
  // 特殊的登录方式
  DOUBLE_CHECK = '/auth/login/double_check',
}

export enum RoutePathEnum {
  ROOT = '/',
  VERIFY = '/verify',
  WECHAT = '/frontend/wechat',
  DINGTALK = '/frontend/dingtalk',
  CHOOSE_APP = '/choose-app/:token',
  CHOOSE_IDENTITY = '/choose-identity/:token',
}
