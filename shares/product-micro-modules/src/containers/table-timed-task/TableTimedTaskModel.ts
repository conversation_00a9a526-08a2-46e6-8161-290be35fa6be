import { Observable } from 'rxjs';
import { ICurdOptions } from '@mdtBsComponents/data-list-comp-table-curd';
import { IBusinessResult } from '@mdtProComm/interfaces';
import { IFormData } from '../dialog-modify-form-timed-task';

export interface ITableData extends IFormData {
  id: string;
  triggerDisplay: string;
  expired: string;
  nextRunTime: string;
  active: boolean;
  updateTime: number; // 修改时需要传到后端
}
export interface ITableTimedTaskModel {
  // 查询权限
  queryPermissions: () => ICurdOptions<ITableData>;

  // 查询定时任务列表
  queryTimedTaskList: () => Observable<ITableData[]>;

  // 创建定时任务
  createTimedTask: (createData: IFormData) => Observable<IBusinessResult<ITableData>>;

  // 修改定时任务
  updateTimedTask: (updateData: IFormData, originalUpdateData: ITableData) => Observable<IBusinessResult<ITableData>>;

  // 删除定时任务
  deleteTimedTask: (deleteData: ITableData) => Observable<IBusinessResult<ITableData>>;

  // 激活定时任务
  enableTimedTask: (originalUpdateData: ITableData) => Observable<IBusinessResult<ITableData>>;
}
