import { IOrderQuery, IPaginationQuery, ITaggroupTags } from './comm';
import { IDatapkgGeometryType, IDatapkgPermission } from './datapkgs';

export type IAlbumType = 'datapkg';

export interface IAlbumsQuery extends IPaginationQuery {
  datapkg_id?: string;
  album_type?: IAlbumType;
  q?: string;
  apps?: string | number;
}

export interface IAlbumPost {
  name: string;
  description?: string;
  rich_description?: object;
  public?: boolean;
  album_type?: IAlbumType;
}

export type IAlbumPatch = Partial<Omit<IAlbumPost, 'public'>>;

export interface IAlbum extends IAlbumPost {
  id: string;
  app_id: number;
  create_time: number;
  update_time: number;
}

export interface IAlbumDatapkgIdsQuery extends IOrderQuery, IPaginationQuery {
  name?: string;
  privilege?: IDatapkgPermission[] | IDatapkgPermission | boolean;
  tags?: ITaggroupTags[];
  fuzzy_tag?: string;
  geometry_type?: IDatapkgGeometryType[] | IDatapkgGeometryType;
  column?: string;
  create_time_max?: number;
  create_time_min?: number;
  update_time_max?: number;
  update_time_min?: number;
}

export interface IAlbumsDatapkgIdsQuery extends IAlbumDatapkgIdsQuery {
  album_ids?: string[];
}

export interface IAlbumStats {
  datapkg_count: number;
  album_id: string;
}
