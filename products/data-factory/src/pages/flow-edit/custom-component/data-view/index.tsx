import { FC, memo, useState } from 'react';
import { IPosition } from '@antv/xflow';
import { useObservableState } from '@mdtBsComm/hooks/use-observable-state';
import Add from '@mdtDesign/icons/add';
import Close from '@mdtDesign/icons/close';
import Remove from '@mdtDesign/icons/remove';
import { VirtualizedTable } from '@mdtDesign/table';
import i18n from '../../../../languages';
import { useFlowEditContext } from '../../flowEditContext';
import './index.less';

interface IProps {
  position: IPosition;
}
const DataView: FC<IProps> = memo(({ position }) => {
  const { flowEditController: controller } = useFlowEditContext();
  const flowEditConfigController = controller.getFlowEditConfigController();
  const [expand, setExpand] = useState<boolean>(true);

  const tableShow = useObservableState(() => controller.getTableShow());
  const selectNode = useObservableState(() => flowEditConfigController.getSelectNode()).node;
  const flowResultList: any = useObservableState(() => controller.getTaskRespList$());
  const flowNodeId: string = useObservableState(() => controller.getFlowNodeId());

  const dataViewWidth = selectNode ? 'calc(100% - 380px)' : '100%';
  const drawerGlobalOpenModal = () => {
    controller.drawerGlobalOpenModal();
  };
  return (
    <div>
      {flowResultList[flowNodeId] && (
        <div className="dataView" style={{ ...position, width: dataViewWidth, display: tableShow ? 'block' : 'none' }}>
          <div className="viewHead">
            <span>{i18n.chain.dataFactory.flow.domDataPreview}</span>
            <div className="viewHeadIcon">
              <div className="setLimitBox">
                <div>{i18n.chain.dataFactory.flow.domDataPreviewDesc}</div>
                <div>
                  <a onClick={drawerGlobalOpenModal}>{i18n.chain.dataFactory.flow.clickHere}</a>
                  {i18n.chain.dataFactory.flow.runSamplesSetting}
                </div>
              </div>
              <div className="icon" onClick={() => setExpand(!expand)}>
                {expand ? <Remove size={30} /> : <Add size={30} />}
              </div>
              <div className="icon" style={{ marginLeft: '2px' }} onClick={() => controller.cheageTableIsNone(false)}>
                <Close size={30} />
              </div>
            </div>
          </div>

          {expand && flowResultList[flowNodeId] && (
            <div className="viewBody" style={{ alignItems: 'center' }}>
              <VirtualizedTable
                defaultColumnWidth={200}
                dataSource={flowResultList[flowNodeId]?.dataSource}
                columns={flowResultList[flowNodeId]?.columns}
                type="assist-bg"
              />
            </div>
          )}
        </div>
      )}
    </div>
  );
});

export default DataView;
