import { FC } from 'react';
import { useObservableState } from '@mdtBsComm/hooks/use-observable-state';
import { IconButton } from '@mdtDesign/button';
import Scrollbar from '@mdtDesign/scrollbar';
import { ModalComp } from '../modal-comp';
import { ModalToggleFullScreenController } from './ModalToggleFullScreenController';
import './index.less';

interface IProps {
  controller: ModalToggleFullScreenController;
  className?: string;
  id?: string;
}

// 子组件--标题======================================================================================
const Header: FC<IProps> = ({ controller }) => {
  const { visible } = useObservableState(() => controller.getVisible$());
  const options = controller.getUiOption();
  const icon = visible ? options.closeIcon : options.openIcon;

  const onClick = () => {
    controller.toggleModal();
  };

  return (
    <div className="component_modal-fullscreen_header">
      <div className="modal-fullscreen_title">{options.headerTitle}</div>
      <IconButton type="only-icon" icon={icon} onClick={onClick} />
    </div>
  );
};

// 是否全屏展示======================================================================================
const ModalToggleFullScreen: FC<IProps> = (props) => {
  const { controller, id, className, children } = props;

  return (
    <>
      <div id={id} className={className}>
        <Header controller={controller} />
        {children}
      </div>
      <ModalComp controller={controller}>
        <Header controller={controller} />
        <div className="component_modal-fullscreen_inner">
          <Scrollbar>{children}</Scrollbar>
        </div>
      </ModalComp>
    </>
  );
};

export { ModalToggleFullScreen };
