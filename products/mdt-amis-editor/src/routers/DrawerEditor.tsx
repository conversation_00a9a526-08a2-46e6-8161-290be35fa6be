import React from 'react';
import { useController } from '@mdtBsComm/hooks/use-controller';
import { AppController } from '../app/AppController';
import { DrawerEditor, DrawerEditorController } from '../pages/drawer-editor';

const DrawerEditorView = () => {
  const [controller] = useController(() => {
    const ctrl = new DrawerEditorController(AppController.getInstance());
    return [ctrl, null];
  });

  return <DrawerEditor controller={controller} />;
};

export default DrawerEditorView;
