import _ from 'lodash';
import {
  ApolloClient,
  ApolloQueryResult,
  FetchResult,
  HttpLink,
  InMemoryCache,
  MutationOptions,
  OperationVariables,
  QueryOptions,
} from '@apollo/client';
import axios, { AxiosInstance, AxiosPromise, AxiosRequestConfig, AxiosResponse } from 'axios';
import { IRequestOwner } from '@mdtApis/interfaces';
import notification from '@mdtDesign/notification';
import toastApi from '@mdtDesign/toast';
import { IResponseError } from './interfaces';
import { RcHandlersController } from './RcHandlersController';

// 产品无权限的异常类型
const MDT_PRODUCT_FORBIDDEN_EXCEPTION = 'MdtProductForbiddenException';
// 语法错误提示
const SYNTAX_ERROR_MSG = '抱歉，网络异常';
// 接口异常提示
const RESPONS_EERROR_MSG = '请求异常';
const errorResp: IResponseError = {
  success: false,
  msg: SYNTAX_ERROR_MSG,
  status: 400,
  requestId: '',
  backendRequestId: '',
};
const defaultOptions = {
  watchQuery: {
    fetchPolicy: 'no-cache',
    errorPolicy: 'ignore',
  },
  query: {
    fetchPolicy: 'no-cache',
    errorPolicy: 'all',
  },
};

export interface IInitOptions {
  token: string;
  url: string;
  deal401?: Function;
}

export type IBffRequestConfig = AxiosRequestConfig & {
  enableBffProxy?: boolean;
  disableBffProxy?: boolean;
  owner?: IRequestOwner;
};
export interface IBffRequestInstance extends AxiosInstance {
  <T>(config: IBffRequestConfig): AxiosPromise<T>;
  <T>(url: string, config?: IBffRequestConfig): AxiosPromise<T>;
  request: <T = any, R = AxiosResponse<T>>(config: IBffRequestConfig) => Promise<R>;
}

export class ApolloService {
  private static client?: ApolloClient<any>;
  private static deal401?: Function;
  private static axiosIns?: IBffRequestInstance;
  private static rcHandlersController = new RcHandlersController();

  public static getAxiosIns() {
    if (!this.axiosIns) {
      this.initAxiosIns();
    }
    return this.axiosIns!;
  }

  public static initClient({ token, url, deal401 }: IInitOptions) {
    if (this.client) return;
    this.deal401 = deal401;
    this.initAxiosIns();
    this.client = new ApolloClient({
      link: new HttpLink({
        uri: url,
        fetch: this.customerFetch,
        headers: {
          authorization: token,
        },
        includeUnusedVariables: false,
      }),
      // cache is an instance of InMemoryCache, which Apollo Client uses to cache query results after fetching them.
      cache: new InMemoryCache({
        addTypename: window.location.hostname === 'localhost',
      }),

      // @ts-ignore
      defaultOptions: defaultOptions,
    });
  }

  public static destroy() {
    if (this.client) {
      this.client.clearStore();
      this.client.stop();
      this.client = undefined;
    }
    this.axiosIns = undefined;
    this.deal401 = undefined;
  }

  public static query<T = any, TVariables = OperationVariables>(options: QueryOptions<TVariables, T>): Promise<any> {
    // 目前apollo client只允许一个operation https://github.com/apollographql/apollo-client/blob/main/src/utilities/graphql/getFromAST.ts
    const queryNames = this.getQueryName(_.get(options, 'query.definitions[0].selectionSet.selections', []));
    return this.handleApolloResult(this.client!.query(options), queryNames);
  }

  public static mutate<T = any, TVariables = OperationVariables>(
    options: MutationOptions<T, TVariables>,
  ): Promise<any> {
    const queryNames = this.getQueryName(_.get(options, 'mutation.definitions[0].selectionSet.selections', []));
    return this.handleApolloResult(this.client!.mutate(options), queryNames);
  }

  private static getQueryName(sections: any[]) {
    // 并行请求时，如果有相同的请求需要为每个请求设置了别名，在获取请求名称时先从别名取
    return _.map(sections, (s) => _.get(s, 'alias.value') || _.get(s, 'name.value'));
  }

  private static handleApolloResult(
    request: Promise<ApolloQueryResult<any>> | Promise<FetchResult<any>>,
    names: string[],
  ) {
    const isOneResp = _.size(names) === 1;

    return request
      .then((resp) => {
        this.handleRespError(resp);
        const respData = resp.data || {};
        if (isOneResp) {
          return _.first(_.values(respData));
        }
        return _.map(names, (item) => _.get(respData, item));
      })
      .catch((e) => {
        // 遇到cancel应该忽略报错, 并且返回 canceled = true
        // 调用api的地方需要用 takeWhile((v) => !v.canceled)来过滤掉取消的后续响应
        // 处理的错误类型： 无产品权限、gql语法问题（eg.字段不对应、路径不正确）、bff服务器异常（断网、服务器无响应）、canceled。
        let canceled = false;
        if (e.message === 'canceled') {
          canceled = true;
        } else {
          const msg = e.cause === MDT_PRODUCT_FORBIDDEN_EXCEPTION ? e.message : SYNTAX_ERROR_MSG;
          toastApi.error(msg);
        }

        const resp = {
          ...errorResp,
          canceled,
        };
        if (isOneResp) {
          return resp;
        }
        return _.times(_.size(names), () => resp);
      });
  }

  // 后端接口异常处理
  private static handleRespError(resp: ApolloQueryResult<any> | FetchResult<any>) {
    const { data, errors } = resp;

    // 无产品权限
    const noAuthError = _.find(errors, (e) => e.message === MDT_PRODUCT_FORBIDDEN_EXCEPTION);
    if (noAuthError) {
      // @ts-ignore
      throw new Error(_.get(noAuthError, 'extensions.exception.msg'), { cause: MDT_PRODUCT_FORBIDDEN_EXCEPTION });
    }

    // eg: bff后端请求rest接口时没有响应
    if (!data) {
      throw new Error(_.get(errors, '[0].message'));
    }

    const errorNames: string[] = [];
    let msgs: string[] = [];
    let requestId = '';
    let backendRequestId = '';
    let is401 = false;
    let rc = NaN;
    _.forEach(data, (item, name) => {
      if (!item.success) {
        errorNames.push(name);
        console.error(item);
        msgs.push(item.msg);
        requestId = item.requestId;
        backendRequestId = item.backendRequestId;
        rc = item.rc;
        if (item.status === 401) is401 = true;
      }
    });

    if (this.rcHandlersController.hasHandler(rc)) {
      this.rcHandlersController.handle(rc, {
        msgs,
        requestId,
      });
      return;
    }

    // 接口异常(eg: rest接口报参数错误, 如果backendRequestId为空说明是bff后端自定义报错)
    if (!_.isEmpty(msgs)) {
      notification.error({
        message: RESPONS_EERROR_MSG,
        description: (
          <div style={{ maxHeight: '120px', overflow: 'auto' }}>
            异常内容：
            {_.map(msgs, (msg, index) => (
              <div key={index}>{msg}</div>
            ))}
            <div>
              request ID: <br /> {requestId}
            </div>
            <div>
              {backendRequestId ? 'backend request ID:' : ''} <br /> {backendRequestId}
            </div>
            <div>query name: {_.join(errorNames, ',')}</div>
          </div>
        ),
        duration: 0,
        key: requestId,
      });
    }

    if (is401 && this.deal401) this.deal401();
  }

  private static initAxiosIns() {
    if (this.axiosIns) return;
    this.axiosIns = axios.create();
    this.axiosIns.interceptors.request.use((config) => {
      let requestName = '';
      try {
        // redirectToBffPublicInner方法请求的data是对象
        const configData = _.isString(config.data) ? JSON.parse(config.data) : config.data;
        requestName = configData.operationName;
      } catch (e) {
        console.log(e);
      }
      requestName = requestName ? `/${requestName}` : '';
      config.url = `${config.url}${requestName}`;
      return config;
    });
  }

  private static customerFetch: typeof fetch = async (input: any, init: any, operation?: any) => {
    const context = operation!.getContext();
    const cancelToken = context.cancelToken;

    return this.axiosIns!.request({
      url: input,
      method: init.method,
      data: init.body,
      cancelToken,
      headers: init.headers,
      owner: context.owner,
      disableBffProxy: context.disableBffProxy,
      enableBffProxy: context.enableBffProxy,
    });
  };
}

export const bffResponseErrorType = `
  ... on ResponseErrorType {
    success
    msg
    status
    requestId
    backendRequestId
    rc
    extra {
      type
      value
    }
  }
`;

export const bffResponsePaginationData = `
  success
  total_count
  page_num
  page_size
  extra {
    type
    value
  }
  page_data
`;

export const bffResponseData = `
  success
  extra {
    type
    value
  }
  page_data
`;

export const bffEmptyResponseData = `
  success
  extra {
    type
    value
  }
  page_data {
    empty
  }
`;
