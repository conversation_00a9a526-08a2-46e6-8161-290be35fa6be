// api处理===========================================================================================
import { compressToEncodedURIComponent } from 'lz-string';
import urlParameterAppend from 'url-parameter-append';
import { microAfterLoginSuccess, microApiUrl } from '@mdtProSso/utils/microUtil';
import { AppController } from '../app/AppController';
import { API_AUTH_URL, API_URL, ENCODE_PARAM, IS_MICRO_ENV, REDIRECT } from '../config';
import { savePrivacyToStore, saveTokenToStore } from './storageUtil';
import { getQFromUrl, isDialog } from './util';

// 接入后端的api
let apiUrl = IS_MICRO_ENV ? microApiUrl() : API_URL;
apiUrl = apiUrl || API_URL;

const apiAuthUrl = API_AUTH_URL;

export const fetchRequest = (url: string, config?: any) => {
  return AppController.getInstance().getFetchRequest().fetchRequest(url, config);
};

export const getTokenHeader = (tk: string, itk?: string) => {
  return AppController.getInstance().getFetchRequest().getHeaderRequestToken({}, tk, itk);
};

// api错误处理
const requestError = (response: any, status: number, i18n: any): string => {
  const { rc, msg_args } = response;
  let trc = i18n.translate(`rc.${rc}`);
  typeof trc === 'function' && (trc = trc(msg_args));
  return rc ? trc : i18n.translate(`api.${status}`);
};

savePrivacyToStore('true');

// 登录成功
const afterLoginSuccess = async (result: any, extraUri?: string) => {
  let mergedResult = result;
  const isDatlas = result.isDatlas;
  if (isDatlas) {
    const headers = getTokenHeader(result.access_token);
    const response = await fetchRequest(`login/datlas`, {
      headers: headers,
    });
    const datlasResult = await response.json();
    datlasResult.rc === 0 && (mergedResult = { ...result, ...datlasResult.result, isDatlas: false });
  }
  // 微服务不需要跳转, 父子组件通信即可
  if (IS_MICRO_ENV) {
    microAfterLoginSuccess(mergedResult);
    return;
  }

  // 弹窗形式直接通知
  if (isDialog()) {
    const notificationWindow = window.parent!;
    notificationWindow.postMessage(
      {
        action: 'dialog-login',
        data: mergedResult,
      },
      '*',
    );
    return;
  }

  // 常规的逻辑处理
  saveTokenToStore(mergedResult.access_token);
  const url = REDIRECT || extraUri || API_URL;
  const isEncodeDatlas = ENCODE_PARAM && mergedResult.isDatlas;
  let paramsKey = '';
  let params = {};
  // todo 即将废弃
  if (isEncodeDatlas) {
    const oldQ = getQFromUrl(url);
    oldQ.itk && delete oldQ.itk;
    let encodedQ = JSON.stringify({ ...oldQ, tk: mergedResult.access_token });
    params = compressToEncodedURIComponent(encodedQ);
    paramsKey = 'q';
  } else {
    params = mergedResult.access_token;
    paramsKey = mergedResult.isDatlas ? 'datlas_token' : 'collector_token';
  }
  if (url) {
    const urlWithParams = urlParameterAppend(url, paramsKey, params);
    window.location.replace(urlWithParams);
  }
};

export { apiUrl, apiAuthUrl, requestError, afterLoginSuccess };
