import _ from 'lodash';
import { BehaviorSubject } from 'rxjs';
import { skip } from 'rxjs/operators';
import { RequestController } from '@mdtBsControllers/request-controller';
import toastApi from '@mdtDesign/toast';
import { AccessLevelEnum, PreferencesEnum, TaskStatusEnum } from '@mdtProComm/constants';
import defaultAppLogo from '@mdtProMicroModules/resource/logo.png';
import { CreateUploadFileTask } from '@mdtProTasks/CreateUploadFileTask';
import { AppController } from '../../app/AppController';
import { UPLOAD_LOGO_MAX_KB } from '../../config';
import i18n from '../../languages';
import { IAppStyleModel } from './AppStyleModel';

// 上传的类型
const ACCEPT_TYPES = '.png, .jpg, .jpeg, .svg, .ico';
// 最大上传size
const MAX_SIZE = 1024 * UPLOAD_LOGO_MAX_KB;
// favicon 尺寸限制
const FAVICON_SIZE = [64, 64];

export interface ICardInterface {
  type: 'image' | 'string' | 'boolean' | 'select';
  value: any;
  name: string;
  extra?: any;
}

// 在当前模块中使用的偏好集合
export type IAppStyleKey =
  | PreferencesEnum.DATLAS_LOGO
  | PreferencesEnum.DATLAS_FAVICON
  | PreferencesEnum.DATLAS_WEB_TITLE
  | PreferencesEnum.DATLAS_WEB_DESCRIPTION
  | PreferencesEnum.DATLAS_HIDE_HELP_CENTER
  | PreferencesEnum.DATLAS_ENABLE_WATERMARK
  | PreferencesEnum.DATLAS_INACTIVITY_LOGOUT_INTERVAL
  | PreferencesEnum.DATLAS_PROMPT_CHANGE_PASSWORD_INTERVAL;

type IDataSource = Record<IAppStyleKey, ICardInterface>;

class AppStyleController extends RequestController {
  private app: AppController;
  private Model: IAppStyleModel;
  private dataSource$ = new BehaviorSubject<IDataSource>(this.getInitialDataSource());
  private viewData$ = new BehaviorSubject<IDataSource>(this.getInitialDataSource());
  private loadingKeys$ = new BehaviorSubject<IAppStyleKey[]>([]);
  // cache 用于监听数据源变化时，对比新的数据源找到变化的key
  private dataSourceCache = this.getInitialDataSource();

  public constructor(app: AppController, Model: IAppStyleModel) {
    super();
    this.app = app;
    this.Model = Model;
    this.initDataSource();
  }

  public destroy() {
    super.destroy();
    this.dataSource$.complete();
    this.dataSource$.next(null!);
    this.viewData$.complete();
    this.viewData$.next(null!);
    this.loadingKeys$.complete();
    this.app = null!;
    this.Model = null!;
  }

  public getGroupRelations() {
    return [
      { title: i18n.chain.orgAdmin.appStyle.logo, keys: [PreferencesEnum.DATLAS_LOGO] },
      {
        title: i18n.chain.orgAdmin.appStyle.favicon,
        keys: [
          PreferencesEnum.DATLAS_FAVICON,
          PreferencesEnum.DATLAS_WEB_TITLE,
          PreferencesEnum.DATLAS_WEB_DESCRIPTION,
        ],
      },
      {
        title: i18n.chain.orgAdmin.appStyle.other,
        keys: [
          PreferencesEnum.DATLAS_HIDE_HELP_CENTER,
          PreferencesEnum.DATLAS_ENABLE_WATERMARK,
          PreferencesEnum.DATLAS_INACTIVITY_LOGOUT_INTERVAL,
          PreferencesEnum.DATLAS_PROMPT_CHANGE_PASSWORD_INTERVAL,
        ],
      },
    ];
  }

  public getViewData$() {
    return this.viewData$;
  }

  public getLoadingKeys$() {
    return this.loadingKeys$;
  }

  public onDataChanged = (key: IAppStyleKey, value: ICardInterface['value'], editDataSourceRedirect = false) => {
    if (editDataSourceRedirect) {
      const dataSource = this.getDataSourceValue();
      const newDataSource = { ...dataSource, [key]: { ...dataSource[key], value } };
      this.dataSource$.next(newDataSource);
      return;
    }
    const viewData = this.getViewDataValue();
    const newViewData = { ...viewData, [key]: { ...viewData[key], value } };
    this.dataSource$.next(this.dataRestorer(newViewData));
  };

  public onUploadFunc = (files: File[], key: IAppStyleKey) => {
    const file = files[0];

    const taskStart = () => {
      const task = new CreateUploadFileTask(file, 'image', { accessLevel: AccessLevelEnum.ANYONE });
      const handleUploaded = async (fileId: string) => {
        task.destroy();
        this.onDataChanged(key, fileId, true);
      };
      task.taskResp$.subscribe(async (resp) => {
        const status = resp.status;
        if (status === TaskStatusEnum.SUCCESSFUL) {
          handleUploaded(resp.result);
        } else if (status === TaskStatusEnum.FAILED) {
          handleUploaded(i18n.chain.orgAdmin.logo.fileUploadFailed(file.name));
        }
      });
    };

    if (file.size > MAX_SIZE) {
      toastApi.error(i18n.chain.orgAdmin.logo.maxSize(UPLOAD_LOGO_MAX_KB));
      return;
    }

    if (_.isEqual(key, PreferencesEnum.DATLAS_FAVICON)) {
      this.checkFileSize(file, FAVICON_SIZE[0], FAVICON_SIZE[1], taskStart);
      return;
    }

    taskStart();
  };

  /**
   * 重置全部/根据key重置
   * @param key IAppStyleKey | 'all' 传入all全部重置
   * @returns void
   */
  public onReset = async (key: IAppStyleKey | 'all') => {
    const initialDataSource = this.getInitialDataSource();
    if (_.isEqual(key, 'all')) {
      this.dataSource$.next(initialDataSource);
      return;
    }

    const dataSource = this.getDataSourceValue();
    this.dataSource$.next({ ...dataSource, [key]: initialDataSource[key as IAppStyleKey] });
  };

  /**
   * 用于view层展示的数据处理
   * @param data IDataSource
   * @returns data IDataSource
   */
  private dataTransformer = async (data: IDataSource) => {
    const result = await this.parseFileToImage(data);
    // 帮助中心偏好获取的是隐藏，view层的check取反
    _.update(result, `${PreferencesEnum.DATLAS_HIDE_HELP_CENTER}`, (v: ICardInterface) => ({ ...v, value: !v.value }));
    return result;
  };

  /**
   * 用于还原来自view层的数据
   * @param viewData IDataSource
   * @returns dataSource IDataSource
   */
  private dataRestorer(viewData: IDataSource): IDataSource {
    const result = _.cloneDeep(viewData);
    _.update(result, `${PreferencesEnum.DATLAS_HIDE_HELP_CENTER}`, (v: ICardInterface) => ({ ...v, value: !v.value }));
    return this.resetImageToDataSource(result);
  }

  private initDataSource = async () => {
    let mergedDataSource = _.cloneDeep(this.dataSource$.getValue());

    // 偏好赋值
    mergedDataSource = this.overrideDataWithPreferences(mergedDataSource);
    this.dataSource$.next(mergedDataSource);
    this.dataSourceCache = mergedDataSource;
    this.viewDataChange(mergedDataSource);
    this.listenDataSourceChange();
  };

  private listenDataSourceChange = () => {
    this.dataSource$.pipe(skip(1)).subscribe(async (data) => {
      const values = this.getBatchPreferencesValues(data);
      const noChanged = _.isEmpty(values);
      if (!noChanged) {
        // loading
        this.loadingKeys$.next(_.keys(values) as IAppStyleKey[]);
        const { success } = await this.Model.putUserPreferencesBatch(values).toPromise();
        success && this.afterDataSourceChanged(data);
        this.loadingKeys$.next([]);
      }
    });
  };

  private afterDataSourceChanged(dataSource: IDataSource) {
    toastApi.success(i18n.chain.orgAdmin.appStyle.updateSuccess);
    this.updateCache(dataSource);
    this.viewDataChange(dataSource);
  }

  private updateCache(dataSource: IDataSource) {
    this.dataSourceCache = dataSource;
  }

  private getBatchPreferencesValues(dataSource: IDataSource) {
    const changedKey = this.findDifferenceKey(this.dataSourceCache, dataSource);
    const putData: any = {};
    _.forEach(changedKey, (key) => {
      putData[key] = dataSource[key].value;
    });
    return putData;
  }

  private findDifferenceKey(newData: IDataSource, old: IDataSource): IAppStyleKey[] {
    return _.reduce(
      old,
      (diffKeys: IAppStyleKey[], value, key) => {
        if (!_.isEqual(value, _.get(newData, key))) {
          diffKeys.push(key as IAppStyleKey);
        }
        return diffKeys;
      },
      [],
    );
  }

  private async viewDataChange(data: IDataSource) {
    const dataSource = _.cloneDeep(data);
    // view层数据转换
    const viewData = await this.dataTransformer(dataSource);
    this.viewData$.next(viewData);
  }

  private async parseFileToImage(data: IDataSource) {
    const dataSource = _.cloneDeep(data);

    for (let [key, value] of Object.entries(dataSource)) {
      const isImg = value.type === 'image';
      const isImgEmptyPreference = !!(isImg && value.value);
      if (isImg) {
        dataSource[key as IAppStyleKey].value = isImgEmptyPreference
          ? (await this.Model.getFileUrl(value.value as string).toPromise()).result
          : defaultAppLogo;
      }
    }

    return dataSource;
  }

  private checkFileSize = (file: File, width: number, height: number, success?: () => void) => {
    const img = new Image();
    img.onload = () => {
      const w = img.naturalWidth;
      const h = img.naturalHeight;
      window.URL.revokeObjectURL(img.src);
      if (w === width && h === height) {
        success?.();
      } else {
        toastApi.error(i18n.chain.orgAdmin.appStyle.sizeError(width, height));
      }
    };
    img.onerror = function () {
      toastApi.error(i18n.chain.orgAdmin.appStyle.fileError);
    };
    img.src = window.URL.createObjectURL(file);
  };

  private resetImageToDataSource(viewData: IDataSource) {
    const dataSource = _.cloneDeep(viewData);

    for (let [key, value] of Object.entries(dataSource)) {
      const isImg = value.type === 'image';
      const regex = /^\s*data:image\/(png|jpg|jpeg|gif);base64,[A-Za-z0-9+/]+={0,2}\s*$/i;
      const isBase64 = regex.test(value.value);
      const isValue = value.value;
      if (isImg && isBase64) {
        dataSource[key as IAppStyleKey].value = '';
      } else if (isImg && isValue) {
        dataSource[key as IAppStyleKey].value = this.dataSourceCache[key as IAppStyleKey].value;
      }
    }

    return dataSource;
  }

  private getDataSourceValue() {
    return this.dataSource$.getValue();
  }

  private getViewDataValue() {
    return this.viewData$.getValue();
  }

  private overrideDataWithPreferences(data: IDataSource) {
    const prefC = this.app.getPreferencesController()!;
    const dataSource = _.cloneDeep(data);

    const preferenceMethods = {
      [PreferencesEnum.DATLAS_LOGO]: prefC.getDatlasLogo.bind(prefC),
      [PreferencesEnum.DATLAS_FAVICON]: prefC.getDatlasFavicon.bind(prefC),
      [PreferencesEnum.DATLAS_WEB_TITLE]: prefC.getDatlasWebTitle.bind(prefC),
      [PreferencesEnum.DATLAS_WEB_DESCRIPTION]: prefC.getDatlasWebDescription.bind(prefC),
      [PreferencesEnum.DATLAS_HIDE_HELP_CENTER]: prefC.getDatlasHideHelpCenter.bind(prefC),
      [PreferencesEnum.DATLAS_ENABLE_WATERMARK]: prefC.getDatlasEnableWatermark.bind(prefC),
      [PreferencesEnum.DATLAS_INACTIVITY_LOGOUT_INTERVAL]: prefC.getDatlasInactivityLogoutInterval.bind(prefC),
      [PreferencesEnum.DATLAS_PROMPT_CHANGE_PASSWORD_INTERVAL]: prefC.getDatlasPromptChangePasswordInterval.bind(prefC),
    };

    _.forEach(preferenceMethods, (getMethod, key) => {
      if (dataSource[key as IAppStyleKey] && _.isFunction(getMethod)) {
        dataSource[key as IAppStyleKey].value = getMethod();
      }
    });

    return dataSource;
  }

  private getInitialDataSource(): IDataSource {
    return {
      [PreferencesEnum.DATLAS_LOGO]: {
        type: 'image',
        value: '',
        name: i18n.chain.orgAdmin.appStyle.logoName,
        extra: {
          accept: ACCEPT_TYPES,
        },
      },
      [PreferencesEnum.DATLAS_FAVICON]: {
        type: 'image',
        value: '',
        name: i18n.chain.orgAdmin.appStyle.faviconName,
        extra: {
          accept: ACCEPT_TYPES,
        },
      },
      [PreferencesEnum.DATLAS_WEB_TITLE]: {
        type: 'string',
        value: '',
        name: i18n.chain.orgAdmin.appStyle.windowTitleName,
        extra: {
          placeholder: 'Datlas - 脉策低代码数据分析平台',
        },
      },
      [PreferencesEnum.DATLAS_WEB_DESCRIPTION]: {
        type: 'string',
        value: '',
        name: i18n.chain.orgAdmin.appStyle.windowDescName,
        extra: {
          placeholder: 'Datlas -- low code data analysis platform',
        },
      },
      [PreferencesEnum.DATLAS_HIDE_HELP_CENTER]: {
        type: 'boolean',
        value: false,
        name: i18n.chain.orgAdmin.appStyle.helpCenterName,
      },
      [PreferencesEnum.DATLAS_ENABLE_WATERMARK]: {
        type: 'boolean',
        value: false,
        name: i18n.chain.orgAdmin.appStyle.waterMark,
      },
      [PreferencesEnum.DATLAS_INACTIVITY_LOGOUT_INTERVAL]: {
        type: 'select',
        value: 0,
        name: i18n.chain.orgAdmin.appStyle.idleTimeoutName,
        extra: {
          description: i18n.chain.orgAdmin.appStyle.idleTimeoutDesc,
          options: [
            {
              label: i18n.chain.orgAdmin.appStyle.tenMinutes,
              value: 60 * 10,
            },
            {
              label: i18n.chain.orgAdmin.appStyle.halfHour,
              value: 60 * 30,
            },
            {
              label: i18n.chain.orgAdmin.appStyle.oneHour,
              value: 60 * 60,
            },
            {
              label: i18n.chain.orgAdmin.appStyle.twoHours,
              value: 60 * 60 * 2,
            },
            {
              label: i18n.chain.orgAdmin.appStyle.fourHours,
              value: 60 * 60 * 4,
            },
            {
              label: i18n.chain.orgAdmin.appStyle.eightHours,
              value: 60 * 60 * 8,
            },
            {
              label: i18n.chain.orgAdmin.appStyle.never,
              value: 0,
            },
          ],
        },
      },
      [PreferencesEnum.DATLAS_PROMPT_CHANGE_PASSWORD_INTERVAL]: {
        type: 'select',
        value: 0,
        name: i18n.chain.orgAdmin.appStyle.pswSaftyName,
        extra: {
          description: i18n.chain.orgAdmin.appStyle.pswSaftyDesc,
          options: [
            {
              label: i18n.chain.orgAdmin.appStyle.sevenDays,
              value: 60 * 60 * 24 * 7,
            },
            {
              label: i18n.chain.orgAdmin.appStyle.fifteenDays,
              value: 60 * 60 * 24 * 15,
            },
            {
              label: i18n.chain.orgAdmin.appStyle.threeMonth,
              value: 60 * 60 * 24 * 90,
            },
            {
              label: i18n.chain.orgAdmin.appStyle.halfYear,
              value: 60 * 60 * 24 * 183,
            },
            {
              label: i18n.chain.orgAdmin.appStyle.nineMonth,
              value: 60 * 60 * 24 * 274,
            },
            {
              label: i18n.chain.orgAdmin.appStyle.oneYear,
              value: 60 * 60 * 24 * 365,
            },
            {
              label: i18n.chain.orgAdmin.appStyle.never,
              value: 0,
            },
          ],
        },
      },
    };
  }
}

export { AppStyleController };
