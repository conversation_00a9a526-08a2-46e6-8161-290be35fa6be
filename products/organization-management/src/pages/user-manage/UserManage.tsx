import { FC } from 'react';
import { ModalWithBtnsCompEmotion } from '@mdtBsComponents/modal-with-btns-comp-emotion';
import { DrawerModifyFormPermissionUser } from '@mdtProMicroModules/containers/drawer-modify-form-permission-user';
import { TableCurdWithSimpleSearch } from '@mdtProMicroModules/containers/table-curd-with-simple-search';
import { UserManageController } from './UserManageController';

// 角色管理页面=======================================================================================
interface IProps {
  controller: UserManageController;
}
const UserManage: FC<IProps> = ({ controller }) => {
  return (
    <div style={{ padding: '0 20px 20px 20px', height: '100%', width: '100%' }}>
      <TableCurdWithSimpleSearch controller={controller.getTableController()} />
      <DrawerModifyFormPermissionUser controller={controller.getModifyController()} />
      <ModalWithBtnsCompEmotion controller={controller.getDeleteControllerr()} />
    </div>
  );
};

export { UserManage };
