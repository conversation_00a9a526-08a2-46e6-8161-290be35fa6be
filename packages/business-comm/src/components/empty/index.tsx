import { FC } from 'react';
import { Empty as EmptyComponent } from '@metroDesign/empty';
import i18n from '../../languages';
import './index.less';

interface IProps {
  title?: string;
  description?: string;
  Image?: any;
}

const Empty: FC<IProps> = ({ description = i18n.chain.comNoData, Image, title }) => {
  return (
    <div className="dmc-empty dmc-empty-normal">
      <EmptyComponent image={Image}>
        {title && <div className="dmc-empty-title">{title}</div>}
        <div className="dmc-empty-description">{description}</div>
      </EmptyComponent>
    </div>
  );
};

export { Empty };
