import { IOrderQuery, IPaginationQuery } from './comm';

export interface IPagesQuery extends IOrderQuery, IPaginationQuery {
  ids?: string; // 用逗号分隔的uuid字符串
  name_like?: string;
  name?: string; // vault名称
  editable?: boolean;
}

export interface IPage {
  nodes: object;
  id: string;
  app_id: number;
  permissions: string[];
  user_id: number;
  name: string;
  vaultTitle: string;
  update_time: number;
  preview: string;
}
