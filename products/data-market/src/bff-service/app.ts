import {
  BaseBffService,
  bindExternalApp,
  IBindExternalAppOptions,
  IQueryExternalAppListOptions,
  IQueryGrantedAppListOptions,
  IUnbindExternalAppOptions,
  queryExternalAppList,
  queryGrantedAppList,
  unbindExternalApp,
} from '@mdtBsBffServices/services';
export class DMAppBffService extends BaseBffService {
  public queryGrantedAppList(options: IQueryGrantedAppListOptions) {
    return queryGrantedAppList(this.productName, options);
  }

  public queryExternalAppList(options: IQueryExternalAppListOptions) {
    return queryExternalAppList(this.productName, options);
  }

  public bindExternalApp(options: IBindExternalAppOptions) {
    return bindExternalApp(this.productName, options);
  }

  public unbindExternalApp(options: IUnbindExternalAppOptions) {
    return unbindExternalApp(this.productName, options);
  }
}
