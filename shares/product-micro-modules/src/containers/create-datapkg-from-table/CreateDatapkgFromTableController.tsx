import _ from 'lodash';
import { FC } from 'react';
import { BehaviorSubject } from 'rxjs';
import { debounceTime, skip } from 'rxjs/operators';
import { DebounceTimeEnum } from '@mdtBsComm/constants';
import { ILabelValue } from '@mdtBsComm/interfaces';
import { FormCompController } from '@mdtBsComponents/form-comp';
import { ModalWithBtnsCompDrawerController } from '@mdtBsComponents/modal-with-btns-comp-drawer';
import toastApi from '@mdtDesign/toast';
import { OwnershipEnum, PermissionEnum } from '@mdtProComm/constants';
import {
  CreateDatapkgPreviewController,
  IEditPermission,
} from '@mdtProTasks/create-datapkg-from-upload-local-file-task/create-datapkg-preview';
import type { IFrontendRefreshTimer } from '../../ascatter-expand/datapkg-refresh-timer/constants';
import { transformRefreshTimerFrontendToModel } from '../../ascatter-expand/datapkg-refresh-timer/refreshtimerUtil';
import { DatlasAppController } from '../../datlas/app/DatlasAppController';
import i18n from '../../languages';
import { CreateDatapkgFromTable } from './CreateDatapkgFromTable';
import { ICreateDatapkgFromTableModel, IGeneratePkgOptions } from './CreateDatapkgFromTableModel';

export interface IDatasourceForm {
  datasource: string;
  schema: string;
  table: string;
}

export enum DatasourceFormAttrs {
  DATASOURCE = 'datasource',
  SCHEMEA = 'schema',
  TABLE = 'table',
}

export interface IPkgForm extends IFrontendRefreshTimer {
  ownership?: string;
  name: string;
  geometryType: string;
}

export enum PkgFormAttrs {
  NAME = 'name',
  GEOMETRY_TYPE = 'geometryType',
  OWNERSHIP = 'ownership',
}

export enum SectionsEnum {
  DATASOURCE = 'datasource',
  PKG_CREATE = 'pkg_create',
}

interface IControllerOptions {
  Model: ICreateDatapkgFromTableModel;
  goDatasourceFunc?: () => void;
  dsOptions?: {
    datasource: string;
    schema: string;
    table: string;
  };
}
interface IDsOptions {
  datasource: string;
  schema: string;
  table: string;
}

class CreateDatapkgFromTableController extends ModalWithBtnsCompDrawerController {
  private Model: ICreateDatapkgFromTableModel;
  private activeSection$ = new BehaviorSubject(SectionsEnum.DATASOURCE);
  // 数据源表单控制器
  private dsFormController: FormCompController<IDatasourceForm>;
  // 数据包创建表单控制器
  private pkgFormController: FormCompController<IPkgForm>;
  private dsOptions$ = new BehaviorSubject<ILabelValue[]>([]);
  private schemaOptions$ = new BehaviorSubject<ILabelValue[]>([]);
  private tableOptions$ = new BehaviorSubject<ILabelValue[]>([]);
  private geometryOptions: ILabelValue[] = [];
  private previewController?: CreateDatapkgPreviewController;
  private loadingPreviewData$ = new BehaviorSubject(true);
  private isNameExist = false;
  private goDatasourceFunc?: () => void;
  private isDsInited = false;

  public constructor(options: IControllerOptions) {
    super({
      modalCompOptions: {
        modalOptions: () => this.initDrawerProps(),
        innerViewController: () => this,
        InnerView: CreateDatapkgFromTable as FC,
      },
      uiOptions: () => this.initUiOptions(),
      clickOkBtnFunc: () => this.onClickCreate(),
      beforeCancelFunc: async (originalData, isCloseBtn) => {
        if (isCloseBtn) return true;
        if (this.activeSection$.getValue() === SectionsEnum.DATASOURCE) return true;
        this.activeSection$.next(SectionsEnum.DATASOURCE);
        this.changeDisabledOkBtn(true);
        return false;
      },
    });

    this.Model = options.Model;
    this.goDatasourceFunc = options.goDatasourceFunc;
    this.dsFormController = new FormCompController<IDatasourceForm>();
    this.pkgFormController = new FormCompController<IPkgForm>();
    this.Model.queryDatasourceOptions().subscribe((options) => {
      this.dsOptions$.next(options);
    });
    this.Model.queryGeometryOptions().subscribe((options) => {
      this.geometryOptions = options;
    });
    this.listenDsFormChange();
    this.listenPkgFormChange();
    this.changeDisabledOkBtn(true);
  }

  public destroy() {
    this.activeSection$.complete();
    this.dsOptions$.complete();
    this.schemaOptions$.complete();
    this.tableOptions$.complete();
    this.loadingPreviewData$.complete();
    this.geometryOptions = [];
    this.dsFormController.destroy();
    this.pkgFormController.destroy();
    this.previewController?.destroy();
    this.previewController = undefined;
    this.goDatasourceFunc = null!;
    this.Model = null!;
  }

  public showCreateDatasource() {
    const permissionCtrl = DatlasAppController.getInstance().getUserPermissionController();
    const hasPermission = permissionCtrl.checkUserPermission(PermissionEnum.TEMP_DBLINK_ADD);
    return hasPermission && !!this.goDatasourceFunc;
  }

  public openModal(dsOptions?: IDsOptions | OwnershipEnum) {
    this.isDsInited = _.get(dsOptions, 'datasource');
    if (this.isDsInited) {
      const { datasource, schema, table } = dsOptions! as IDsOptions;
      this.activeSection$.next(SectionsEnum.PKG_CREATE);
      this.changeDisabledOkBtn(false);
      this.dsFormController.changeFormData({
        datasource,
        schema,
        table,
      });
      this.queryTableInfo(datasource, schema, table);
    } else if (_.isString(dsOptions)) {
      this.pkgFormController.changeFormData({
        ...this.pkgFormController.getFormDataValue()!,
        ownership: dsOptions,
      });
    }
    return super.openModal(dsOptions);
  }

  public ownershipFix() {
    return _.isString(this.getModalRest());
  }

  public goDatasource() {
    this.goDatasourceFunc!();
  }

  public getLoadPreviewData() {
    return this.loadingPreviewData$;
  }

  public getPreviewController() {
    return this.previewController!;
  }

  public getdsOptions$() {
    return this.dsOptions$;
  }

  public getSchemaOptions$() {
    return this.schemaOptions$;
  }

  public getTableOptions$() {
    return this.tableOptions$;
  }

  public getGeometryOptions$() {
    return this.geometryOptions;
  }

  public getActiveSection$() {
    return this.activeSection$;
  }

  public getDsFormController() {
    return this.dsFormController;
  }

  public getPkgFormController() {
    return this.pkgFormController;
  }

  public onClickCreate = async () => {
    const result = await this.validate();
    if (!result) return { success: false };
    const { keyColumns, hasRepeatKeyColumn } = this.previewController!.getFieldData();
    if (hasRepeatKeyColumn) {
      toastApi.error(i18n.chain.proMicroModules.datapkg.keyNoRepeat);
      return { success: false };
    }

    const dsFormData = this.dsFormController.getFormDataValue()!;
    const pkgFormData = this.pkgFormController.getFormDataValue()!;
    const params: IGeneratePkgOptions = {
      dbId: dsFormData.datasource,
      schema: dsFormData.schema,
      tableName: dsFormData.table,
      values: pkgFormData,
      keyColumns,
      ownership: pkgFormData.ownership as any,
    };
    transformRefreshTimerFrontendToModel(pkgFormData, params);
    const resp = await this.Model.generatePkg(params).toPromise();
    if (resp.success) {
      toastApi.success(i18n.chain.comTip.createSuccess);
    }
    return resp;
  };

  public onClickNextStep = async () => {
    await this.dsFormController.validateFormData();
    const hasError = this.dsFormController.dataHasError();
    if (hasError) return;
    this.activeSection$.next(SectionsEnum.PKG_CREATE);
    this.changeDisabledOkBtn(false);
    const values = this.dsFormController.getFormData$().getValue()!;
    this.queryTableInfo(values.datasource, values.schema, values?.table);
  };

  private queryTableInfo(datasource: string, schema: string, table: string) {
    this.loadingPreviewData$.next(true);
    this.Model.queryTableInfo(datasource, schema, table).subscribe((val) => {
      const permission: BehaviorSubject<IEditPermission> = new BehaviorSubject({
        enableEditKey: true,
        enableEditType: false,
      } as any);
      this.previewController = new CreateDatapkgPreviewController({
        rows: val.rows,
        columnType: val.columnType,
        keyColumns: val.keyColumn,
        editPermission$: permission,
      });
      this.loadingPreviewData$.next(false);
    });
  }

  private listenDsFormChange() {
    this.dsFormController.getChangedValues$().subscribe((val) => {
      const oldValue = this.dsFormController.getFormData$().getValue()!;
      if (val.datasource) {
        this.dsFormController.changeFormData({
          ...oldValue,
          schema: '',
          table: '',
        });
        this.dsFormController.getFormIns()?.resetFields(['schema', 'table']);
        this.Model.querySchemaOptions(val.datasource).subscribe((options) => {
          this.schemaOptions$.next(options);
        });
      } else if (val.schema) {
        this.dsFormController.changeFormData({
          ...oldValue,
          table: '',
        });
        this.dsFormController.getFormIns()?.resetFields(['table']);
        this.Model.queryTableOptions(oldValue.datasource, val.schema).subscribe((options) => {
          this.tableOptions$.next(options);
        });
      }
    });
  }

  private async validate() {
    if (this.isNameExist) return false;
    await this.pkgFormController.validateFormData();
    return !this.pkgFormController.dataHasError();
  }

  private validateName() {
    if (this.isNameExist) {
      this.pkgFormController
        .getFormIns()
        .setFields([{ name: 'name', errors: [i18n.chain.proMicroModules.datapkg.datapkgSql.pkgExistError] }]);
    }
    return !this.isNameExist;
  }

  private listenPkgFormChange() {
    this.pkgFormController
      .getChangedValues$()
      .pipe(skip(1), debounceTime(DebounceTimeEnum.MAX))
      .subscribe((val) => {
        const dsFormValue = this.dsFormController.getFormData$().getValue()!;
        if (val.name) {
          this.Model.checkNameExistFunc(dsFormValue.datasource, val.name).subscribe((val) => {
            this.isNameExist = val;
            this.validateName();
          });
        }
      });
  }

  private initUiOptions() {
    const isDsSection = this.activeSection$.getValue() === SectionsEnum.DATASOURCE;
    const okDisplay = isDsSection ? 'none' : 'flex';
    const cancelDisplay = isDsSection || this.isDsInited ? 'none' : 'flex';
    const okBtnProps = { style: { display: okDisplay }, type: 'primary' as 'primary' };
    const cancelBtnProps = { style: { display: cancelDisplay } };
    return {
      title: i18n.chain.proMicroModules.datapkg.addFormDataset,
      cancelText: i18n.chain.comButton.previous,
      okText: i18n.chain.proMicroModules.datapkg.generatePkg,
      okBtnProps,
      cancelBtnProps,
    };
  }

  private initDrawerProps = () => {
    return {
      width: '100vw',
      level: null,
    };
  };
}

export { CreateDatapkgFromTableController };
