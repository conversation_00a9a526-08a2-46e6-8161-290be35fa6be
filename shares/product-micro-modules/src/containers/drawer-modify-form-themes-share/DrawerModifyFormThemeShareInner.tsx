import _ from 'lodash';
import { FC, MouseEvent } from 'react';
import { FormField } from '@mdtBsComm/components/form';
import { FormComp } from '@mdtBsComponents/form-comp';
import { ModalWithBtnsCompDialog } from '@mdtBsComponents/modal-with-btns-comp-dialog';
import { LinkButton } from '@mdtDesign/button';
// import CheckBox from '@mdtDesign/checkbox';
import Tag from '@mdtDesign/tag';
import { DbColumnTypeEnum } from '@mdtProComm/constants';
import { TransformIdToName } from '../../components/transform-id-to-name';
import i18n from '../../languages';
import { DrawerModifyFormThemeShareController, FormDataAttrs } from './DrawerModifyFormThemeShareController';
import './index.less';

interface IProps {
  controller: DrawerModifyFormThemeShareController;
}

const TagList: FC<any> = (props: any) => {
  const { value = [], onChange, type } = props;
  const onClickClose = (e: MouseEvent, tag: string) => {
    const newValue = _.filter(value, ({ title }) => {
      return !_.isEqual(tag, title);
    });
    onChange?.(newValue);
  };
  return (
    <div className="module_drawer-modify-form-theme-share-form-item">
      {_.map(value, (value) => (
        <Tag
          className="module_drawer-modify-form-theme-share-form-item-tag"
          tag={
            type === FormDataAttrs.USERS ? (
              <TransformIdToName id={value} type={DbColumnTypeEnum.USER_ID} />
            ) : (
              value.title
            )
          }
          key={value}
          onClickClose={onClickClose}
          closable
        />
      ))}
    </div>
  );
};
// DrawerModifyRoleInner组件=========================================================================
export function DrawerModifyFormThemeShareInner({ controller }: IProps) {
  const onClick = (type: FormDataAttrs) => {
    controller.editByUser(type);
  };
  return (
    <div className="module_drawer-modify-form-theme-share">
      <FormComp controller={controller.getFormCompController()}>
        <LinkButton
          className="module_drawer-modify-form-theme-share-edit-btn"
          onClick={() => onClick(FormDataAttrs.USERS)}
        >
          {i18n.chain.proMicroModules.themeShare.editUser}
        </LinkButton>
        <FormField
          name={FormDataAttrs.USERS}
          label={i18n.chain.proMicroModules.themeShare.username}
          required={true}
          direction="column"
          rules={[{ required: true, message: i18n.chain.proMicroModules.themeShare.usernameEmptyError }]}
        >
          <TagList type={FormDataAttrs.USERS} />
        </FormField>
        <LinkButton
          className="module_drawer-modify-form-theme-share-edit-btn"
          onClick={() => onClick(FormDataAttrs.THEMES)}
        >
          {i18n.chain.proMicroModules.themeShare.editTheme}
        </LinkButton>
        <FormField
          name={FormDataAttrs.THEMES}
          label={i18n.chain.proMicroModules.themeShare.themeVisible}
          direction="column"
        >
          <TagList />
        </FormField>
        <LinkButton
          className="module_drawer-modify-form-theme-share-edit-btn"
          onClick={() => onClick(FormDataAttrs.APPS)}
        >
          {i18n.chain.proMicroModules.themeShare.editOrgs}
        </LinkButton>
        <FormField
          name={FormDataAttrs.APPS}
          label={i18n.chain.proMicroModules.themeShare.orgsVisible}
          direction="column"
        >
          <TagList />
        </FormField>
        {/* <FormField name={FormDataAttrs.SHOW_DATAPKG_WITH_NO_ALBUMS} label="" valuePropName="checked" direction="column">
          <CheckBox>展示不属于任何主题库的数据包</CheckBox>
        </FormField> */}
      </FormComp>
      <ModalWithBtnsCompDialog controller={controller.getEditUserController()} />
      <ModalWithBtnsCompDialog controller={controller.getUserSelectorController()} />
    </div>
  );
}
