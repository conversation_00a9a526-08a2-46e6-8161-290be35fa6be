import { cn as microCn } from '@mdtProMicroModules/languages';

const cn = {
  ...microCn,
  datamarket: {
    menu: {
      dashboard: '数据看板',
      smartSearch: '智能搜索',
      allData: '全部数据',
      selfApp: '当前机构',
      dataSearch: '数据搜索',
      share: '共享到外部机构',
      metaData: '元数据模板管理',
      template: '数据包文档',
      asset: '元数据管理',
      approval: '数据审批',
      approvalApp: '本机构',
      approvalOther: '其它机构',
    },
    pkgSearch: {
      dataTag: '数据标签',
      searchTag: '搜索标签',
      ownerApp: '本机构',
    },
    dashboard: {
      app: '机构',
      theme: '主题库',
      tagGroup: '标签组',
      tag: '标签',
      allData: '全部数据',
      myApp: '我的机构',
      uncategroizedDatapkd: '未分类数据包',
      indicatorCard: '指标卡',
      leaderboard: '排行榜',
      accumulation: '累积',
      day7: '最近7天',
      day30: '最近30天',
      实时: '实时',
      数据包个数: '数据包个数',
      数据总行数: '数据总行数',
      主题库个数: '主题库个数',
      标签组个数: '标签组个数',
      标签个数: '标签个数',
      ETL任务个数: 'ETL任务个数',
      数据质量监控个数: '数据质量监控个数',
      图表个数: '图表个数',
      地图个数: '地图个数',
      可视化组件被调用次数: '可视化组件被调用次数',
      数据包被浏览次数: '数据包被浏览次数',
      数据包被使用次数: '数据包被使用次数',
      数据包被直接使用次数: '数据包被直接使用次数',
      数据包使用次数: '数据包使用次数',
      应用页面数: '应用页面数',
      应用调用次数: '应用调用次数',
      最近创建数据包: '最近创建数据包',
      最近更新数据包: '最近更新数据包',
      statistics: '统计指标',
      groupMap: '导览图',
      'T+1': 'T+1',
    },
    pkg: {
      appSetting: '机构设置',
      shareDesc: '只允许将本机构的数据共享到外部机构。',
      dataSize: '数据量',
      lastUpdate: '上次更新',
      copyLink: '复制链接',
      copyLinkSuccess: '复制链接成功',
      publicLinkShare: '公开链接分享',
      noAuth: '无权限',
      dataApply: '数据申请',
      pkgEmptyError: '数据包不存在或其他原因导致无法请求到数据',
      pkgLoading: '数据详情加载中',
      basic: '基本概览',
      data: '表格预览',
      column: '字段属性',
      desc: '数据包说明',
      genealogy: '血缘图',
      read: '使用权限',
      readDesc: '可使用此数据制作图表、地图',
      viewDetail: '检索权限',
      viewDetailDesc: '可对数据内容进行排序、筛选操作或在低码ETL中作为数据输入',
      download: '下载权限',
      downloadDesc: '可下载数据到本地',
    },
    apply: '申请',
    hasApply: '已获取',
    applying: '申请中',
    emptyContent: 'App不存在或未授权',
    themeEmptyTip: '主题库不存在或未授权',
  },
};

export type Locale = typeof cn;
export default cn;
