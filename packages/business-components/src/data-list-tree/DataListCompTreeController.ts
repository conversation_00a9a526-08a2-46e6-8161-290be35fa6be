import { DataNode, TreeProps } from '@mdtDesign/tree';
import { DataListCompController, IControllerOptions } from '../data-list-comp';

export type ITreeProps = (() => TreeProps) | TreeProps;

class DataListCompTreeController extends DataListCompController<ITreeProps, DataNode> {
  public constructor(options: IControllerOptions<ITreeProps, DataNode>) {
    super(options);
  }
}

export { DataListCompTreeController };
