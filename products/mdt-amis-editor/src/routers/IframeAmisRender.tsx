import React from 'react';
import { useCreation } from 'ahooks';
import { getAmisJsonFromSession } from '@mdtProMicroModules/amis-expand/amisUtil';
import { AmisRender } from '@mdtProMicroModules/amis-expand-prod/AmisRender';

const AmisRenderView = () => {
  const schame = useCreation(() => {
    return getAmisJsonFromSession();
  }, []);

  return <AmisRender schema={schame as any} />;
};

export default AmisRenderView;
