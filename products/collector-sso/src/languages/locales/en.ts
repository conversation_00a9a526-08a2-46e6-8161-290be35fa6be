import dayjs from 'dayjs';
import 'dayjs/locale/en';
import { Locale } from './cn';
dayjs.locale('en');

const en: Locale = {
  // api
  api: {
    0: 'Network timeout',
    400: 'Bad Request',
    500: 'Internal Server Error',
    502: 'Bad Gateway',
    503: 'Service Unavailable',
    504: 'Gateway Timeout',
  },
  rc: {
    1005: 'SMS service error',
    1011: 'Account does not exist or password error',
    1012: 'Account expired',
    1013: 'Phone is unregistered',
    1014: 'Verification code request too frequent',
    1015: 'Verification code error or expired',
    1016: 'WeChat verification error',
    1017: 'WeChat verification code error or failed',
    1018: 'Illegal WeChat login',
    1020: 'WeChat account registered',
    1026: 'Verification code sent failed, invalid phone number',
    1050: [
      'The {{times}} time login failed, please login in again wait for {{timedelta}}',
      {
        times: 0,
        timedelta: (value = 0) => {
          return dayjs.duration(value, 's').humanize();
        },
      },
    ],
    1051: [
      'User is locked, login in after {{timedelta}}',
      {
        timedelta: (value = 0) => {
          return dayjs.duration(value, 's').humanize();
        },
      },
    ],
    1077: 'Verification Code Error',
    3002: 'WeChat API error',
    3003: 'Known Ali cloud message authentication code interface error',
    3004: 'Unknown Ali cloud message authentication code interface error',
  },
  // languages
  cn: '简体中文',
  en: 'English',
  // tabs
  loginByPhone: 'SMS',
  loginByWechat: 'WeChat',
  loginByAccount: 'Account',
  loginByDingtalk: 'Dingtalk',
  loginByDatlas: 'Datlas',
  loginByDingtalkOAuth: 'Dingtalk OAuth',
  // 通用
  buttonLabel: 'Login',
  buttonOkLabel: 'OK',
  buttonCancelLabel: 'Cancel',
  buttonUpdateLabel: 'Save',
  buttonLoadingLabel: 'Logging in...',
  loginSuccess: 'Login successful',
  // 标题
  loginTitle: 'Collector Manager',
  // 手机登录相关
  phoneLabel: 'Phone Number',
  phonePlaceholder: 'Please enter your phone number',
  phoneVerifyEmpty: 'Phone number cannot be empty',
  phoneVerifyInvalid: ['Invalid phone number(example: {{example}})', { example: '' }],
  captchaLabel: 'Verification Code',
  captchaPlaceholder: 'Please enter verification code',
  captchaButtonLabel: 'Get Code',
  captchaSendedTip: 'Verification code has sent',
  captchaVerifyEmpty: 'Verification code cannot be empty',
  captchaVerifyInvalid: 'Please enter 6-digit code(example: 123456)',
  captchaButtonSendedLabel: ['Resend after({{gap}}s)', { gap: 0 }],
  accountVerify: 'Username can contain Chinese characters/letters/numbers/underscores',
  passwordVerify: 'Password must contain letters and numbers and be at least 6 characters long',
  // 忘记密码相关
  pfTitle: 'Reset your password',
  pfDesc: 'Reset your password by phone number/email',
  accountLabel: 'Phone Number/Email',
  accountPlaceholder: 'Please input...',
  accountVerifyEmpty: 'Phone number/Email cannot be empty',
  accountEmpty: 'Account is not exist',
  accountVerifyInvalid: 'Invalid phone number/email',
  newPasswordLabel: 'New Password',
  newPasswordError:
    'At least 8 characters, including at least three types of uppercase letters, lowercase letters, numbers, and special characters',
  passwordConfirmLabel: 'Password confirmed',
  passwordConfirmError: 'Inconsistent password entered twice',
  passwordChangeSuccess: 'Change your password success',
  // 微信登录相关
  wechatSuccessContent: 'Scanned successfully, please confirm this scanning',
  wechatFailContent: 'Login canceled, scan again to login/bind',
  wechatStatusContent: 'Scan to login/bind in using WeChat',
  // 账号登录相关
  nameLabel: 'Account',
  namePlaceholder: 'Please enter your account',
  nameVerifyEmpty: 'Account cannot be empty',
  passwordLabel: 'Password',
  passwordPlaceholder: 'Please enter your password',
  passwordVerifyEmpty: 'Password cannot be empty',
  passwordForget: 'Password forget',
  accountErrorTip: 'Password entered incorrectly for the third time',
  accountErrorTipDesc:
    'If you enter the wrong password again, the account will be locked. You can retrieve the password by yourself through your mobile phone number or email; if you have not linked your mobile phone number or email, please notify the institution user administrator in writing to help reset the password.',
  // 双因子登录
  doubleCheckTitle: 'Double Check',
  doubleCheckDesc:
    'Administrator has set up login protection, you need to verify again. We have sent a captcha to your mobile phone',
  doubleCheckOk: 'Check',
  // 协议
  privacyText: 'I have read and accept the',
  privacy1: 'Terms of Service',
  privacySlot: 'and',
  privacy2: 'Privacy Policy',
  privacyDesc: ['In order to better protect your legal rights, please read and agree{{str}}', { str: '' }],
  privacyOk: 'I Agree',
  privacyCancel: 'Disagree',
  // 认证
  verifySuccess: 'Validated successfully, page forwarding…',
  verifyChecking: 'Validating, please wait...',
  verifyFailed: 'Authentication failed, please contact administrator!',
  verifyFailedJumpTip: ['{{count}}s after will forward to the login page.', { count: 0 }],
  verifySlideSuccess: 'Verified!',
  verifyPlaceholder: 'Slide block right',
  verifyTitle: 'Security Check',
  verifyRetryText: 'Retry',
};

export default en;
