import { FC } from 'react';
import { Field, ObjectField, observer } from '@formily/react';
import { PkgActionEnum, RequestTypeEnum } from '@mdtProMicroModules/amis-expand/constants';
import i18n from '../../../../languages';
import { ColumnSelect } from './ColumnSelect';

const ACTION_NAME = 'action';
export const DataPkg: FC<{ parentName: string }> = observer(({ parentName }) => {
  return (
    <ObjectField
      name="datapkg"
      reactions={(field) => {
        const type = field.query(`..${parentName}`).get('value');
        field.setState({ display: type === RequestTypeEnum.DATAPKG ? 'visible' : 'hidden' });
      }}
    >
      <Field
        required
        name="pkgId"
        title={i18n.chain.selectDataPkg}
        decorator={['FormItem']}
        component={['PkgSelect', { auth: 'update' }]}
      />
      <Field
        required
        name={ACTION_NAME}
        title={i18n.chain.actionSettings}
        initialValue={PkgActionEnum.QUERY}
        decorator={['FormItem']}
        component={['Select', { placeholder: i18n.chain.selectAction }]}
        dataSource={[
          { label: i18n.chain.queryData, value: PkgActionEnum.QUERY },
          { label: i18n.chain.addData, value: PkgActionEnum.ADD },
          { label: i18n.chain.updateData, value: PkgActionEnum.UPDATE },
          { label: i18n.chain.deleteData, value: PkgActionEnum.DELETE },
        ]}
      />
      <ObjectField name="request_params">
        <UpdateSetting parentName={ACTION_NAME} />
        <UpdateDelete parentName={ACTION_NAME} />
      </ObjectField>
    </ObjectField>
  );
});

const UpdateSetting: FC<{ parentName: string }> = observer(({ parentName }) => {
  return (
    <ObjectField
      name="update"
      reactions={(field) => {
        const action = field.query(`..${parentName}`).get('value');
        field.setState({ display: action === PkgActionEnum.UPDATE ? 'visible' : 'hidden' });
      }}
    >
      <Field
        name="uniqueKey"
        title={i18n.chain.jsonComp.uniqueKey}
        decorator={['FormItem']}
        component={[ColumnSelect]}
        reactions={(field) => {
          const pkgId = field.query(`...pkgId`).get('value');
          field.componentProps = {
            ...field.componentProps,
            pkgId,
          };
        }}
      />
    </ObjectField>
  );
});

const UpdateDelete: FC<{ parentName: string }> = observer(({ parentName }) => {
  return (
    <ObjectField
      name="delete"
      reactions={(field) => {
        const action = field.query(`..${parentName}`).get('value');
        field.setState({ display: action === PkgActionEnum.DELETE ? 'visible' : 'hidden' });
      }}
    >
      <Field
        name="uniqueKey"
        title={i18n.chain.jsonComp.uniqueKey}
        decorator={['FormItem']}
        component={[ColumnSelect]}
        reactions={(field) => {
          const pkgId = field.query(`...pkgId`).get('value');
          field.componentProps = {
            ...field.componentProps,
            pkgId,
          };
        }}
      />
    </ObjectField>
  );
});
