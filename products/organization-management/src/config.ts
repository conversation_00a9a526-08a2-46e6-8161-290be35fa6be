import { initCommConfig } from '@mdtProMicroModules/datlas/datlasConfig';

const cfs = initCommConfig('organization-management', {
  isDevelop: __IS_DEVELOPMENT,
  developProxyApiUrl: __DEVELOP_PROXY_API_URL,
  developEnvOrigin: __DEVELOP_ENV_ORIGIN,
});
__webpack_public_path__ = cfs.deployPublicPath;

export * from '@mdtProMicroModules/datlas/datlasConfig';
export const UPLOAD_LOGO_MAX_KB = cfs.uploadLogoMaxKb || 500;
