import { drawerApi } from '@metroDesign/drawer';
import { BehaviorSubject } from 'rxjs';
import { RequestController } from '@mdtBsControllers/request-controller';
import type { IEmptyFunc } from '@mdtProComm/interfaces';
import { DatlasAppController } from '../../datlas/app/DatlasAppController';
import {
  IReportInfo,
  OneTableReportCreate,
  OneTableReportCreateController,
  OneTableReportCreateModelBff,
} from '../one-table-report-create';
import type { IOneTableReportTemplateListModel } from './OneTableReportTemplateListModelBff';

export interface ITemplateData {
  id: string;
  name: string;
  time: string;
}

export interface IOneTableReportTemplateListControllerOptions {
  Model: IOneTableReportTemplateListModel;
  onSuccess: IEmptyFunc;
}

export class OneTableReportTemplateListController extends RequestController {
  public loading$ = new BehaviorSubject(true);
  public list: ITemplateData[] = [];
  private Model: IOneTableReportTemplateListModel;
  private onSuccess: IEmptyFunc;

  public constructor({ Model, onSuccess }: IOneTableReportTemplateListControllerOptions) {
    super();
    this.Model = Model;
    this.onSuccess = onSuccess;
    this.init();
  }

  public destroy() {
    super.destroy();
  }

  public handleCreate(reportInfo: IReportInfo) {
    drawerApi.open({
      width: '100%',
      className: 'drawer-report-create',
      okButtonProps: { style: { display: 'none' } },
      cancelButtonProps: { style: { display: 'none' } },
      closable: true,
      children: (onClose) => {
        const ctrl = new OneTableReportCreateController(
          {
            Model: OneTableReportCreateModelBff,
            app: DatlasAppController.getInstance(),
            isCreate: true,
            isCreateFromTemplate: false,
            reportInfo,
          },
          () => {
            this.onSuccess();
            onClose();
          },
          onClose,
        );
        return <OneTableReportCreate controller={ctrl} />;
      },
    });
  }

  public handleUse(tmpl: ITemplateData) {
    drawerApi.open({
      width: '100%',
      className: 'drawer-report-create',
      okButtonProps: { style: { display: 'none' } },
      cancelButtonProps: { style: { display: 'none' } },
      closable: true,
      children: (onClose) => {
        const ctrl = new OneTableReportCreateController(
          {
            Model: OneTableReportCreateModelBff,
            app: DatlasAppController.getInstance(),
            isCreate: true,
            isCreateFromTemplate: true,
            wfSpecId: tmpl.id,
          },
          () => {
            this.onSuccess();
            onClose();
          },
          onClose,
        );
        return <OneTableReportCreate controller={ctrl} />;
      },
    });
  }

  // public handlePreview(tmpl: ITemplateData) {
  //   console.log(tmpl);
  // }

  private init() {
    this.Model.queryTemplateList().subscribe((val) => {
      this.list = val;
      this.loading$.next(false);
    });
  }
}
