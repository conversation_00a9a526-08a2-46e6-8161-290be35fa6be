import {
  BaseBffService,
  IQueryIndexItemListOptions,
  IQueryNodeItemOptions,
  IQuerySortItemListOptions,
  queryIndexItemList,
  queryNodeItem,
  querySortItemList,
} from '@mdtBsBffServices/services';

export class DMStatisticBffService extends BaseBffService {
  public queryIndexItemList(options: IQueryIndexItemListOptions) {
    return queryIndexItemList(this.productName, options);
  }

  public querySortItemList(options: IQuerySortItemListOptions) {
    return querySortItemList(this.productName, options);
  }

  public queryNodeItem(options: IQueryNodeItemOptions) {
    return queryNodeItem(this.productName, options);
  }
}
