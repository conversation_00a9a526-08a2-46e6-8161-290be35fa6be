import _ from 'lodash';
import { History } from 'history';
import urlParameterAppend from 'url-parameter-append';
import { IframeChannelController } from '@mdtBsControllers/iframe-channel-controller';
import { RequestClass } from '@mdtProSso/request';
import {
  API_URL,
  API_URL_BASE_PATH,
  ENCODE_APIS,
  ENCODE_APIS_OPTIONS,
  PROXY_URL,
  SITE_ORIGIN,
  TRANSFORM_API_METHOD,
} from '../config';
import { RoutePathEnum } from '../constants/enum';
import { ILoginUser } from '../interface';

/**
 * 使用单例模式
 */
class AppController {
  // 全局私有变量
  private static instance: AppController | undefined;
  // 获取实例
  public static getInstance(history?: History) {
    if (!AppController.instance) {
      AppController.instance = new AppController(history!);
    }
    return AppController.instance;
  }
  // 销毁单例
  public static destroy() {
    AppController.instance?.destroy();
    AppController.instance = undefined;
  }
  // 获取路径
  public static getVerifyPath() {
    return AppController.instance?.getVerifyPath();
  }

  public static gotoChoose(result: ILoginUser, rc?: number) {
    if (!_.isEmpty(result.identities)) {
      AppController.instance?.gotoChooseIdentity(result.auth);
      return;
    }
    AppController.instance?.gotoChooseApp(result.auth, rc);
  }

  public static gotoChooseByInfo(token: string, args: ILoginUser, rc?: number) {
    if (_.isEmpty(args.identities)) {
      return this.gotoChooseAppByInfo(token, args.apps, rc);
    }
    return this.gotoChooseIDentityByInfo(token, args.identities);
  }

  // 不登录跳转
  public static gotoChooseAppByInfo(token: string, apps: ILoginUser['apps'], rc?: number) {
    return AppController.instance?.gotoChooseAppByInfo(token, apps, rc);
  }

  // 不登录跳转身份选择
  public static gotoChooseIDentityByInfo(token: string, identities: ILoginUser['identities']) {
    return AppController.instance?.gotoChooseIdentityByInfo(token, identities);
  }

  public static goPrivateRedirect(url: string) {
    window.location.replace(url);
  }

  private history: History;
  private fetchRequest: RequestClass;
  private iframeController: IframeChannelController;
  private constructor(history: History) {
    this.history = history;
    this.fetchRequest = new RequestClass({
      encodeApis: ENCODE_APIS,
      encodeApisOptions: ENCODE_APIS_OPTIONS,
      transformApiMethod: TRANSFORM_API_METHOD,
      baseUrl: API_URL + API_URL_BASE_PATH,
      proxyUrl: PROXY_URL,
    });
    this.iframeController = new IframeChannelController();
    this.iframeController.subscribe('custom_config', (data) => {
      this.reloadToSetJsonDataToStorage(data);
    });
  }

  public destroy() {
    this.fetchRequest.destroy();
    this.history.length = 0;
  }

  public getFetchRequest() {
    return this.fetchRequest;
  }

  public getVerifyPath() {
    const path = this.history.createHref({ pathname: RoutePathEnum.VERIFY });
    const url = new URL(path, SITE_ORIGIN);
    return url.toString();
  }

  public gotoChooseAppByInfo(token: string, apps: ILoginUser['apps'], rc?: number) {
    const sessionKey = 'mdt-login-apps';
    // 约定通过session存下app信息
    sessionStorage.setItem(sessionKey, JSON.stringify(apps));
    this.gotoChooseApp(token, rc);
  }

  public gotoChooseIdentityByInfo(token: string, identities: ILoginUser['identities']) {
    const sessionKey = 'mdt-login-identities';
    // 约定通过session存下app信息
    sessionStorage.setItem(sessionKey, JSON.stringify(identities));
    this.gotoChooseIdentity(token);
  }

  // 有app的信息则代表当前的app已过期
  public gotoChooseApp(token: string, rc?: number) {
    let chooseAppPath = this.getChooseAppPath(token);
    if (rc === 1040) chooseAppPath = urlParameterAppend(chooseAppPath, 'currentAppType', 'disabled');
    window.top!.location.href = chooseAppPath;
  }

  public gotoChooseIdentity(token: string) {
    window.top!.location.href = this.getChooseIdentityPath(token);
  }

  public goPrivateRedirect(url: string) {
    window.location.replace(url);
  }

  private getChooseAppPath(token: string) {
    const path = this.history.createHref({
      pathname: `choose-app/${token}`,
      search: window.location.search,
    });
    const url = new URL(path, SITE_ORIGIN);
    return url.toString();
  }

  private getChooseIdentityPath(token: string) {
    const path = this.history.createHref({
      pathname: `choose-identity/${token}`,
      search: window.location.search,
    });
    const url = new URL(path, SITE_ORIGIN);
    return url.toString();
  }

  private reloadToSetJsonDataToStorage(jsonData: any) {
    sessionStorage.setItem('custom_config_key', JSON.stringify(jsonData));
    window.location.reload();
  }
}

export { AppController };
