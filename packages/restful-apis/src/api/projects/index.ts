import { PROJECTS_URL } from '../../config';
import { IApiResponse, IProject, IProjectsQuery, IRequestPromise, IRequestRequestConfig } from '../../interfaces';
import request from '../../request';

// 查询项目列表
export const queryProjects = (config?: IRequestRequestConfig<IProjectsQuery>) => {
  return request({
    ...(config || {}),
    method: 'get',
    url: `${PROJECTS_URL}`,
  }) as IRequestPromise<IApiResponse<IProject[]>>;
};
