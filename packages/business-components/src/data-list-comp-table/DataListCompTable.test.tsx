import _ from 'lodash';
import { faker } from '@faker-js/faker';
import { render, screen } from '@testing-library/react';
import { DataListCompTable } from './DataListCompTable';
import { DataListCompTableController } from './DataListCompTableController';

const controllers: DataListCompTableController[] = [];
let controller: DataListCompTableController;

const mockData: any[] = [];
for (let i = 0; i < 100; i++) {
  mockData.push({ id: i, animal: faker.animal.bear() });
}

const EmptyView = () => <div>暂无数据</div>;

describe('DataListCompTableTest', () => {
  describe('默认样式', () => {
    beforeAll(() => {
      controller = new DataListCompTableController({
        dataListControllerOptions: {
          pageSize: 30,
        },
        compOptions: {
          columns: [],
        },
        EmptyView: EmptyView,
      });
      controllers.push(controller);
    });
    test('正确渲染', () => {
      render(<DataListCompTable controller={controller} />);
      expect(screen.getByText('暂无数据')).toBeVisible();
    });
  });
  describe('测试', () => {
    beforeAll(() => {
      controller = new DataListCompTableController({
        dataListControllerOptions: {},
        compOptions: {
          columns: [],
        },
      });
      controllers.push(controller);
      controller.initDataDirectly([100, mockData]);
    });
    test('dom应该是10', async () => {
      render(<DataListCompTable controller={controller} />);
      expect(await screen.queryAllByTestId('data-rowindex').length).toBeLessThan(100);
    });
  });
});

afterAll(() => {
  _.forEach(controllers, (it) => {
    it.destroy();
  });
  controllers.length = 0;
});
