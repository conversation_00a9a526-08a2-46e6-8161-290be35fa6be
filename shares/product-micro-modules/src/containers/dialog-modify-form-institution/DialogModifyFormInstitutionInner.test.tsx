import _ from 'lodash';
import { render, screen } from '@testing-library/react';
// import { screen as screenEvent } from 'query-extensions';
import { Observable } from 'rxjs';
import { IBusinessResult } from '@mdtProComm/interfaces';
import { DialogModifyFormInstitutionController, IControllerOptions } from './DialogModifyFormInstitutionController';
import { DialogModifyFormInstitutionInner } from './DialogModifyFormInstitutionInner';

const controllers: DialogModifyFormInstitutionController<any>[] = [];
let controller: DialogModifyFormInstitutionController<any>;

const options: IControllerOptions<any> = {
  modifyDataFunc: (params: any, originalData?: any) => {
    console.log('originalData: ', originalData);
    console.log('params: ', params);
    return new Promise<IBusinessResult<void>>((resolve) => {
      resolve({ success: true });
    });
  },
  queryNameFunc: (id: string) => {
    console.log('id: ', id);
    return new Observable<string>((observable) => {
      observable.next(id);
    });
  },
  selfAppId: 0,
};

describe('DialogModifyFormInstitutionControllerTest', () => {
  describe('测试默认状态下行为', () => {
    beforeAll(() => {
      controller = new DialogModifyFormInstitutionController(options);

      controllers.push(controller);
    });

    test('正确渲染', () => {
      render(<DialogModifyFormInstitutionInner controller={controller} />);
      expect(screen.getByText('机构ID')).toBeVisible();
    });
    // test('input添加数据', () => {
    //     render(<DialogModifyFormInstitutionInner controller={controller} />)
    //     fireEvent.input(screen.getAllByPlaceholderText(/请输入机构ID获取名称/)[0], { target: { value: 'chuck' } })
    //     fireEvent.input(screen.getAllByPlaceholderText(/请输入机构ID/)[1], { target: { value: 1 } })
    //     controller.getFormCompController().changeFormDataError(false)
    //     console.log(controller.getFormCompController().dataHasError());
    //     controller.clickOkBtn()

    // })
  });
});

afterAll(() => {
  _.forEach(controllers, (it) => {
    it.destroy();
  });
  controllers.length = 0;
});
