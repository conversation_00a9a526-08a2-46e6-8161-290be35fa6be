import {
  BaseBffService,
  IQueryDatapkgColumnListOptions,
  IQueryDatapkgDetailOptions,
  IQueryDatapkgGenealogyListOptions,
  IQueryDatapkgListOptions,
  IQueryDatapkgPreviewDataOptions,
  IQueryDatapkgRowsOptions,
  IUpdateDatapkgListMetaOptions,
  queryDatapkgColumnList,
  queryDatapkgDetail,
  queryDatapkgGenealogyList,
  queryDatapkgPreviewData,
  queryDatapkgRowsFirstPage,
  queryDatapkgRowsNextPage,
  queryDatapkgsFirstPage,
  queryDatapkgsNextPage,
  updateDatapkgListMeta,
} from '@mdtBsBffServices/services';

// todo remove extends, 请求改为枚举,以页面形式组织代码
export class DMDatapkgBffService extends BaseBffService {
  public queryDatapkgs(options: IQueryDatapkgListOptions) {
    return options.isFirstPage
      ? queryDatapkgsFirstPage(this.productName, options)
      : queryDatapkgsNextPage(this.productName, options);
  }

  public queryDatapkgDetail(options: IQueryDatapkgDetailOptions) {
    return queryDatapkgDetail(this.productName, options);
  }

  public queryDatapkgColumnList(options: IQueryDatapkgColumnListOptions) {
    return queryDatapkgColumnList(this.productName, options);
  }

  public queryDatapkgPreviewData(options: IQueryDatapkgPreviewDataOptions) {
    return queryDatapkgPreviewData(this.productName, options);
  }

  public queryDatapkgRows(options: IQueryDatapkgRowsOptions) {
    return options.isFirstPage
      ? queryDatapkgRowsFirstPage(this.productName, options)
      : queryDatapkgRowsNextPage(this.productName, options);
  }

  public queryDatapkgGenealogyList(options: IQueryDatapkgGenealogyListOptions) {
    return queryDatapkgGenealogyList(this.productName, options);
  }

  public updateDatapkgListMeta(options: IUpdateDatapkgListMetaOptions) {
    return updateDatapkgListMeta(this.productName, options);
  }
}
