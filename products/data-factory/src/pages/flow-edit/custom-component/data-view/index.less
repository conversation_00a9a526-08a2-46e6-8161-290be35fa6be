.dataView {
  position: absolute;
  display: flex;
  flex-direction: column;
  width: 100%;
  max-height: 400px;
  padding: 2px 5px;
  overflow: auto;
  background-color: var(--dmc-page-header-bg-color);
}

.viewHead {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 50px;
  padding: 5px;
  font-size: 20px;
  line-height: 30px;

  .viewHeadIcon {
    display: flex;

    .setLimitBox {
      margin-right: 15px;
      font-size: 12px;
      line-height: 1.5;

      a {
        color: var(--dmc-blue-600-color);
        cursor: pointer;
      }
    }

    .icon {
      width: 30px;
      height: 30px;
      border: 1px solid #000;
      cursor: pointer;
    }
  }
}

.viewBody {
  display: 'flex';
  width: 100%;
  height: 345px;
}
