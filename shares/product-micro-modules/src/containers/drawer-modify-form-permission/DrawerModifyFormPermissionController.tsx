import { FC } from 'react';
import { combineLatest, from, of } from 'rxjs';
import { Observable } from 'rxjs/internal/Observable';
import { distinctUntilChanged, map } from 'rxjs/operators';
import { FormCompController } from '@mdtBsComponents/form-comp';
import {
  IBusinessResult,
  IDrawerHeaderProps,
  IDynamicOptions,
  IInnerView,
  ModalWithBtnsCompDrawerController,
} from '@mdtBsComponents/modal-with-btns-comp-drawer';
import i18n from '../../languages';
import { CheckboxPermissionListController } from '../checkbox-permission-list';
import { DrawerModifyFormPermissionInner } from './DrawerModifyFormPermissionInner';

export type ILoadDrawerUiDataFunc<U> = () => Observable<U>;
export type IInitInnerControlerFunc<U, V> = (
  modalData: U,
  rest?: V,
) => [FormCompController<V>, CheckboxPermissionListController];
export type IModifyDataFunc<F, V> = (params: F, originalData?: V) => Promise<IBusinessResult<V>>;

export interface IControllerOptions<U, F, V> {
  loadDrawerUiDataFunc: ILoadDrawerUiDataFunc<U>;
  modifyDataFunc: IModifyDataFunc<F, V>;
  FormInnerView: FC<any>;
  initInnerControlerFunc: IInitInnerControlerFunc<U, V>;
  initDrawerHeaderPropsFunc: IDynamicOptions<IDrawerHeaderProps<any>>;
}

// U-Ui所需的数据模型，F-form的数据模型，V-编辑的数据模型
class DrawerModifyFormPermissionController<U, F, V> extends ModalWithBtnsCompDrawerController<V, U> {
  // 权限组件控制器
  private permissionController?: CheckboxPermissionListController;
  // 表单控制器
  private formCompController?: FormCompController<V>;
  // 加载界面所需要的数据
  private loadDrawerUiDataFunc: ILoadDrawerUiDataFunc<U>;
  // 点击ok按钮回调
  private modifyDataFunc: IModifyDataFunc<F, V>;
  // 初始化inner controller
  private initInnerControlerFunc: IInitInnerControlerFunc<U, V>;
  // 表单的内容
  private FormInnerView?: FC<any>;

  public constructor(options: IControllerOptions<U, F, V>) {
    super({
      modalCompOptions: {
        modalOptions: () => this.initDrawerProps(),
        innerViewController: () => this,
        InnerView: DrawerModifyFormPermissionInner as IInnerView<ModalWithBtnsCompDrawerController<V, U>, V, U>,
        beforeOpenFunc: (rest) => this.initModalData(rest),
      },
      uiOptions: options.initDrawerHeaderPropsFunc,
      clickOkBtnFunc: (originalData) => this.onClickDrawerOkBtn(originalData),
      beforeCancelFunc: () => this.destroyEditInfo(),
    });
    this.modifyDataFunc = options.modifyDataFunc;
    this.loadDrawerUiDataFunc = options.loadDrawerUiDataFunc;
    this.FormInnerView = options.FormInnerView;
    this.initInnerControlerFunc = options.initInnerControlerFunc;
  }

  public destroy() {
    super.destroy();
    this.destroyEditInfo();
    this.modifyDataFunc = this.defaultModifyDataFunc;
    this.loadDrawerUiDataFunc = this.defaultLoadDrawerUiDataFunc;
    this.initInnerControlerFunc = this.defaultInitInnerControlerFunc;
    this.FormInnerView = undefined;
  }

  // 复写父类的方法
  public openModal(rest?: V) {
    this.destroyEditInfo();
    return super.openModal(rest);
  }

  // 复写父类方法
  public closeModal() {
    this.destroyEditInfo();
    super.closeModal();
  }

  public getPermissionController() {
    return this.permissionController!;
  }

  public getFormCompController() {
    return this.formCompController!;
  }

  public getFormInnerView() {
    return (this.FormInnerView || 'div') as FC<any>;
  }

  private destroyEditInfo = async () => {
    this.formCompController?.destroy();
    this.formCompController = undefined;
    this.permissionController?.destroy();
    this.permissionController = undefined;
    return true;
  };

  // 点击弹窗OK按钮
  private onClickDrawerOkBtn = async (originalData?: V) => {
    // 保证代码健壮, 一般都能保证有值
    if (!this.permissionController || !this.formCompController) {
      console.warn(i18n.chain.proMicroModules.permission.editError);
      return { success: false };
    }
    await this.formCompController.validateFormData();
    // 如果有错误，则返回
    if (this.formCompController.dataHasError()) return { success: false };
    const permission = this.permissionController.getSelectedDataValue();
    const formData = this.formCompController.getFormDataValue() || {};
    const params = { ...formData, permission } as unknown as F;
    return this.modifyDataFunc(params, originalData);
  };

  // 弹窗所需信息
  private initDrawerProps = () => {
    return {
      width: '70vw',
    };
  };

  // 获取弹窗所需的数据
  private initModalData = async (rest?: V) => {
    return from(this.loadDrawerUiDataFunc())
      .pipe(
        map((v) => {
          this.initEditInfo(v, rest);
          return v;
        }),
      )
      .toPromise();
  };

  private initEditInfo(modalData: U, rest?: V) {
    // 每次打开弹窗都重新new controller, 快速重置各种状态
    const [fc, pc] = this.initInnerControlerFunc(modalData, rest);
    if (!fc || !pc) return;
    this.formCompController = fc;
    this.permissionController = pc;
    // 根据输入决定确定按钮是否可用
    combineLatest(fc.getFormDataError$(), pc.getSelectedData$())
      .pipe(
        map(([v1, v2]) => v1 || v2.length === 0),
        distinctUntilChanged(),
      )
      .subscribe((v) => {
        this.changeDisabledOkBtn(v);
      });
  }

  // 默认请求数据方法
  private defaultLoadDrawerUiDataFunc() {
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    return of({} as U);
  }

  // 默认初始化方法
  private defaultInitInnerControlerFunc() {
    return [] as unknown as [FormCompController<V>, CheckboxPermissionListController];
  }

  // 默认的修改数据方法
  private async defaultModifyDataFunc() {
    return { success: false };
  }
}

export { DrawerModifyFormPermissionController };
