export interface ILoginUserInfo {
  admin_menu: object;
  detail_menu: object;
  app_id: number;
  id: number;
  name: string;
  permission: Record<string, number[]>;
  role: string[];
  roles: number[];
  wechatbinded: boolean;
  dingtalkbinded: boolean;
  phone: string;
  phone_confirmed: boolean;
  email: string;
  email_confirmed: boolean;
  uuid: string;
  verify_code: string;
  dtverify_code: string;
  expire_time: string;
  expired?: boolean;
}

export interface ILoginApp {
  description: string;
  id: number;
  expire_time: string | null;
  name: string;
  permission: Record<string, number[]>;
  logo?: string;
}

export interface IIdentities {
  app_name: string;
  enable: boolean;
  entity_id: number;
  expired?: boolean;
  id: string;
  app_id: string;
  name: string;
  nickname: string | null;
  is_main?: boolean;
}
export interface ILoginUser {
  auth: string;
  customer: ILoginUserInfo;
  mdt_user: string;
  apps: {
    all: ILoginApp[];
    default: ILoginApp;
  };
  identities?: IIdentities[];
  mdt_product: number[];
}
