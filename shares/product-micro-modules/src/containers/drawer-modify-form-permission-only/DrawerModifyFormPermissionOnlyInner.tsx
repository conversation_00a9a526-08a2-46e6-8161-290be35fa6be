import { CheckboxPermissionList } from '../checkbox-permission-list';
import {
  DrawerModifyFormPermissionOnlyController,
  IPermission,
  IViewData,
} from './DrawerModifyFormPermissionOnlyController';
import './index.less';

// 子组件--基本信息表单================================================================================
interface IProps<F extends IPermission, V extends IViewData> {
  controller: DrawerModifyFormPermissionOnlyController<F, V>;
}

// DrawerModifyRoleInner组件=========================================================================
export function DrawerModifyFormPermissionOnlyInner<F extends IPermission, V extends IViewData>({
  controller,
}: IProps<F, V>) {
  return (
    <div className="module_drawer-modify-form-permission-only">
      <div className="drawer-permission-only_permissions">
        <div className="drawer-permission-only_card">
          <CheckboxPermissionList controller={controller.getPermissionController()} />
        </div>
      </div>
    </div>
  );
}
