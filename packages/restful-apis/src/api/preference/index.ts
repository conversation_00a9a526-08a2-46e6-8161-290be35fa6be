import { PREFERENCES_BTACH, PREFERENCES_SPEC, PREFERENCES_URL } from '../../config';
import {
  IApiResponse,
  IEmptyObj,
  IPreferencesBatchParams,
  IPreferencesBatchQuery,
  IPreferencesBatchRespData,
  IPreferencesData,
  IPreferencesDeleteBatchParams,
  IPreferencesNameQuery,
  IPreferencesPutBatchParams,
  IPreferencesQuery,
  IPreferencesResp,
  IPreferencesSpec,
  IRequestPromise,
  IRequestRequestConfig,
} from '../../interfaces';
import request from '../../request';

// v2
export const getPreferences = (query?: IPreferencesQuery, config?: IRequestRequestConfig) => {
  return request({
    ...(config || {}),
    method: 'get',
    url: PREFERENCES_URL,
    params: query,
  }) as IRequestPromise<IApiResponse<IPreferencesResp>>;
};

// 修改单个偏好
export const putPreferences = (
  query: IPreferencesNameQuery,
  data: IPreferencesData,
  config?: IRequestRequestConfig,
) => {
  const { name, ...rest } = query;
  return request({
    ...(config || {}),
    method: 'put',
    url: `${PREFERENCES_URL}/${name}`,
    params: rest,
    data,
  }) as IRequestPromise<IApiResponse<IEmptyObj>>;
};

// 修改多个偏好
export const putPreferencesBatch = (
  data: IPreferencesBatchParams,
  query?: IPreferencesQuery,
  config?: IRequestRequestConfig,
) => {
  return request({
    ...(config || {}),
    method: 'put',
    url: `${PREFERENCES_URL}`,
    params: query,
    data,
  }) as IRequestPromise<IApiResponse<IEmptyObj>>;
};

// 删除单个偏好
export const deletePreferences = (
  query: IPreferencesNameQuery,
  data: IPreferencesData,
  config?: IRequestRequestConfig,
) => {
  const { name, ...rest } = query;
  return request({
    ...(config || {}),
    method: 'delete',
    url: `${PREFERENCES_URL}/${name}`,
    params: rest,
    data,
  }) as IRequestPromise<IApiResponse<IEmptyObj>>;
};

// 新增偏好定义
export const postPreferencesSpecs = (data: IPreferencesSpec, config?: IRequestRequestConfig) => {
  return request({
    ...(config || {}),
    method: 'post',
    url: `${PREFERENCES_SPEC}`,
    data,
  }) as IRequestPromise<IApiResponse<IEmptyObj>>;
};

// 批量查询App内若干用户偏好
export const getUserPreferencesBatch = (query?: IPreferencesBatchQuery, config?: IRequestRequestConfig) => {
  return request({
    ...(config || {}),
    method: 'get',
    url: PREFERENCES_BTACH,
    params: query,
  }) as IRequestPromise<IApiResponse<IPreferencesBatchRespData[]>>;
};

// 批量修改App内若干用户偏好
export const putUserPreferencesBatch = (
  data: IPreferencesPutBatchParams,
  query?: IPreferencesBatchQuery,
  config?: IRequestRequestConfig,
) => {
  return request({
    ...(config || {}),
    method: 'put',
    url: PREFERENCES_BTACH,
    params: query,
    data,
  }) as IRequestPromise<IApiResponse<IEmptyObj>>;
};

// 批量删除App内若干用户偏好
export const deletePreferencesBatch = (
  data: IPreferencesDeleteBatchParams,
  query?: IPreferencesBatchQuery,
  config?: IRequestRequestConfig,
) => {
  return request({
    ...(config || {}),
    method: 'delete',
    url: PREFERENCES_BTACH,
    params: query,
    data,
  }) as IRequestPromise<IApiResponse<IEmptyObj>>;
};
