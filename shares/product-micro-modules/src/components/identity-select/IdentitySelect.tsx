import { FC } from 'react';
import { ChevronDown } from '@metro/icons';
import { Avatar } from '@metroDesign/avatar';
import { Button } from '@metroDesign/button';
import { Dropdown } from '@metroDesign/dropdown';
import { Flex } from '@metroDesign/flex';
import { Typography } from '@metroDesign/typography';
import { IdentitySelectController } from './IdentitySelectController';
import './index.less';

export const IDentityView: FC<{
  name: string;
  orgName?: string;
  size?: 'small' | 'normal';
  disabled?: boolean;
}> = ({ name, orgName, size = 'normal', disabled }) => {
  return (
    <Flex gap="small" align="center" className={`identity-wrap-${size}`}>
      {size === 'normal' ? <Avatar size="m">{(name || orgName)?.substring(0, 1)}</Avatar> : null}
      <Flex vertical gap={2} align="flex-start">
        <Typography.Text style={{ color: 'unset' }}>{name}</Typography.Text>
        {orgName ? (
          <Typography.Text type="secondary" disabled={disabled}>
            @{orgName}
          </Typography.Text>
        ) : null}
      </Flex>
    </Flex>
  );
};

export const IdentitySelect: FC<{ controller: IdentitySelectController }> = ({ controller }) => {
  const appCtrl = controller.getApp();
  const isDropdown = controller.getIsDropdown();
  const isMain = controller.getIsMain();
  const orgName = appCtrl.getUserOrgName();
  const userName = appCtrl.getUserName();
  const userUuid = isMain ? controller.getMainIdentityKey() : appCtrl.getUserUuid();
  const isImpersonate = !!appCtrl.getImpersonateToken();

  const View = <IDentityView name={userName} orgName={orgName} size="small" disabled={isImpersonate} />;

  if (isDropdown) {
    return (
      <Dropdown
        menu={{ items: controller.generateIdentitiesData(), selectedKeys: [userUuid] }}
        disabled={isImpersonate}
        overlayClassName="identity-overlay-wrap"
      >
        <Button icon={<ChevronDown />} iconDirection="right" focus={false} withoutBorder ghost>
          {View}
        </Button>
      </Dropdown>
    );
  }

  return View;
};
