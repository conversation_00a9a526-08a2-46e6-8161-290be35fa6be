.nodeClass {
  display: flex;
  gap: 2px;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 30px;
  padding: 3px 4px 3px 3px;
  line-height: 30px;
  border-radius: 4px;

  .left {
    display: flex;
    align-items: center;

    span {
      margin-left: 5px;
      font-weight: 500;
      font-size: 13px;
      font-style: normal;
    }
  }

  .rightIcon {
    width: 14px;
    height: 14px;
    margin-right: 0;
    line-height: 14px;
  }
}

.nodeItem {
  width: 100%;
  /* stylelint-disable-next-line scale-unlimited/declaration-strict-value */
  color: #838c9c;
  font-weight: 500;
  font-size: 13px;
  font-style: normal;
}

.node-container {
  display: flex;
  gap: 8px;
  align-items: center;
  justify-content: space-between;
  width: 214px;
  height: 40px;
  padding: 4px 4px 4px 16px;
  background-color: var(--dmc-primary-panel-2);
  border: 1px solid var(--dmc-split-page-color);
  border-left-width: 6px;
  border-left-style: solid;
  border-radius: 6px;
  border-top-left-radius: 6px;
  border-bottom-left-radius: 6px;
  box-shadow: 0 4px 12px -2px #0713270d, 0 3px 3px -2px #0713270a, 0 1px 2px -4px #07132708;
}

.node-label {
  width: 150px;
  height: 18px;
  overflow: hidden;
  color: var(--dmc-text-7);
  font-weight: 500;
  font-size: 13px;
  line-height: 18px;
  white-space: nowrap;
  text-overflow: ellipsis;
}
