import { setGlobalOptions } from 'amis-core';
import { setThemeConfig } from 'amis-editor-core';
import themeConfig from 'amis-theme-editor-helper/lib/systemTheme/cxd';
import { DatlasApp } from '@mdtProMicroModules/datlas/app';
import '@mdtProMicroModules/amis-expand-prod/importStyle';
import { AppController } from './AppController';
import 'amis-editor-core/lib/style.css';
import './app.less';

setThemeConfig(themeConfig);
setGlobalOptions({ pdfjsWorkerSrc: new URL('pdfjs-dist/build/pdf.worker.min.mjs', import.meta.url).toString() });

const App = () => {
  return <DatlasApp getController={(history) => AppController.getInstance(history)} />;
};

export default App;
