const { task, src, parallel, series, dest } = require('gulp');
const through2 = require('through2');
const ts = require('gulp-typescript');
const less = require('gulp-less');
const postcss = require('gulp-postcss');
const nested = require('postcss-nested');
const cssnext = require('postcss-cssnext');
const cssnano = require('cssnano');

const esmProject = ts.createProject('tsconfig.esm.json');
const replaceCodes = [
  { reg: /@mdtDesign\//gm, esm: '@datlas/design/esm/components/' },
  { reg: /@mdtBpmnPropertiesPanel\//gm, esm: '@mdt/bpmn-js-properties-panel/dist/esm/' },
  { reg: /@mdtBsBffServices\//gm, esm: '@mdt/business-bff-services/dist/esm/' },
  { reg: /@mdtBsComm\//gm, esm: '@mdt/business-comm/dist/esm/' },
  { reg: /@mdtBsComponents\//gm, esm: '@mdt/business-components/dist/esm/' },
  { reg: /@mdtBsControllers\//gm, esm: '@mdt/business-controllers/dist/esm/' },
  { reg: /@mdtBsServices\//gm, esm: '@mdt/business-services/dist/esm/' },
  { reg: /@mdtLogin\//gm, esm: '@mdt/login/dist/esm/' },
  { reg: /@mdtApis\//gm, esm: '@mdt/restful-apis/dist/esm/' },
  { reg: /@mdtFormily\//gm, esm: '@mdt/formily/dist/esm/' },
  { reg: /@mdtFormDesign\//gm, esm: '@mdt/form-design/dist/esm/' },
  { reg: /@mdtProComm\//gm, esm: '@mdt/product-comm/dist/esm/' },
  { reg: /@mdtProMicroModules\//gm, esm: '@mdt/product-micro-modules/dist/esm/' },
  { reg: /@mdtProTasks\//gm, esm: '@mdt/product-tasks/dist/esm/' },
  { reg: /@mdtProFormEditor\//gm, esm: '@mdt/product-form-editor/dist/esm/' },
  { reg: /@metroDesign\//gm, esm: '@metro/components/dist/esm/' },
];

const replaceCssFile = (file, code) => {
  if (file.path.match(/\.d.ts$/)) {
    code = code.replace(/import '\.\/.*\.less';[\n\r]/gm, '');
  } else if (file.path.match(/\.js$/)) {
    code = code.replace(/\.less/gm, '.css');
  }
  return code;
}

// esmodule编译
task('esm', function() {
  return esmProject
    .src()
    .pipe(esmProject())
    .pipe(
      through2.obj(function(file, encoding, next) {
        if (file.isBuffer()) {
          let code = file.contents.toString();
          code = replaceCssFile(file, code);
          replaceCodes.forEach(it => {
            code = code.replace(it.reg, it.esm);
          });
          file.contents = Buffer.from(code);
        }
        next(null, file);
      }),
    )
    .pipe(dest('dist/esm'));
});

// 编译less
task('less', function() {
  return src('src/**/*.less')
    .pipe(less())
    .pipe(postcss([nested(), cssnext({ warnForDeprecations: false }), cssnano()]))
    .pipe(dest('dist/esm'));
});

// 复制资源到对应路径
task('copy', function() {
  return src([
    'src/**/*.svg',
    'src/**/*.png',
    'src/**/*.jpg',
    'src/**/*.jpeg',
    'src/**/*.css',
    'src/**/*.json'
  ])
    .pipe(dest('dist/esm/'));
});

task('default', series(parallel('esm', 'less'), 'copy'));
