import { Button } from '@metroDesign/button';
import { BehaviorSubject } from 'rxjs';
import { parseStrToObj } from '@mdtBsComm/utils/stringUtil';
import { IframeChannelController } from '@mdtBsControllers/iframe-channel-controller';
import { DrawerAmisEditorMessageEnum } from '@mdtProComm/constants';
import { AppController } from '../../app/AppController';
import { EditorController } from '../../containers/editor';
import i18n from '../../languages';

export class DrawerEditorController extends EditorController {
  public drawerVisible$ = new BehaviorSubject<boolean>(false);

  public constructor(app: AppController) {
    super(app, {});
  }

  public handleParentMessage = (data: any) => {
    const { payload, type } = data || {};
    if (type === DrawerAmisEditorMessageEnum.OPEN) {
      this.handleValueChange(parseStrToObj(payload));
      this.drawerVisible$.next(true);
    }
  };

  public loadFinish() {
    IframeChannelController.sendMessageToParent(DrawerAmisEditorMessageEnum.LOADED, '');
  }

  public renderExtraBtns = () => {
    return (
      <>
        <Button style={{ marginLeft: 100 }} className="header-action-btn" onClick={this.handleCancel}>
          {i18n.chain.comButton.cancel}
        </Button>
        <Button className="header-action-btn" type="primary" onClick={this.handleUse}>
          {i18n.chain.comButton.use}
        </Button>
      </>
    );
  };

  public destroy() {
    this.drawerVisible$.complete();
  }

  private handleUse = () => {
    const val = this.value$.getValue();
    IframeChannelController.sendMessageToParent(DrawerAmisEditorMessageEnum.SUCCESS, val);
    this.drawerVisible$.next(false);
  };

  private handleCancel = () => {
    IframeChannelController.sendMessageToParent(DrawerAmisEditorMessageEnum.CLOSE, '');
    this.drawerVisible$.next(false);
  };
}
