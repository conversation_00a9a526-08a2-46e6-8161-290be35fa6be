import _ from 'lodash';
import { FC, memo } from 'react';
import classnames from 'classnames';
import { TooltipText } from '@mdtBsComm/components/tooltip-text';
import Checkbox from '@mdtDesign/checkbox';
import Icon from '@mdtDesign/icon';
import i18n from '../../../languages';
import { useTableCardDatapkgSearchContext } from '../tableCardDatapkgSearchContext';
import { IDatapkgItem } from '../TableCardDatapkgSearchModel';
import './index.less';

interface IProps {
  item: IDatapkgItem;
  selected: boolean;
}

const CardItem: FC<IProps> = memo(({ item, selected }) => {
  const { tableCardDatapkgSearchController: controller } = useTableCardDatapkgSearchContext();
  const enableSelect = controller.getEnableSelect();
  const timeLabel = controller.getCardItemTimeLabel();
  const hc = controller.getHiddenCardCount();

  // 名称
  const selectBox = enableSelect ? <Checkbox checked={selected} /> : null;
  const nameBlock = (
    <div className="pkg-search_item-name-wrap" style={{ color: `${item.geoTypeColor}` }}>
      <Icon icon={item.geoTypeIcon} />
      <TooltipText text={item.name} className="pkg-search_item-name" />
      {selectBox}
    </div>
  );

  // app名称和供应商
  const se =
    item.source && item.source !== '-' ? (
      <div className="pkg-search_item-app">
        <Icon icon="supplier" />
        <TooltipText text={item.source} className="pkg-search_app-name" />
      </div>
    ) : null;
  const appBlock = (
    <div className="pkg-search_item-app-wrap">
      <div className="pkg-search_item-app">
        <Icon icon="institution" />
        <TooltipText text={item.appName} className="pkg-search_app-name" />
      </div>
      {se}
    </div>
  );

  // 权限
  const ps = _.size(item.permissonLabels) ? (
    _.map(item.permissonLabels, (it) => (
      <div key={it} className="pkg-search_permissions">
        <Icon icon="success-outlined" />
        {it}
      </div>
    ))
  ) : (
    <div className="pkg-search_permissions">{i18n.chain.proMicroModules.datapkg.noAuth}</div>
  );
  const permissionsBlock = <div className="pkg-search_permissions-wrap">{ps}</div>;

  const ce = hc ? null : (
    <div className="pkg-search_count">
      <Icon icon="layers" />
      <span>{item.count}</span>
    </div>
  );

  // 更新时间和条数
  const updateTimeBlock = (
    <div className="pkg-search_update-time-wrap">
      <div>{`${timeLabel || i18n.chain.comButton.update} ${item.updateTimeDisplay}`}</div>
      {ce}
    </div>
  );

  const cls = classnames('pkg-search_card-item', { 'pkg-search_card-item-selected': selected });
  return (
    <div className={cls} onClick={() => controller.onClickPkgListItem(item, selected)}>
      {nameBlock}
      {appBlock}
      {permissionsBlock}
      {updateTimeBlock}
    </div>
  );
});

export { CardItem };
