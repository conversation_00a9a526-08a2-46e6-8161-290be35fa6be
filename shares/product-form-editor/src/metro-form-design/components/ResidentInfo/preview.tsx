import _ from 'lodash';
import React, { useEffect } from 'react';
import { createBehavior, createResource, ITreeNode, TreeNode } from '@designable/core';
import { DnFC, TreeNodeWidget, useDesigner, useTreeNode } from '@designable/react';
import { Select as FormilySelect } from '../../../metro-formily/index';
import { QuestionIcon } from '../../icons';
import { AllLocales } from '../../locales';
import { AllSchemas } from '../../schemas';
import { createNodeId, MetroSettingProp } from '../../shared';
import { fieldNameToConfigMap, ResidentFieldNameEnum } from '../../utils';
import { createFieldSchema } from '../Field/util';
import './styles.less';

const COMP_NAME = 'ResidentInfo';

export { fieldNameToConfigMap, ResidentFieldNameEnum };

const addIdPrefix = (fieldName: ResidentFieldNameEnum) => `metro_resident_${fieldName}`;

export const isResidentIdCard = (node: ITreeNode) => node.id === addIdPrefix(ResidentFieldNameEnum.ID_CARD);
export const residentIdCardName = addIdPrefix(ResidentFieldNameEnum.ID_CARD);
export const residentNameName = addIdPrefix(ResidentFieldNameEnum.NAME);
export const residentBirthdayName = addIdPrefix(ResidentFieldNameEnum.BIRTHDAY);
export const residentMobileName = addIdPrefix(ResidentFieldNameEnum.MOBILE);
export const residentAddrName = addIdPrefix(ResidentFieldNameEnum.ADDRESS);

export const IS_RESIDENT_COMP_KEY = 'x-is-resident-comp';

export const isResidentChildComp = (nodeProps: any) => _.get(nodeProps, IS_RESIDENT_COMP_KEY);

export const isResidentInfoComp = (schema: any) => _.get(schema, 'x-component') === COMP_NAME;

export const getResidentChildDesignerProps = (node: TreeNode) => {
  if (isResidentChildComp(node.props)) {
    return {
      draggable: false,
      cloneable: false,
      deletable: false,
      droppable: false,
    };
  }
  return {};
};

const generateChildNode = (fieldName: ResidentFieldNameEnum, parentNode: TreeNode) => {
  const config = fieldNameToConfigMap[fieldName];
  return new TreeNode(
    {
      id: addIdPrefix(fieldName),
      componentName: 'Field',
      props: {
        type: 'string',
        title: config.title,
        'x-decorator': 'FormItem',
        'x-component': config.comp,
        [IS_RESIDENT_COMP_KEY]: true,
      },
    },
    parentNode,
  );
};

export const ResidentInfo: DnFC<React.ComponentProps<typeof FormilySelect>> = () => {
  const node = useTreeNode();
  const designer = useDesigner();

  useEffect(() => {
    if (!node) return;

    const clearChildren = () => {
      const children = node.children;
      if (_.isEmpty(children)) return;
      node.setChildren();
    };
    const fieldList = node.props?.[MetroSettingProp.residentFieldList];
    if (_.isEmpty(fieldList)) {
      clearChildren();
    } else {
      const selectedFiledList = _.filter(fieldList, (it) => !_.isEmpty(it.isSelect));
      if (_.isEmpty(selectedFiledList)) {
        clearChildren();
        return;
      }
      const children = node.children;
      const newChildren = _.map(selectedFiledList, (it) => {
        const id = it.fieldName;
        const idWithPrefix = addIdPrefix(id);
        const oldChildren = _.find(children, (child) => child.id === idWithPrefix);
        if (oldChildren) return oldChildren;
        return generateChildNode(id, node);
      });
      node.setChildren(...newChildren);
    }
  }, [node, node.props]);

  return (
    <div className="resident-info-designer">
      {node?.children.map((child: any) => {
        return (
          <div {...createNodeId(designer, child.id)} key={child.id}>
            <TreeNodeWidget key={child.id} node={child} />
          </div>
        );
      })}
    </div>
  );
};

ResidentInfo.Behavior = createBehavior({
  name: COMP_NAME,
  extends: ['Field'],
  selector: (node) => node.props?.['x-component'] === COMP_NAME,
  designerProps: () => {
    return {
      propsSchema: createFieldSchema(AllSchemas.ResidentInfo, { hiddenDefault: true }),
    };
  },
  designerLocales: AllLocales.ResidentInfo,
});

ResidentInfo.Resource = createResource({
  icon: <QuestionIcon comp={COMP_NAME} />,
  elements: [
    {
      componentName: 'Field',
      props: {
        type: 'void',
        'x-decorator': 'FormItem',
        'x-decorator-props': {
          className: 'resident-info-wrap',
        },
        'x-component': COMP_NAME,
      },
    },
  ],
});
