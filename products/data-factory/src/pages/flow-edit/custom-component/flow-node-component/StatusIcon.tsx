import { FC } from 'react';
import Alert from '@mdtDesign/icons/alert';
import Help from '@mdtDesign/icons/help-2';
import Location from '@mdtDesign/icons/location-3';
import ReFresh from '@mdtDesign/icons/refresh-2';
import Success from '@mdtDesign/icons/success';
import ToolTip from '@mdtDesign/tooltip';
import i18n from '../../../../languages';
import { NodeStatusEnum } from '../../_util/enum';

interface IProps {
  status?: NodeStatusEnum;
  statusMsg?: string;
}

const StatusIcon: FC<IProps> = ({ status, statusMsg }) => {
  // 失败
  if (status === NodeStatusEnum.ERROR) {
    return (
      <ToolTip title={statusMsg || i18n.chain.dataFactory.flow.domRunError}>
        <Alert color="#DD505E" />
      </ToolTip>
    );
  }

  // 成功
  if (status === NodeStatusEnum.SUCCESS) {
    return <Success color="#2BB480" />;
  }

  // 运行中
  if (status === NodeStatusEnum.PROCESSING) {
    return <ReFresh className="refresh-spin" />;
  }

  // 运行到此
  if (status === NodeStatusEnum.LOCATION) {
    return (
      <ToolTip title={i18n.chain.dataFactory.flow.domRunEnd}>
        <Location color="#2BB480" />
      </ToolTip>
    );
  }

  // 警告
  if (status === NodeStatusEnum.WARNING) {
    return (
      <ToolTip title={i18n.chain.dataFactory.flow.domNeedParams}>
        <Help color="#F2A70D" />
      </ToolTip>
    );
  }

  return null;
};

export { StatusIcon };
