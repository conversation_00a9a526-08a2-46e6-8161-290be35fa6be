import { useController } from '@mdtBsComm/hooks/use-controller';
import {
  WorkflowApplicationList,
  WorkflowApplicationListController,
  WorkflowApplicationListModel,
} from '@mdtProMicroModules/pages/workflow-application-list';
import { WorkflowApplicationListModelBff } from '@mdtProMicroModules/pages/workflow-application-list/WorkflowApplicationListModelBff';
import { getModel } from '../../_util/modelUtil';

const ApplicationListView = () => {
  const [controller] = useController(() => {
    const ctrl = new WorkflowApplicationListController({
      Model: getModel(WorkflowApplicationListModel, WorkflowApplicationListModelBff),
    });
    return [ctrl, null];
  });

  return <WorkflowApplicationList controller={controller} />;
};

export default ApplicationListView;
