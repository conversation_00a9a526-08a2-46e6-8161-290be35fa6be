import _ from 'lodash';
import { from } from 'rxjs';
import { map, takeWhile } from 'rxjs/operators';
import { ALBUM_PKG_LIST_DATA } from '@mdtBsBffServices/services';
import { DatapkgPermissionEnum } from '@mdtProComm/constants';
import { IAlbumDatapkgIdsQuery, IRequestCancelToken } from '@mdtProComm/interfaces';
import { getDatapkgPermissionLabels } from '@mdtProComm/utils/datapkgUtil';
import { IDatapkgItem } from '@mdtProMicroModules/containers/table-card-datapkg-search';
import { DatapkgSearchModel } from '../../_util/DatapkgSearchModel';
import { albumBffService } from '../../bff-service';
import i18n from '../../languages';

export class AlbumPkgSearchModelBff extends DatapkgSearchModel {
  // 查询下一页数据
  public static queryNextPageTickets(
    albumId: string,
    data: IAlbumDatapkgIdsQuery,
    cancelToken: IRequestCancelToken,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    apps?: string,
  ) {
    return from(
      albumBffService.queryAlbumPkgsNextPage({ albumId, data, cancelToken, respData: ALBUM_PKG_LIST_DATA }),
    ).pipe(
      takeWhile((resp) => !resp.canceled),
      map((resp) => {
        if (!resp.success) return [];
        return _.map(resp.page_data.datapkgs, (item) => ({
          ...item,
          appName: i18n.chain.datamarket.pkgSearch.ownerApp,
          permissonLabels: getDatapkgPermissionLabels(_.difference(item.permissions, [DatapkgPermissionEnum.UPDATE])),
        }));
      }),
    );
  }

  // 查询第一页数据
  public static queryFirstPagePkgs(
    albumId: string,
    data: IAlbumDatapkgIdsQuery,
    cancelToken: IRequestCancelToken,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    apps?: string,
  ) {
    return from(
      albumBffService.queryAlbumPkgsFirstPage({ albumId, data, cancelToken, respData: ALBUM_PKG_LIST_DATA }),
    ).pipe(
      takeWhile((resp) => !resp.canceled),
      map((resp) => {
        if (!resp.success) return [0, []] as [number, IDatapkgItem[]];
        const list = _.map(resp.page_data.datapkgs, (item) => ({
          ...item,
          appName: i18n.chain.datamarket.pkgSearch.ownerApp,
          permissonLabels: getDatapkgPermissionLabels(_.difference(item.permissions, [DatapkgPermissionEnum.UPDATE])),
        }));
        return [resp.total_count, list];
      }),
    );
  }

  // 查询主题库的标签
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  public static queryAllTags(albumId: string, cancelToken: IRequestCancelToken, apps?: string) {
    return from(albumBffService.queryAlbumTags({ albumId, cancelToken })).pipe(
      takeWhile((resp) => !resp.canceled),
      map((resp) => {
        const data = _.map(resp.success ? resp.page_data : [], (it) => ({
          group: it.taggroup_name,
          groupId: it.taggroup_id,
          data: it.taggroup_tags,
        }));
        return [0, data];
      }),
    );
  }
}
