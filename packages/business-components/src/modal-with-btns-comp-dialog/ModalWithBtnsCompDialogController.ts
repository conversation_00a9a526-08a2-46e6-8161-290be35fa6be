import _ from 'lodash';
import { DialogProps } from '@mdtDesign/dialog';
import {
  DialogFooter,
  IControllerOptions as IModalWithBtnsCompControllerOptions,
  IDialogFooterProps,
  ModalTypeEnum,
  ModalWithBtnsCompController,
} from '../modal-with-btns-comp';

export type IDialogProps = Omit<
  DialogProps,
  'visible' | 'okText' | 'cancelText' | 'onOk' | 'okButtonProps' | 'cancelButtonProps'
>;
export type IControllerOptions<UO = any, MO = any, C = any, V = any, MV = any> = Omit<
  IModalWithBtnsCompControllerOptions<UO, MO, C, V, MV>,
  'modalType'
>;

/**
 * C: 内部界面的controller
 * V: 触发弹窗的值
 * MV: 内部界面UI需要的值，支持动态获取，比如下拉框的值
 */
class ModalWithBtnsCompDialogController<C = any, V = any, MV = any> extends ModalWithBtnsCompController<
  IDialogFooterProps,
  IDialogProps,
  C,
  V,
  MV
> {
  public constructor(options: IControllerOptions<IDialogFooterProps, IDialogProps, C, V, MV>) {
    super({
      ...options,
      modalCompOptions: {
        ...options.modalCompOptions,
        modalOptions: () => {
          const preOptions = _.isFunction(options.modalCompOptions.modalOptions)
            ? options.modalCompOptions.modalOptions()
            : options.modalCompOptions.modalOptions;
          return {
            animation: 'zoom',
            maskAnimation: 'fade',
            centered: true,
            onCancel: this.closeModal.bind(this),
            maskClosable: false,
            footer: DialogFooter,
            ...(preOptions || {}),
          };
        },
      },
      modalType: ModalTypeEnum.DIALOG,
      uiOptions: options.uiOptions || (() => this.defaultFooterOptions()),
    });
  }

  private defaultFooterOptions = (): IDialogFooterProps => {
    return { okBtnProps: { type: 'primary' } };
  };
}

export { ModalWithBtnsCompDialogController };
