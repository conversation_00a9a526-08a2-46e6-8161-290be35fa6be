import { FC } from 'react';
import { Spin } from '@metroDesign/spin';
import { useObservableState } from '@mdtBsComm/hooks/use-observable-state';
import { ModalWithBtnsCompDialog } from '@mdtBsComponents/modal-with-btns-comp-dialog';
import { ModalWithBtnsCompEmotion } from '@mdtBsComponents/modal-with-btns-comp-emotion';
import { DrawerModifyFormPermissionUserInfo } from '@mdtProMicroModules/containers/drawer-modify-form-permission-user-info';
import { OrgManageTreeList } from '@mdtProMicroModules/containers/org-manage-tree-list';
import { TableCurdWithSimpleSearch } from '@mdtProMicroModules/containers/table-curd-with-simple-search';
import { DrawerResouceShareOther } from '@mdtProMicroModules/pages/resource-share-other';
import { OrganizationController } from './OrganizationController';
import './index.less';

// 子组件-右侧表格====================================================================================
const RightContent: FC<IProps> = ({ controller }) => {
  const current = useObservableState(controller.getCurrentSelectId$());
  const tableController = controller.getTableController(current);
  const allUserController = controller.getAllUserController();
  const importUserController = controller.getImportUserController();
  const importOrgController = controller.getImportOrgController();
  const statusUserController = controller.getStatusUserController();
  const deleteUserController = controller.getDeleteUserController();
  const removeRoleUserController = controller.getRemoveRoleUserController();
  const addRoleUserController = controller.getAddRoleUserController();
  const resetPwdController = controller.getResetPwdController();
  const unlockUserController = controller.getUnlockUserController();
  const resourceShareController = controller.getResouceShareController();
  return (
    <div className="page_organization-right">
      <TableCurdWithSimpleSearch controller={tableController} />
      <DrawerModifyFormPermissionUserInfo controller={allUserController} />
      <ModalWithBtnsCompDialog controller={resetPwdController} />
      <ModalWithBtnsCompEmotion controller={deleteUserController} />
      <ModalWithBtnsCompEmotion controller={statusUserController} />
      <ModalWithBtnsCompEmotion controller={removeRoleUserController} />
      <ModalWithBtnsCompDialog controller={addRoleUserController} />
      <ModalWithBtnsCompDialog controller={importUserController} />
      <ModalWithBtnsCompDialog controller={importOrgController} />
      <ModalWithBtnsCompDialog controller={unlockUserController} />
      <DrawerResouceShareOther controller={resourceShareController} />
    </div>
  );
};

// 组织与部门页面=======================================================================================
interface IProps {
  controller: OrganizationController;
}
const Organization: FC<IProps> = ({ controller }) => {
  const orgController = controller.getOrgController();
  const orgLoading = useObservableState(controller.getOrgLoading$());

  return (
    <div className="page_organization">
      <Spin spinning={orgLoading} fillParent>
        <OrgManageTreeList controller={orgController} />
      </Spin>
      <RightContent controller={controller} />
    </div>
  );
};

export { Organization };
