/* eslint-disable sonarjs/no-duplicate-string */
/* eslint-disable @typescript-eslint/explicit-member-accessibility */
/**
 * @file Flex 常见布局 二拖一布局
 */
import i18n from '../../../languages';
import { FlexPluginBase } from '../FlexPluginBase';

export default class Layout2_1_v2 extends FlexPluginBase {
  name = i18n.chain.jsonComp.layout2_1_v2;
  isBaseComponent = false;
  pluginIcon = 'layout-2with1-plugin';
  description = i18n.chain.jsonComp.layout2_1_v2Description;
  tags = [i18n.chain.jsonComp.commonLayout];
  order = 305;
  scaffold: any = {
    type: 'flex',
    className: 'p-1',
    items: [
      {
        type: 'flex',
        items: [
          {
            type: 'wrapper',
            size: 'xs',
            body: [],
            style: {
              flex: '1 1 auto',
              flexBasis: 'auto',
              flexGrow: 1,
              display: 'block',
            },
          },
          {
            type: 'wrapper',
            size: 'xs',
            body: [],
            style: {
              flex: '1 1 auto',
              flexBasis: 'auto',
              flexGrow: 1,
              display: 'block',
            },
          },
        ],
        style: {
          flex: '0 0 auto',
          flexBasis: '100px',
        },
        alignItems: 'stretch',
      },
      {
        type: 'wrapper',
        size: 'xs',
        body: [],
        style: {
          flex: '1 1 auto',
          flexBasis: '200px',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'flex-start',
          alignItems: 'stretch',
        },
      },
    ],
    style: {
      overflowX: 'auto',
      margin: '0',
      maxWidth: 'auto',
      height: '350px',
      overflowY: 'auto',
    },
    direction: 'column',
    justify: 'center',
    alignItems: 'stretch',
    isFixedHeight: true,
  };
}
