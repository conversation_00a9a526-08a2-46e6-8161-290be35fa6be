.page-custom-login {
  width: 100%;
  margin: 20px;
  padding: 20px;
  background: var(--dmc-page-100-color);

  &-content {
    &-title {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20px;
      color: var(--dmc-text-8);
      font-weight: 500;
      font-size: 23px;
      line-height: 32px;
    }

    &-list {
      height: 100%;

      .module_card-curd-with-simple-search .card-curd_header {
        display: none;
      }
    }
  }
}

.custom_login_item {
  overflow: hidden;
  border: 1px solid var(--dmc-border-1);
  border-radius: 4px;

  &-title {
    color: var(--dmc-text-color);
  }

  &-logo {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 120px;
    overflow: hidden;

    img {
      width: 100%;
      height: 120px;
      background-size: cover;
    }
  }

  &-desc {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 50px;
    padding: 0 10px 0 20px;
    border-top: 1px solid var(--dmc-border-1);
  }

  &-select {
    position: absolute;
    right: 0;
    bottom: 0;
  }
}
