import _ from 'lodash';
import { IDatapkg, IDatapkgColumn, IDatapkgGeometryType } from '@mdtApis/interfaces';
import { QuestionTypeEnum } from '@mdtProComm/constants';
import { transformDbColumnTypeToQuestion } from '@mdtProComm/utils/columnUtil';
import { UploadTypeEnum } from '@mdtProFormEditor/metro-form-design/components';
import { MetroSettingProp } from '@mdtProFormEditor/metro-form-design/shared';
import i18n from '../languages';

const EXCLUDE_COLUMNS = ['bd09'];

const getCommonSetting = (colName: string) => {
  return { title: colName, name: colName };
};

// ------------geometry--------------
const geometryInputTypeMap: Record<string, string> = {
  point: 'Point',
  line: 'LineString',
  polygon: 'Polygon',
};
const getAvaliableGeometryType = (type: IDatapkgGeometryType) => {
  const avaliableTypes = ['point', 'line', 'polygon'];
  const avaliableType = _.includes(avaliableTypes, type) ? type : 'point';
  return geometryInputTypeMap[avaliableType];
};

const geometryFactory = {
  schema: (name: string, geoType: string) => {
    return {
      type: 'string',
      title: name,
      'x-decorator': 'FormItem',
      'x-component': 'GeometryInput',
      'x-component-props': {
        placeholder: i18n.chain.proMicroModules.workflow.edit.geometryInputDesc,
        operationMode: 'default',
        limit: 500,
        geometryType: geoType,
        valueType: 'wkb',
        previewMode: 'default',
        previewMapWidth: '100%',
        previewMapHeight: '200px',
      },
      'x-designable-id': name,
    };
  },
  setting: (name: string, geoType: string) => {
    return {
      ...getCommonSetting(name),
      'x-component-props': {
        placeholder: i18n.chain.proMicroModules.workflow.edit.geometryInputDesc,
        geometryType: geoType,
        operationMode: 'default',
        limit: 500,
      },
    };
  },
};
// ------------input--------------
const inputFactory = {
  schema: (name: string, pkgId?: string) => {
    const commSchema = {
      type: 'string',
      title: name,
      'x-decorator': 'FormItem',
      'x-component': 'Input',
      'x-component-props': {
        placeholder: i18n.chain.comPlaceholder.input,
      },
      'x-designable-id': name,
    };

    return pkgId
      ? {
          ...commSchema,
          'x-decorator-props': { style: { display: 'none' } },
          default: pkgId,
          'x-index': 100,
        }
      : commSchema;
  },
  setting: (name: string) => {
    return {
      ...getCommonSetting(name),
    };
  },
};

// ------------textarea--------------
const textareaFactory = {
  schema: (name: string) => {
    return {
      type: 'string',
      title: name,
      'x-decorator': 'FormItem',
      'x-component': 'TextArea',
      'x-component-props': {
        placeholder: i18n.chain.comPlaceholder.input,
      },
      'x-designable-id': name,
    };
  },
  setting: (name: string) => {
    return {
      ...getCommonSetting(name),
      [MetroSettingProp.inputType]: 'multiple',
    };
  },
};

// ------------integer--------------
const integerFactory = {
  schema: (name: string) => {
    return {
      ...inputFactory.schema(name),
      type: 'number',
      'x-validator': `{{(value)=> {
        if (!value) return '';
        if (!isInteger(value)) return '${i18n.chain.proMicroModules.formEditor.ruleInt}';
      }}} `,
    };
  },
  setting: (name: string) => {
    return {
      ...getCommonSetting(name),
      [MetroSettingProp.inputContentType]: 'integer',
    };
  },
};

// ------------decimal--------------
const decimalFactory = {
  schema: (name: string) => {
    return {
      ...inputFactory.schema(name),
      type: 'number',
      'x-validator': `{{(value)=> {
        if (!value) return '';
        if (!isDecimal(value)) return '${i18n.chain.proMicroModules.formEditor.ruleFloat}';
      }}} `,
    };
  },
  setting: (name: string) => {
    return {
      ...getCommonSetting(name),
      [MetroSettingProp.inputContentType]: 'decimal',
    };
  },
};
// ------------time--------------
const timeFactory = {
  schema: (name: string) => {
    return {
      type: 'time',
      title: name,
      'x-decorator': 'FormItem',
      'x-component': 'TimePicker',
      'x-component-props': {
        picker: 'time',
        valueType: 'datetime',
      },
      'x-designable-id': name,
    };
  },
  setting: (name: string) => {
    return {
      ...getCommonSetting(name),
      'x-component-props': {
        picker: 'time',
        valueType: 'datetime',
      },
    };
  },
};
// ------------date--------------
const dateFactory = {
  schema: (name: string) => {
    return {
      type: 'date',
      title: name,
      'x-decorator': 'FormItem',
      'x-component': 'DatePicker',
      'x-component-props': {
        picker: 'date',
        valueType: 'datetime',
      },
      'x-designable-id': name,
    };
  },
  setting: (name: string) => {
    return {
      ...getCommonSetting(name),
      'x-component-props': {
        picker: 'date',
        valueType: 'datetime',
      },
    };
  },
};
// ------------dateTime--------------
const dateTimeFactory = {
  schema: (name: string) => {
    return {
      type: 'datetime',
      title: name,
      'x-decorator': 'FormItem',
      'x-component': 'DatePicker',
      'x-component-props': {
        picker: 'dateTime',
        valueType: 'datetime',
      },
      'x-designable-id': name,
    };
  },
  setting: (name: string) => {
    return {
      ...getCommonSetting(name),
      'x-component-props': {
        picker: 'dateTime',
        valueType: 'datetime',
      },
    };
  },
};
// ------------timestamp--------------
const timestampFactory = {
  schema: (name: string) => {
    return {
      type: 'date',
      title: name,
      'x-decorator': 'FormItem',
      'x-component': 'DatePicker',
      'x-component-props': {
        picker: 'dateTime',
        valueType: 'timestamp',
      },
      'x-designable-id': name,
    };
  },
  setting: (name: string) => {
    return {
      ...getCommonSetting(name),
      'x-component-props': {
        picker: 'dateTime',
        valueType: 'timestamp',
      },
    };
  },
};
// ------------bool--------------
const boolFactory = {
  schema: (name: string) => {
    return {
      type: 'boolean',
      title: name,
      'x-decorator': 'FormItem',
      'x-component': 'Switch',
      'x-designable-id': name,
    };
  },
  setting: (name: string) => {
    return {
      ...getCommonSetting(name),
    };
  },
};
// ------------file--------------
const fileFactory = {
  schema: (name: string) => {
    return {
      type: 'array',
      title: name,
      'x-decorator': 'FormItem',
      'x-component': 'Upload',
      [MetroSettingProp.uploadType]: UploadTypeEnum.FILE,
      'x-designable-id': name,
    };
  },
  setting: (name: string) => {
    return {
      ...getCommonSetting(name),
    };
  },
};
// ------------array--------------
const arrayFactory = {
  schema: (name: string) => {
    return {
      type: 'array',
      title: name,
      'x-decorator': 'FormItem',
      'x-component': 'Checkbox.Group',
      'x-component-props': {
        className: 'metro-formily-checkbox-group',
        direction: 'vertical',
        spaceProps: { size: 'small' },
      },
      'x-designable-id': name,
    };
  },
  setting: (name: string) => {
    return {
      ...getCommonSetting(name),
    };
  },
};

// TODO 后续针对user_id, org_id, group_id有专门的组件让用户自己选择
const questionInitFactory: Record<QuestionTypeEnum, { schema: any; setting: any }> = {
  [QuestionTypeEnum.GEOMETRY]: geometryFactory,
  [QuestionTypeEnum.TEXT]: inputFactory,
  [QuestionTypeEnum.TEXTAREA]: textareaFactory,
  [QuestionTypeEnum.INTEGER]: integerFactory,
  [QuestionTypeEnum.DECIMAL]: decimalFactory,
  [QuestionTypeEnum.SWITCH]: boolFactory,
  [QuestionTypeEnum.TIME]: timeFactory,
  [QuestionTypeEnum.DATE]: dateFactory,
  [QuestionTypeEnum.DATETIME]: dateTimeFactory,
  [QuestionTypeEnum.TIMESTAMP]: timestampFactory,
  [QuestionTypeEnum.FILE]: fileFactory,
  [QuestionTypeEnum.ARRAY]: arrayFactory,
};

const DATAPKG_ID_KEY = 'datapkg_id';
export const generateFormEditorSchema = ({
  pkgDetail,
  columns,
  includeDatapkgId = true,
}: {
  pkgDetail: IDatapkg;
  columns: IDatapkgColumn[];
  includeDatapkgId?: boolean;
}) => {
  const allSettingValues: Record<string, any> = {};
  const properties = _.reduce(
    columns,
    (p: Record<string, any>, c, i) => {
      const cName = c.name;
      if (_.includes(EXCLUDE_COLUMNS, cName)) return p;

      const questionType = transformDbColumnTypeToQuestion(c.type);
      const factory = questionInitFactory[questionType];
      let schema: any;
      let setting: any;
      const colName = c.name;
      // TODO 后续针对user_id, org_id, group_id有专门的组件让用户自己选择
      if (questionType === QuestionTypeEnum.GEOMETRY) {
        const gt = getAvaliableGeometryType(pkgDetail.geometry_type);
        schema = factory.schema(colName, gt);
        setting = factory.setting(colName, gt);
      } else {
        // TODO 可以对column的format进行映射
        schema = factory.schema(colName);
        setting = factory.setting(colName);
      }
      // 保证组件显示循序
      schema['x-index'] = i;
      p[colName] = schema;
      allSettingValues[colName] = setting;
      return p;
    },
    {},
  );

  // 隐藏属性，提交表单时带上
  pkgDetail.id &&
    includeDatapkgId &&
    (properties[DATAPKG_ID_KEY] = {
      ...questionInitFactory[QuestionTypeEnum.TEXT].schema(DATAPKG_ID_KEY, pkgDetail.id),
      name: DATAPKG_ID_KEY,
    });

  const formilySchema = {
    form: { colon: false, layout: 'vertical' },
    schema: { type: 'object', properties: properties },
  };

  return { formilySchema, allSettingValues };
};
