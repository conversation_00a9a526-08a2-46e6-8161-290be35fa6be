.one-table-new-report-detail {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  padding: 4px 24px;
  overflow: hidden;

  .form-info {
    padding: 20px;
    background-color: var(--metro-fill-0);
    border-radius: 12px;

    .metro-form-view {
      max-width: unset;
    }

    .module-wrap {
      background-color: transparent;
    }
  }

  .version-select {
    &.metro-select {
      width: 230px;
      color: var(--metro-primary-default);
    }
  }

  .data-table {
    height: 100%;
  }

  .delivery-management {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .workflow-node-list .metro-steps-item-container .metro-divider {
    margin: 4px 0;
  }
}

.one-table-new-report-detail-drawer {
  .metro-drawer-title {
    width: 100%;
  }
}

.one-table-new-report-detail-header {
  display: flex;
  gap: 10px;
  align-items: center;
  margin-bottom: 10px;

  .title {
    flex: 1;

    .metro-typography {
      margin-bottom: 0;
    }
  }

  .once-tip {
    padding: 0 8px;
    color: var(--metro-primary-default);
    background-color: var(--metro-primary-tp-1);
    border-radius: 4px;

    .metro-typography {
      /* stylelint-disable scale-unlimited/declaration-strict-value */
      color: unset;
    }
  }
}

.one-table-new-report-detail-header-operator {
  display: flex;
  gap: 10px;
  align-items: center;
}

.one-table-new-operator-popover-content {
  padding: 12px;
  background-color: var(--metro-fill-0);
  border-radius: 12px;
}
