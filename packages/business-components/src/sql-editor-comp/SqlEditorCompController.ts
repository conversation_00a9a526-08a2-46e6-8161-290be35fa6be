import _ from 'lodash';
import type { EditorProps } from '@monaco-editor/react';
import { BehaviorSubject, Observable } from 'rxjs';
import { format } from 'sql-formatter';

export type IAceEditorProps = (() => EditorProps) | EditorProps;
export type IRunSqlFunc = () => Observable<any>;

export interface ISqlEditorOptions {
  runSqlFunc: IRunSqlFunc;
}

export interface ISqlEditorControllerProps {
  options: ISqlEditorOptions;
  aceEditorProps?: IAceEditorProps;
}

class SqlEditorCompController {
  // sql内容
  private sql$ = new BehaviorSubject<string>('');
  // 是否正在运行sql
  private runSqlBtnLoading$ = new BehaviorSubject<boolean>(false);
  // 是否正在格式化sql
  private formatSqlBtnLoading$ = new BehaviorSubject<boolean>(false);

  private runSqlFunc: IRunSqlFunc;
  private aceEditorProps?: EditorProps;

  public constructor(options: ISqlEditorControllerProps) {
    this.runSqlFunc = options.options.runSqlFunc;
    const aceEditorProps = options.aceEditorProps;
    aceEditorProps && (this.aceEditorProps = _.isFunction(aceEditorProps) ? aceEditorProps() : aceEditorProps);
  }

  public destroy() {
    this.sql$.complete();
    this.runSqlBtnLoading$.complete();
    this.formatSqlBtnLoading$.complete();
    this.runSqlFunc = null!;
    this.aceEditorProps = undefined;
  }
  public getAceEditorProps() {
    return this.aceEditorProps || {};
  }

  public getSql$() {
    return this.sql$;
  }

  public setSql(v: string) {
    this.sql$.next(v);
  }

  public getRunSqlBtnLoading$() {
    return this.runSqlBtnLoading$;
  }

  public setRunSqlBtnLoading$(v: boolean) {
    return this.runSqlBtnLoading$.next(v);
  }

  public getFormatSqlBtnLoading$() {
    return this.formatSqlBtnLoading$;
  }

  public setFormatSqlBtnLoading$(v: boolean) {
    return this.formatSqlBtnLoading$.next(v);
  }

  public runSql() {
    this.runSqlBtnLoading$.next(true);
    this.runSqlFunc().subscribe(() => {
      this.runSqlBtnLoading$.next(false);
    });
  }

  public formatSql() {
    this.formatSqlBtnLoading$.next(true);
    const sql = format(this.sql$.value);
    this.sql$.next(sql);
    this.formatSqlBtnLoading$.next(false);
  }
}

export { SqlEditorCompController };
