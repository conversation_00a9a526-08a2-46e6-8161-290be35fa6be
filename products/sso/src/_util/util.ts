import _ from 'lodash';
import { decompressFromEncodedURIComponent } from 'lz-string';
import urlParameterAppend from 'url-parameter-append';
import { getParamFromUrl, getParamsFromUrl } from '@mdtLogin/util/urlUtil';
import {
  DATLAS_URL,
  DK_OAUTH_CLIENT_ID,
  DK_OAUTH_CORP_ID,
  DK_OAUTH_EXCLUSIVE_CORP_ID,
  DK_OAUTH_EXCLUSIVE_LOGIN,
  DK_OAUTH_ORG_TYPE,
  DK_OAUTH_PROMPT,
  DK_OAUTH_REDIRECT_URI,
  DK_OAUTH_RESPONSE_TYPE,
  DK_OAUTH_SCOPE,
  SITE_ORIGIN,
  WX_OAUTH_FORCE_POPUP,
  WX_OAUTH_ID,
  WX_OAUTH_REDIRECT_URI,
  WX_OAUTH_RESPONSE_TYPE,
  WX_OAUTH_SCOPE,
  WX_WORK_OAUTH_AGENTID,
  WX_WORK_OAUTH_ID,
  WX_WORK_OAUTH_REDIRECT_URI,
  WX_WORK_OAUTH_RESPONSE_TYPE,
  WX_WORK_OAUTH_SCOPE,
} from '../config';

// 接收参数
export enum urlParam {
  q = 'q', // 跨产品之间url参数传递统一使用

  openid = 'openid', // 微信
  wxcode = 'wxcode', // 微信
  vcode = 'vcode', // 微信
  qrType = 'qr_type', // 微信
  wechatRedirect = 'wechatRedirect', // 登陆成功跳转地址
  language = 'language', // 语言
  tab = 'tab', // 登录方式
  redirect = 'redirect', // sso回调地址
  force = 'force', // 强制登录
  configPath = 'config_path', // config自定义类型
  theme = 'theme', // 主题模式
  type = 'type', // 展示类型
  selfRedirect = 'self_redirect', // 本页重定向
  ssoOut = 'sso_out', // 是否需要根据配置项跳转到外部sso
  out = 'out', // 等价于config中的productSsoOut参数
  config = 'config', // 解析config的文件的公共路径，用于自定义登录
  clientId = 'client_id', // 第三方应用的clientId，用token换取code
  configNamespace = 'config_namespace', // config的命名空间，通过参数匹配，在配置文件中查找

  code = 'code', // 通用的code参数，一般配合其他状态参数使用，比如同时存在code和platform=wechat_official，代表需要走绑定流程
  wwAgentId = 'ww_agent_id', // 企业微信的agentId
  wwAppId = 'ww_app_id', // 企业微信的appId
  wwApplicationId = 'ww_application_id', // 企业微信的applicationId
  passThrough = 'pass_through', // 接收的参数会透传到 q 参数内，用于信息转发
  platform = 'platform', // 平台标识
  platformState = 'platform_state', // 平台状态，给后端透传到第三方的参数
  tabsAutoHide = 'tabs_auto_hide', // 是否自动隐藏tab
  identityId = 'identity_id', // 身份id, 传入会在登录的时候指定改身份登录
}

// 平台枚举
export enum platformEnum {
  WECHAT_OFFICIAL = 'wechat_official',
  WECHAT_MINI = 'wechat_mini',
  WECHAT_WORK = 'wechat_work',
  DINGTALK = 'dingtalk',
  ZZDING = 'zzding',
}

// url
export const getLanguageFromUrl = () => {
  return getParamFromUrl(urlParam.language);
};

export const getClientIdFromUrl = () => {
  return getParamFromUrl(urlParam.clientId);
};

export const getTabFromUrl = () => {
  return getParamFromUrl(urlParam.tab);
};

export const getWwAgentIdFromUrl = () => {
  return getParamFromUrl(urlParam.wwAgentId);
};

export const getWwAppIdFromUrl = () => {
  return getParamFromUrl(urlParam.wwAppId);
};

export const getWwApplicationIdFromUrl = () => {
  return getParamFromUrl(urlParam.wwApplicationId);
};

export const getPlatformStateFromUrl = () => {
  return getParamFromUrl(urlParam.platformState);
};

export const getStateFromUrl = () => {
  return getParamFromUrl('state');
};

export const getRedirectFromUrl = (url?: string): string => {
  // sso成功后的跳转地址
  return getParamFromUrl(urlParam.redirect, url) || '';
};

export const getSelfRedirectFromUrl = (url?: string) => {
  return getParamFromUrl(urlParam.selfRedirect, url) || '';
};

export const getForceFromUrl = () => {
  return getParamFromUrl(urlParam.force);
};

export const getConfigNamesapceFromUrl = () => {
  return getParamFromUrl(urlParam.configNamespace);
};

export const getThemeFromUrl = () => {
  return getParamFromUrl(urlParam.theme);
};

export const getIdentityIdFromUrl = () => {
  const identityId = getParamFromUrl(urlParam.identityId);
  if (!identityId) {
    return {};
  }
  const [appId, userId] = identityId.split('_');
  return {
    appId,
    userId,
  };
};

export const isDialog = () => {
  return getParamFromUrl(urlParam.type) === 'dialog' || undefined;
};

export const isTabsAutoHide = () => {
  const paramsValue = getParamFromUrl(urlParam.tabsAutoHide);
  return paramsValue ? paramsValue === 'true' : undefined;
};

export const getVcodeFromUrl = () => {
  return getParamFromUrl(urlParam.vcode);
};

export const getQFromUrl = (url?: string): Record<string, any> => {
  const q = getParamFromUrl(urlParam.q, url);
  return q ? parseStrToObj(decompressFromEncodedURIComponent(q) || '') : {};
};

export const getNoSsoOutFromUrl = () => {
  return getParamFromUrl(urlParam.ssoOut) === 'false';
};

export const getOutFromUrl = () => {
  const value = getParamFromUrl(urlParam.out);
  return value ? parseStrToObj(decompressFromEncodedURIComponent(value) || '') : {};
};

export const getConfigPathFromUrl = () => {
  return getParamFromUrl(urlParam.configPath);
};

export const getConfigFromUrl = () => {
  return decompressFromEncodedURIComponent(getParamFromUrl(urlParam.config));
};

export const getCodeFromUrl = () => {
  return getParamFromUrl(urlParam.code);
};

export const getPassThroughFromUrl = () => {
  const value = getParamFromUrl(urlParam.passThrough);
  return value ? parseStrToObj(decompressFromEncodedURIComponent(decodeURIComponent(value)) as string) : {};
};

const WX_VERIFY_KEY = [urlParam.openid, urlParam.wxcode, urlParam.vcode, urlParam.qrType];
export const getWXVerifyFromUrl = (): string[] => {
  return getParamsFromUrl(WX_VERIFY_KEY);
};

const WX_REDIRECT_KEY = [urlParam.vcode, urlParam.wechatRedirect, urlParam.qrType];
export const getWXRedirectFromUrl = (): string[] => {
  return getParamsFromUrl(WX_REDIRECT_KEY);
};

export const parseStrToObj = <T = object>(str: string): T => {
  let val;
  try {
    val = str ? JSON.parse(str) : {};
  } catch (e: any) {
    console.warn(`转换失败，不是合法的数据结构: ${str} -> ${e.message}`);
    val = {};
  }
  return val as T;
};

export const getPlatformFromUrl = () => {
  return getParamFromUrl(urlParam.platform);
};

export const isWechatOAuth = () => {
  return _.isEqual(getPlatformFromUrl(), platformEnum.WECHAT_OFFICIAL) && !!WX_OAUTH_ID;
};

export const isWechatWorkOAuth = () => {
  return _.isEqual(getPlatformFromUrl(), platformEnum.WECHAT_WORK) && (!!getWwAppIdFromUrl() || !!WX_WORK_OAUTH_ID);
};

export const isZzdingOAuth = () => {
  const ua = navigator.userAgent.toLowerCase();
  return ua.includes('taurusapp') || _.isEqual(getPlatformFromUrl(), platformEnum.ZZDING);
};

export const isWechatOAuthCallback = () => {
  return !!getCodeFromUrl() && _.isEqual(getPlatformFromUrl(), platformEnum.WECHAT_OFFICIAL) && !!WX_OAUTH_ID;
};

export const isWechatWorkOAuthCallback = () => {
  return (
    !!getCodeFromUrl() &&
    _.isEqual(getPlatformFromUrl(), platformEnum.WECHAT_WORK) &&
    (!!getWwAppIdFromUrl() || !!WX_WORK_OAUTH_ID)
  );
};

export const isForwarder = () => {
  return !_.isEmpty(getPassThroughFromUrl());
};

export const getForwarderUrl = () => {
  const pathname = '/forwarder-h5';
  return urlParameterAppend(`${DATLAS_URL}${pathname}`);
};

export const getDefaultOAuthRedirectUrl = () => {
  return encodeURIComponent(
    urlParameterAppend(
      SITE_ORIGIN,
      urlParam.passThrough,
      getParamFromUrl(urlParam.passThrough),
      urlParam.platform,
      getPlatformFromUrl(),
      urlParam.wwApplicationId,
      getWwApplicationIdFromUrl(),
      urlParam.redirect,
      getRedirectFromUrl(),
    ),
  );
};

// 微信授权url
export const getWechatOAuthUrl = () => {
  const redirectUri = WX_OAUTH_REDIRECT_URI || getDefaultOAuthRedirectUrl();
  const OAUTH_LOGIN_URL = 'https://open.weixin.qq.com/connect/oauth2/authorize';
  const wechatRedirectAppend = '#wechat_redirect';
  return `${urlParameterAppend(
    OAUTH_LOGIN_URL,
    'appid',
    WX_OAUTH_ID,
    'redirect_uri',
    redirectUri,
    'response_type',
    WX_OAUTH_RESPONSE_TYPE,
    'scope',
    WX_OAUTH_SCOPE,
    'state',
    getPlatformStateFromUrl(),
    'forcePopup',
    WX_OAUTH_FORCE_POPUP,
  )}${wechatRedirectAppend}`;
};

// 企业微信授权url
export const getWechatWorkOAuthUrl = () => {
  const redirectUri = WX_WORK_OAUTH_REDIRECT_URI || getDefaultOAuthRedirectUrl();
  const OAUTH_LOGIN_URL = 'https://open.weixin.qq.com/connect/oauth2/authorize';
  const wechatRedirectAppend = '#wechat_redirect';
  return `${urlParameterAppend(
    OAUTH_LOGIN_URL,
    'appid',
    getWwAppIdFromUrl() || WX_WORK_OAUTH_ID,
    'redirect_uri',
    redirectUri,
    'response_type',
    WX_WORK_OAUTH_RESPONSE_TYPE,
    'scope',
    WX_WORK_OAUTH_SCOPE,
    'agentid',
    getWwAgentIdFromUrl() || WX_WORK_OAUTH_AGENTID,
    'state',
    getPlatformStateFromUrl(),
  )}${wechatRedirectAppend}`;
};

// 钉钉授权url
export const getDingtalkOAuthUrl = () => {
  const redirectUri = DK_OAUTH_REDIRECT_URI || getDefaultOAuthRedirectUrl();
  const OAUTH_LOGIN_URL = 'https://login.dingtalk.com/oauth2/auth';
  return `${urlParameterAppend(
    OAUTH_LOGIN_URL,
    'redirect_uri',
    redirectUri,
    'response_type',
    DK_OAUTH_RESPONSE_TYPE,
    'client_id',
    DK_OAUTH_CLIENT_ID,
    'scope',
    DK_OAUTH_SCOPE,
    'prompt',
    DK_OAUTH_PROMPT,
    'org_type',
    DK_OAUTH_ORG_TYPE,
    'corpid',
    DK_OAUTH_CORP_ID,
    'exclusive_login',
    DK_OAUTH_EXCLUSIVE_LOGIN,
    'exclusive_corpid',
    DK_OAUTH_EXCLUSIVE_CORP_ID,
    'state',
    getPlatformStateFromUrl(),
  )}`;
};
