import { FC } from 'react';
import { ModalWithBtnsCompDialog } from '@mdtBsComponents/modal-with-btns-comp-dialog';
import { ModalWithBtnsCompEmotion } from '@mdtBsComponents/modal-with-btns-comp-emotion';
import { DrawerPreviewGeometryData } from '@mdtProMicroModules/containers/drawer-preview-geometry-data';
import { FolderOperationCommon } from '@mdtProMicroModules/containers/folder-operation-common';
import { TableCurdWithSimpleSearch } from '@mdtProMicroModules/containers/table-curd-with-simple-search';
import { CreateDatapkg } from '@mdtProMicroModules/pages/create-datapkg';
import { DrawerDatapkgDetailFactory } from '@mdtProMicroModules/pages/datapkg-detail-factory';
import { DrawerWithinPage } from '@mdtProMicroModules/pages/drawer-within-page';
import { PersonalDatapkgController } from './PersonalDatapkgController';

interface IProps {
  controller: PersonalDatapkgController;
}

const PersonalDatapkg: FC<IProps> = ({ controller }) => {
  return (
    <div className="page-pkg-list">
      <TableCurdWithSimpleSearch controller={controller.getTableController()} />
      <ModalWithBtnsCompEmotion controller={controller.getDeleteController()} />
      <DrawerDatapkgDetailFactory controller={controller.getDrawerDetailController()} />
      <CreateDatapkg controller={controller.getCreatePkgController()} />
      <ModalWithBtnsCompDialog controller={controller.getSqlEditController()} />
      <ModalWithBtnsCompDialog controller={controller.getDataEditController()} />
      <DrawerPreviewGeometryData controller={controller.getPreviewGeometryDataController()} />
      {controller.getFolderOperationCtrl() && (
        <FolderOperationCommon controller={controller.getFolderOperationCtrl()!} />
      )}
      <DrawerWithinPage controller={controller.getFillDesignController()} />
    </div>
  );
};

export { PersonalDatapkg };
