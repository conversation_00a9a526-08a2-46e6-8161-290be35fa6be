import {
  BaseBffService,
  IQueryDatasetListOptions,
  IQueryDatasetPkgsOptions,
  queryDatasetList,
  queryDatasetPkgs,
} from '@mdtBsBffServices/services';

export class DMDatasetBffService extends BaseBffService {
  public queryDatasetList(options: IQueryDatasetListOptions) {
    return queryDatasetList(this.productName, options);
  }

  public queryDatasetPkgs(options: IQueryDatasetPkgsOptions) {
    return queryDatasetPkgs(this.productName, options);
  }
}
