import { en as microEn } from '@mdtProMicroModules/languages';
import { Locale } from './cn';

const en: Locale = {
  ...microEn,
  datamarket: {
    menu: {
      dashboard: 'Dashboard',
      smartSearch: 'Smart Search',
      allData: 'All Data',
      selfApp: 'Current Inst',
      dataSearch: 'Data Search',
      share: 'Share',
      metaData: 'Meta Data Manage',
      template: 'Data Packet Documentation',
      asset: 'Metadata Management',
      approval: 'Data Approval',
      approvalApp: 'Self Inst',
      approvalOther: 'Other Inst',
    },
    pkgSearch: {
      dataTag: 'Data Tags',
      searchTag: 'Search Tags',
      ownerApp: 'Owner Institution',
    },
    dashboard: {
      app: 'Institution',
      theme: 'Theme Library',
      tagGroup: 'Tag Group',
      tag: 'Tag',
      allData: 'All Data',
      myApp: 'My Institution',
      uncategroizedDatapkd: 'Uncategorized Data Packages',
      indicatorCard: 'Indicator Card',
      leaderboard: 'Leaderboard',
      accumulation: 'Accumulation',
      day7: 'Last 7 Days',
      day30: 'Last 30 Days',
      实时: 'Real-time',
      数据包个数: 'Data Package Count',
      数据总行数: 'Total Data Rows',
      主题库个数: 'Theme Library Count',
      标签组个数: 'Tag Group Count',
      标签个数: 'Tag Count',
      ETL任务个数: 'ETL Task Count',
      数据质量监控个数: 'Data Quality Monitoring Count',
      图表个数: 'Chart Count',
      地图个数: 'Map Count',
      可视化组件被调用次数: 'Number of Times Visual Component Called',
      数据包被浏览次数: 'Data Package Views',
      数据包被使用次数: 'Data Package Usage Count',
      数据包被直接使用次数: 'Number of Times Data Package Used Directly',
      数据包使用次数: 'Data Package Usage',
      应用页面数: 'Application Page Count',
      应用调用次数: 'Application Invocation Count',
      最近创建数据包: 'Latest Created Data Package',
      最近更新数据包: 'Latest Updated Data Package',
      statistics: 'Statistical Indicators',
      groupMap: 'Guide Map',
      'T+1': 'T+1',
    },
    pkg: {
      appSetting: 'Settings',
      shareDesc: 'Only allowing sharing of data within the institution to outside institutions.',
      dataSize: 'Size',
      lastUpdate: 'Update',
      copyLink: 'Copy Link',
      copyLinkSuccess: 'Link Successfully Copied',
      publicLinkShare: 'Share Public Link',
      noAuth: 'No Permission',
      dataApply: 'Data Apply',
      pkgEmptyError: 'Data package does not exist or could not be requested for other reasons.',
      pkgLoading: 'Loading data package details',
      basic: 'Basic Overview',
      data: 'Table Preview',
      column: 'Column Attributes',
      desc: 'Data Package Description',
      genealogy: 'Lineage Graph',
      read: 'Use Permission',
      readDesc: 'Use this data to create graphs and maps',
      viewDetail: 'Query Permission',
      viewDetailDesc: 'Sort and filter data or use it as input for low-code ETL',
      download: 'Download Permission',
      downloadDesc: 'Download data to local machine.',
    },
    apply: 'Apply',
    hasApply: 'Applied',
    applying: 'Applying',
    emptyContent: 'App does not exist or is not authorized',
    themeEmptyTip: 'The theme does not exist or is not authorized',
  },
};

export default en;
