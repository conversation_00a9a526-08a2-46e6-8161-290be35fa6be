.page_operation-log {
  width: 100%;
  height: 100%;
  padding: 0 20px 20px;

  &-placeholder {
    color: var(--dmc-select-text-txt-color);
  }

  .dmc-date-picker-separator-icon {
    width: 14px;
    color: var(--dmc-border-btn-color);
  }

  .com_form_item {
    width: unset;
    margin-bottom: 0;
  }

  .dmc-dropmenu {
    width: 140px;
  }

  .dmc-form-item-label {
    margin: 0;
  }

  .dmc-empty-description {
    color: var(--dmc-text-5);
  }

  div.dmc-virtual-table td.dmc-virtual-empty-table-cell {
    height: 300px;
  }

  .dmc-date-picker-input > input {
    color: var(--dmc-select-text-color);
    background: var(--dmc-date-picker-bg-color);
  }

  .dmc-date-picker-input > input:not([value='']) {
    border: 1px solid var(--dmc-border-btn-color);
  }

  .dmc-date-picker-focused .dmc-date-picker-input > input:not([value='']) {
    border: 1px solid var(--dmc-date-picker-border-color);
  }

  .dmc-date-picker-input > input:not(.dmc-date-picker-input > input:not([value=''])) {
    border: 1px solid var(--dmc-border-btn-color);
  }
}

.page_operation-log-title {
  flex-grow: 1;
  padding-bottom: 16px;
  color: var(--dmc-text-8);
  font-weight: 500;
  font-size: 23px;
  line-height: 32px;
}

.page_operation-log-header {
  display: flex;
  align-items: center;
  justify-content: space-between;

  &-left {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
  }

  &-right {
    display: flex;
    gap: 10px;
  }
}
