import { FC, memo } from 'react';
import { useParams } from 'react-router-dom';
import { useCreation } from 'ahooks';
import { Empty } from '@mdtBsComm/components/empty';
import { useController } from '@mdtBsComm/hooks/use-controller';
import { getModel } from '../../_util/modelUtil';
import { useAppContext } from '../../app/appContext';
import i18n from '../../languages';
import {
  AppPkgSearch,
  AppPkgSearchController,
  AppPkgSearchModel,
  AppPkgSearchModelBff,
} from '../../pages/app-pkg-search';

const AppPkgSearchInner: FC<{ appId: string; name: string }> = memo(({ appId, name }) => {
  const [controller] = useController(() => {
    const ctrl = new AppPkgSearchController({
      appId: appId,
      title: name,
      Model: getModel(AppPkgSearchModel, AppPkgSearchModelBff),
    });
    return [ctrl, null];
  }, [appId, name]);

  return <AppPkgSearch controller={controller} />;
});

const AppPkgSearchView = () => {
  const { appController } = useAppContext();
  const { id } = useParams<Record<string, string>>();
  const name = useCreation(() => {
    if (id === `${appController.getAppId()}`) {
      return i18n.chain.datamarket.menu.selfApp;
    }
    return appController.getGrantedAppController().getGrantedAppName(id);
  }, [id]);

  if (name) {
    return <AppPkgSearchInner key={id} appId={id} name={name} />;
  }
  return <Empty description={i18n.chain.datamarket.emptyContent} />;
};

export default AppPkgSearchView;
