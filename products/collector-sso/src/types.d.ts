declare module 'entity-convert';
declare module 'md5';
declare module '*.less';
declare module '*.svg';
declare module '*.bmp';
declare module '*.gif';
declare module '*.jpg';
declare module '*.jpeg';
declare module '*.png';
declare module '*.mp4';

// eslint-disable-next-line @typescript-eslint/naming-convention
interface Window {
  // 微服务自动注入字段
  microApp?: any;
  rawWindow?: any;
  __MICRO_APP_NAME__?: string;
  __MICRO_APP_ENVIRONMENT__?: boolean;
  __MICRO_APP_PUBLIC_PATH__?: string;
  __MICRO_APP_BASE_ROUTE__?: string;
  // 项目配置
  __DM_COLLECTOR_SSO_CFS: {
    deployPublicPath?: string;
    deployEnv?: 'dev' | 'staging' | 'prod' | 'pri';
    deployWindowTitle?: string;
    deployWindowDesc?: string;
    deployEnableHashRouter?: false;
    deployRouterPath?: string;
    backendApiUrl?: string;
    backendMapApiUrl?: string;
    backendRedirectUri?: string;
    backendAuthApiUrl?: string;
    backendSettingId?: string;
    backendTemplateId?: string;
    backendEncodeApis?: any;
    backendEncodeApisOptions?: any;
    backendTransformApiMethod?: any;
    productRedirect?: string;
    productLanguage?: string;
    productTab?: string;
    productTabs?: string[];
    productLogo?: string;
    productPrivacy?: boolean | { visible: boolean; check?: boolean };
    productDingtalkId?: string;
    productWechatId?: string;
    datlasUrl?: string;
    productVerification?: boolean | any;
    backendDingtalkSettingId?: string;
    backendWechatSettingId?: string;
    productTitle?: string;
    productFooter?: any;
    productEncodeParam?: boolean;
    deployFavIcon?: string;
    deployFavicon?: string;
    productDingtalkOAuthId?: string;
    backendDingtalkOAuthSettingId?: string;
    productNamespaceCustomMap?: { [key: string]: string };
    backendApiUrlBasePath?: string;
  };
}
