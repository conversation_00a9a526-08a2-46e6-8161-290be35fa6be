/* eslint-disable no-template-curly-in-string */
/* eslint-disable sonarjs/no-duplicate-string */
/* eslint-disable @typescript-eslint/explicit-member-accessibility */
import {
  BaseEventContext,
  BasePlugin,
  getSchemaTpl,
  RendererPluginAction,
  RendererPluginEvent,
  tipedLabel,
} from 'amis-editor';
import { getEventControlConfig } from 'amis-editor/lib/renderer/event-control/helper';
import { ValidatorTag } from 'amis-editor/lib/validator';
import '.';
import i18n from '../../../languages';
const TYPE = 'upload';

class UploadPlugin extends BasePlugin {
  rendererName = TYPE;
  scaffold = {
    type: TYPE,
  };

  $schema = '/schemas/UnkownSchema.json';

  // 用来配置名称和描述
  name = i18n.chain.jsonComp.mdtUpload;
  description = i18n.chain.jsonComp.mdtUploadDesc;

  // tag，决定会在哪个 tab 下面显示的
  tags = [i18n.chain.jsonComp.mdtComponent];

  // 图标
  icon = 'fa fa-upload';
  // 组件名称
  pluginIcon = 'input-file-plugin';
  previewSchema = {
    type: 'form',
    className: 'text-left',
    wrapWithPanel: false,
    mode: 'horizontal',
    body: [
      {
        ...this.scaffold,
      },
    ],
  };

  notRenderFormZone = true;

  // 事件定义
  events: RendererPluginEvent[] = [
    {
      eventName: 'change',
      eventLabel: i18n.chain.jsonComp.valChange,
      description: i18n.chain.jsonComp.valChangeDesc,
      dataSchema: [
        {
          type: 'object',
          properties: {
            data: {
              type: 'object',
              title: i18n.chain.jsonComp.data,
              properties: {
                file: {
                  type: 'object',
                  title: i18n.chain.jsonComp.dataTitle,
                },
              },
            },
          },
        },
      ],
    },
    {
      eventName: 'remove',
      eventLabel: i18n.chain.jsonComp.removeFile,
      description: i18n.chain.jsonComp.removeFileDesc,
      dataSchema: [
        {
          type: 'object',
          properties: {
            data: {
              type: 'object',
              title: i18n.chain.jsonComp.data,
              properties: {
                item: {
                  type: 'object',
                  title: i18n.chain.jsonComp.removeFileDesc2,
                },
              },
            },
          },
        },
      ],
    },
    {
      eventName: 'success',
      eventLabel: i18n.chain.jsonComp.uploadSuccess,
      description: i18n.chain.jsonComp.uploadSuccessDesc,
      dataSchema: [
        {
          type: 'object',
          properties: {
            data: {
              type: 'object',
              title: i18n.chain.jsonComp.data,
              properties: {
                item: {
                  type: 'object',
                  title: i18n.chain.jsonComp.dataTitle,
                },
                result: {
                  type: 'object',
                  title: i18n.chain.jsonComp.uploadSuccessDesc2,
                },
              },
            },
          },
        },
      ],
    },
    {
      eventName: 'fail',
      eventLabel: i18n.chain.jsonComp.uploadFail,
      description: i18n.chain.jsonComp.uploadFailDesc,
      dataSchema: [
        {
          type: 'object',
          properties: {
            data: {
              type: 'object',
              title: i18n.chain.jsonComp.data,
              properties: {
                item: {
                  type: 'object',
                  title: i18n.chain.jsonComp.dataTitle,
                },
                error: {
                  type: 'object',
                  title: i18n.chain.jsonComp.uploadFailDesc2,
                },
              },
            },
          },
        },
      ],
    },
  ];

  // 动作定义
  actions: RendererPluginAction[] = [
    {
      actionType: 'clear',
      actionLabel: i18n.chain.jsonComp.clearData,
      description: i18n.chain.jsonComp.clearDataDesc,
    },
    {
      actionType: 'setValue',
      actionLabel: i18n.chain.jsonComp.setValue,
      description: i18n.chain.jsonComp.setValueDesc,
    },
  ];

  panelJustify = true;
  panelBodyCreator = (context: BaseEventContext) => {
    return getSchemaTpl('tabs', [
      {
        title: i18n.chain.jsonComp.property,
        body: getSchemaTpl('collapseGroup', [
          {
            title: i18n.chain.jsonComp.basic,
            body: [
              getSchemaTpl('layout:originPosition', { value: 'left-top' }),
              getSchemaTpl('formItemName'),
              {
                type: 'input-group',
                name: 'limitSize',
                label: i18n.chain.jsonComp.limitSize,
                body: [
                  {
                    type: 'input-number',
                    name: 'limitSize',
                  },
                  {
                    type: 'tpl',
                    addOnclassName: 'border-0 bg-none',
                    tpl: 'KB',
                  },
                ],
              },
              getSchemaTpl('switch', {
                name: 'compressImage',
                label: i18n.chain.jsonComp.compressImage,
                value: true,
                visibleOn: 'this.limitSize > 0 && !this.limitSizeErrorMsg',
              }),
              {
                type: 'input-text',
                name: 'limitSizeErrorMsg',
                label: i18n.chain.jsonComp.limitSizeMsg,
                visibleOn: 'this.limitSize > 0 && !this.compressImage',
              },
              getSchemaTpl('switch', {
                name: 'carema',
                label: i18n.chain.jsonComp.caremaText,
              }),
              {
                type: 'container',
                className: 'ae-ExtendMore',
                visibleOn: 'this.carema === true',
                body: [
                  {
                    type: 'checkboxes',
                    name: 'watermark',
                    options: [
                      {
                        label: i18n.chain.jsonComp.watermarkLabel1,
                        value: 'user',
                      },
                      {
                        label: i18n.chain.jsonComp.wartermarkLabel2,
                        value: 'date',
                      },
                    ],
                  },
                ],
              },
              {
                type: 'input-number',
                name: 'minCount',
                label: i18n.chain.jsonComp.minCount,
              },
              {
                type: 'input-number',
                name: 'maxCount',
                label: i18n.chain.jsonComp.maxCount,
              },
              {
                type: 'input-text',
                name: 'accept',
                label: tipedLabel(i18n.chain.jsonComp.fileType, i18n.chain.jsonComp.fileTypeDesc),
              },
            ],
          },
          getSchemaTpl('status', {
            isFormItem: true,
            unsupportStatic: true,
          }),
          getSchemaTpl('validation', { tag: ValidatorTag.File }),
        ]),
      },
      {
        title: i18n.chain.jsonComp.style,
        body: getSchemaTpl('collapseGroup', [
          getSchemaTpl('style:formItem', { renderer: context.info.renderer }),
          getSchemaTpl('style:classNames', {
            unsupportStatic: true,
            schema: [
              getSchemaTpl('className', {
                name: 'descriptionClassName',
                label: i18n.chain.jsonComp.styleDesc,
              }),
              getSchemaTpl('className', {
                name: 'btnClassName',
                label: i18n.chain.jsonComp.styleDesc2,
              }),
              getSchemaTpl('className', {
                name: 'btnUploadClassName',
                label: i18n.chain.jsonComp.styleDesc3,
              }),
            ],
          }),
        ]),
      },
      {
        title: i18n.chain.jsonComp.event,
        className: 'p-none',
        body: [
          getSchemaTpl('eventControl', {
            name: 'onEvent',
            ...getEventControlConfig(this.manager, context),
          }),
        ],
      },
    ]);
  };
}

export default UploadPlugin;
