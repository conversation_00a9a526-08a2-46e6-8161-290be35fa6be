import { useController } from '@mdtBsComm/hooks/use-controller';
import { getAppIdFromUrl } from '@mdtProComm/utils/urlUtil';
import { getModel } from '../../_util/modelUtil';
import i18n from '../../languages';
import {
  DatapkgShare,
  DatapkgShareController,
  DatapkgShareModel,
  DatapkgShareModelBff,
} from '../../pages/datapkg-share';

const DatapkgShareView = () => {
  const [controller] = useController(() => {
    const ctrl = new DatapkgShareController({
      title: i18n.chain.datamarket.menu.share,
      Model: getModel(DatapkgShareModel, DatapkgShareModelBff),
      defaultAppId: getAppIdFromUrl() as number,
    });
    return [ctrl, null];
  });

  return <DatapkgShare controller={controller} />;
};

export default DatapkgShareView;
