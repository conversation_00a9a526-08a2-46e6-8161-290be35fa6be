import _ from 'lodash';
import { ChangeEvent, FC, Fragment, ReactNode, useEffect, useState } from 'react';
import { Select } from '@metroDesign/select';
import { If } from '@mdtBsComm/components/if';
import { useObservableState } from '@mdtBsComm/hooks/use-observable-state';
import Button, { LinkButton } from '@mdtDesign/button';
import { Dropelement } from '@mdtDesign/dropzone';
import Input from '@mdtDesign/input';
import Popconfirm from '@mdtDesign/popconfirm';
import Scrollbar from '@mdtDesign/scrollbar';
import Spin from '@mdtDesign/spin';
import Toggle from '@mdtDesign/toggle';
import i18n from '../../languages';
import { AppStyleController, IAppStyleKey, ICardInterface } from './AppStyleController';
import './index.less';

interface IProps {
  controller: AppStyleController;
}

interface IViewProps {
  data: ICardInterface;
  onChange?: (value: ICardInterface['value']) => void;
  onUpload?: (files: File[]) => void;
  onReset?: () => void;
}

const prefixCls = 'page_app-style';

const ImageView: FC<IViewProps> = ({ data, onUpload, onReset }) => {
  const { name, value, extra } = data;
  return (
    <div className={`${prefixCls}-image-view`}>
      <div className={`${prefixCls}-image-view-logo`}>
        <If data={value} else={<div className={`${prefixCls}-image-view-logo-skeletion`} />}>
          <img src={value} alt="logo" draggable={false} />
        </If>
      </div>
      <span className={`${prefixCls}-image-view-name`}>{name}</span>
      <Dropelement accept={extra.accept} onDropAccepted={(files) => onUpload?.(files)}>
        <Button>{i18n.chain.comButton.change}</Button>
      </Dropelement>
      <LinkButton leftIcon="reset" status="plain" onClick={onReset}>
        {i18n.chain.comButton.reset}
      </LinkButton>
    </div>
  );
};

const BooleanView: FC<IViewProps> = ({ data, onChange }) => {
  const { name, value } = data;
  return (
    <div className={`${prefixCls}-boolean-view`}>
      <div className={`${prefixCls}-boolean-view-name`}>{name}</div>
      <Toggle
        checked={value}
        showText
        checkedText={i18n.chain.orgAdmin.appStyle.open}
        unCheckedText={i18n.chain.orgAdmin.appStyle.close}
        onChange={onChange}
      />
    </div>
  );
};

const SelectView: FC<IViewProps> = ({ data, onChange }) => {
  const { name, value, extra } = data;
  return (
    <div className={`${prefixCls}-select-view`}>
      <div className={`${prefixCls}-select-view-name`}>{name}</div>
      <div className={`${prefixCls}-select-view-desc`}>{extra.description}</div>
      <Select value={value} options={extra.options} onChange={onChange} width={120} />
    </div>
  );
};

const StringView: FC<IViewProps> = ({ data, onChange, onReset }) => {
  const { name, value, extra } = data;

  const placeholder = extra?.placeholder || i18n.chain.comPlaceholder.input;
  const [editingValue, setEditingValue] = useState(value);
  const [editing, setEditing] = useState(false);

  useEffect(() => {
    !editing && setEditingValue(value);
  }, [editing, value]);

  const onInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value;
    setEditingValue(inputValue);
  };
  const onConfirm = () => {
    onChange?.(editingValue);
    setEditing(!editing);
  };

  const onCancel = () => {
    setEditingValue(value);
    setEditing(!editing);
  };

  const onEdit = () => {
    setEditing(!editing);
  };

  return (
    <div className={`${prefixCls}-string-view`}>
      <div className={`${prefixCls}-string-view-name`}>{name}</div>
      <If data={editing} else={<div className={`${prefixCls}-string-view-title`}>{value || '--'}</div>}>
        <Input
          value={editingValue}
          onChange={onInputChange}
          className={`${prefixCls}-string-view-title`}
          placeholder={placeholder}
        />
      </If>
      <If
        data={editing}
        else={
          <>
            <Button onClick={onEdit}>{i18n.chain.comButton.change}</Button>
            <LinkButton leftIcon="reset" status="plain" onClick={onReset}>
              {i18n.chain.comButton.reset}
            </LinkButton>
          </>
        }
      >
        <Button type="primary" onClick={onConfirm}>
          {i18n.chain.comButton.save}
        </Button>
        <Button onClick={onCancel} className={`${prefixCls}-string-view-cancel`}>
          {i18n.chain.comButton.cancel}
        </Button>
      </If>
    </div>
  );
};

const CardList: FC<IProps> = ({ controller }) => {
  const viewData = useObservableState(() => controller.getViewData$());
  const loadingKeys = useObservableState(() => controller.getLoadingKeys$());
  const groupRelations = controller.getGroupRelations();

  const TypeView: Record<ICardInterface['type'], ReactNode> = {
    image: ImageView,
    string: StringView,
    boolean: BooleanView,
    select: SelectView,
  };

  return (
    <Scrollbar className={`${prefixCls}-cardlist`}>
      {_.map(groupRelations, ({ title, keys }) => {
        return (
          <div key={title} className={`${prefixCls}-card`}>
            <div className={`${prefixCls}-card-title`}>{title}</div>
            {_.map(keys, (key: IAppStyleKey, index: number) => {
              const isLast = index === keys.length - 1;
              const data = viewData[key];
              const View = TypeView[data.type];
              const isViewNode = View && typeof View === 'function';
              const spinning = _.includes(loadingKeys, key);
              const onChange = (value: ICardInterface['value']) => {
                controller.onDataChanged(key, value);
              };
              const onUpload = (files: File[]) => {
                controller.onUploadFunc(files, key);
              };
              const onReset = () => {
                controller.onReset(key);
              };

              if (!isViewNode) return null;
              return (
                <Fragment key={key}>
                  <Spin spinning={spinning}>
                    <View data={data} onChange={onChange} onReset={onReset} onUpload={onUpload} />
                  </Spin>
                  <If data={!isLast}>
                    <div className={`${prefixCls}-card-divider`} />
                  </If>
                </Fragment>
              );
            })}
          </div>
        );
      })}
    </Scrollbar>
  );
};

const PageTitle: FC<IProps> = ({ controller }) => {
  const resetAll = () => controller.onReset('all');
  return (
    <div className={`${prefixCls}-page-title`}>
      <div className={`${prefixCls}-title`}>{i18n.chain.orgAdmin.menu.appStyle}</div>
      <Popconfirm
        okText={i18n.chain.comButton.confirm}
        cancelText={i18n.chain.comButton.cancel}
        message={i18n.chain.orgAdmin.appStyle.resetAllTips}
        description={i18n.chain.orgAdmin.appStyle.resetAllTipsDesc}
        placement="bottomRight"
        trigger="click"
        emotion="help-2"
        onConfirm={resetAll}
      >
        <Button leftIcon="reset">{i18n.chain.orgAdmin.appStyle.resetAll}</Button>
      </Popconfirm>
    </div>
  );
};

const AppStyle: FC<IProps> = ({ controller }) => {
  return (
    <div className={prefixCls}>
      <PageTitle controller={controller} />
      <CardList controller={controller} />
    </div>
  );
};

export { AppStyle };
