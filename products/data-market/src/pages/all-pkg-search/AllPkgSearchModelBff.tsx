import _ from 'lodash';
import { from, Observable } from 'rxjs';
import { map, takeWhile } from 'rxjs/operators';
import { getDatapkgSimpleListRespData } from '@mdtBsBffServices/services';
import { DatapkgPermissionEnum } from '@mdtProComm/constants';
import { IDatapkgsQuery, IRequestCancelToken } from '@mdtProComm/interfaces';
import { getDatapkgPermissionLabels } from '@mdtProComm/utils/datapkgUtil';
import { IDatapkgItem } from '@mdtProMicroModules/containers/table-card-datapkg-search';
import { DatapkgSearchModel } from '../../_util/DatapkgSearchModel';
import { pkgBffService } from '../../bff-service';
import i18n from '../../languages';

export class AllPkgSearchModelBff extends DatapkgSearchModel {
  // 查询第一页数据
  public static queryFirstPagePkgs(
    data: IDatapkgsQuery,
    params: any,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    cancelToken: IRequestCancelToken,
  ): Observable<[number, IDatapkgItem[]]> {
    return from(
      pkgBffService.queryDatapkgs({
        data,
        params,
        isFirstPage: true,
        respData: getDatapkgSimpleListRespData(['app']),
        cancelToken,
      }),
    ).pipe(
      takeWhile((resp) => !resp.canceled),
      map((resp) => {
        if (!resp.success) return [0, []] as [number, IDatapkgItem[]];
        const localePath = 'geo.';
        const list = _.map(resp.page_data, (item) => ({
          ...item,
          geoTypeDisplay: item.geoTypeDisplay?.includes(localePath)
            ? item.geoTypeDisplay
            : // @ts-ignore
              i18n.chain.geo[item.geoTypeDisplay],
          appName: item.app?.name,
          permissonLabels: getDatapkgPermissionLabels(_.difference(item.permissions, [DatapkgPermissionEnum.UPDATE])),
        }));
        return [resp.total_count, list] as unknown as [number, IDatapkgItem[]];
      }),
    );
  }

  // 查询下一页数据
  public static queryNextPageTickets(
    data: IDatapkgsQuery,
    params: any,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    cancelToken: IRequestCancelToken,
  ): Observable<IDatapkgItem[]> {
    return from(
      pkgBffService.queryDatapkgs({ data, params, respData: getDatapkgSimpleListRespData(['app']), cancelToken }),
    ).pipe(
      takeWhile((resp) => !resp.canceled),
      map((resp) => {
        if (!resp.success) return [];
        const localePath = 'geo.';
        return _.map(resp.page_data, (item) => ({
          ...item,
          geoTypeDisplay: item.geoTypeDisplay?.includes(localePath)
            ? item.geoTypeDisplay
            : // @ts-ignore
              i18n.chain.geo[item.geoTypeDisplay],
          appName: item.app?.name,
          permissonLabels: getDatapkgPermissionLabels(_.difference(item.permissions, [DatapkgPermissionEnum.UPDATE])),
        })) as any;
      }),
    );
  }
}
