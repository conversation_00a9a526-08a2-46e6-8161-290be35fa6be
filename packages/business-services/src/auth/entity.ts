import { queryEntitys } from '@mdtApis/api/auth';
import { IEntity, IEntitysQuery, IRequestRequestConfig, IServerResponse } from '../interfaces';

// 获取用户名称
export const queryUserNamesAsync = async (data: IEntitysQuery, config?: IRequestRequestConfig) => {
  return queryEntitys('user', data, config) as unknown as IServerResponse<IEntity[]>;
};

// 获取用户名称
export const queryUserNamesWithUuidAsync = async (data: IEntitysQuery, config?: IRequestRequestConfig) => {
  return queryEntitys('user_uuid', data, config) as unknown as IServerResponse<IEntity[]>;
};

// 获取app名称
export const queryAppNamesAsync = async (data: IEntitysQuery, config?: IRequestRequestConfig) => {
  return queryEntitys('app', data, config) as unknown as IServerResponse<IEntity[]>;
};

// 获取角色信息
export const queryRoleNamesAsync = async (data: IEntitysQuery, config?: IRequestRequestConfig) => {
  return queryEntitys('role', data, config) as unknown as IServerResponse<IEntity[]>;
};

// 获取群组信息
export const queryGroupNamesAsync = async (data: IEntitysQuery, config?: IRequestRequestConfig) => {
  return queryEntitys('group', data, config) as unknown as IServerResponse<IEntity[]>;
};
