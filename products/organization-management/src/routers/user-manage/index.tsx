import { useController } from '@mdtBsComm/hooks/use-controller';
import { AppController } from '../../app/AppController';
import { UserManage, UserManageController } from '../../pages/user-manage';
import { UserManageModel } from '../../pages/user-manage/UserManageModel';

const UserManageView = () => {
  const [controller] = useController(() => {
    const ctrl = new UserManageController(AppController.getInstance(), UserManageModel);
    return [ctrl, null];
  });
  return <UserManage controller={controller} />;
};

export default UserManageView;
