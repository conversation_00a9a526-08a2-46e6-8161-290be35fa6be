import React, { FC } from 'react';
import { Drawer } from '@metroDesign/drawer';
import { useObservableState } from '@mdtBsComm/hooks/use-observable-state';
import { useLoadFinish } from '../../_util/useLoadFinish';
import { useParentMessage } from '../../_util/useParentMessage';
import { Editor } from '../../containers/editor';
import { DrawerEditorController } from './DrawerEditorController';
import './index.less';

export const DrawerEditor: FC<{ controller: DrawerEditorController }> = ({ controller }) => {
  const open = useObservableState(controller.drawerVisible$);

  useParentMessage(controller);

  useLoadFinish(controller);

  return (
    <Drawer
      placement="right"
      width="100vw"
      maskClosable={false}
      destroyOnClose
      closable={false}
      open={open}
      okButtonProps={{ style: { display: 'none' } }}
      operatorsTarget="extra"
      className="drawer-amis-editor"
    >
      <Editor key={Math.random()} controller={controller as any} />
    </Drawer>
  );
};
