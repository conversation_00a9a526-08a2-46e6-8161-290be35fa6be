import { useController } from '@mdtBsComm/hooks/use-controller';
import { GrantedOtForm, GrantedOtFormController, GrantedOtFormModel } from '@mdtProMicroModules/pages/granted-ot-form';

const GrantedOtFormView = () => {
  const [controller] = useController(() => {
    const ctrl = new GrantedOtFormController({ Model: GrantedOtFormModel });
    return [ctrl, null];
  });

  return <GrantedOtForm controller={controller} />;
};

export default GrantedOtFormView;
