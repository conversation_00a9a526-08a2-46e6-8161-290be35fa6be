import _ from 'lodash';
import { utils, writeFile } from 'xlsx';

export interface IParams {
  data?: any[];
  dataType?: string;
  sheetName?: string;
  saveFile?: boolean;
  fileName?: string;
  fitWidth?: boolean;
  header?: string[];
}

/**
 * 将table、json数据导出为excel
 * @param {any[]} options
 * @param {[]} options.data 数据源
 * @param {"table"|"json"} options.dataType 数据类型
 * @param {string} options.sheetName sheet名称
 * @param {boolean} options.saveFile 是否保存为文件，如果为false则返回workBook
 * @param {string} options.fileName 文件名称
 * @param {boolean} options.fitWidth是否自适应列宽(如果dataType="json",配置此属性将无效)
 * @param {[]} options.header xlsx内部参数
 */
export const exportTo = ({
  data = [],
  dataType = 'json',
  sheetName = 'Sheet1',
  saveFile = true,
  fileName = 'template.xlsx',
  fitWidth = true,
  header = [],
}: // eslint-disable-next-line sonarjs/cognitive-complexity
IParams = {}) => {
  const getCellWidth = (value: any) => {
    if (_.isEmpty(value)) {
      return 10;
    } else if (/.*[\u4e00-\u9fa5]+.*$/.test(value)) {
      // 中文的长度
      const chineseLength = value.match(/[\u4e00-\u9fa5]/g).length;
      // 其他不是中文的长度
      const otherLength = value.length - chineseLength;
      return chineseLength * 2.1 + otherLength * 1.1;
    } else {
      return value.toString().length * 1.1;
    }
  };
  try {
    if (_.isEmpty(data)) throw 'exportTo: data is null or undefined.';

    let sheet: any = {};
    switch (dataType) {
      case 'table':
        sheet = utils.table_to_sheet(data, { raw: true });
        break;
      case 'json':
        // @ts-ignore
        sheet = utils.json_to_sheet(data, { row: true, header: header });

        if (fitWidth) {
          const colWidths: any[] = [];
          const colNames = Object.keys(data[0]); // 所有列的名称数组

          // 计算每一列的所有单元格宽度
          // 先遍历行
          _.forEach(data, (row) => {
            // 列序号
            let index = 0;
            // 遍历列
            _.forIn(row, (key) => {
              if (_.isEmpty(colWidths[index])) colWidths[index] = [];
              // eslint-disable-next-line sonarjs/no-nested-switch
              switch (typeof key) {
                case 'string':
                case 'number':
                case 'boolean':
                  colWidths[index].push(getCellWidth(key));
                  break;
                case 'object':
                case 'function':
                  colWidths[index].push(0);
                  break;
              }
              index++;
            });
          });

          sheet['!cols'] = [];
          // 每一列取最大值最为列宽
          _.forEach(colWidths, (widths: any, index: any) => {
            // 计算列头的宽度
            widths.push(getCellWidth(colNames[index]));
            // 设置最大值为列宽
            sheet['!cols'].push({ wch: Math.max(...widths) });
          });
        }
        break;
    }

    let workBook = {
      SheetNames: [sheetName],
      Sheets: {
        [sheetName]: sheet,
      },
    };

    if (saveFile) {
      writeFile(workBook, fileName);
      return true;
    } else {
      return workBook;
    }
  } catch (error) {
    console.error('exportTo: ', error);
    throw error;
  }
};
