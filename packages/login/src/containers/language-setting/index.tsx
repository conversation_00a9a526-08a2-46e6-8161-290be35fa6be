import { FC, MouseEvent, useState } from 'react';
import { Overlay } from '../../components/overlay';
import { Config<PERSON>eyEnum, ILabelValue, ILanguage, useConfig } from '../../config';

const LanguageSetting: FC = () => {
  const config = useConfig(ConfigKeyEnum.LANGUAGE) as ILanguage;
  const [state, setState] = useState(() => config.initialState());

  if (config.disable) return null;

  const _clickItem = (e: MouseEvent) => {
    e.stopPropagation();
    const language = (e.target as HTMLDivElement).getAttribute('data-value');
    if (!language) return;
    setState((v: any) => ({ ...v, language, visible: false }));
    config.changeLanguage?.(language);
  };

  const _onMouseEnter = () => {
    setState((v: any) => ({ ...v, visible: true }));
  };

  const _onMouseLeaver = () => {
    setState((v: any) => ({ ...v, visible: false }));
  };

  const languages = config.supportLanguages;
  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
  const item = languages.find((it: any) => it.value === state.language) || ({} as ILabelValue);

  const renderOverlay = () => {
    return languages.map(({ value, label }) => {
      return (
        <div key={value} className="mdt-login-overlay-item" data-value={value}>
          <div className="iti__name">{label}</div>
        </div>
      );
    });
  };

  return (
    <div className="mdt-login-language-setting-wrap" onMouseEnter={_onMouseEnter} onMouseLeave={_onMouseLeaver}>
      <div className="mdt-login-language-setting">
        <span className="language">{item.label}</span>
        <Overlay visible={state.visible} onClick={_clickItem} renderOverlay={renderOverlay} />
      </div>
    </div>
  );
};

LanguageSetting.displayName = 'LanguageSetting';
export { LanguageSetting };
