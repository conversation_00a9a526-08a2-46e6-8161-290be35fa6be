.page_db-detail {
  display: flex;
  flex-direction: row;
  width: 100%;
  height: 100%;

  .drawer-head {
    position: fixed;
    top: 0;
    left: 10vw;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: calc(90vw - 25px);
    height: 52px;
    margin-left: 25px;
    padding: 0 14px 0 25px;
    background-color: var(--dmc-primary-panel-3);

    .left-box {
      display: flex;
      align-items: center;

      .title-text {
        margin-left: 15px;
        color: var(--dmc-text-color);
        font-weight: 500;
        font-size: 14px;
        line-height: 20px;
      }

      .conn-status {
        margin-left: 8px;
      }
    }

    .right-btn {
      margin-left: 8px;
    }
  }

  &_loading {
    width: 100%;
    height: 100%;
  }

  &_empty {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
  }

  .preview-table-container {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;

    .preview-table-head {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      height: 48px;
      padding: 0 12px 0 20px;
      background: var(--dmc-page-100-color);
      border: 1px solid var(--dmc-split-page-color);

      .left-box {
        font-weight: 500;
        font-size: 14px;
        font-style: normal;
        line-height: 20px;

        span {
          margin-right: 16px;
          color: var(--dmc-text-8);
        }

        .dmc-btn-link {
          font-weight: 500;
        }
      }

      .right-box {
        box-sizing: border-box;
        border: 1px solid var(--dmc-split-page-color);
        border-radius: 6px;
      }
    }
  }
}
