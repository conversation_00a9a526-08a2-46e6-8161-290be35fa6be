import axios from 'axios';
import getDmRc from '@datlas/dm-rc';
import { initRequestConfig } from '@mdtApis/request';
import { IRequestError, IRequestRequestConfig, IRequestResponse, IServerResponse } from './interfaces';

const request = axios.create();

request.interceptors.response.use(
  (response) => {
    const { status, data } = response;
    if ((status >= 200 && status < 300) || data?.rc === 0) {
      return Promise.resolve(response);
    }
    return Promise.reject(response);
  },
  (error) => {
    return Promise.reject(error);
  },
);

const requestSuccess = (resp: IRequestResponse): IServerResponse => {
  const { data = {} } = resp;
  let result = data;
  if (data.rc === 0 && data.result) {
    result = data.result;
  }
  return { success: true, data: result };
};

const requestError = (error: IRequestError): IServerResponse => {
  if (error instanceof axios.Cancel || (error.config as IRequestRequestConfig).quiet === true) {
    return { success: false, canceled: true };
  }

  if (!error.response) {
    const msg = getDmRc({ status: 0 });
    return { success: false, msg, rc: 0 };
  }

  const { status, data } = error.response;
  if (status >= 401) {
    const msg = getDmRc({ status });
    return { success: false, msg, rc: status };
  }

  const { rc, msg_args } = data || {};
  const msg = getDmRc({ rc, msg_args });
  return { success: false, msg, rc };
};

export const updateRequestDefaults = (baseUrl: string, token: string, impersonateToken?: string) => {
  request.defaults.baseURL = baseUrl;
  const requestHeader = impersonateToken
    ? { Authorization: token, 'X-Impersonate-Token': impersonateToken }
    : { Authorization: token };
  // @ts-ignore
  request.defaults.headers.common = { ...request.defaults.headers.common, ...requestHeader };
};

// 初始化配置
initRequestConfig(request, requestSuccess, requestError);

export { request };
