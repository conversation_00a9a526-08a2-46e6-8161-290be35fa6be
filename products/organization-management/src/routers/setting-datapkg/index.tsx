import { useController } from '@mdtBsComm/hooks/use-controller';
import { AppController } from '../../app/AppController';
import { DataSetting, DataSettingController, DataSettingModel } from '../../pages/data-setting';

const PreferenceDatapkgView = () => {
  const [controller] = useController(() => {
    const app = AppController.getInstance();
    const ctrl = new DataSettingController(app, DataSettingModel.initModel(app));
    return [ctrl, null];
  });

  return <DataSetting controller={controller} />;
};

export default PreferenceDatapkgView;
