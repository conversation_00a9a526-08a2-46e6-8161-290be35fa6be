import { useController } from '@mdtBsComm/hooks/use-controller';
import { DatlasAppController } from '@mdtProMicroModules/datlas/app/DatlasAppController';
import { WorkflowSettingH5, WorkflowSettingH5Controller } from '@mdtProMicroModules/pages/workflow-setting-h5';

const PageSettingH5 = () => {
  const [controller] = useController(() => {
    const ctrl = new WorkflowSettingH5Controller({
      app: DatlasAppController.getInstance(),
      showBottomMenu: true,
    });
    return [ctrl, null];
  });

  return <WorkflowSettingH5 controller={controller} />;
};

export default PageSettingH5;
