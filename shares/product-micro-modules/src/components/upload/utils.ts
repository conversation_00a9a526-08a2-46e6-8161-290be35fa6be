import _ from 'lodash';
import { toastApi } from '@metroDesign/toast';
import imageCompression, { type Options as ImageCompressionOptions } from 'browser-image-compression';
import { downloadFromUrl } from '@mdtBsComm/utils/downloadUtil';
import { splitKey } from '@mdtBsComm/utils/stringUtil';
import { getFileUrlAsync } from '@mdtBsServices/files';
import { FileTypeEnum } from '@mdtProComm/constants';
import type { IFileType } from '@mdtProComm/interfaces';
import i18n from '../../languages';

export const uploadFileTypeList = [
  FileTypeEnum.CODE,
  FileTypeEnum.VIDEO,
  FileTypeEnum.IMAGE,
  FileTypeEnum.DOC,
  FileTypeEnum.DATA,
  FileTypeEnum.THUMBNAIL,
  FileTypeEnum.DOWNLOAD,
];

export const getValidUploadFileType = (fileType: IFileType): FileTypeEnum => {
  if (_.includes(uploadFileTypeList, fileType)) return fileType as FileTypeEnum;
  return FileTypeEnum.DOC;
};

export const isViewableFile = (fileType: IFileType, format: string) => {
  return fileType === FileTypeEnum.IMAGE || (fileType === FileTypeEnum.DOC && format === 'pdf');
};

export const downloadFileById = async (id: string) => {
  const fileResp = await getFileUrlAsync(id, {
    params: { redirect: false },
    quiet: true,
  });
  if (!fileResp.success) {
    toastApi.error(i18n.chain.proMicroModules.formView.downloadFileError);
    return;
  }
  const url = fileResp.data?.sign_url.url || '';
  const fileName = splitKey(fileResp.data?.name || '')[0];
  url && downloadFromUrl(url, fileName);
};

// 检查文件大小是否超过限制
export const checkFileSize = (file: File, maxSize?: number): { limit: boolean; fileSize: number } => {
  const fileSize = file.size;
  const limit = maxSize ? fileSize > maxSize : false;

  return {
    limit,
    fileSize,
  };
};

// 压缩图片到指定大小
export const compressImage = async (file: File, options: ImageCompressionOptions) => {
  const isImage = file.type.includes('image');
  if (!isImage) {
    return file;
  }
  return await imageCompression(file, options);
};
