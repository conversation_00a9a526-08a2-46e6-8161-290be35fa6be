import _ from 'lodash';
import { combineLatest, from, Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { IPage, IPagesQuery, IProject, IProjectsQuery } from '@mdtApis/interfaces';
import { getFileUrlAsync } from '@mdtBsServices/files';
import { queryPagesPaginationAsync } from '@mdtBsServices/pages';
import { deletePreferencesBatchAsync, putUserPreferencesBatchAsync } from '@mdtBsServices/preference';
import { queryProjectsPaginationAsync } from '@mdtBsServices/projects';
import { PreferencesEnum } from '@mdtProComm/constants';
import { IOrgs, IRoles, IUsers } from '@mdtProComm/interfaces';
import { IHomeInfo } from '@mdtProMicroModules/containers/drawer-choose-home-page';
import { IUserQueryConfig, UserModel } from '@mdtProMicroModules/models/UserModel';
import { concurRequest } from '../../_util/concurRequest';
import pageDark from './page-dark.svg';
import pageLight from './page-light.svg';

export interface IHomePage {
  id: string;
  title: string;
  updateTime: number;
  preview: string;
  defaultPlay?: boolean;
  url: string;
  type: 'page' | 'project';
}

// 扩展user
export interface IHomeUsers extends IUsers {
  homeInfo?: IHomeInfo;
}

export interface IHomeUsersRespData {
  userList: IHomeUsers[];
  orgList: IOrgs[];
  total: number;
}

export interface IRolesAndOrgsData {
  roleList: IRoles[];
  orgList: IOrgs[];
  roleIdNameMap: Record<number, string>;
  orgIdNameMap: Record<string, IOrgs>;
}

class HomeSettingModel {
  public static queryUsers(params: IUserQueryConfig) {
    return from(
      new Promise<IHomeUsers[]>((resolve) => {
        const request = async () => {
          const usersResp = await UserModel.queryUsersPagination(params).toPromise();

          resolve(usersResp);
        };
        request();
      }),
    );
  }

  public static queryUsersTotal(params: IUserQueryConfig) {
    return from(
      new Promise<[number, IHomeUsers[]]>((resolve) => {
        const request = async () => {
          const totalResp = await UserModel.queryUsersPaginationTotal(params).toPromise();

          resolve(totalResp);
        };
        request();
      }),
    );
  }

  // 获取带有大屏id的用户列表
  public static queryUsersWithPage(params: IUserQueryConfig) {
    return from(
      new Promise<IHomeUsersRespData>((resolve) => {
        const request = async () => {
          const [total, userList] = await this.queryUsersTotal(params).toPromise();

          resolve({
            userList,
            orgList: [],
            total,
          });
        };
        request();
      }),
    ).pipe(
      map((resp) => {
        return resp;
      }),
    );
  }

  // 获取角色和部门数据
  public static queryRolesAndOrgs(appId: number, rootId: string): Observable<IRolesAndOrgsData> {
    return from(UserModel.queryRolesAndOrgs(appId, rootId)).pipe(
      map((value) => {
        const { roleList, orgList, roleIdNameMap, orgIdNameMap } = value;
        return {
          roleList,
          orgList,
          roleIdNameMap,
          orgIdNameMap,
        };
      }),
    );
  }

  public static getUserPageUrl(user: IHomeInfo, theme: string): Observable<IHomeInfo> {
    const defaultSrc = theme === 'dark' ? pageDark : pageLight;
    return from(
      new Promise<IHomeInfo>((resolve) => {
        const request = async () => {
          const urlResp = user.preview
            ? await getFileUrlAsync(user.preview, { params: { redirect: false }, quiet: true })
            : { success: true, data: { sign_url: { url: user.url } } };
          const url = urlResp.success ? urlResp.data?.sign_url.url || defaultSrc : defaultSrc;
          resolve({ ...user, url });
        };
        request();
      }),
    );
  }

  public static queryPageProjectList(theme: string, search?: string): Observable<IHomePage[]> {
    return from(
      new Promise<IHomePage[]>((resolve) => {
        const request = async () => {
          const params: IProjectsQuery = {
            order_by: '-update_time',
            with_status: true,
            page_num: 0,
            page_size: 200,
          };
          search && (params.fuzzy_name = search);
          const listResp = await queryProjectsPaginationAsync({ params });
          const filteredList = _.filter(listResp.data?.dataResult, ({ project_status: status }) => status !== 0);
          const dataResult: [IProject, string][] = _.map(filteredList, (d) => [d, theme]) || [];
          const res = (await concurRequest({
            promiseArgs: dataResult,
            promiseFunc: this.transformProject,
          })) as IHomePage[];
          resolve(res);
        };
        request();
      }),
    );
  }

  public static queryPageList(theme: string, search?: string): Observable<IHomePage[]> {
    return from(
      new Promise<IHomePage[]>((resolve) => {
        const request = async () => {
          const params: IPagesQuery = {
            order_by: '-update_time',
            page_num: 0,
            page_size: 200,
          };
          search && (params.name_like = search);
          const listResp = await queryPagesPaginationAsync({ params });
          const dataResult: [IPage, string][] = _.map(listResp.data?.dataResult, (d) => [d, theme]) || [];
          const res = (await concurRequest({
            promiseArgs: dataResult,
            promiseFunc: this.transformPage,
          })) as IHomePage[];
          resolve(res);
        };
        request();
      }),
    );
  }

  public static queryList(theme: string): Observable<IHomePage[]> {
    return combineLatest(this.queryPageProjectList(theme), this.queryPageList(theme)).pipe(
      map(([v1, v2]) => [...v1, ...v2]),
    );
  }

  // 合并用户和偏好数据
  public static enhanceUsersWithPreferences(
    users: IHomeUsers[],
    preferences: Record<string, IHomeInfo>,
    theme: string,
  ): Promise<IHomeUsers[]> {
    return new Promise((resolve) => {
      const process = async () => {
        const promiseArgs = _.map(users, (user) => [preferences[user.id], theme]);
        const homeInfoArrs = (await concurRequest({
          promiseArgs,
          promiseFunc: async (page: IHomeInfo) => page,
        })) as IHomePage[];

        const enhancedUserList = _.map(users, (user, i) => ({
          ...user,
          homeInfo: homeInfoArrs[i],
        }));

        resolve(enhancedUserList);
      };
      process();
    });
  }

  // 上传首页设置的偏好
  public static putHomePagePreference = (users: number[], values: IHomeInfo) => {
    const { title, type, id, preview } = values;
    return from(
      putUserPreferencesBatchAsync({
        values: {
          [PreferencesEnum.USER_HOME_PAGE]: {
            title,
            type,
            id,
            preview,
            defaultPlay: true,
          },
        },
        layer_ids: users,
      }),
    ).pipe(
      map((resp) => {
        return { ...resp };
      }),
    );
  };

  // 删除用户偏好
  public static deleteHomePagePreference = (users: number[]) => {
    return from(deletePreferencesBatchAsync({ names: [PreferencesEnum.USER_HOME_PAGE], layer_ids: users })).pipe(
      map((resp) => {
        return { ...resp };
      }),
    );
  };

  // 将组织列表转换为树形结构
  private static transformOrgsToTree(orgList: IOrgs[], rootId: string): IOrgs[] {
    return _.map(
      _.filter(orgList, (org) => org.pid === rootId),
      (org) => ({
        ...org,
        children: this.getOrgChildren(orgList, org.key),
      }),
    );
  }

  // 获取组织的子节点
  private static getOrgChildren(orgList: IOrgs[], parentKey: string): IOrgs[] {
    return _.map(
      _.filter(orgList, (org) => org.pid === parentKey),
      (org) => ({
        ...org,
        children: this.getOrgChildren(orgList, org.key),
      }),
    );
  }

  // 翻译成可使用的大屏结构
  private static transformPage = async (page: IPage, theme: string): Promise<IHomePage> => {
    const { vaultTitle: title, update_time: updateTime, preview, id } = page;
    const defaultSrc = theme === 'dark' ? pageDark : pageLight;
    const urlResp = await getFileUrlAsync(preview, { params: { redirect: false }, quiet: true });
    const url = urlResp.success ? urlResp.data?.sign_url.url || defaultSrc : defaultSrc;
    return {
      id,
      title,
      updateTime,
      preview,
      url,
      type: 'page',
    };
  };

  // 项目翻译成可使用的大屏结构
  private static transformProject = async (page: IProject, theme: string): Promise<IHomePage> => {
    const { update_time: updateTime, create_time: createTime, extra, project_id, name } = page;
    const { preview } = extra?.homePage || {};
    const defaultSrc = theme === 'dark' ? pageDark : pageLight;
    return {
      id: project_id,
      title: name,
      updateTime: updateTime ?? createTime,
      preview: preview || '',
      url: defaultSrc,
      type: 'project',
    };
  };
}
export type IHomeSettingModel = typeof HomeSettingModel;
export { HomeSettingModel };
