import { useContext } from 'react';
import { createNameContext } from '@mdtBsComm/utils/contextUtil';
import { TableCurdWithSimpleSearchController } from './TableCurdWithSimpleSearchController';

export interface IContext {
  tableCurdWithSimpleSearchController: TableCurdWithSimpleSearchController<any>;
}

const context = createNameContext<IContext>('TableCurdWithSimpleSearchController');

export const useTableCurdWithSimpleSearchProvider = () => {
  return context.Provider;
};

export const useTableCurdWithSimpleSearchContext = () => {
  return useContext(context);
};
