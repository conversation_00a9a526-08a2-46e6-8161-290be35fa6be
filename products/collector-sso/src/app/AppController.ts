import { History } from 'history';
import { RequestClass } from '@mdtProSso/request';
import { RoutePathEnum } from '../_util/constants';
import {
  API_URL,
  API_URL_BASE_PATH,
  ENCODE_APIS,
  ENCODE_APIS_OPTIONS,
  PROXY_URL,
  TRANSFORM_API_METHOD,
} from '../config';

/**
 * 使用单例模式
 */
class AppController {
  // 全局私有变量
  private static instance: AppController | undefined;
  // 获取实例
  public static getInstance(history?: History) {
    if (!AppController.instance) {
      AppController.instance = new AppController(history!);
    }
    return AppController.instance;
  }
  // 销毁单例
  public static destroy() {
    AppController.instance?.destroy();
    AppController.instance = undefined;
  }
  // 获取路径
  public static getVerifyPath() {
    return AppController.instance?.getVerifyPath();
  }

  private history: History;
  private fetchRequest: RequestClass;
  private constructor(history: History) {
    this.history = history;
    this.fetchRequest = new RequestClass({
      encodeApis: ENCODE_APIS,
      encodeApisOptions: ENCODE_APIS_OPTIONS,
      transformApiMethod: TRANSFORM_API_METHOD,
      baseUrl: API_URL + API_URL_BASE_PATH,
      proxyUrl: PROXY_URL,
    });
  }

  public destroy() {
    this.history.length = 0;
  }

  public getFetchRequest() {
    return this.fetchRequest;
  }

  public getVerifyPath() {
    const path = this.history.createHref({ pathname: RoutePathEnum.VERIFY });
    const url = new URL(path, window.location.origin);
    return url.toString();
  }
}

export { AppController };
