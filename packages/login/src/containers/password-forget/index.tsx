import { ChangeEvent, FC, useContext, useState } from 'react';
import { Button } from '../../components/button';
import { FormItem } from '../../components/form-item';
import { CaptchaInput, Input, PasswordInput } from '../../components/input';
import { Spin } from '../../components/spin';
import { ConfigKeyEnum, IPasswordForget, useConfig } from '../../config';
import { LoginContext } from '../global-context/context';
import { LoginTitle } from '../login-title';

const PasswordForget: FC = () => {
  const { isPasswordForgetState } = useContext(LoginContext);
  const [, setIsPasswordForget] = isPasswordForgetState || [];
  const config = useConfig(ConfigKeyEnum.PASSWORD_FORGET) as IPasswordForget;
  const [state, setState] = useState(() => config.initialState());
  const [accountChecked, setAccountChecked] = useState(false);
  const [accountLoading, setAccountLoading] = useState(false);

  const onAccountBlur = async (e: ChangeEvent<HTMLInputElement>) => {
    if (accountChecked) {
      return;
    }
    const account = e.target.value.trim();
    setAccountLoading(true);
    const accountError = await config.verifyAccount(account, e.type);
    setAccountLoading(false);
    !accountError && setAccountChecked(true);
    setState((v: any) => ({ ...v, account, accountError }));
  };

  const onPhoneChange = async (e: ChangeEvent<HTMLInputElement>) => {
    setAccountChecked(false);
    const account = e.target.value.trim();
    setState((v: any) => ({ ...v, account, accountError: '' }));
  };

  const onCaptchaBlur = async (e: ChangeEvent<HTMLInputElement>) => {
    const captcha = e.target.value.trim();
    const captchaError = await config.verifyCaptcha(captcha);
    setState((v: any) => ({ ...v, captcha, captchaError }));
  };

  const onCaptchaChange = async (e: ChangeEvent<HTMLInputElement>) => {
    const captcha = e.target.value.trim();
    setState((v: any) => ({ ...v, captcha, captchaError: '' }));
  };

  const onPasswordChange = async (e: ChangeEvent<HTMLInputElement>) => {
    const password = e.target.value.trim();
    const passwordError = await config.verifyPassword(password);
    setState((v: any) => ({ ...v, password, passwordError }));
  };

  const onPasswordConfirmChange = async (e: ChangeEvent<HTMLInputElement>) => {
    const passwordConfirm = e.target.value.trim();
    const passwordConfirmError = await config.verifyPasswordConfirm({ ...state, passwordConfirm });
    setState((v: any) => ({ ...v, passwordConfirm, passwordConfirmError }));
  };

  const goBack = () => {
    setIsPasswordForget?.(false);
  };

  // 按下回车或登录按钮
  const onPressLogin = async () => {
    const [hasError, allError] = await config.verifyAll(state);
    setState((v: any) => ({ ...v, ...allError }));
    // 如果都没有错误
    if (hasError) return;
    setState((v: any) => ({ ...v, buttonLoading: true, buttonDisabled: true }));

    await config.onSubmit({ ...state, buttonLoading: true, buttonDisabled: true }, setState, goBack);
  };

  // 发送验证码
  const sendCaptcha = (send: () => void) => {
    config.sendCaptcha(state);
    send();
  };

  // 返回上一页
  const goBackClick = (e: any) => {
    if (config.gobackClick) {
      config.gobackClick?.(e, goBack);
    } else {
      goBack();
    }
  };

  return (
    <div className="mdt-login-password-forget-container">
      <div className="mdt-login-password-forget-content">
        <div className="mdt-login-goback" onClick={goBackClick}>
          <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M11.5575 5.5575L10.5 4.5L6 9L10.5 13.5L11.5575 12.4425L8.1225 9L11.5575 5.5575Z" fill="#202939" />
          </svg>
        </div>
        <LoginTitle title={config.title} />
        <form autoComplete="off">
          {state.steps === 'check' && (
            <>
              <FormItem label={config.accountLabel} msgTip={state.accountError}>
                <Input
                  value={state.account}
                  hasError={!!state.accountError}
                  placeholder={config.accountPlaceholder}
                  onBlur={onAccountBlur}
                  onChange={onPhoneChange}
                  onPressEnter={onPressLogin}
                  suffix={accountLoading && <Spin />}
                />
              </FormItem>
              <FormItem label={config.captchaLabel} msgTip={state.captchaError}>
                <CaptchaInput
                  value={state.captcha}
                  hasError={!!state.captchaError}
                  placeholder={config.captchaPlaceholder}
                  interval={config.captchaInterval}
                  captchaButtonLabel={config.captchaButtonLabel}
                  captchaButtonSendedLabel={config.captchaButtonSendedLabel}
                  disabled={!state.account}
                  sendCaptcha={sendCaptcha}
                  onBlur={onCaptchaBlur}
                  onChange={onCaptchaChange}
                  onPressEnter={onPressLogin}
                />
              </FormItem>
            </>
          )}
          {state.steps === 'confirm' && (
            <>
              <FormItem label={config.passwordLabel} msgTip={state.passwordError}>
                <PasswordInput
                  value={state.password}
                  hasError={!!state.passwordError}
                  placeholder={config.passwordPlaceholder}
                  onChange={onPasswordChange}
                  onPressEnter={onPressLogin}
                />
              </FormItem>
              <FormItem label={config.passwordConfirmLabel} msgTip={state.passwordConfirmError}>
                <PasswordInput
                  value={state.passwordConfirm}
                  hasError={!!state.passwordConfirmError}
                  placeholder={config.passwordConfirmPlaceholder}
                  onChange={onPasswordConfirmChange}
                  onPressEnter={onPressLogin}
                />
              </FormItem>
            </>
          )}
          {config.renderExtraFormItem?.(state, setState)}
        </form>
        <Button loading={state.buttonLoading} disabled={state.buttonDisabled} onClick={onPressLogin}>
          {state.buttonLoading ? state.buttonLoadingLabel : state.buttonLabel}
        </Button>
      </div>
    </div>
  );
};

PasswordForget.displayName = 'PasswordForget';
export { PasswordForget };
