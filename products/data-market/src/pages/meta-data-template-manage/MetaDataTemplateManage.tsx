import { FC } from 'react';
import { useObservableState } from '@mdtBsComm/hooks/use-observable-state';
import { TableTaggroup } from '@mdtProMicroModules/containers/table-taggroup';
import { TableTheme } from '@mdtProMicroModules/containers/table-theme';
import { MetaDataTemplateManageController, TagsPage } from './MetaDataTemplateManageController';
import styles from './index.module.less';

interface IProps {
  controller: MetaDataTemplateManageController;
}

const MetaDataTemplateManage: FC<IProps> = ({ controller }) => {
  const activePage = useObservableState(controller.getActivePage$());

  const page =
    activePage === TagsPage.THEME ? (
      <TableTheme controller={controller.getThemesController()} />
    ) : (
      <TableTaggroup controller={controller.getTaggroupController()} />
    );

  return <div className={styles.tagsManagePage}>{page}</div>;
};

export { MetaDataTemplateManage };
