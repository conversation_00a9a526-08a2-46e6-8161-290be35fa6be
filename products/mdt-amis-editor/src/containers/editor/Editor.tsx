import { FC } from 'react';
import { But<PERSON> } from '@metroDesign/button';
import { Select } from '@metroDesign/select';
import { Editor as AmisEditor, ShortcutKey } from 'amis-editor';
import { useObservableState } from '@mdtBsComm/hooks/use-observable-state';
import './disabledEditorPlugin';
import i18n from '../../languages';
import Plugins from '../../plugin';
import { editorEnv } from './editor-expand/amis-env';
import { ReactComponent as H5Icon } from './icons/h5-preview.svg';
import { ReactComponent as PcIcon } from './icons/pc-preview.svg';
import { EditorController } from './EditorController';
import { editorLanguages, getSchemaUrl, varsSchema } from './util';
import './index.less';

export interface IEditorProps {
  controller: EditorController;
}

const ViewMode: FC<IEditorProps> = ({ controller }) => {
  const isMobile = useObservableState(controller.isMobile$);

  const changeToMobileMode = () => {
    controller.changeIsMobile(true);
  };

  const changeToPCMode = () => {
    controller.changeIsMobile(false);
  };

  return (
    <div className="editor-view-mode-group">
      <div
        className={`editor-view-mode-btn editor-header-icon ${!isMobile ? 'is-active' : ''}`}
        onClick={changeToPCMode}
      >
        <PcIcon />
      </div>
      <div
        className={`editor-view-mode-btn editor-header-icon ${isMobile ? 'is-active' : ''}`}
        onClick={changeToMobileMode}
      >
        <H5Icon />
      </div>
    </div>
  );
};

const LocaleSelect: FC<IEditorProps> = ({ controller }) => {
  const curLanguage = useObservableState(controller.language$);

  const changeLocale = (value: string) => {
    controller.changeLocale(value);
  };

  return (
    <Select
      className="margin-left-space"
      options={editorLanguages}
      value={curLanguage}
      allowClear={false}
      onChange={(e: any) => changeLocale(e.value)}
    />
  );
};

const PreviewBtn: FC<IEditorProps> = ({ controller }) => {
  const isPreview = useObservableState(controller.isPreview$);

  const onPreview = () => {
    controller.changeIsPreview(!isPreview);
  };

  return (
    <Button className="header-action-btn" type={isPreview ? 'primary' : 'secondary'} onClick={onPreview}>
      {isPreview ? i18n.chain.edit : i18n.chain.preview}
    </Button>
  );
};

const EditorInner: FC<IEditorProps> = ({ controller }) => {
  const isMobile = useObservableState(controller.isMobile$);
  const isPreview = useObservableState(controller.isPreview$);
  const value = useObservableState(controller.value$);
  const ctx = useObservableState(controller.ctx$);
  return (
    <div className="editor-inner">
      {controller ? (
        <AmisEditor
          theme="cxd"
          i18nEnabled={false}
          preview={isPreview}
          isMobile={isMobile}
          value={value}
          onChange={controller.handleValueChange}
          className="is-fixed"
          $schemaUrl={getSchemaUrl()}
          showCustomRenderersPanel={true}
          amisEnv={editorEnv}
          plugins={Plugins}
          schemas={varsSchema}
          ctx={ctx}
        />
      ) : null}
    </div>
  );
};

export const Editor: FC<IEditorProps> = ({ controller }) => {
  const headerComp = controller.renderHeaderTitle ? (
    controller.renderHeaderTitle()
  ) : (
    <div className="editor-title">{i18n.chain.editorTitle}</div>
  );

  const extraBtn = controller.renderExtraBtns ? controller.renderExtraBtns() : null;

  const lsComp = controller.showLanguage ? <LocaleSelect controller={controller} /> : null;

  return (
    <div className="editor-page">
      <div className="editor-header">
        {headerComp}
        <div className="editor-view-mode-group-container">
          <ViewMode controller={controller} />
        </div>
        <div className="editor-header-actions">
          <ShortcutKey />
          {lsComp}
          <PreviewBtn controller={controller} />
          {extraBtn}
        </div>
      </div>
      <EditorInner controller={controller} />
    </div>
  );
};
