import { useContext } from 'react';
import { createNameContext } from '@mdtBsComm/utils/contextUtil';
import FlowHomeController from './FlowHomeController';

export interface IContext {
  flowHomeController: FlowHomeController;
}

const context = createNameContext<IContext>('FlowHomeController');

export const useFlowHomeProvider = () => {
  return context.Provider;
};

export const useFlowHomeContext = () => {
  return useContext(context);
};
