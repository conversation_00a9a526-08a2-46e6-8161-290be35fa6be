export interface IMonitorViewRow {
  key: string;
  pkg_name: string;
  // creator: string;
  manual_result: boolean | null;
  auto_result: boolean | null;
  pkg_update_time: number | null;
  constraints_update_time: number | null;
}

export interface ITimedTaskViewRow {
  key: string;
  source_name: string;
  task_name: string;
  creator: string;
  task_type: string;
  trigger_type: string;
  active?: boolean;
  task_update_time?: number;
  task_range_time?: number[];
}
