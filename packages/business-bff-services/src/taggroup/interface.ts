import { ITaggroupPost, ITaggroupsQuery } from '@mdtApis/interfaces';
import { ICancelTokenOption } from '../interfaces';

export interface IQueryTaggroupListOptions extends ICancelTokenOption {
  params?: ITaggroupsQuery;
}

export interface ICreateTaggroupOptions extends ICancelTokenOption {
  data: ITaggroupPost;
}

export interface IUpdateTaggroupOptions extends ICancelTokenOption {
  id: string;
  data: ITaggroupPost;
}

export interface IDeleteTaggroupOptions extends ICancelTokenOption {
  id: string;
}
