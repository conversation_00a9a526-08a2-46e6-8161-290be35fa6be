import { FC } from 'react';
import { useObservableState } from '@mdtBsComm/hooks/use-observable-state';
import { ModalWithBtnsCompEmotion } from '@mdtBsComponents/modal-with-btns-comp-emotion';
import Button from '@mdtDesign/button';
import Input from '@mdtDesign/input';
import Select from '@mdtDesign/select';
import { resourceTypeOptions } from '@mdtProComm/utils/resourceUtil';
import { TableCurdWithSimpleSearch } from '../../containers/table-curd-with-simple-search';
import i18n from '../../languages';
import { ResourceShareBatchAuth } from '../resource-share-batch-auth';
import { ResourceShareOtherUpdateController } from './ResourceShareOtherUpdateController';
import './index.less';

interface IProps {
  controller: ResourceShareOtherUpdateController;
}

const SearchBox = ({ controller }: IProps) => {
  const searchVal = useObservableState(controller.getSearchVal$());
  const filterVal = useObservableState(controller.getFilterValVal$());

  return (
    <div className="operation-wrap">
      <ResourceShareBatchAuth
        getSelectedData={() => controller.getSelectedDataListValue()}
        onOk={controller.handleUpdatePs}
        appPkgPermissions={controller.getAppPkgPermissions()}
      />
      <Button onClick={() => controller.cancelPermission()}>
        {i18n.chain.proMicroModules.resource.removeResource}
      </Button>
      <Input
        prefixIcon="search"
        value={searchVal}
        onChange={(e) => controller.changeSearchVal$(e.target.value)}
        placeholder={i18n.chain.proMicroModules.resource.searchResource}
        allowClear={false}
      />
      <Select
        value={filterVal}
        onChange={(val) => controller.changeFilterValVal$(val)}
        options={resourceTypeOptions}
        placeholder={i18n.chain.comPlaceholder.select}
      />
    </div>
  );
};

export const ResourceShareOtherUpdate: FC<IProps> = ({ controller }) => {
  const update = useObservableState(controller.getUpdatePs$());
  return (
    <div className="module_table-my-resource-share-update">
      <SearchBox controller={controller} />
      <TableCurdWithSimpleSearch controller={controller} />
      <ModalWithBtnsCompEmotion key={update} controller={controller.getDeleteController()} />
    </div>
  );
};
