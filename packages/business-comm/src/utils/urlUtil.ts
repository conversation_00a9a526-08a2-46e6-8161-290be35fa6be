import _ from 'lodash';
import { compressToEncodedURIComponent, decompressFromEncodedURIComponent } from 'lz-string';

export const getParamValueFromUrl = (param: string, url = window.location.href): string => {
  const name = _.replace(param, /[\[\]]/g, '\\$&');
  const regex = new RegExp('[?&]' + name + '(=([^&#]*)|&|#|$)');
  const results = regex.exec(url);
  if (!results || !results[2]) return '';
  return decodeURIComponent(_.replace(results[2], /\+/g, ' ')) || '';
};

export const getDecompressValueFromUrl = (key: string, url?: string): string => {
  let val = getParamValueFromUrl(key, url);
  val = decompressFromEncodedURIComponent(val) ?? '';
  return val;
};

export const saveCompressValueToUrl = (value: string | object): string => {
  let val = _.isString(value) ? value : JSON.stringify(value);
  val = compressToEncodedURIComponent(val);
  return val;
};

// Image -> base64
export const imageToBase64 = (image: HTMLImageElement) => {
  let canvas = document.createElement('canvas');
  let width = image.width;
  let height = image.height;

  canvas.width = width;
  canvas.height = height;
  let context = canvas.getContext('2d');
  context?.drawImage(image, 0, 0, width, height);
  return canvas.toDataURL('image/png');
};

// url -> base64
export const urlToBase64 = (url: string, callback: any) => {
  let image = new Image();

  image.setAttribute('crossOrigin', 'Anonymous');
  image.src = url;

  image.onload = function () {
    let dataURL = imageToBase64(image);
    callback?.(dataURL);
  };
};

// url -> base64 async
export const urlToBase64Async = (url: string) => {
  return new Promise((resolve) => {
    urlToBase64(url, (data: any) => {
      resolve(data);
    });
  });
};
