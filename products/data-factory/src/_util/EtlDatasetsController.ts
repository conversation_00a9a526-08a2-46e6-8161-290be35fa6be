import _ from 'lodash';
import { IDataset } from '@mdtBsServices/interfaces';
import { DatasetRoleEnum } from '@mdtProComm/constants/enum';
import { DatasetsController } from '@mdtProComm/controllers/DatasetsController';
import { ILabelValue } from '@mdtProComm/interfaces';
import { AppController } from '../app/AppController';
import i18n from '../languages';

class EtlDatasetsController extends DatasetsController {
  private outDb: ILabelValue[] = []; // 外链数据库
  private inDb: ILabelValue[] = [];
  private etlDb: ILabelValue[] = [];
  private datasets?: IDataset[];

  public constructor() {
    super();
  }

  public destroy() {
    this.inDb = [];
    this.outDb = [];
    this.etlDb = [];
    this.datasets = undefined;
  }

  public get outDbValue() {
    return this.outDb;
  }

  public get inDbValue() {
    return this.inDb;
  }

  public get etlDbValue() {
    return this.etlDb;
  }

  public initDatasets(datasets: IDataset[]) {
    super.initDatasets(datasets);
    this.datasets = datasets;
  }

  public loadEtlDatasets() {
    const datasets = this.datasets ?? [];
    const ins: ILabelValue[] = [];
    const outs: ILabelValue[] = [];
    const etls: ILabelValue[] = [];
    const userManageDbLinkPermission = AppController.getInstance()
      .getUserPermissionController()
      .getUserManageDbLinkPermission();

    _.forEach(datasets, (it) => {
      const { role, id, name } = it;
      if (DatasetRoleEnum.EXCLUSIVE === role) {
        const db = { label: i18n.chain.proMicroModules.approval.selpApp, value: id };
        ins.push(db);
        etls.push(db);
      } else if (DatasetRoleEnum.OUT_LINK === role && userManageDbLinkPermission) {
        const db = { label: name, value: id };
        etls.push(db);
        outs.push(db);
      } else if (DatasetRoleEnum.COLLECTOR === role) {
        const db = { label: 'Collector', value: id };
        // ins.push(db);
        etls.push(db);
      } else if (DatasetRoleEnum.PRIVATE === role) {
        const db = { label: name, value: id };
        ins.push(db);
      }
    });
    this.inDb = ins;
    this.outDb = outs;
    this.etlDb = etls;
  }
}

export { EtlDatasetsController };
