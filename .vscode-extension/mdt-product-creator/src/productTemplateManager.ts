import * as fs from 'fs';
import * as path from 'path';

export interface ProductTemplateContext {
  productId: string;
  productName: string;
  productDesc: string;
  date: string;
  isProduct: boolean;
}

/**
 * 产品模板管理器
 */
export class ProductTemplateManager {
  private context: ProductTemplateContext;
  private rootPath: string;
  private productPath: string;

  constructor(rootPath: string, context: ProductTemplateContext) {
    this.rootPath = rootPath;
    this.context = context;
    this.productPath = path.join(rootPath, 'products', context.productId);
  }

  /**
   * 创建产品目录结构
   */
  public createDirectoryStructure(): void {
    // 确保产品目录不存在
    if (fs.existsSync(this.productPath)) {
      throw new Error(`产品目录 ${this.context.productId} 已存在`);
    }

    // 创建主要目录结构
    const directories = [
      '', // 产品根目录
      'src',
      'src/app',
      'src/pages',
      'src/routers',
      'src/types',
      'src/languages',
      'src/languages/locales',
      'src/_util',
      '.config',
      '.config/develop',
    ];

    directories.forEach((dir) => {
      fs.mkdirSync(path.join(this.productPath, dir), { recursive: true });
    });
  }

  /**
   * 获取产品简写名称
   * 例如：data-market -> dm, user -> us
   */
  private getProductShortName(): string {
    const words = this.context.productId.split('-');
    if (words.length === 1) {
      // 如果只有一个单词，取前两个字母
      return words[0].substring(0, 2);
    } else {
      // 如果有多个单词，取每个单词的首字母
      return words.map((word) => word.charAt(0)).join('');
    }
  }

  /**
   * 创建产品配置文件
   */
  public createConfigFiles(): void {
    // package.json - 参考one-table，只保留必要依赖
    const packageJson = {
      name: this.context.productId,
      cnName: this.context.productName,
      description: `${this.context.productName}--${this.context.productDesc}`,
      version: '0.1.0',
      private: true,
      scripts: {
        start: 'cross-env DEVELOP_ENV=dev craco start',
        'start:staging': 'cross-env DEVELOP_ENV=staging craco start',
        'start:debug': 'cross-env DEVELOP_ENV=debug craco start',
        release: 'CI=false craco build',
        'release:analyze': 'craco build --analyze',
        test: 'craco test',
      },
      cracoConfig: 'craco.config.js',
      dependencies: {
        '@mdt/product-micro-modules': '^1.48.5',
        '@mdt/product-tasks': '^1.25.3',
      },
      browserslist: {
        production: ['>0.2%', 'not dead', 'not op_mini all'],
        development: ['last 1 chrome version', 'last 1 firefox version', 'last 1 safari version'],
      },
    };

    fs.writeFileSync(path.join(this.productPath, 'package.json'), JSON.stringify(packageJson, null, 2));

    // tsconfig.json - 参考one-table
    const tsConfig = {
      extends: '../../tsconfig.comm.json',
      compilerOptions: {
        module: 'esnext',
        noEmit: true,
      },
      include: ['src'],
      exclude: ['node_modules', 'build'],
    };

    fs.writeFileSync(path.join(this.productPath, 'tsconfig.json'), JSON.stringify(tsConfig, null, 2));

    // craco.config.js - 参考data-market配置
    const cracoConfig = `const { loadProductConfig } = require('../../_template/craco');

module.exports = {
  plugins: [
    ...loadProductConfig({
      secdir: '/sub-${this.context.productId.replace(/-/g, '').toLowerCase()}/',
    }),
  ],
};
`;

    fs.writeFileSync(path.join(this.productPath, 'craco.config.js'), cracoConfig);

    // 创建.config/config.js - 全局配置变量命名保持统一
    const globalVarPrefix = `__DM_${this.context.productId.replace(/-/g, '_').toUpperCase()}`;
    const configJs = `window.${globalVarPrefix}_CFS = {
  deployEnv: 'prod',
  jumpDatlasAdminUrl: 'https://admin.maicedata.com',
  jumpCollectorUrl: 'https://collectoradmin.maicedata.com',
  filePreviewUrl: 'https://kkfileview.maicedata.com/onlinePreview',
};
`;

    fs.writeFileSync(path.join(this.productPath, '.config', 'config.js'), configJs);

    // 创建开发环境配置
    const configDevJs = `window.${globalVarPrefix}_CFS = {
  deployEnv: 'dev',
  jumpDatlasAdminUrl: 'https://admin.maicedata-dev.com',
  jumpCollectorUrl: '',
  filePreviewUrl: 'https://kkfileview.maicedata-dev.com/onlinePreview',
};
`;

    fs.writeFileSync(path.join(this.productPath, '.config', 'develop', 'config.dev.js'), configDevJs);

    // 创建测试环境配置
    const configStagingJs = `window.${globalVarPrefix}_CFS = {
  deployEnv: 'staging',
  jumpDatlasAdminUrl: 'https://admin.maicedata-staging.com',
  jumpCollectorUrl: 'https://collectoradmin.maicedata-staging.com',
  filePreviewUrl: 'https://kkfileview.maicedata-staging.com/onlinePreview',
};
`;

    fs.writeFileSync(path.join(this.productPath, '.config', 'develop', 'config.staging.js'), configStagingJs);

    // 创建调试环境配置
    const configDebugJs = `window.${globalVarPrefix}_CFS = {
  deployEnv: 'debug',
  jumpDatlasAdminUrl: 'https://admin.maicedata-dev.com',
  jumpCollectorUrl: '',
  filePreviewUrl: 'https://kkfileview.maicedata-dev.com/onlinePreview',
};
`;

    fs.writeFileSync(path.join(this.productPath, '.config', 'develop', 'config.debug.js'), configDebugJs);

    // README.md
    const readme = `# ${this.context.productName}

${this.context.productDesc}
`;

    fs.writeFileSync(path.join(this.productPath, 'README.md'), readme);

    // 环境配置文件
    fs.writeFileSync(
      path.join(this.productPath, '.env.development'),
      `REACT_APP_DEPLOY_ENV=development
PORT=3000
`,
    );

    fs.writeFileSync(
      path.join(this.productPath, '.env.production'),
      `REACT_APP_DEPLOY_ENV=production
`,
    );

    // CHANGELOG.md
    fs.writeFileSync(
      path.join(this.productPath, 'CHANGELOG.md'),
      `# ${this.context.productName} 更新日志

## 0.1.0 (${this.context.date})

* 初始版本创建
`,
    );
  }

  /**
   * 创建源代码文件
   */
  public createSourceFiles(): void {
    // 获取产品简写名称
    const shortName = this.getProductShortName();

    // 不再创建 ProductPrefixEnum 和 ModuleIdEnum 扩展文件
    // 这些枚举将由用户手动添加到 product-comm/constants 文件中

    // src/index.tsx
    fs.writeFileSync(
      path.join(this.productPath, 'src', 'index.tsx'),
      `import './start';
import './config';
`,
    );

    // src/start.tsx - 参考resource-share
    fs.writeFileSync(
      path.join(this.productPath, 'src', 'start.tsx'),
      `import loadable from '@loadable/component';
import { startApp } from '@mdtProMicroModules/datlas';

startApp(loadable(() => import('./app')));
`,
    );

    // src/config.ts
    fs.writeFileSync(
      path.join(this.productPath, 'src', 'config.ts'),
      `import { initCommConfig } from '@mdtProMicroModules/datlas/datlasConfig';

const cfs = initCommConfig('${this.context.productId}', {
  isDevelop: __IS_DEVELOPMENT,
  developProxyApiUrl: __DEVELOP_PROXY_API_URL,
  developEnvOrigin: __DEVELOP_ENV_ORIGIN,
});
__webpack_public_path__ = cfs.deployPublicPath;

export * from '@mdtProMicroModules/datlas/datlasConfig';
`,
    );

    // src/react-app-env.d.ts - 直接使用示例提供的版本
    fs.writeFileSync(
      path.join(this.productPath, 'src', 'react-app-env.d.ts'),
      `/// <reference types="react-scripts" />

declare const __IS_DEVELOPMENT: boolean;
declare const __DEVELOP_PROXY_API_URL: string;
declare const __DEVELOP_ENV_ORIGIN: string;
declare var __webpack_public_path__: string;
`,
    );

    // src/setupTests.ts
    fs.writeFileSync(
      path.join(this.productPath, 'src', 'setupTests.ts'),
      `// jest-dom adds custom jest matchers for asserting on DOM nodes.
// allows you to do things like:
// expect(element).toHaveTextContent(/react/i)
// learn more: https://github.com/testing-library/jest-dom
import '@testing-library/jest-dom';
`,
    );

    // src/app/index.tsx - 使用getInstance模式
    fs.writeFileSync(
      path.join(this.productPath, 'src', 'app', 'index.tsx'),
      `import { DatlasApp } from '@mdtProMicroModules/datlas/app';
import { AppController } from './AppController';
import './index.less';

const App = () => {
  return <DatlasApp getController={(history) => AppController.getInstance(history)} />;
};

export default App;
`,
    );

    // 创建 src/app/index.less
    fs.writeFileSync(
      path.join(this.productPath, 'src', 'app', 'index.less'),
      `.${this.context.productId}-app {
  // 应用全局样式
}
`,
    );

    // 创建 src/app/AppController.ts
    fs.writeFileSync(
      path.join(this.productPath, 'src', 'app', 'AppController.ts'),
      `import { History } from 'history';
import { ModuleIdEnum } from '@mdtProComm/constants';
import { DatlasAppController } from '@mdtProMicroModules/datlas/app/DatlasAppController';
import { RouterController } from '../_util/RouterController';
import { UserPermissionController } from '../_util/UserPermissionController';
import i18n from '../languages';
import { AppSideMenuController } from './AppSideMenuController';

/**
 * 应用控制器
 */
class AppController extends DatlasAppController<RouterController, UserPermissionController, AppSideMenuController> {
  private constructor(history: History) {
    super({ i18n });
    this.routerController = new RouterController(history, this);
  }

  protected async afterAuthSuccess() {
    await super.afterAuthSuccess();
    this.initAppReleation();
  }

  // 构造
  private initAppReleation() {
    // 初始化完必备信息后，构建用户的权限
    const upc = new UserPermissionController(this.getUserPermission()!, this.getPermissionController());
    this.userPermissionController = upc;
    this.appHeaderController = this.initAppHeader({
      defaultProduct: ModuleIdEnum.${this.context.productId.toUpperCase().replace(/-/g, '_')},
      defaultModule: ModuleIdEnum.HOME,
    });
    // 初始化路由
    this.routerController!.initRoutes();
    // 初始化左侧菜单
    this.appSideMenuController = new AppSideMenuController(this);
    this.appSideMenuController.initMenus();
  }
}

export { AppController };
`,
    );

    // 创建 src/app/AppSideMenuController.ts
    fs.writeFileSync(
      path.join(this.productPath, 'src', 'app', 'AppSideMenuController.tsx'),
      `import { DataNode } from '@mdtBsComm/components/side-menu';
import DashboardIcon from '@mdtDesign/icons/dashboard';
import { DatlasAppSideMenuController } from '@mdtProMicroModules/datlas/app-side-menu';
import { RoutePathEnum } from '../_util/constants';
import i18n from '../languages';

class AppSideMenuController extends DatlasAppSideMenuController {
  private hasBottomFixedMenu = false;

  public getExtraClassName() {
    return \`${this.context.productId}-menu \${this.hasBottomFixedMenu ? 'with-bottom-fixed-menu' : ''}\`;
  }

  public async initMenus() {
    const app = this.app!;
    const psc = app.getUserPermissionController();
    const menuPs = psc.getMenuPermission();

    const menus: DataNode[] = [];
    if (menuPs.enableDashboard) {
      menus.push({
        key: RoutePathEnum.DASHBOARD,
        title: (
          <>
            <DashboardIcon size={15} />
            {i18n.chain.${this.context.productId.replace(/-/g, '')}.menu.dashboard}
          </>
        ),
        className: 'top-level',
      });
    }

    this.changeMenuData(menus);
  }
}

export { AppSideMenuController };
`,
    );

    // 创建 src/app/appContext.ts
    fs.writeFileSync(
      path.join(this.productPath, 'src', 'app', 'appContext.ts'),
      `import { createContext } from 'react';
import { AppController } from './AppController';

export const AppContext = createContext<AppController | null>(null);
`,
    );

    // 创建 _util/constants.ts
    fs.writeFileSync(
      path.join(this.productPath, 'src', '_util', 'constants.ts'),
      `// 路由常量定义
export enum RoutePathEnum {
  // 首页
  DASHBOARD = '/',
}
`,
    );

    // 创建 _util/RouterController.tsx - 使用相对路径引用
    fs.writeFileSync(
      path.join(this.productPath, 'src', '_util', 'RouterController.tsx'),
      `import loadable from '@loadable/component';
import { IRoute } from '@mdtProComm/controllers/BaseRouterController';
import { DatlasRouterController } from '@mdtProMicroModules/datlas/comm/DatlasRouterController';
import { RoutePathEnum } from './constants';

const DashboardView = loadable(() => import('../routers/dashboard'));

export const allAuthRoutes: IRoute[] = [
  {
    path: RoutePathEnum.DASHBOARD,
    View: DashboardView,
    permissionKey: true,
  },
];

class RouterController extends DatlasRouterController {
  public getAllAuthRoutes() {
    return allAuthRoutes;
  }
}

export { RouterController };
`,
    );

    // 创建 _util/UserPermissionController.ts - 简化版本，enableDashboard直接设为true
    fs.writeFileSync(
      path.join(this.productPath, 'src', '_util', 'UserPermissionController.ts'),
      `import { BaseUserPermissionController } from '@mdtProComm/controllers/BaseUserPermissionController';

class UserPermissionController extends BaseUserPermissionController {
  // 获取菜单权限
  public getMenuPermission() {
    return {
      enableDashboard: true,
      // 其他菜单权限...
    };
  }
  
  // 其他权限管理方法...
}

export { UserPermissionController };
`,
    );

    // 创建 routers/dashboard/index.tsx
    fs.mkdirSync(path.join(this.productPath, 'src', 'routers', 'dashboard'), { recursive: true });
    fs.writeFileSync(
      path.join(this.productPath, 'src', 'routers', 'dashboard', 'index.tsx'),
      `import React from 'react';

const Dashboard: React.FC = () => {
  return <div>Dashboard</div>;
};

export default Dashboard;
`,
    );

    // 创建 languages/index.ts - 参考one-table
    fs.writeFileSync(
      path.join(this.productPath, 'src', 'languages', 'index.ts'),
      `import { setValidateLanguage } from '@formily/core';
import { createI18n, I18n } from '@i18n-chain/core';
import { LanguageEnum } from '@mdtProComm/constants';
import microI18n from '@mdtProMicroModules/languages';
import cn, { Locale } from './locales/cn';
import en from './locales/en';

const i18n = createI18n({ defaultLocale: { key: LanguageEnum.CN, values: cn } });
i18n.define(LanguageEnum.EN, en);

const originalLocale = i18n.locale.bind(i18n);
i18n.locale = async (lang: string) => {
  originalLocale(lang);
  microI18n.locale(lang);
  setValidateLanguage(lang);
};
export default i18n as unknown as I18n<Locale, Locale>;
`,
    );

    // 创建中文翻译文件 - 简化结构，只保留首页
    fs.writeFileSync(
      path.join(this.productPath, 'src', 'languages', 'locales', 'cn.ts'),
      `import { cn as microCn } from '@mdtProMicroModules/languages';

const cn = {
  ...microCn,
  ${this.context.productId.replace(/-/g, '')}: {
    menu: {
      dashboard: '首页',
    }
  },
  appName: '${this.context.productName}',
};

export type Locale = typeof cn;
export default cn;
`,
    );

    // 创建英文翻译文件 - 简化结构，只保留首页
    fs.writeFileSync(
      path.join(this.productPath, 'src', 'languages', 'locales', 'en.ts'),
      `import { en as microEn } from '@mdtProMicroModules/languages';
import { Locale } from './cn';

const en: Locale = {
  ...microEn,
  ${this.context.productId.replace(/-/g, '')}: {
    menu: {
      dashboard: 'Dashboard',
    }
  },
  appName: '${this.context.productId}',
};

export default en;
`,
    );
  }

  /**
   * 首字母大写
   */
  private capitalizeFirst(str: string): string {
    return str
      .replace(/-/g, '_')
      .split('_')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join('');
  }

  /**
   * 创建产品的所有文件
   */
  public createAll(): void {
    this.createDirectoryStructure();
    this.createConfigFiles();
    this.createSourceFiles();
  }
}
