import _ from 'lodash';
import { FC, useRef } from 'react';
import { Field, ObjectField, observer } from '@formily/react';
import { RequestTypeEnum } from '@mdtProMicroModules/amis-expand/constants';
import { IEditor, IMonaco, MonacoEditor } from '@mdtProMicroModules/pages/form-editor/formily-externals/monaco-editor';
import i18n from '../../../../languages';

const CodeEditorComp: FC = observer((props: any) => {
  const { value, onChange, ...resetProps } = props;
  const editorRef = useRef<IEditor>();
  const changeRef = useRef(onChange);
  const valueRef = useRef(value);
  changeRef.current = onChange;
  valueRef.current = value;

  const finishedEdit = (editor: IEditor, monaco: IMonaco) => {
    const model = editor.getModel()!;
    const markers = monaco.editor.getModelMarkers({ resource: model.uri });
    // 如果有错误，则不保存
    if (markers.length) return;
    // 格式化js代码
    editor.getAction('editor.action.formatDocument').run();
    // 获取格式化后的代码
    const newValue = _.trim(editor.getValue());
    if (valueRef.current === newValue) return;
    editor.setValue(newValue);
    changeRef.current(newValue);
  };

  const onMount = (editor: IEditor) => {
    editorRef.current = editor;
  };

  return (
    <MonacoEditor
      {...resetProps}
      height={400}
      language="javascript"
      value={valueRef.current}
      fullScreenTitle={i18n.chain.fullEdit}
      finishedEdit={finishedEdit}
      onMount={onMount}
      fnName=" fetcher(params: {request, data, params}) "
      placeholder="return request({url: 'xxx', method: 'post', data: data});"
    />
  );
});

export const CustomApi: FC<{ parentName: string }> = observer(({ parentName }) => {
  return (
    <ObjectField
      name="custom"
      reactions={(field) => {
        const type = field.query(`..${parentName}`).get('value');
        field.setState({ display: type === RequestTypeEnum.CUSTOM ? 'visible' : 'hidden' });
      }}
    >
      <Field required name="code" title="Code" decorator={['FormItem']} component={[CodeEditorComp]} />
    </ObjectField>
  );
});
