import _ from 'lodash';
import { ReactNode } from 'react';
import type { RuleCondition, RuleField, RuleFormWidgets } from '@metro/rule-form';
import { IOperatorFilter } from '@mdtApis/interfaces';
import { endOf, startOf } from '@mdtBsComm/utils/dayUtil';
import { parseFormatToPicker } from '@mdtBsComm/utils/formatUtil';
import { ColumnOperatorFilterEnum, QuestionCompEnum } from '@mdtProComm/constants';
import { getColumnOperatorFilterLabel } from '@mdtProComm/utils/columnUtil';
import { getDatetimeDefaultFormat } from '@mdtProComm/utils/formilyUtil';
import { type IControllerOptions, FilterRuleFormController, IColumn } from '../../../../../components/filter-rule-form';
import i18n from '../../../../../languages';
import { IColumnData } from '../util';

type IUsableColumn = Omit<IColumnData, 'node' | 'dataSource' | 'id' | 'type'> & {
  type: string;
  name?: string;
  dataSource?: any[];
};

function getDateTimeList(columns: IUsableColumn[]): IDateTime[] {
  return _.compact(
    _.map(
      _.filter(columns, ({ type }) => _.includes(['date', 'time'], type)),
      ({ name, componentProps = {}, component, title }) => {
        const type = `${component}_${componentProps?.picker + componentProps?.showTime || 'defaultfalse'}`;
        return {
          name,
          component: component || 'DatePicker',
          componentProps,
          type: type,
          format: componentProps?.format ?? getDatetimeDefaultFormat(componentProps),
          title,
        };
      },
    ),
  );
}

function filterArrayColumns(columns: IUsableColumn[]): IUsableColumn[] {
  return _.reject(columns, ({ type, component }) => _.eq(type, 'array') && _.eq(component, 'UserSelectInput'));
}

function getUserSelectList(columns: IUsableColumn[]): IDateTime[] {
  return _.compact(
    _.map(
      _.filter(columns, ({ type, component }) => _.eq(type, 'array') && _.eq(component, 'UserSelectInput')),
      ({ name, componentProps = {}, title }) => {
        const type = `UserSelectInput_${componentProps?.withoutOrg ? 'with_org' : 'without_org'}`;
        return {
          name,
          component: 'UserSelect',
          componentProps,
          type,
          title,
        };
      },
    ),
  );
}

function getTypes(columns: IDateTime[]) {
  return _.map(columns, ({ type }) => ({
    value: type,
    label: type,
  }));
}

function getDateTimeMapper(columns: IDateTime[]): Record<PropertyKey, string[]> {
  const mapper: Record<PropertyKey, string[]> = {};
  _.forEach(columns, ({ type }) => {
    mapper[type] = [
      ColumnOperatorFilterEnum.EQ,
      ColumnOperatorFilterEnum.IS,
      ColumnOperatorFilterEnum.GT,
      ColumnOperatorFilterEnum.GE,
      ColumnOperatorFilterEnum.LT,
      ColumnOperatorFilterEnum.LE,
      ColumnOperatorFilterEnum.BETWEEN,
    ];
  });
  return mapper;
}

function getSelectuserMapper(columns: IDateTime[]): Record<PropertyKey, string[]> {
  const mapper: Record<PropertyKey, string[]> = {};
  _.forEach(columns, ({ type }) => {
    mapper[type] = [ColumnOperatorFilterEnum.EQ, ColumnOperatorFilterEnum.NE];
  });
  return mapper;
}

function getFields(columns: IDateTime[]): any[] {
  return _.map(columns, ({ type, name, title }) => ({
    type,
    name,
    title,
  }));
}

function getDateTimeWidgets(columns: IDateTime[]): RuleFormWidgets {
  const widgets: RuleFormWidgets = {};
  _.forEach(columns, ({ type, component, componentProps }) => {
    const rangeComponent = component === 'TimePicker' ? 'TimeRangePicker' : 'DateRangePicker';
    widgets[type] = {
      default: [component, componentProps],
      [ColumnOperatorFilterEnum.BETWEEN]: [rangeComponent, componentProps],
      [ColumnOperatorFilterEnum.IS]: null,
    };
  });
  return widgets;
}

function getSelectuserWidgets(columns: IDateTime[]): RuleFormWidgets {
  const widgets: RuleFormWidgets = {};
  _.forEach(columns, ({ type, component, componentProps }) => {
    widgets[type] = {
      default: [component, componentProps],
    };
  });
  return widgets;
}

interface IDateTime {
  component: string;
  componentProps: Record<string, any>;
  type: string;
  name: string;
  format?: string;
  title?: ReactNode;
}

class OperatorFilterListController extends FilterRuleFormController<IOperatorFilter> {
  public constructor(options: IControllerOptions<IOperatorFilter>) {
    const dateTimeColumns = getDateTimeList(options.columns);
    const userSelectColumns = getUserSelectList(options.columns);
    super({
      ...options,
      columns: filterArrayColumns(options.columns),
      fieldTypeExclude: ['date', 'time'],
      TitleView: () => null,
      FooterView: () => null,
      ruleFormProps: {
        notExclude: ['array', 'date', 'time'],
        types: [
          ...getTypes(dateTimeColumns),
          ...getTypes(userSelectColumns),
          { label: 'arrayCascader', value: 'arrayCascader' },
        ],
        mapper: {
          bool: [ColumnOperatorFilterEnum.EQ, ColumnOperatorFilterEnum.NE, ColumnOperatorFilterEnum.IS],
          float: [
            ColumnOperatorFilterEnum.EQ,
            ColumnOperatorFilterEnum.NE,
            ColumnOperatorFilterEnum.IS,
            ColumnOperatorFilterEnum.GT,
            ColumnOperatorFilterEnum.LT,
            ColumnOperatorFilterEnum.GE,
            ColumnOperatorFilterEnum.LE,
            ColumnOperatorFilterEnum.BETWEEN,
          ],
          int: [
            ColumnOperatorFilterEnum.EQ,
            ColumnOperatorFilterEnum.NE,
            ColumnOperatorFilterEnum.IS,
            ColumnOperatorFilterEnum.GT,
            ColumnOperatorFilterEnum.LT,
            ColumnOperatorFilterEnum.GE,
            ColumnOperatorFilterEnum.LE,
            ColumnOperatorFilterEnum.BETWEEN,
          ],
          string: [
            ColumnOperatorFilterEnum.EQ,
            ColumnOperatorFilterEnum.NE,
            ColumnOperatorFilterEnum.IS,
            ColumnOperatorFilterEnum.IN,
            ColumnOperatorFilterEnum.CONTAIN,
            ColumnOperatorFilterEnum.START_WITH,
            ColumnOperatorFilterEnum.END_WITH,
          ],
          array: [
            ColumnOperatorFilterEnum.EQ,
            ColumnOperatorFilterEnum.NE,
            ColumnOperatorFilterEnum.IS,
            ColumnOperatorFilterEnum.IN,
          ],
          arrayCascader: [ColumnOperatorFilterEnum.EQ, ColumnOperatorFilterEnum.NE, ColumnOperatorFilterEnum.IS],
          ...getDateTimeMapper(dateTimeColumns),
          ...getSelectuserMapper(userSelectColumns),
        },
        fields: [...getFields(dateTimeColumns), ...getFields(userSelectColumns)],
        operators: [
          { label: getColumnOperatorFilterLabel(ColumnOperatorFilterEnum.IN), value: ColumnOperatorFilterEnum.IN },
        ],
        widgets: {
          bool: {
            default: 'Bool',
            [ColumnOperatorFilterEnum.IS]: null,
          },
          float: {
            default: '{{Array.isArray($self.dataSource) ? "Select" : "InputNumber"}}',
            [ColumnOperatorFilterEnum.BETWEEN]: 'InputNumberBetween',
            [ColumnOperatorFilterEnum.IS]: null,
          },
          int: {
            default: '{{Array.isArray($self.dataSource) ? "Select" : "InputNumber"}}',
            [ColumnOperatorFilterEnum.BETWEEN]: 'InputNumberBetween',
            [ColumnOperatorFilterEnum.IS]: null,
          },
          string: {
            default: '{{Array.isArray($self.dataSource) ? "Select" : "Input"}}',
            [ColumnOperatorFilterEnum.IN]: '{{Array.isArray($self.dataSource) ? "Multiple" : "InputTags"}}',
            [ColumnOperatorFilterEnum.IS]: null,
          },
          array: {
            default: 'Select',
            [ColumnOperatorFilterEnum.IN]: 'Multiple',
            [ColumnOperatorFilterEnum.IS]: null,
          },
          arrayCascader: {
            default: 'Cascader',
            [ColumnOperatorFilterEnum.IN]: 'MultipleCascader',
            [ColumnOperatorFilterEnum.IS]: null,
          },
          ...getSelectuserWidgets(userSelectColumns),
          ...getDateTimeWidgets(dateTimeColumns),
        },
      },
      transformCondition: (node: RuleCondition) => {
        const condition = _.flow(this.handleDateTimeCondition.bind(this))(_.cloneDeep(node), dateTimeColumns);
        return { param: condition.value, operator: condition.operator, column: condition.name, not: condition.not };
      },
      transformColumnToFields: (columns: IColumn[]): RuleField[] => {
        return _.map(columns, ({ type, name, title, dataSource, component }) => {
          const mergedType = component === 'Cascader' && type === 'array' ? 'arrayCascader' : type;
          // 给到options就代表当前筛选项需要根据选择来填写，这里需要过滤部分组件
          const options = !_.includes(
            [
              QuestionCompEnum.INPUT,
              QuestionCompEnum.AUTO_COMPLETE,
              QuestionCompEnum.INPUT_TEXTAREA,
              QuestionCompEnum.NUMBER_PICKER,
            ],
            component,
          )
            ? dataSource
            : undefined;
          return {
            type: mergedType,
            name,
            title,
            options,
            validator: {
              default: [{ required: true, message: `${title}${i18n.chain.comWarning.noEmpty}` }],
              [ColumnOperatorFilterEnum.IS]: [],
            },
          };
        });
      },
    });
  }
  public destroy() {
    super.destroy();
  }

  private handleDateTimeCondition(node: RuleCondition, dateTimeColumns: IDateTime[]): RuleCondition {
    const isDateTimeType = _.includes(_.map(dateTimeColumns, 'name'), node.name);
    if (isDateTimeType) {
      const format = _.find(dateTimeColumns, (item) => item.name === node.name)?.format as string;
      const unit = parseFormatToPicker(format) || 'day';
      const [start, end] = [startOf(node.value, unit), endOf(node.value, unit)];

      if (node.operator === ColumnOperatorFilterEnum.EQ) {
        node.value = [start, end];
      } else if (node.operator === ColumnOperatorFilterEnum.GT) {
        node.value = end;
      } else if (node.operator === ColumnOperatorFilterEnum.GE) {
        node.value = start;
      } else if (node.operator === ColumnOperatorFilterEnum.LT) {
        node.value = start;
      } else if (node.operator === ColumnOperatorFilterEnum.LE) {
        node.value = end;
      }
    }
    return node;
  }
}

export { OperatorFilterListController };
