import _ from 'lodash';
import { from, Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import {
  <PERSON>Job,
  IJobJob,
  IJobPost,
  IJobPut,
  IJobTrigger,
  IJobTriggerCron,
  IJobTriggerInterval,
} from '@mdtApis/interfaces';
import { IBusinessResult } from '@mdtBsComm/interfaces';
import {
  createDate,
  createDateByUnix,
  DATE_FORMATTER_2,
  formateDate,
  transformDateToUnix,
} from '@mdtBsComm/utils/dayUtil';
import { ICurdOptions } from '@mdtBsComponents/data-list-comp-table-curd';
import { deleteJobAsync, postJobAsync, putJobAsync, queryJobsAsync } from '@mdtBsServices/jobs';
import { JobTypeEnum } from '@mdtProComm/constants';
import {
  CronFrequencyEnum,
  IFormData,
  IntervalTypeEnum,
  TriggerEnum,
  triggerMap,
} from '@mdtProMicroModules/containers/dialog-modify-form-timed-task';
import { ITableData, ITableTimedTaskModel } from '@mdtProMicroModules/containers/table-timed-task';

const intervalTypeList = _.values(IntervalTypeEnum);

export class FlowViewTableTimedTaskModel implements ITableTimedTaskModel {
  private flowId: string;

  public constructor(flowId: string) {
    this.flowId = flowId;
  }

  // 查询权限
  public queryPermissions(): ICurdOptions<ITableData> {
    return {
      enableCreate: true,
      enableEdit: true,
      enableDelete: true,
    };
  }

  // 查询定时任务列表
  public queryTimedTaskList(): Observable<ITableData[]> {
    return from(queryJobsAsync({ params: { 'job.flow_uuid': this.flowId, job: 'flow' } })).pipe(
      map((resp) => {
        return _.map(resp.data, this.transformTableData);
      }),
    );
  }

  // 创建定时任务
  public createTimedTask(createData: IFormData): Observable<IBusinessResult<ITableData>> {
    return from(postJobAsync(this.transformBackendData(createData))).pipe(
      map((resp) => {
        return {
          success: resp.success,
          result: resp.success ? this.transformTableData(resp.data!) : undefined,
        };
      }),
    );
  }

  // 修改定时任务
  public updateTimedTask(
    updateData: IFormData,
    originalUpdateData: ITableData,
  ): Observable<IBusinessResult<ITableData>> {
    const putData: IJobPut = {
      ...this.transformBackendData(updateData),
      update_time: originalUpdateData.updateTime,
      active: originalUpdateData.active,
    };
    return from(putJobAsync(originalUpdateData.id, putData)).pipe(
      map((updateResp) => {
        return {
          success: updateResp.success,
          result: updateResp.success ? this.transformTableData(updateResp.data!) : undefined,
        };
      }),
    );
  }

  // 删除定时任务
  public deleteTimedTask(deleteData: ITableData): Observable<IBusinessResult<ITableData>> {
    return from(deleteJobAsync(deleteData.id, { update_time: deleteData.updateTime })).pipe(
      map((resp) => ({
        success: resp.success,
        result: deleteData,
      })),
    );
  }

  // 激活定时任务
  public enableTimedTask(originalUpdateData: ITableData): Observable<IBusinessResult<ITableData>> {
    const putData: IJobPut = { active: !originalUpdateData.active, update_time: originalUpdateData.updateTime };
    return from(putJobAsync(originalUpdateData!.id, putData)).pipe(
      map((resp) => {
        const nextRunTime = resp.data!.next_run_time
          ? formateDate(resp.data!.next_run_time * 1000, DATE_FORMATTER_2)
          : '';
        const result = resp.success
          ? { ...originalUpdateData, updateTime: resp.data!.update_time, active: resp.data!.active!, nextRunTime }
          : undefined;
        return {
          success: resp.success,
          result,
        };
      }),
    );
  }

  private transformTableData(job: IJob): ITableData {
    const trigger = job.trigger;
    const endTime = trigger.end_time;
    const expired = endTime ? formateDate(endTime * 1000, DATE_FORMATTER_2) : '';
    const nextRunTime = job.next_run_time ? formateDate(job.next_run_time * 1000, DATE_FORMATTER_2) : '';

    const formData: IFormData = {
      name: job.name,
      triggerType: trigger.trigger as TriggerEnum,
    };
    if (trigger.trigger === TriggerEnum.INTERVAL) {
      const key = _.find(intervalTypeList, (inte: keyof IJobTriggerInterval) => trigger[inte]);
      if (key) {
        formData.interval = trigger[key as keyof IJobTriggerInterval] as number;
        formData.intervalType = key as IntervalTypeEnum;
      }
    } else {
      if (trigger.day) {
        formData.frequency = CronFrequencyEnum.MONTH;
        formData.monthTime = trigger.day;
      }
      if (trigger.day_of_week) {
        formData.frequency = CronFrequencyEnum.WEEK;
        formData.weekTime = trigger.day_of_week;
      }
      const hour = parseInt(trigger.hour![0], 10);
      const minute = parseInt(trigger.minute!, 10);
      const second = parseInt(trigger.second!, 10);
      const time = createDate();
      formData.time = time.set('hour', hour).set('minute', minute).set('second', second);
    }
    const startTime = trigger.start_time;
    formData.startendtime = startTime && endTime ? [createDateByUnix(startTime), createDateByUnix(endTime)] : undefined;

    const data: ITableData = {
      id: job.id,
      triggerDisplay: triggerMap[trigger.trigger],
      expired,
      active: !!job.active,
      updateTime: job.update_time,
      nextRunTime,
      ...formData,
    };

    return data;
  }

  private transformBackendData(values: IFormData): IJobPost {
    const job: IJobJob = {
      job: JobTypeEnum.FLOW,
      flow_uuid: this.flowId,
    };
    const startendtime = values.startendtime;
    const cronFrequency = values.frequency;
    let trigger: IJobTrigger = {
      trigger: values.triggerType,
      start_time: startendtime ? transformDateToUnix(startendtime[0]) : undefined,
      end_time: startendtime ? transformDateToUnix(startendtime[1]) : undefined,
    };
    if (TriggerEnum.INTERVAL === values.triggerType) {
      trigger = trigger as IJobTriggerInterval;
      trigger[values.intervalType!] = values.interval;
    } else {
      trigger = trigger as IJobTriggerCron;
      const momentTime = values.time!;
      trigger.hour = [`${momentTime.get('hour')}`];
      trigger.minute = `${momentTime.get('minute')}`;
      trigger.second = `${momentTime.get('second')}`;

      cronFrequency === CronFrequencyEnum.MONTH && (trigger.day = values.monthTime);
      cronFrequency === CronFrequencyEnum.WEEK && (trigger.day_of_week = values.weekTime);
    }

    return {
      name: values.name,
      job,
      trigger,
      active: false,
    };
  }
}

export type IFlowViewTableTimedTaskModel = typeof FlowViewTableTimedTaskModel;
