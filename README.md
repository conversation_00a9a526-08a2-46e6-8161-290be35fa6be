# 研发中心前端代码管理仓库

## 发版

> 目前仅限 `dev` `staging` 自由发版

支持产品

- datafactory 数据工厂
- datamarket 数据市场
- devops-frontend 脉策云
- micromdt 统一入口
- mydata 我的数据
- organization-management 机构管理
- resource-share 资源分享
- workfow 流程引擎
- sso | sso 登录
- collector sso | collector sso 登录

```shell
// 全部发版
yarn deploy:dev --all=true
yarn deploy:staging --all=true

// 只发版其中部分
yarn deploy:dev --datafactory=true --datamarket=true
yarn deploy:staging --micromdt=true --mydata=true --sso=true

// 也可以自定义标签
yarn deploy:dev --datafactory=dev_data-factory_20220725 --datamarket=true
yarn deploy:staging --micromdt=dev_sso_20220725
```

## 修改开源代码

```shell
yarn patch-package some-package
```

## 为什么要统一仓库

## 仓库目录规划

## 代码规范

## 前端架构设计

## 第三方依赖约定

| 组件库                 | 版本           | 说明             |
| ---------------------- | -------------- | ---------------- |
| `@datlas/design`       | 固定 1.53.0    | UI 组件库        |
| `@metro/components`    | 跟随版本升级   | UI 组件库 3.0    |
| `@loadable/component`  | 跟随版本升级   | 代码分割         |
| `ahooks`               | 跟随版本升级   | react hooks      |
| `dayjs`                | 跟随版本升级   | 时间处理         |
| `file-saver`           | 跟随版本升级   | 文件保存下载     |
| `lodash`               | 跟随版本升级   | 工具集           |
| `lz-string`            | 跟随版本升级   | 存储加密压缩     |
| `modern-normalize`     | 跟随版本升级   | 浏览器初始化     |
| `react`                | 固定 17.0.2    | react            |
| `react-dom`            | 固定 17.0.2    | react-dom        |
| `react-router-dom`     | 固定 5.3.0     | react-router-dom |
| `rxjs`                 | 固定 6.67      | 响应式           |
| `spark-md5`            | 跟随版本升级   | 加密             |
| `url-parameter-append` | 跟随版本升级   | url 处理         |
| `uuid`                 | 跟随版本升级   | uuid             |
| `web-vitals`           | 固定版本 2.1.4 | pwa              |
