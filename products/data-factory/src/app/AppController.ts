import { History } from 'history';
import { ModuleIdEnum } from '@mdtProComm/constants';
import { DatlasAppController } from '@mdtProMicroModules/datlas/app/DatlasAppController';
import { EtlDatasetsController } from '../_util/EtlDatasetsController';
import { RouterController } from '../_util/RouterController';
import { UserPermissionController } from '../_util/UserPermissionController';
import i18n from '../languages';
import { AppSideMenuController } from './AppSideMenuController';

/**
 * 使用单例模式
 */
class AppController extends DatlasAppController<RouterController, UserPermissionController, AppSideMenuController> {
  // 私有类成员变量
  private etlDatasetsController?: EtlDatasetsController;

  private constructor(history: History) {
    super({ i18n });
    this.routerController = new RouterController(history, this);
    this.etlDatasetsController = new EtlDatasetsController();
  }

  public destroy() {
    super.destroy();
    this.etlDatasetsController?.destroy();
    this.etlDatasetsController = undefined;
  }

  public getDatasetsController() {
    return this.etlDatasetsController!;
  }

  protected async afterAuthSuccess() {
    await super.afterAuthSuccess();
    this.initAppReleation();
    this.getDatasetsController().loadEtlDatasets();
  }

  // 构造
  private initAppReleation() {
    // 初始化完必备信息后，构建用户的权限
    const upc = new UserPermissionController(this.getUserPermission()!, this.getPermissionController());
    this.userPermissionController = upc;
    this.appHeaderController = this.initAppHeader({
      defaultProduct: ModuleIdEnum.DATA_FACTORY,
      defaultModule: ModuleIdEnum.HOME,
    });
    // 初始化路由
    this.routerController!.initRoutes();
    // 初始化左侧菜单
    this.appSideMenuController = new AppSideMenuController(this);
    this.appSideMenuController.initMenus();
  }
}

export { AppController };
