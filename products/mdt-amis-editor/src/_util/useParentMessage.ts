import { useEffect } from 'react';

export const useParentMessage = (controller: any) => {
  // 注册及卸载
  useEffect(() => {
    const listenMessage = ({ data }: MessageEvent) => {
      controller.handleParentMessage(data);
    };

    window.addEventListener('message', listenMessage);
    return () => {
      window.removeEventListener('message', listenMessage);
    };
  }, [controller]);

  return null;
};
