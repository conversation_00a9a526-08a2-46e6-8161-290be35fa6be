import { from } from 'rxjs';
import { map } from 'rxjs/operators';
import { getFileUrlAsync } from '@mdtBsServices/files';
import { putPreferencesBatchAsync } from '@mdtBsServices/preference';

class AppStyleModel {
  public static putUserPreferencesBatch = (values: any) => {
    return from(putPreferencesBatchAsync({ values, at: 'app' })).pipe(
      map((resp) => {
        if (resp.success) {
          return {
            success: true,
            result: resp.data,
          };
        }
        return { success: false, result: {} };
      }),
    );
  };

  public static getFileUrl = (fileId: string) => {
    return from(getFileUrlAsync(fileId, { params: { redirect: false }, quiet: true })).pipe(
      map((resp) => {
        if (resp.success) {
          return {
            success: true,
            result: resp.data?.sign_url.url,
          };
        }
        return { success: false };
      }),
    );
  };
}
export { AppStyleModel };
export type IAppStyleModel = typeof AppStyleModel;
