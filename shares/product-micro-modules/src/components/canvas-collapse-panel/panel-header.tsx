import React from 'react';
import { usePanelContext } from '@antv/xflow';
import { Input } from '@mdtDesign/input';
import i18n from '../../languages';
import type { IOnKeywordChange, IProps, NsCollapsePanelModel } from './interface';

export interface IHeaderProps extends IProps {
  /** 关键字变化 */
  onKeywordChange: IOnKeywordChange;
  /** panel state */
  state: NsCollapsePanelModel.IState;
}

export const NodePanelHeader: React.FC<IHeaderProps> = (props) => {
  const { prefixClz, onKeywordChange, state } = props;
  const { propsProxy } = usePanelContext<IProps>();
  const panelProps = propsProxy.getValue();

  const onChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    // const panelNodes = _.reduce(
    //   state.collapseData,
    //   (acc: IPanelNode[], item) => {
    //     if (item.children) {
    //       acc.push(...item.children);
    //     }
    //     return acc;
    //   },
    //   [],
    // );
    onKeywordChange(e.target.value, state.collapseData);
  };
  return (
    <div className={`${prefixClz}-header`} style={props.style}>
      {panelProps.header && React.isValidElement(panelProps.header) && panelProps.header}
      {panelProps.searchService && (
        <div className={`${prefixClz}-header-search`}>
          <Input
            placeholder={i18n.chain.comPlaceholder.input}
            prefixIcon="search"
            allowClear
            onChange={(e) => onChange(e)}
            style={{ width: '100%' }}
          />
        </div>
      )}
    </div>
  );
};
