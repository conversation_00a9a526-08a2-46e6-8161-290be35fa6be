import { useController } from '@mdtBsComm/hooks/use-controller';
import { UserModel } from '@mdtProMicroModules/models/UserModel';
import { AppController } from '../../app/AppController';
import { RoleUser, RoleUserController, RoleUserModel } from '../../pages/role-user';

const RoleUserView = () => {
  const [controller] = useController(() => {
    const ctrl = new RoleUserController(AppController.getInstance(), RoleUserModel, UserModel);
    return [ctrl, null];
  });

  return <RoleUser controller={controller} />;
};

export default RoleUserView;
