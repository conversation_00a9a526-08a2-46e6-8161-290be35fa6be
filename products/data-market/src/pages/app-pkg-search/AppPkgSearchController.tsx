import _ from 'lodash';
import { skip } from 'rxjs/operators';
import { RequestController } from '@mdtBsControllers/request-controller';
import { OwnershipEnum } from '@mdtProComm/constants';
import { IDatapkgsQuery, IRequestCancelToken } from '@mdtProComm/interfaces';
import {
  IDatapkgItem,
  ILoadDataListRslt,
  IPaginationParams,
  SearchTypeEnum,
  TableCardDatapkgSearchController,
} from '@mdtProMicroModules/containers/table-card-datapkg-search';
import {
  DatapkgDetailPreviewController,
  DrawerDatapkgDetailPreviewController,
} from '@mdtProMicroModules/pages/datapkg-detail-preview';
import { DatapkgDetailPreviewModel } from '@mdtProMicroModules/pages/datapkg-detail-preview/DatapkgDetailPreviewModel';
import { DatapkgDetailPreviewModelBff } from '@mdtProMicroModules/pages/datapkg-detail-preview/DatapkgDetailPreviewModelBff';
import { getModel } from '../../_util/modelUtil';
import { AppController } from '../../app/AppController';
import { IAppPkgSearchModel } from './AppPkgSearchModel';

export interface IControllerOptions {
  title: string;
  appId: string;
  Model: IAppPkgSearchModel;
}

interface IFilterParams {
  cancelToken: IRequestCancelToken;
  params: IDatapkgsQuery;
}

class AppPkgSearchController extends RequestController {
  private readonly tableCardController: TableCardDatapkgSearchController;
  private readonly drawerController: DrawerDatapkgDetailPreviewController;
  private datapkgPreviewCache: Record<string, DatapkgDetailPreviewController> = {};
  private app?: AppController;
  private Model?: IAppPkgSearchModel;
  private appId: string;
  private appName: string;

  public constructor(options: IControllerOptions) {
    super();
    this.app = AppController.getInstance() as AppController;
    this.Model = options.Model;
    // app级别检索的id
    this.appId = options.appId;
    // app级别检索的app名称
    this.appName = options.title;
    this.tableCardController = new TableCardDatapkgSearchController({
      dataListControllerOptions: {
        loadDataListFunc: this.queryFirstPageData,
        loadNextPageDataListFunc: this.queryNextPageData,
        getBackendFilterParams: this.getBackendFilterParams,
      },
      headerTitle: options.title,
      clickDatapkgItemFunc: this.clickDatapkgItem,
      Model: options.Model,
      enabledSearchTypes: [SearchTypeEnum.NAME, SearchTypeEnum.TAG, SearchTypeEnum.COLUMN],
    });
    this.drawerController = new DrawerDatapkgDetailPreviewController(this.app, {
      getPreviewControllerFunc: this.getDatapkgController,
    });
    this.init();
  }

  public destroy() {
    super.destroy();
    this.tableCardController.destroy();
    this.drawerController.destroy();
    _.forEach(this.datapkgPreviewCache, (it) => it.destroy());
    this.datapkgPreviewCache = {};
    this.app = undefined;
    this.Model = undefined;
  }

  public getTableCardController() {
    return this.tableCardController;
  }

  public getDrawerController() {
    return this.drawerController;
  }

  private getDatapkgController = (pkgId: string) => {
    let cache = this.datapkgPreviewCache[pkgId];
    if (!cache) {
      cache = new DatapkgDetailPreviewController({
        pkgId,
        Model: getModel(DatapkgDetailPreviewModel, DatapkgDetailPreviewModelBff),
      });
    }
    this.datapkgPreviewCache[pkgId] = cache;
    return cache;
  };

  private clickDatapkgItem = (item: IDatapkgItem) => {
    this.drawerController.openModal(item.id);
  };

  // 加载首页列表
  private queryFirstPageData = (filterParams: IFilterParams): ILoadDataListRslt<IDatapkgItem> => {
    const params = { apps: this.appId };
    return this.Model!.queryFirstPagePkgs(filterParams.params, params, filterParams.cancelToken, this.appName);
  };

  // 加载下一页列表
  private queryNextPageData = (params: IPaginationParams) => {
    const filterParams = this.getBackendFilterParams();
    return this.Model!.queryNextPageTickets(
      filterParams.params,
      { ...params, apps: this.appId },
      filterParams.cancelToken,
      this.appName,
    );
  };

  private getBackendFilterParams = (): IFilterParams => {
    const tcc = this.tableCardController;
    const app = this.app!;
    const params = tcc.defaultGetSearchParams();
    params.need_permissions = true;
    params.need_tags = true;
    params.status = 'approved';
    params.ownership = OwnershipEnum.APP;
    const isSelfApp = this.appId === `${app.getAppId()}`;
    const pas = app.getAlbumController().getPrefAlbumIds();
    isSelfApp && app.getPreferencesController().getHasSetPkgPref() && (params.album_id = pas);
    const cancelToken = this.getCancelToken();
    return { cancelToken, params };
  };

  // 初始请求
  private init() {
    // 监听toolFilter变化
    const tcc = this.tableCardController;
    tcc.defaultComputeConditionList();
    tcc.listenBackendFilter(tcc.getToolInput$(), tcc.getToolFilter$(), tcc.getToolDateRange$());
    this.listenSearchTypeChange();
    tcc.loadDataList(this.getBackendFilterParams());
  }

  private listenSearchTypeChange() {
    this.tableCardController
      .getSearchType$()
      .pipe(skip(1))
      .subscribe(() => {
        const filter = this.getBackendFilterParams();
        const { name, fuzzy_tag, column } = filter.params;
        if (name || fuzzy_tag || column) {
          this.tableCardController.loadDataList(filter);
        }
      });
  }
}

export { AppPkgSearchController };
