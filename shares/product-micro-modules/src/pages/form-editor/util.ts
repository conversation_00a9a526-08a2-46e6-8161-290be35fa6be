import _ from 'lodash';
import { type Engine, ITreeNode, TreeNode } from '@designable/core';
import type { IFormilySchema } from '@designable/formily-transformer';
import { transformToSchema } from '@designable/formily-transformer';
import type { ISchema } from '@formily/json-schema';
import { OTHER_OPTION_KEY_PREFIX, QuestionCompEnum } from '@mdtProComm/constants/question';
import { UploadTypeEnum } from '@mdtProFormEditor/metro-form-design/components/Upload';
import { MetroSettingProp } from '@mdtProFormEditor/metro-form-design/shared';
import { OTHER_OPTION_LABEL } from '@mdtProFormEditor/metro-form-design/utils';
import { getOtherOptionInnerKey } from '../../components/form-view/util';
import { compose } from '../../utils';
import { IFormEditorSchema } from './FormEditorController';
import { transformSettingProps } from './transformer';

export const BFF_REQUEST_FLAG = 'bff_request:';
export const NODE_PROPS_ID = 'x-designable-id';
export const FORM_NODE_ID = 'form-node-id';
export const NODE_COMP = 'x-component';
export const NODE_COMP_PROPS = 'x-component-props';
export const NODE_DECO_PROPS = 'x-decorator-props';

export enum ArrayBaseCompEnum {
  ARRAY_CARDS = 'ArrayCards',
  ARRAY_TABLE = 'ArrayTable',
}

export const arrayBaseComps = [ArrayBaseCompEnum.ARRAY_CARDS, ArrayBaseCompEnum.ARRAY_TABLE];

export const isArrayBaseComp = (schemaProperty: Record<string, any>) =>
  _.includes(arrayBaseComps, schemaProperty[NODE_COMP]);

interface IProcessParamsToSave {
  properties: Record<string, any>;
  engine: Engine;
}

const sanitizeMap: any = {
  '<': '&lt;',
  '>': '&gt;',
};
const sanitizeReg = /\<|\>/g;
export const sanitizeStr2Xml = (type: string) =>
  _.replace(type, sanitizeReg, (matchStr: string) => sanitizeMap[matchStr]);

export const getFieldKeyToSave = (it: any) => it.name || it[NODE_PROPS_ID];

const transformArrayBaseProperty = (it: Record<string, any>, engine: Engine) => {
  if (it.properties) {
    const propertiesSchema = { schema: { properties: it.properties } };
    it.properties = transformFormilySchemaToSave(propertiesSchema, engine).schema!.properties;
  }
  if (it.items) {
    const itemsSchema = { schema: it.items };
    it.items = transformFormilySchemaToSave(itemsSchema, engine).schema;
  }
  return it;
};

const getAllPropertyKeys = (properties: Record<string, any>) => {
  const keys: string[] = [];
  _.forEach(properties, (p) => {
    keys.push(p[NODE_PROPS_ID]);
    p.properties && keys.push(...getAllPropertyKeys(p.properties));
    // 多选题的其他选项对应于items
    _.isObject(p.items) && keys.push(p.items[NODE_PROPS_ID]);
  });
  return keys;
};

const processArrayBaseCompToSave = (compName: string) => {
  return (props: IProcessParamsToSave) => {
    const { properties, engine } = props;
    const hasArrayBaseComp = !!_.filter(_.values(properties), (it: any) => it[NODE_COMP] === compName).length;
    if (!hasArrayBaseComp) return { properties, engine };

    const schema = transformToSchema(engine.getCurrentTree());
    const list = _.values(schema.schema?.properties);
    const arrayBaseProperties = _.filter(list, (it: any) => it[NODE_COMP] === compName);
    const toRemoveKeys: string[] = [];
    _.forEach(arrayBaseProperties, (it: any) => {
      const key = getFieldKeyToSave(it);
      toRemoveKeys.push(...getAllPropertyKeys(it.items?.properties));
      toRemoveKeys.push(...getAllPropertyKeys(it.properties));
      properties[key] = transformArrayBaseProperty(it, engine);
    });
    _.forEach(toRemoveKeys, (key) => {
      delete properties[key];
    });
    return { properties, engine };
  };
};

const processOtherOptionToSave = ({ properties, engine }: IProcessParamsToSave) => {
  const schema = transformToSchema(engine.getCurrentTree());
  const orignList = _.values(schema.schema?.properties);
  const hasOtherOptionProperties = _.filter(_.values(properties), (it: any) => isCompAddOtherOption(it));
  _.forEach(hasOtherOptionProperties, (it: any) => {
    const orignIt = _.find(orignList, [NODE_PROPS_ID, it[NODE_PROPS_ID]]) ?? (it.properties || it.items) ?? ({} as any);
    const key = getFieldKeyToSave(orignIt);
    const oProperty = _.values(orignIt.properties)[0] || orignIt.items || {};
    oProperty[NODE_DECO_PROPS] = {
      ...oProperty[NODE_DECO_PROPS],
      className: 'other-option-comp-wrap',
    };
    if (orignIt.properties || orignIt.items) {
      const otherOptionKey = getAllPropertyKeys(orignIt.properties || [orignIt.items])[0];
      if (otherOptionKey) {
        // 此schema的id和orignIt.properties || [orignIt.items]schema中的id相同，
        // 保存后再次修改时会有两个node有相同的id，修改其他选项的属性时会无效。为了解决此问题需要在修改时先删除外层schema
        // delete properties[otherOptionKey];  !不能删除
      }
    }
    properties[key] = {
      ...orignIt,
      [MetroSettingProp.otherOptionLabel]: orignIt[MetroSettingProp.otherOptionLabel] || OTHER_OPTION_LABEL,
    };
  });
  return { properties, engine };
};

const changeSchemaKeyToSave = ({ properties, engine }: IProcessParamsToSave) => {
  const list = _.values(properties);
  const obj = _.reduce(
    list,
    (obj: Record<string, any>, it) => {
      const compName = it[NODE_COMP];
      if (!compName) return obj;
      const key = getFieldKeyToSave(it);
      obj[key] = it;
      delete it.dataSource;
      let type: string = it.type;
      if (_.get(it, `x-component-props.valueType`) === 'timestamp') {
        type = 'number';
      }
      it.type = sanitizeStr2Xml(type);
      it.properties && (it.properties = changeSchemaKeyToSave({ properties: it.properties, engine }).properties);
      return obj;
    },
    {},
  );
  return { properties: obj, engine };
};

export const transformFormilySchemaToSave = (formilySchema: IFormilySchema, engine: Engine) => {
  const copiedSchema = _.cloneDeep(formilySchema);
  const properties = copiedSchema.schema?.properties;
  if (!properties) return copiedSchema;

  const composedFn = compose(
    processArrayBaseCompToSave('ArrayCards'),
    processArrayBaseCompToSave('ArrayTable'),
    // ResidentInfo 处理已移动到 CustomComponentManager
    processOtherOptionToSave,
    changeSchemaKeyToSave,
  );
  const params = { properties, engine };
  copiedSchema!.schema!.properties = composedFn(params).properties;

  return copiedSchema;
};

export const transformAllSettingValuesToSave = (
  allSettingValues: Record<string, ISchema>,
  formilySchema: IFormilySchema,
) => {
  const copiedSettings = _.cloneDeep(allSettingValues);
  const pairs = _.toPairs(copiedSettings);
  const properties: any = formilySchema.schema?.properties || {};

  return _.reduce(
    pairs,
    (obj: Record<string, any>, [key, it]) => {
      let attr = it.name || key;
      obj[attr] = it;
      const property = properties[key];
      property && (it[NODE_PROPS_ID] = property[NODE_PROPS_ID]);
      // key 为form-node-id时property为undefined
      const compName = property?.[NODE_COMP];
      if (!isCompHasDataSource(compName)) delete it[MetroSettingProp.datasource];
      return obj;
    },
    {},
  );
};

const fillCompPropsValue = (val: Record<string, any>, key: string, defaultVal: any) => {
  const cp = { ...val[NODE_COMP_PROPS] };
  cp[key] = cp[key] ?? defaultVal;
  val[NODE_COMP_PROPS] = cp;
  return val;
};
const fillValueType = (val: Record<string, any>) => {
  return fillCompPropsValue(val, 'valueType', 'string');
};
const fillWithoutOrg = (val: Record<string, any>) => {
  return fillCompPropsValue(val, 'withoutOrg', true);
};
const deepSearchProperties = (allSettingValues: Record<string, any>, properties: Record<string, any>) => {
  _.forEach(properties, (it, key) => {
    const compName = it[NODE_COMP];
    if (compName === QuestionCompEnum.DATEPICKER || compName === QuestionCompEnum.TIMEPICKER) {
      fillValueType(it);
      const sval = allSettingValues[key];
      sval && fillValueType(sval);
    } else if (compName === QuestionCompEnum.USER_SELECT_INPUT) {
      fillWithoutOrg(it);
      const sval = allSettingValues[key];
      sval && fillWithoutOrg(sval);
    }
    deepSearchProperties(allSettingValues, it.properties);
    deepSearchProperties(allSettingValues, it.items?.properties);
  });
};
export const modifySavedFormilySchemaAndSentting = (val: {
  formilySchema: Record<string, any>;
  allSettingValues: Record<string, any>;
}) => {
  deepSearchProperties(val.allSettingValues, val.formilySchema.schema?.properties);
  return val;
};

export const transformSavedFormilySchema = (formilySchema?: IFormilySchema) => {
  const copiedSchema = _.cloneDeep(formilySchema);
  const properties = copiedSchema?.schema?.properties;
  if (!properties) return copiedSchema || {};
  const list = _.values(properties);
  copiedSchema!.schema!.properties = _.reduce(
    list,
    (obj: Record<string, any>, it: any) => {
      const key = it[NODE_PROPS_ID];
      it.name = it.name || key;
      if (_.startsWith(it.name, OTHER_OPTION_KEY_PREFIX)) return obj;
      obj[key] = it;
      return obj;
    },
    {},
  );

  return copiedSchema;
};

export const transformSavedAllSettingValues = (allSettingValues: Record<string, ISchema>) => {
  const copiedSettings = _.cloneDeep(allSettingValues);
  const pairs = _.toPairs(copiedSettings);

  return _.reduce(
    pairs,
    (obj: Record<string, any>, [key, it]) => {
      let attr = it[NODE_PROPS_ID] || key;
      // 兼容name显示未空的情况
      it.name = it.name || attr;
      obj[attr] = it;
      delete it[NODE_PROPS_ID];
      return obj;
    },
    {},
  );
};

// 如果一个Formily组件有多个展示形式（比如Upload 会有图片和附件, Select会有单选和多选），需要区分出具体的组件名称，方便查找对应的属性转换器
export const getQuestionComponentName = (nodeProps: Record<any, any> = {}) => {
  const componentName = nodeProps[NODE_COMP];
  if (componentName === 'Upload') {
    return nodeProps[MetroSettingProp.uploadType] === UploadTypeEnum.IMAGE ? 'UploadImage' : 'Upload';
  }
  if (componentName === QuestionCompEnum.SELECT && nodeProps[NODE_COMP_PROPS]?.['mode'] === 'multiple') {
    return QuestionCompEnum.SELECT_MULTIPLE;
  }
  if (componentName === QuestionCompEnum.TREESELECT && nodeProps[NODE_COMP_PROPS]?.['multiple']) {
    return QuestionCompEnum.TREESELECT_MULTIPLE;
  }
  return componentName;
};

export const mergeNodePropsWithSetting = (node: ITreeNode, settingValue: any) => {
  const nodeProps = node.props || {};
  const settingProps = transformSettingProps(getQuestionComponentName(node.props), settingValue);
  // 如果相同属性是对象时需要合并
  const mergedObjectProps: Record<string, any> = {};
  const isNewComp = settingProps[NODE_COMP] && nodeProps[NODE_COMP] !== settingProps[NODE_COMP];
  if (!isNewComp) {
    mergedObjectProps[NODE_COMP_PROPS] = {
      ...nodeProps[NODE_COMP_PROPS],
      ...settingProps[NODE_COMP_PROPS],
    };
    mergedObjectProps[NODE_DECO_PROPS] = {
      ...nodeProps[NODE_DECO_PROPS],
      ...settingProps[NODE_DECO_PROPS],
    };
  }

  return {
    ...nodeProps,
    ...settingProps,
    ...mergedObjectProps,
    [NODE_PROPS_ID]: node.id,
  };
};

export const getNewFieldSchema = (formilySchema: IFormilySchema, node: ITreeNode, values: any) => {
  const newProps = mergeNodePropsWithSetting(node, values);
  const properties: Record<string, any> = formilySchema.schema!.properties! as any;
  const nodeID = node.id!;
  const property = properties[nodeID];
  if (property) {
    _.assign(property, _.omit(newProps, ['x-index']));
  } else {
    properties[nodeID] = newProps;
  }

  sureNodeNameAndOtherNameSame(properties, nodeID);

  return formilySchema;
};

export const sureNodeNameAndOtherNameSame = (properties: any, nodeID: string) => {
  const nodePropertie = properties[nodeID];
  const otherOptionId = getOtherOptionInnerKey(nodeID);
  const opt = properties[otherOptionId];
  // 需要保持node name和OtherOption name一致
  opt && (opt.name = getOtherOptionInnerKey(nodePropertie.name));

  if (!nodePropertie[MetroSettingProp.addOtherOption] && properties.hasOwnProperty(otherOptionId)) {
    delete properties[otherOptionId];
  }
};

export const getNewFormSchema = (formilySchema: IFormilySchema, node: ITreeNode, values: any) => {
  let horizontalStyle = { labelCol: 24 };
  if (values.layout === 'horizontal') {
    horizontalStyle = { labelCol: 6 };
  }
  formilySchema.form = { ...node.props, ...values, ...horizontalStyle };
  const defaultSchema = {
    properties: {},
    [NODE_PROPS_ID]: node.id,
  };
  formilySchema.schema = { ...defaultSchema, ...formilySchema.schema };
  return formilySchema;
};

const hasDataSourceComps = [
  QuestionCompEnum.RADIO_GROUP,
  QuestionCompEnum.CHECKBOX_GROUP,
  QuestionCompEnum.SELECT,
  QuestionCompEnum.CASCADER,
  QuestionCompEnum.TREESELECT,
  QuestionCompEnum.AUTO_COMPLETE,
  QuestionCompEnum.INPUT,
  QuestionCompEnum.NUMBER_PICKER,
  QuestionCompEnum.INPUT_TEXTAREA,
];
export const isCompHasDataSource = (compName: string) => _.includes(hasDataSourceComps, compName);

export const hasDataSource = (compName: string, dsSetting?: any) => {
  return isCompHasDataSource(compName) && _.size(_.get(dsSetting, 'dataRequire'));
};

export const getNodeAndChildrenIds = (node: TreeNode) => {
  const keys: string[] = [];
  keys.push(node.id);
  _.forEach(node.children, (c) => {
    keys.push(...getNodeAndChildrenIds(c));
  });
  return keys;
};

export const isCompAddOtherOption = (props?: any) => props?.[MetroSettingProp.addOtherOption];

// 从自增表格拖出一个组件后，其样式需要手动更新
export const ensureSameStyle = (editorSchema: IFormEditorSchema) => {
  const updateSchema = (schema: any) => {
    const decoratorProps = schema[NODE_DECO_PROPS];
    if (decoratorProps) {
      decoratorProps.layout = formLayout;
      decoratorProps.colon = formColon;
    }
  };
  const { formilySchema, allSettingValues } = editorSchema;
  const form = formilySchema.form!;
  const properties = formilySchema.schema?.properties;
  const formLayout = form.layout;
  const formColon = form.colon;
  _.forEach(properties, updateSchema);
  _.forEach(allSettingValues, updateSchema);
};
