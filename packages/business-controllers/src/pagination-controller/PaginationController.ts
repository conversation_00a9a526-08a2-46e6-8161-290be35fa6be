import { BehaviorSubject, combineLatest } from 'rxjs';
import { distinctUntilChanged, map, skip } from 'rxjs/operators';

class PaginationController {
  private pageTotal$ = new BehaviorSubject(0);
  private currentPage$ = new BehaviorSubject(0);
  private hasNextPage$ = new BehaviorSubject(false);
  private loadingNext$ = new BehaviorSubject(false);
  private readonly pageSize: number;

  public constructor(pageSize: number, initialPageNum?: number) {
    this.pageSize = pageSize;
    this.computeHasNextPage();
    if (initialPageNum && initialPageNum !== 0) {
      this.currentPage$.next(initialPageNum);
    }
  }

  public destroy() {
    this.pageTotal$.complete();
    this.currentPage$.complete();
    this.hasNextPage$.complete();
    this.loadingNext$.complete();
  }

  public getPageSize() {
    return this.pageSize;
  }

  public getPageTotal$() {
    return this.pageTotal$;
  }

  public getPageTotalValue() {
    return this.pageTotal$.getValue();
  }

  public getCurrentPage$() {
    return this.currentPage$;
  }

  public getCurrentPageValue() {
    return this.currentPage$.getValue();
  }

  public getHasNextPage$() {
    return this.hasNextPage$;
  }

  public getHasNextPageValue() {
    return this.hasNextPage$.getValue();
  }

  public getLoadingNext$() {
    return this.loadingNext$;
  }

  public getLoadingNextValue() {
    return this.loadingNext$.getValue();
  }

  public changeCurrentPage(page: number) {
    this.currentPage$.next(page);
  }

  public changePageTotal(total: number) {
    this.pageTotal$.next(total);
  }

  public changeLoadingNext(loading: boolean) {
    this.loadingNext$.next(loading);
  }

  public checkNeedMore() {
    const loading = this.getLoadingNextValue();
    const hasNext = this.getHasNextPageValue();
    if (loading || !hasNext) return false;
    // 页数+1
    const prePage = this.getCurrentPageValue();
    this.currentPage$.next(prePage + 1);
    this.loadingNext$.next(true);
    return true;
  }

  public resetPagination() {
    this.pageTotal$.next(0);
    this.currentPage$.next(0);
    this.loadingNext$.next(false);
  }

  protected computeHasNextPage() {
    combineLatest(this.pageTotal$, this.currentPage$)
      .pipe(
        skip(1),
        map(([t, c]) => t > (c + 1) * this.pageSize),
        distinctUntilChanged(),
      )
      .subscribe((v) => {
        this.hasNextPage$.next(v);
      });
  }
}

export { PaginationController };
