import { createI18n } from '@i18n-chain/core';
import { LanguageEnum } from '@mdtBsComm/constants/enum';
import { cn, Locale } from './locales/cn';
import { en } from './locales/en';
export { cn, en };
export type { Locale };
import bsCommI18n from '@mdtBsComm/languages';

const i18n = createI18n({ defaultLocale: { key: LanguageEnum.CN, values: cn } });
i18n.define(LanguageEnum.EN, en);

const originalLocale = i18n.locale.bind(i18n);
i18n.locale = async (lang: string) => {
  originalLocale(lang);
  bsCommI18n.locale(lang);
};
export default i18n;
