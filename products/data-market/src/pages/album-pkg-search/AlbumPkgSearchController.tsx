import _ from 'lodash';
import { BehaviorSubject, combineLatest } from 'rxjs';
import { debounceTime, skip, tap } from 'rxjs/operators';
import { DebounceTimeEnum } from '@mdtBsComm/constants';
import { joinKey, splitKey } from '@mdtBsComm/utils/stringUtil';
import { RequestController } from '@mdtBsControllers/request-controller';
import { IAlbumDatapkgIdsQuery, IRequestCancelToken } from '@mdtProComm/interfaces';
import { getThemeTagFromUrl } from '@mdtProComm/utils/urlUtil';
import {
  CollapseTaggroupsListController,
  IGroupListUnitData,
} from '@mdtProMicroModules/containers/collapse-taggroups-list';
import {
  ConditionGroupEnum,
  IConditionListItem,
  IDatapkgItem,
  ILoadDataListRslt,
  IPaginationParams,
  SearchTypeEnum,
  TableCardDatapkgSearchController,
} from '@mdtProMicroModules/containers/table-card-datapkg-search';
import {
  DatapkgDetailPreviewController,
  DrawerDatapkgDetailPreviewController,
} from '@mdtProMicroModules/pages/datapkg-detail-preview';
import { DatapkgDetailPreviewModel } from '@mdtProMicroModules/pages/datapkg-detail-preview/DatapkgDetailPreviewModel';
import { DatapkgDetailPreviewModelBff } from '@mdtProMicroModules/pages/datapkg-detail-preview/DatapkgDetailPreviewModelBff';
import { getModel } from '../../_util/modelUtil';
import { AppController } from '../../app/AppController';
import i18n from '../../languages';
import { IAlbumPkgSearchModel } from './AlbumPkgSearchModel';

export interface IControllerOptions {
  title: string;
  albumId: string;
  openTagSide$?: BehaviorSubject<boolean>;
  Model: IAlbumPkgSearchModel;
}
interface IFilterParams {
  cancelToken: IRequestCancelToken;
  params: IAlbumDatapkgIdsQuery;
}

class AlbumPkgSearchController extends RequestController {
  private readonly tableCardController: TableCardDatapkgSearchController;
  private readonly tagController: CollapseTaggroupsListController;
  private readonly openTagSide$: BehaviorSubject<boolean>;
  private readonly drawerController: DrawerDatapkgDetailPreviewController;
  private datapkgPreviewCache: Record<string, DatapkgDetailPreviewController> = {};
  private albumId: string;
  private app?: AppController;
  private appId?: string;
  private Model?: IAlbumPkgSearchModel;

  // TODO尝试解除appcontroller
  public constructor(options: IControllerOptions) {
    super();
    this.app = AppController.getInstance() as AppController;
    this.appId = `${this.app.getAppId()}`;
    this.Model = options.Model;
    this.albumId = options.albumId;
    this.openTagSide$ = options.openTagSide$ || new BehaviorSubject<boolean>(true);
    this.tableCardController = new TableCardDatapkgSearchController({
      dataListControllerOptions: {
        loadDataListFunc: this.queryFirstPageData,
        loadNextPageDataListFunc: this.queryNextPageData,
        getBackendFilterParams: this.getBackendFilterParams,
      },
      headerTitle: options.title,
      clickDatapkgItemFunc: this.clickDatapkgItem,
      clearAllConditionListFunc: this.clearAllConditionList,
      delConditionListItemFunc: this.delConditionListItem,
      enabledSearchTypes: [SearchTypeEnum.NAME, SearchTypeEnum.TAG, SearchTypeEnum.COLUMN],
    });
    this.tagController = new CollapseTaggroupsListController({
      loadGroupDataListFunc: this.queryAllTaggroups,
      headerTitle: i18n.chain.datamarket.pkgSearch.dataTag,
      inputPlaceholder: i18n.chain.datamarket.pkgSearch.searchTag,
    });
    this.drawerController = new DrawerDatapkgDetailPreviewController(this.app, {
      getPreviewControllerFunc: this.getDatapkgController,
    });
    this.init();
  }

  public destroy() {
    super.destroy();
    this.tableCardController.destroy();
    this.tagController.destroy();
    this.openTagSide$.complete();
    this.drawerController.destroy();
    _.forEach(this.datapkgPreviewCache, (it) => it.destroy());
    this.datapkgPreviewCache = {};
    this.app = undefined;
    this.Model = undefined;
  }

  public getTableCardController() {
    return this.tableCardController;
  }

  public getDrawerController() {
    return this.drawerController;
  }

  public getTagController() {
    return this.tagController;
  }

  public getOpenTagSide$() {
    return this.openTagSide$;
  }

  public changeOpenTagSide(open: boolean) {
    this.openTagSide$.next(open);
  }

  private getDatapkgController = (pkgId: string) => {
    let cache = this.datapkgPreviewCache[pkgId];
    if (!cache) {
      cache = new DatapkgDetailPreviewController({
        pkgId,
        Model: getModel(DatapkgDetailPreviewModel, DatapkgDetailPreviewModelBff),
      });
    }
    this.datapkgPreviewCache[pkgId] = cache;
    return cache;
  };

  private clickDatapkgItem = (item: IDatapkgItem) => {
    this.drawerController.openModal(item.id);
  };

  // 加载标签数据
  private queryAllTaggroups = (cancelToken?: IRequestCancelToken) => {
    return this.Model!.queryAllTags(this.albumId, cancelToken!, this.appId).pipe(
      tap((resp) => {
        this.updateConditions(resp[1]);
      }),
    );
  };

  private updateConditions(groups: IGroupListUnitData[]) {
    const tcc = this.tableCardController;
    const oldConditions = tcc.getConditionList$().getValue();
    if (!_.size(oldConditions)) return;
    const conditions = _.map(oldConditions, (c) => {
      if (!c.group) {
        const groupId = splitKey(c.id)[1];
        const groupName = _.find(groups, ['groupId', groupId])?.group;
        return {
          ...c,
          group: groupName || '',
        };
      }
      return c;
    });
    tcc.changeConditionList(conditions);
  }

  // 加载首页列表
  private queryFirstPageData = (filterParams: IFilterParams): ILoadDataListRslt<IDatapkgItem> => {
    return this.Model!.queryFirstPagePkgs(this.albumId, filterParams.params, filterParams.cancelToken, this.appId);
  };

  // 加载下一页列表
  private queryNextPageData = (params: IPaginationParams) => {
    const filterParams = this.getBackendFilterParams();
    return this.Model!.queryNextPageTickets(
      this.albumId,
      { ...params, ...filterParams.params },
      filterParams.cancelToken,
      this.appId,
    );
  };

  // 获取参数
  private getBackendFilterParams = (): IFilterParams => {
    const tcc = this.tableCardController;
    const params: IAlbumDatapkgIdsQuery = tcc.defaultGetSearchParams();
    const cancelToken = this.getCancelToken();
    const tgc = this.tagController;
    const tagList = tgc.getSelectedDataValue();
    if (_.size(tagList)) {
      params.tags = _.map(tagList, (item) => ({
        taggroup_id: item.groupId,
        taggroup_tags: item.data,
      }));
    }

    return { cancelToken, params };
  };

  // 删除全部过滤条件
  private clearAllConditionList = () => {
    this.tableCardController.changeToolFilter([]);
    this.tagController.delAllSelectedData();
  };

  // 删除一个过滤条件
  private delConditionListItem = (item: IConditionListItem) => {
    const [group, gruopId] = splitKey(item.id);
    if (group === ConditionGroupEnum.TAG) {
      // 所属标签
      this.tagController.delSelectedData(gruopId, item.name);
    } else {
      const old = this.tableCardController.getToolFilterValue();
      const filters = _.reject(old, { key: item.id });
      this.tableCardController.changeToolFilter(filters);
    }
  };

  // 初始请求
  private init() {
    const tgc = this.tagController;
    const tcc = this.tableCardController;
    const defaultGroup = getThemeTagFromUrl();
    const hasDefaultGroup = !!(defaultGroup.taggroupId && defaultGroup.tag);
    this.computeCondition(hasDefaultGroup);
    // 监听字段过滤后端
    tcc.listenBackendFilter(tcc.getToolInput$(), tcc.getToolFilter$(), tgc.getSelectedData$(), tcc.getToolDateRange$());
    this.listenSearchTypeChange();
    const params = this.getBackendFilterParams();

    if (hasDefaultGroup) {
      tgc.changeSelectedData$([
        {
          groupId: defaultGroup.taggroupId,
          group: '',
          data: [defaultGroup.tag],
        },
      ]);
    } else {
      tcc.loadDataList(params);
    }
    // 请求数据
    tgc.loadDataList();
  }

  // 搜索类型需要单独监听，因为在搜索内容为空的情况下切换搜索类型无需搜索数据。
  private listenSearchTypeChange() {
    this.tableCardController
      .getSearchType$()
      .pipe(skip(1))
      .subscribe(() => {
        const filter = this.getBackendFilterParams();
        const { name, fuzzy_tag, column } = filter.params;
        if (name || fuzzy_tag || column) {
          this.tableCardController.loadDataList(filter);
        }
      });
  }

  private computeCondition(hasDefault: boolean) {
    const tcc = this.tableCardController;
    const tgc = this.tagController;
    combineLatest(tcc.getToolFilter$(), tgc.getSelectedData$())
      .pipe(skip(hasDefault ? 0 : 1), debounceTime(DebounceTimeEnum.MIN))
      .subscribe(([filters, groups]) => {
        const fs = _.map(filters, (it) => {
          const [group] = splitKey(it.key);
          const groupLabel = this.Model!.getFilterGroupLabel(group);
          return { id: it.key, name: it.title, group: groupLabel };
        });
        const ts = _.flatMap(groups, (group) => {
          return _.map(group.data, (it) => ({
            id: joinKey(ConditionGroupEnum.TAG, group.groupId, it),
            name: it,
            group: group.group,
          }));
        });
        tcc.changeConditionList([...fs, ...ts]);
      });
  }
}

export { AlbumPkgSearchController };
