import { useEffect } from 'react';
import { Redirect, Route, Switch, useHistory } from 'react-router-dom';
import { DingtalkPage, initLoginConfig, LoginPage, VerifyPage, WechatPage } from '@mdtLogin/index';
import { useCreation } from '@mdtLogin/util/useCreation';
import { RoutePathEnum } from '../_util/constants';
import { isDialog } from '../_util/util';
import { AppController } from './AppController';
import { getConfig } from './loginConfig';
import 'modern-normalize/modern-normalize.css';
import './index.less';
/**
 * 重要，需要使用前初始化
 */
initLoginConfig(getConfig());

const Login = () => (
  <LoginPage
    className={`mdt-login-collector-layout-content ${!isDialog() ? 'mdt-login-collector-tabs-nav-autohide' : ''}`}
  />
);

const Verify = () => <VerifyPage className="mdt-login-collector-verify" />;
const Wechat = () => <WechatPage />;
const Dingtalk = () => <DingtalkPage />;

const App = () => {
  const history = useHistory();
  const controller = useCreation(() => AppController.getInstance(history));

  useEffect(() => {
    return () => {
      controller.destroy();
    };
  }, [controller]);

  return (
    <Switch>
      <Route path={RoutePathEnum.ROOT} exact>
        <Login />
      </Route>
      <Route path={RoutePathEnum.VERIFY} exact>
        <Verify />
      </Route>
      <Route path={RoutePathEnum.WECHAT} exact>
        <Wechat />
      </Route>
      <Route path={RoutePathEnum.DINGTALK} exact>
        <Dingtalk />
      </Route>
      <Route>
        <Redirect to={RoutePathEnum.ROOT} />
      </Route>
    </Switch>
  );
};
export default App;
