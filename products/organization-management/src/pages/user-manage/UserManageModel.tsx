import _ from 'lodash';
import { from, of } from 'rxjs';
import { Observable } from 'rxjs/internal/Observable';
import { map, takeWhile } from 'rxjs/operators';
import { IBusinessResult } from '@mdtBsComm/interfaces';
import {
  createDate,
  Dayjs,
  equalTime,
  formateDate,
  isExpirationDateFromNow,
  transformDateToUnix,
} from '@mdtBsComm/utils/dayUtil';
import { MoreActionType } from '@mdtBsComponents/data-list-comp-table-curd';
import { deleteUsersAsync, getAllUsersAsync, postUserAsync, putUserAsync, queryRolesAsync } from '@mdtBsServices/auth';
import {
  IRequestCancelToken,
  IRequestSettledResult,
  IRole,
  IServerResponse,
  IUser,
  IUserPost,
  IUserPostAdmin,
  IUserPut,
  IUserPutAdmin,
} from '@mdtBsServices/interfaces';
import { IFormData } from '@mdtProMicroModules/containers/drawer-modify-form-permission-user';
import { GroupPermissionModel } from '@mdtProMicroModules/models/GroupPermissionModel';
import i18n from '../../languages';

export interface ITableData {
  id: number;
  name: string;
  email: string;
  phone: string;
  role: number[];
  roleDisplay: string[];
  disable: boolean;
  disableDisplay: string;
  expireTime: Dayjs;
  expireTimeDisplay: string;
  permission: number[];
}
export interface IRoleData {
  label: string;
  value: number;
}
export interface IUsersRespData {
  userList: ITableData[];
  roleList: IRoleData[];
  roleIdNameMap: Record<number, string>;
  roleIdPermissionMap: Record<number, number[]>;
}
export enum IMoreItemsKey {
  DELETE = 'delete',
}

export class UserManageModel extends GroupPermissionModel {
  public static transformToTableData(item: IUser | ITableData, roleMap: Record<number, string>): ITableData {
    let expireTimeDisplay = i18n.chain.proMicroModules.user.noExpire;
    let et: Dayjs;
    let disable: boolean;
    let permission: number[];
    if ('disable' in item) {
      disable = item.disable;
      et = createDate(item.expireTime);
      permission = item.permission;
    } else {
      disable = !item.enable;
      et = createDate(item.expire_time);
      permission = _.flatMap(item.permission, (p) => p);
    }
    const tip = isExpirationDateFromNow(et, 0) ? '' : i18n.chain.proMicroModules.user.expired;
    expireTimeDisplay = `${formateDate(et)}${tip}`;
    const user: ITableData = {
      id: item.id,
      name: item.name,
      email: item.email,
      phone: item.phone,
      role: item.role,
      roleDisplay: _.compact(_.map(item.role, (it) => roleMap[it])),
      disable: disable,
      disableDisplay: disable ? i18n.chain.orgAdmin.organization.freeze : i18n.chain.orgAdmin.organization.enable,
      expireTime: et,
      expireTimeDisplay,
      permission: permission,
    };
    return user;
  }

  public static queryAllUsers(appId: number, cancelToken?: IRequestCancelToken) {
    return from(
      new Promise<IServerResponse<IUsersRespData>>((resolve) => {
        const request = async () => {
          const cnf = { cancelToken, params: { app_id: appId } };
          const [usersResp, rolesResp] = (await Promise.allSettled([
            getAllUsersAsync(appId, {}, { cancelToken }),
            queryRolesAsync(cnf),
          ])) as [IRequestSettledResult<IUser[]>, IRequestSettledResult<IRole[]>];
          const roleIdNameMap: Record<number, string> = {};
          const roleIdPermissionMap: Record<number, number[]> = {};
          const roleList = _.map(rolesResp.value.data, ({ id, name, permission }) => {
            roleIdNameMap[id] = name;
            roleIdPermissionMap[id] = permission || [];
            return { label: name, value: id };
          });
          const userList = _.map(usersResp.value.data, (u) => this.transformToTableData(u, roleIdNameMap));
          resolve({
            ...usersResp.value,
            data: {
              userList,
              roleList,
              roleIdNameMap,
              roleIdPermissionMap,
            },
          });
        };
        request();
      }),
    ).pipe(
      takeWhile((resp) => !resp.canceled),
      map((resp) => {
        return resp.data!;
      }),
    );
  }

  public static updateUser(
    data: IFormData,
    orginalData: ITableData,
    appId: number,
    roleIdNameMap: Record<number, string>,
  ): Observable<IBusinessResult<ITableData>> {
    const postData: IUserPut = { id: orginalData.id };
    data.name !== orginalData.name && (postData.name = data.name);
    data.email !== orginalData.email && (postData.email = data.email);
    data.phone !== orginalData.phone && (postData.phone = data.phone);
    const putData: IUserPutAdmin = { app_id: appId };
    data.disable !== orginalData.disable && (putData.enable = !data.disable);
    // 处理时间
    !equalTime(data.expireTime, orginalData.expireTime) && (putData.expire_time = transformDateToUnix(data.expireTime));
    // 处理权限
    const nps = _.difference(data.permission, orginalData.permission);
    const dps = _.difference(orginalData.permission, data.permission);
    nps.length && (putData.new_permission = nps);
    dps.length && (putData.del_permission = dps);
    // 处理角色
    const nrs = _.difference(data.role, orginalData.role);
    const drs = _.difference(orginalData.role, data.role);
    nrs.length && (putData.new_role = nrs);
    drs.length && (putData.del_role = drs);
    // 判断是否有更改，如果没有则不追加
    _.size(putData) > 1 && (postData.admin = [putData]);
    // 如果未做改变，则直接关闭窗口
    if (_.size(postData) === 1) {
      return of({ success: true, result: orginalData });
    }
    return from(putUserAsync(postData)).pipe(
      map((resp) => {
        if (resp.success) {
          const td = { ...data, id: orginalData.id };
          return {
            success: true,
            result: this.transformToTableData(td as ITableData, roleIdNameMap),
          };
        }
        return { success: false };
      }),
    );
  }

  public static createUser(
    data: IFormData,
    appId: number,
    roleIdNameMap: Record<number, string>,
  ): Observable<IBusinessResult<ITableData>> {
    const postAdmin: IUserPostAdmin = {
      app_id: appId,
      enable: !data.disable,
      expire_time: transformDateToUnix(data.expireTime),
      permission: data.permission,
    };
    _.size(data.role) && (postAdmin.role = data.role);
    const postData: IUserPost = {
      email: data.email,
      name: data.name,
      admin: [postAdmin],
    };
    return from(postUserAsync(postData)).pipe(
      map((resp) => {
        if (resp.success) {
          const td = { ...data, id: resp.data!.user_id };
          const result = this.transformToTableData(td as ITableData, roleIdNameMap);
          return { success: true, result };
        }
        return { success: false };
      }),
    );
  }

  public static deleteUser(data: ITableData, appId: number, pass_to: number): Observable<IBusinessResult<ITableData>> {
    return from(deleteUsersAsync(`${data.id}`, appId, pass_to)).pipe(
      map((resp) => ({ success: resp.success, result: data, actionType: MoreActionType.DELETE })),
    );
  }
}

export type IUserManageModel = typeof UserManageModel;
