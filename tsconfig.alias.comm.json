{"compilerOptions": {"baseUrl": ".", "paths": {"react": ["../../node_modules/react"], "history": ["../../node_modules/history/cjs/history.min.js"], "react-router": ["../../node_modules/react-router/cjs/react-router.min.js"], "@loadable/component": ["../../node_modules/@loadable/component/dist/loadable.min.js"], "spark-md5": ["../../node_modules/spark-md5/spark-md5.min.js"], "lz-string": ["../../node_modules/lz-string/libs/lz-string.min.js"], "@mdtDesign/*": ["../../node_modules/@datlas/design/esm/components/*"], "@mdtBpmnPropertiesPanel/*": ["../../packages/bpmn-js-properties-panel/src/*"], "@mdtBsBffServices/*": ["../../packages/business-bff-services/src/*"], "@mdtBsComm/*": ["../../packages/business-comm/src/*"], "@mdtBsComponents/*": ["../../packages/business-components/src/*"], "@mdtBsControllers/*": ["../../packages/business-controllers/src/*"], "@mdtBsServices/*": ["../../packages/business-services/src/*"], "@mdtLogin/*": ["../../packages/login/src/*"], "@mdtApis/*": ["../../packages/restful-apis/src/*"], "@mdtFormily/*": ["../../packages/formily/src/*"], "@mdtDesignable/*": ["../../packages/form-design/src/*"], "@mdtProComm/*": ["../../shares/product-comm/src/*"], "@mdtProSso/*": ["../../shares/product-sso/src/*"], "@mdtProFormEditor/*": ["../../shares/product-form-editor/src/*"], "@mdtProMicroModules/*": ["../../shares/product-micro-modules/src/*"], "@mdtProTasks/*": ["../../shares/product-tasks/src/*"], "@mdtSentry/*": ["../../utils/sentry/src/*"], "@metroDesign/*": ["../../node_modules/@metro/components/dist/esm/*"]}}}