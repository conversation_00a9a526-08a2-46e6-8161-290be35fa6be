import _ from 'lodash';
import { DatlasAppController } from '../../../datlas/app/DatlasAppController';
import { isDecimal, isEmail, isIdCard, isInteger, isPhone, isUrl } from '../../../utils';

const getDecimalCount = (value = '') => {
  const val = `${value}`;
  const indx = val.indexOf('.');
  return indx > -1 ? val.substring(indx + 1).length : 0;
};

const lessThan = (val: number, len: number) => {
  return val < len;
};

const moreThan = (val: number, len: number) => {
  return val > len;
};

const lenLessThan = (val = '', len: number) => {
  return _.size(val) < len;
};

const lenMoreThan = (val = '', len: number) => {
  return _.size(val) > len;
};

const systemPresetVariables = (): Record<string, any> => {
  return DatlasAppController.getInstance()!.getDatlasContext();
};

export const mergeFormViewScope = (scope: any) => {
  return {
    isInteger,
    isDecimal,
    isPhone,
    isIdCard,
    isEmail,
    isUrl,
    getDecimalCount,
    lenLessThan,
    lenMoreThan,
    lessThan,
    moreThan,
    isArray: _.isArray,
    ...scope,
    $system: systemPresetVariables(),
  };
};
