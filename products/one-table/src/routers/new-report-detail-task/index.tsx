import { useController } from '@mdtBsComm/hooks/use-controller';
import { getAssignWfIdFromUrl } from '@mdtProComm/utils/urlUtil';
import {
  OneTableNewReportDetail,
  OneTableNewReportDetailController,
  OneTableNewReportDetailModel,
} from '@mdtProMicroModules/pages/one-table-new-report-detail';
import { DetailFromPageEnum } from '@mdtProMicroModules/utils/oneTableNewUtil';
import './index.less';

const NewReportDtailTaskView = () => {
  const assignWfId = getAssignWfIdFromUrl();
  const [controller] = useController(() => {
    return [
      new OneTableNewReportDetailController({
        Model: OneTableNewReportDetailModel,
        fromPage: DetailFromPageEnum.TASK,
        assignWfId,
      }),
      null,
    ];
  }, []);

  return (
    <div className="new-page-report-detail-task">
      <OneTableNewReportDetail controller={controller} />
    </div>
  );
};

export default NewReportDtailTaskView;
