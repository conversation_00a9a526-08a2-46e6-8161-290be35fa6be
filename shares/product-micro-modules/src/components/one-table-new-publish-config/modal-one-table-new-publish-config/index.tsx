import { type ModalProps, Modal } from '@metroDesign/modal';
import i18n from '../../../languages';
import { type IProps, OneTableNewPublishConfig } from '../OneTableNewPublishConfig';

export type IOpenOnetableNewPublishConfig = Omit<IProps, 'onClose'>;

export const openOnetableNewPublishConfigModal = (
  childrenProps: IOpenOnetableNewPublishConfig,
  modalProps?: Omit<ModalProps, 'children'>,
) => {
  Modal.open({
    title: i18n.chain.proMicroModules.oneTable.publishJob,
    footer: null,
    ...(modalProps || {}),
    children: (onClose) => <OneTableNewPublishConfig {...childrenProps} onClose={onClose} />,
  });
};
