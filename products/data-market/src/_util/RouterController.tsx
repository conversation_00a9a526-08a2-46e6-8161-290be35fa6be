import loadable from '@loadable/component';
import { PermissionEnum } from '@mdtProComm/constants/permission';
import { IRoute } from '@mdtProComm/controllers/BaseRouterController';
import { DatlasRouterController } from '@mdtProMicroModules/datlas/comm/DatlasRouterController';
import { RoutePathEnum } from './constants';
import { DataMarketPermissionEnum } from './UserPermissionController';

const DashboardView = loadable(() => import('../routers/dashboard'));
const PkgSmartSearchView = loadable(() => import('../routers/pkg-smart-search'));
const AllPkgSearchView = loadable(() => import('../routers/all-pkg-search'));
const AppPkgSearchView = loadable(() => import('../routers/app-pkg-search'));
const AlbumPkgSearchView = loadable(() => import('../routers/album-pkg-search'));
const DataApprovalAppView = loadable(() => import('../routers/approval-app'));
const DataApprovalOtherView = loadable(() => import('../routers/approval-other'));
const MetaDataTemplateManageThemeView = loadable(() => import('../routers/assets-metadata-theme'));
const MetaDataTemplateManageTagGroupView = loadable(() => import('../routers/assets-metadata-tag-group'));
const TableMdTemplateView = loadable(() => import('../routers/assets-template'));
const DatapkgShareView = loadable(() => import('../routers/datapkg-share'));
const DataDetailShare = loadable(() => import('../routers/data-detail-share'));

export const allAuthRoutes: IRoute[] = [
  {
    path: RoutePathEnum.DASHBOARD,
    View: DashboardView,
    permissionKey: DataMarketPermissionEnum.MENU_DASHBOARD,
  },
  {
    path: RoutePathEnum.SMART_SEARCH,
    View: PkgSmartSearchView,
    permissionKey: DataMarketPermissionEnum.MENU_SEARCH,
  },
  {
    path: RoutePathEnum.SEARCH_ALL,
    View: AllPkgSearchView,
    permissionKey: DataMarketPermissionEnum.MENU_SEARCH,
    // headerLess: true, // 不展示header
    // sideMenuLess: true, // 不展示sideMenu
  },
  {
    path: `${RoutePathEnum.SEARCH_APP}/:id`,
    View: AppPkgSearchView,
    permissionKey: DataMarketPermissionEnum.MENU_SEARCH,
  },
  {
    path: `${RoutePathEnum.SEARCH_ALBUM}/:id`,
    View: AlbumPkgSearchView,
    permissionKey: DataMarketPermissionEnum.MENU_SEARCH,
  },
  {
    path: `${RoutePathEnum.SHARE}`,
    View: DatapkgShareView,
    permissionKey: PermissionEnum.OPT_SEARCH_APPLY_SHARE,
  },
  {
    path: `${RoutePathEnum.ASSETS_METADATA_THEME}`,
    View: MetaDataTemplateManageThemeView,
    permissionKey: DataMarketPermissionEnum.MENU_THEME,
  },
  {
    path: `${RoutePathEnum.ASSETS_METADATA_TAG_GROUP}`,
    View: MetaDataTemplateManageTagGroupView,
    permissionKey: DataMarketPermissionEnum.MENU_TAGGROUP,
  },
  {
    path: `${RoutePathEnum.ASSETS_TEMPLATE}`,
    View: TableMdTemplateView,
    permissionKey: DataMarketPermissionEnum.MENU_DOCTEMPLATE,
  },
  {
    path: `${RoutePathEnum.APPROVAL_APP}`,
    View: DataApprovalAppView,
    permissionKey: DataMarketPermissionEnum.MENU_APPROVAL,
  },
  {
    path: `${RoutePathEnum.APPROVAL_OTHER}`,
    View: DataApprovalOtherView,
    permissionKey: DataMarketPermissionEnum.MENU_APPROVAL,
  },
  {
    path: `${RoutePathEnum.PKG_DETAIL}/:id`,
    View: DataDetailShare,
    headerLess: true,
    sideMenuLess: true,
    permissionKey: PermissionEnum.PRODUCT_MENU_DM,
  },
];

class RouterController extends DatlasRouterController {
  public getAllAuthRoutes() {
    return allAuthRoutes;
  }

  public getDatapkgShareUrl(pkgId: string) {
    const path = this.createHref({ pathname: `${RoutePathEnum.PKG_DETAIL}/${pkgId}` });
    const url = new URL(path, window.location.origin);
    return url.toString();
  }

  public gotoApprovalOther() {
    this.gotoPath(RoutePathEnum.APPROVAL_OTHER);
  }
}

export { RouterController };
