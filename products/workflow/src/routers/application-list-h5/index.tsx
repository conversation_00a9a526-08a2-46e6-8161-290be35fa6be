import { useController } from '@mdtBsComm/hooks/use-controller';
import { WorkflowApplicationListModel } from '@mdtProMicroModules/pages/workflow-application-list';
import { WorkflowApplicationListModelBff } from '@mdtProMicroModules/pages/workflow-application-list/WorkflowApplicationListModelBff';
import {
  WorkflowApplicationListH5,
  WorkflowApplicationListH5Controller,
} from '@mdtProMicroModules/pages/workflow-application-list-h5';
import { getModel } from '../../_util/modelUtil';

const PageApplicationListH5 = () => {
  const [controller] = useController(() => {
    const ctrl = new WorkflowApplicationListH5Controller({
      Model: getModel(WorkflowApplicationListModel, WorkflowApplicationListModelBff),
    });
    return [ctrl, null];
  });

  return <WorkflowApplicationListH5 controller={controller} />;
};

export default PageApplicationListH5;
