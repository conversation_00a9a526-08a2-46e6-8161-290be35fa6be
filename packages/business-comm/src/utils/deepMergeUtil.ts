import isObject from 'lodash/isObject';
import { ReactNode } from 'react';

type IValue = Record<string, any>;

/** 深度遍历对象进行合并，包含数组和对象的深层合并 */
function deepMerge<T extends IValue>(target: T, ...sources: Partial<T>[]): T {
  if (!sources.length) return target;
  const source = sources.shift();

  if (isObject(target) && isObject(source)) {
    for (const key in source) {
      if (isReactNode(source[key]) || !isObject(source[key])) {
        target[key] = source[key] as T[Extract<keyof T, string>];
      } else if (Array.isArray(target[key]) && Array.isArray(source[key])) {
        // 合并数组并去重
        target[key] = Array.from(
          new Set([...(target[key] as unknown as any[]), ...(source[key] as any[])]),
        ) as unknown as T[Extract<keyof T, string>];
      } else if (isObject(target[key]) && isObject(source[key])) {
        target[key] = deepMerge(target[key] as IValue, source[key] as IValue) as T[Extract<keyof T, string>];
      } else {
        target[key] = source[key] as T[Extract<keyof T, string>];
      }
    }
  }

  return deepMerge(target, ...sources);
}

// 辅助函数检查对象是否为 ReactNode
function isReactNode(value: any): value is ReactNode {
  // @ts-ignore
  return value !== null && typeof value === 'object' && (value as ReactNode)?.$$typeof !== undefined;
}

export { deepMerge };
