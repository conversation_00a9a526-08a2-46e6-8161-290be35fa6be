import _ from 'lodash';
import { AsyncSubject, of } from 'rxjs';
import { map } from 'rxjs/operators';
import { IBusinessResult } from '@mdtBsComponents/modal-with-btns-comp';
import { IEmotionProps, ModalWithBtnsCompEmotionController } from '@mdtBsComponents/modal-with-btns-comp-emotion';
import { RequestController } from '@mdtBsControllers/request-controller';
import { IRequestCancelToken } from '@mdtBsServices/interfaces';
import { LinkButton } from '@mdtDesign/button';
import Tag from '@mdtDesign/tag';
import toastApi from '@mdtDesign/toast';
import {
  DrawerModifyFormPermissionUserController,
  IFormData,
  IModalData,
} from '@mdtProMicroModules/containers/drawer-modify-form-permission-user';
import {
  ILoadDataListRslt,
  IVirtualizedTableProps,
  TableCurdWithSimpleSearchController,
} from '@mdtProMicroModules/containers/table-curd-with-simple-search';
import { AppController } from '../../app/AppController';
import { IMoreItemsKey, ITableData, IUserManageModel } from './UserManageModel';

class UserManageController extends RequestController {
  private Model: IUserManageModel;
  private tableController: TableCurdWithSimpleSearchController<ITableData>;
  private modifyController: DrawerModifyFormPermissionUserController<ITableData>;
  private deleteController: ModalWithBtnsCompEmotionController<ITableData>;
  private drawerUiDataCache: Partial<IModalData> = {};
  private app?: AppController;
  private appId: number;
  private userId: number;

  public constructor(app: AppController, Model: IUserManageModel) {
    super();
    this.Model = Model;
    this.app = app;
    this.appId = app.getAppId()!;
    this.userId = app.getUserId()!;
    this.tableController = new TableCurdWithSimpleSearchController<ITableData>({
      dataListCompTableCurdControllerOptions: {
        dataListControllerOptions: {
          loadDataListFunc: this.queryAllData,
        },
        curdOptions: this.initCurdOptions,
        tableOptions: this.initTableOptions,
        clickCreateBtnFunc: this.createTableData,
        clickEditBtnFunc: this.editTableData,
        clickMoreBtnFunc: this.moreItemsTableData,
      },
      headerOptions: this.initHeaderOptions,
    });
    this.modifyController = new DrawerModifyFormPermissionUserController<ITableData>({
      loadDrawerUiDataFunc: this.queryDrawerUiData,
      modifyDataFunc: this.modifyDataToService,
    });
    this.deleteController = new ModalWithBtnsCompEmotionController<ITableData>({
      clickOkBtnFunc: this.deleteDataToService,
      modalCompOptions: { modalOptions: this.initDeleteModalOptions },
    });
    this.init();
  }

  public destroy() {
    super.destroy();
    this.tableController.destroy();
    this.modifyController.destroy();
    this.deleteController.destroy();
    this.drawerUiDataCache = {};
    this.app = undefined;
    this.Model = null!;
  }

  public getTableController() {
    return this.tableController;
  }

  public getModifyController() {
    return this.modifyController;
  }

  public getDeleteControllerr() {
    return this.deleteController;
  }

  // 加载角色列表
  private queryAllData = (cancelToken?: IRequestCancelToken): ILoadDataListRslt<ITableData> => {
    return this.Model.queryAllUsers(this.appId, cancelToken).pipe(
      map((value) => {
        this.drawerUiDataCache.roleList = value.roleList;
        this.drawerUiDataCache.roleIdNameMap = value.roleIdNameMap;
        this.drawerUiDataCache.roleIdPermissionMap = value.roleIdPermissionMap;
        return [0, value.userList];
      }),
    );
  };

  // 渲染操作拦所需信息
  private initCurdOptions = () => {
    const { enableDelete, ...rest } = this.app!.getUserPermissionController()!.getUserManagePermission();
    const moreItems = [
      {
        title: '删除',
        icon: 'delete-2',
        key: IMoreItemsKey.DELETE,
        danger: true,
        disabled: !enableDelete,
      },
    ];
    return { ...rest, moreItems };
  };

  // 渲染表头所需信息
  private initTableOptions = (): IVirtualizedTableProps => {
    return {
      columns: [
        {
          name: '用户名称',
          code: 'name',
          width: 180,
          ellipsis: true,
          render: (value: string, r: ITableData) => (
            <LinkButton onClick={() => this.editTableData(r)}>{value}</LinkButton>
          ),
        },
        {
          name: '所属角色',
          code: 'roleDisplay',
          render: (value: string[]) => _.map(value, (it) => <Tag key={it} tag={it} />),
        },
        {
          name: '是否可用',
          code: 'disableDisplay',
          width: 100,
          render: (value: string) => (
            <Tag key={value} tag={value} color={value === '可用' ? 'blue-700' : 'grey-blue-400'} />
          ),
        },
        { name: '过期时间', code: 'expireTimeDisplay', width: 180 },
      ],
      type: 'page-bg',
      primaryKey: 'id',

      withVerticalBorder: false,
    };
  };

  // 渲染头部所需信息
  private initHeaderOptions = () => {
    return {
      createBtnLabel: '新建用户',
      inputPlaceholder: '搜索用户',
      title: '用户权限',
    };
  };

  // 新增数据
  private createTableData = () => {
    return this.modifyController.openModal();
  };

  // 编辑数据
  private editTableData = (orginalData?: ITableData) => {
    const { enableEdit } = this.app!.getUserPermissionController()!.getUserManagePermission();
    if (!enableEdit) {
      let result = new AsyncSubject<IBusinessResult<ITableData>>();
      result.next({ success: true });
      result.complete();
      return result;
    }
    return this.modifyController.openModal(orginalData);
  };

  // 更多数据
  private moreItemsTableData = (key: string, orginalData?: ITableData) => {
    // 删除数据
    if (key === IMoreItemsKey.DELETE) {
      return this.deleteController.openModal(orginalData);
    }
    let result = new AsyncSubject<IBusinessResult<ITableData>>();
    result.next({ success: true });
    result.complete();
    return result;
  };

  // 加载弹窗user的权限
  private queryDrawerUiData = () => {
    if (!this.drawerUiDataCache.appExpireTime) {
      const app = this.app!;
      this.drawerUiDataCache.appExpireTime = app.getAppExpireTime();
      const ps = this.app!.getPermissionController();
      const mdtProduct = this.app!.getUserMdtProduct();
      this.drawerUiDataCache.permissions = this.Model.getGroupPermissions(
        this.app!.getAppPermission()!,
        ps!.getPermissionLabel.bind(ps),
        ps!.getPermissionModule.bind(ps),
        ps!.getPermissionType.bind(ps),
        mdtProduct,
      );
    }
    return of(this.drawerUiDataCache as Required<IModalData>);
  };

  // 向服务端修改数据
  private modifyDataToService = (params: IFormData, orginalData?: ITableData) => {
    return orginalData ? this.updateDataToService(params, orginalData!) : this.addDataToService(params);
  };

  // 更新数据
  private async updateDataToService(data: IFormData, orginalData: ITableData) {
    const modalUiData = this.modifyController.getModalUiData()!;
    const resp = await this.Model.updateUser(data, orginalData, this.appId, modalUiData.roleIdNameMap).toPromise();
    resp.success && toastApi.success('更新用户成功');
    return resp;
  }

  // 新建数据
  private async addDataToService(data: IFormData) {
    const modalUiData = this.modifyController.getModalUiData()!;
    const resp = await this.Model.createUser(data, this.appId, modalUiData.roleIdNameMap).toPromise();
    resp.success && toastApi.success('新建用户成功');
    return resp;
  }

  // 删除数据
  private deleteDataToService = async (data?: ITableData) => {
    const resp = await this.Model.deleteUser(data!, this.appId, this.userId).toPromise();
    resp.success && toastApi.success('删除用户成功');
    return resp;
  };

  // 渲染删除弹窗所需信息
  private initDeleteModalOptions = (): IEmotionProps => {
    const delData = this.deleteController.getModalRest();
    return {
      emotion: 'alert',
      title: `确认删除"${delData?.name}"用户`,
      description: '删除后，该用户将无法再登录使用本产品',
    };
  };

  // 初始请求
  private init() {
    this.tableController.listenFrontFilter();
    const cancelToken = this.getCancelToken();
    this.tableController.loadDataList(cancelToken);
  }
}

export { UserManageController };
