import { jsx, jsxs } from '@bpmn-io/properties-panel/preact/jsx-runtime';
import { useService } from 'bpmn-js-properties-panel';
import { addToolTip } from '../utils/tooltipUtil';

export function ButtonEntry(props: any) {
  const translate = useService('translate');
  const { onClick, children, tip = translate('config'), tipWidth } = props;

  return jsxs('div', {
    class: 'bio-properties-panel-button',
    children: [
      children,
      jsx('span', {
        class: 'bpmn-icon-service bio-properties-panel-button-icon',
        onClick: onClick,
        ...addToolTip({ tip, tipWidth }),
      }),
    ],
  });
}
