import { useController } from '@mdtBsComm/hooks/use-controller';
import { OneTableMissionCenterFormModelBff } from '@mdtProMicroModules/pages/one-table-mission-center-form';
import {
  MissionCenterForm,
  MissionCenterFormController,
} from '@mdtProMicroModules/pages-in-micro/one-table/pages/mission-center-form';

const PageMissionCenterForm = () => {
  const [controller] = useController(() => {
    const ctrl = new MissionCenterFormController({
      Model: OneTableMissionCenterFormModelBff,
    });
    return [ctrl, null];
  });

  return <MissionCenterForm controller={controller} />;
};

export default PageMissionCenterForm;
