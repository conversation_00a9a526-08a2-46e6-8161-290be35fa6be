import type { Dayjs } from '@mdtBsComm/utils/dayUtil';

export interface IFrontendRefreshTimer {
  refreshTimer?: string;
  nextRefreshTime?: Dayjs;
}

export interface IModelRefreshTimer {
  refreshTimer?: string | null;
  nextRefreshTime?: number | null;
  lastRefreshTime?: number | null;
}

export const enum RefreshTimerFrontendEnum {
  REFRESH_TIMER = 'refreshTimer',
  NEXT_REFRESH_TIME = 'nextRefreshTime',
  LAST_REFRESH_TIME = 'lastRefreshTime',
}
