import { isTextAreaEntryEdited, TextFieldEntry } from '@bpmn-io/properties-panel';
import { useService } from 'bpmn-js-properties-panel';
import { ButtonEntry } from '../entries/ButtonEntry';
import { getRelevantBusinessObject, isBpmnCallAcivity } from '../utils/ElementUtil';

export function CallActivityProps(props: any) {
  const { element } = props;

  if (!isBpmnCallAcivity(element)) {
    return [];
  }

  return [{ id: 'callActivity', component: CallActivityConfig, isEdited: isTextAreaEntryEdited }];
}

function CallActivityConfig(props: any) {
  const { element } = props;
  const debounce = useService('debounceInput');
  const translate = useService('translate');
  const mdtExternal = useService('mdtExternalProvider');
  const modeling = useService('modeling');

  const getValue = () => {
    const businessObject = getRelevantBusinessObject(element);
    return businessObject.$attrs.config;
  };

  const setValue = (value: any) => {
    modeling.updateProperties(element, {
      config: value,
    });
  };

  const onClick = (e: any) => {
    e.stopPropagation();
    e.preventDefault();
    mdtExternal.handleConfigCallActivity(getValue(), setValue);
  };

  const label = ButtonEntry({ children: translate('Setting'), onClick });

  return TextFieldEntry({
    element,
    id: 'callActivityInput',
    label,
    getValue,
    setValue,
    debounce,
    disabled: true,
  });
}
