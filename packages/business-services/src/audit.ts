import { queryAudit, queryAuditImpersonate } from '@mdtApis/api';
import { getTotalArgs } from './_util/totalUtil';
import {
  IAuditImpersonateQuery,
  IAuditImpersonateResponse,
  IAuditQuery,
  IAuditResponse,
  IRequestRequestConfig,
  IServerPaginationResponse,
} from './interfaces';

export const queryLogsAsync = async (query?: IAuditQuery, config?: IRequestRequestConfig) => {
  const cnf = { ...(config || {}), needMetadata: true };
  return queryAudit(query, cnf) as unknown as IServerPaginationResponse<IAuditResponse[]>;
};

export const queryLogsTotalAsync: typeof queryLogsAsync = (...args) => {
  return queryLogsAsync(...getTotalArgs(args, 1));
};

export const queryImpersonateLogsAsync = async (query?: IAuditImpersonateQuery, config?: IRequestRequestConfig) => {
  const cnf = { ...(config || {}), needMetadata: true };
  return queryAuditImpersonate(query, cnf) as unknown as IServerPaginationResponse<IAuditImpersonateResponse[]>;
};

export const queryImpersonateLogsTotalAsync: typeof queryImpersonateLogsAsync = (...args) => {
  return queryImpersonateLogsAsync(...getTotalArgs(args, 1));
};
