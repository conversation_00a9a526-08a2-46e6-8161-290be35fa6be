.micro_app_wrap,
#micro-root {
  width: 100%;
  height: 100%;
}

[data-theme='dark'] .micro-app {
  // stylelint-disable-next-line
  @import '@mdtProComm/components/bpmn/dark.less';
}

.micro_app_wrap {
  display: flex;
  flex-direction: column;

  .micro-app {
    z-index: 1;
    flex: 1;
    min-width: 0;
    min-height: 0;
    color: var(--dmc-text-color);
    background-color: var(--dmc-main-page-4);
  }
}

#micro-root {
  background: transparent;
}

// stylelint-disable-next-line
.micro-app micro-app-body {
  width: 100%;
  height: 100%;
}

::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: #0000004c;
  border-radius: 6px;
}

::-webkit-scrollbar-corner {
  background: transparent;
}

.app-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
}

#subapp-viewport {
  display: flex;
  flex: 1;
  min-width: 0;
  min-height: 0;
}
