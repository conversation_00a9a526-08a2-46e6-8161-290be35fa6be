/* eslint-disable sonarjs/no-duplicate-string */
/* eslint-disable @typescript-eslint/explicit-member-accessibility */
/**
 * @file Flex 常见布局 1:1 左右均分
 */
import i18n from '../../../languages';
import { FlexPluginBase } from '../FlexPluginBase';

export default class Layout1_1 extends FlexPluginBase {
  name = i18n.chain.jsonComp.leftRightEqually;
  isBaseComponent = false;
  pluginIcon = 'layout-2cols-plugin';
  description = i18n.chain.jsonComp.leftRightEquallyDesc;
  tags = [i18n.chain.jsonComp.commonLayout];
  order = 200;
  scaffold: any = {
    type: 'flex',
    className: 'p-1',
    items: [
      {
        type: 'wrapper',
        size: 'xs',
        body: [],
        style: {
          flex: '1 1 auto',
          flexBasis: 'auto',
          flexGrow: 1,
          display: 'block',
        },
      },
      {
        type: 'wrapper',
        size: 'xs',
        body: [],
        style: {
          flex: '1 1 auto',
          flexBasis: 'auto',
          flexGrow: 1,
          display: 'block',
        },
      },
    ],
    alignItems: 'stretch',
  };
}
