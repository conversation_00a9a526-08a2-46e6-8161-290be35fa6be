import { map } from 'rxjs/operators';
import {
  IBusinessResult,
  IEmotionProps,
  ModalWithBtnsCompEmotionController,
} from '@mdtBsComponents/modal-with-btns-comp-emotion';
import toastApi from '@mdtDesign/toast';
import Toggle from '@mdtDesign/toggle';
import i18n from '../../languages';
import { DialogModifyFormTimedTaskController, IFormData } from '../dialog-modify-form-timed-task';
import { IVirtualizedTableProps, TableCurdWithSimpleSearchController } from '../table-curd-with-simple-search';
import { ITableData, ITableTimedTaskModel } from './TableTimedTaskModel';

class TableTimedTaskController {
  private tableController: TableCurdWithSimpleSearchController<ITableData>;
  private deleteController: ModalWithBtnsCompEmotionController<ITableData>;
  private modifyController: DialogModifyFormTimedTaskController<ITableData>;
  private Model: ITableTimedTaskModel;

  public constructor(Model: ITableTimedTaskModel) {
    this.Model = Model;
    this.tableController = new TableCurdWithSimpleSearchController({
      dataListCompTableCurdControllerOptions: {
        dataListControllerOptions: {
          loadDataListFunc: () => this.Model.queryTimedTaskList().pipe(map((data) => [0, data])),
        },
        tableOptions: () => this.initTableProps(),
        curdOptions: () => this.Model.queryPermissions(),
        clickCreateBtnFunc: () => this.handleCreate(),
        clickEditBtnFunc: (item) => this.handleEdit(item),
        clickDeleteBtnFunc: (item) => this.handleDelete(item),
      },
      headerOptions: this.loadHeaderOptions(),
    });
    this.deleteController = new ModalWithBtnsCompEmotionController({
      clickOkBtnFunc: this.deleteConfirm,
      modalCompOptions: { modalOptions: this.initDeleteModalOptions },
    });
    this.modifyController = new DialogModifyFormTimedTaskController<ITableData>({
      modifyDataFunc: this.modifyDataToService,
    });
    this.init();
  }

  public destroy() {
    this.tableController.destroy();
    this.deleteController.destroy();
    this.modifyController.destroy();
    this.Model = null!;
  }

  public getTableController() {
    return this.tableController;
  }

  public getDeleteController() {
    return this.deleteController;
  }

  public getModifyController() {
    return this.modifyController;
  }

  private deleteConfirm = async (item?: ITableData) => {
    const resp = await this.Model.deleteTimedTask(item!).toPromise();
    if (resp.success) {
      toastApi.success(i18n.chain.proMicroModules.timeTask.delSuccess);
    }
    return resp;
  };

  // 创建数据
  private handleCreate = () => {
    return this.modifyController.openModal();
  };

  // 编辑数据
  private handleEdit(data?: ITableData) {
    return this.modifyController.openModal(data);
  }

  // 删除数据
  private handleDelete(data?: ITableData) {
    return this.deleteController.openModal(data);
  }

  private async handleEnable(data: ITableData) {
    const resp = await this.Model.enableTimedTask(data).toPromise();
    if (resp.success) {
      toastApi.success(i18n.chain.proMicroModules.timeTask.operatorSucces);
      this.tableController.editDataInList(resp.result!);
    }
  }

  // 渲染表头所需信息
  private initTableProps = (): IVirtualizedTableProps => {
    return {
      columns: [
        { name: i18n.chain.proMicroModules.timeTask.name, code: 'name', width: 100 },
        { name: i18n.chain.proMicroModules.timeTask.timingWay, code: 'triggerDisplay', width: 100 },
        { name: i18n.chain.proMicroModules.timeTask.expired, code: 'expired', width: 200 },
        { name: i18n.chain.proMicroModules.timeTask.nextRunTime, code: 'nextRunTime', width: 200 },
        {
          name: i18n.chain.proMicroModules.timeTask.avtive,
          code: 'active',
          width: 100,
          render: (active, record) => {
            return <Toggle checked={active} onChange={() => this.handleEnable(record)} />;
          },
        },
      ],
      type: 'page-bg',
      primaryKey: 'id',
      emptyContent: i18n.chain.comNoData,
      withVerticalBorder: false,
    };
  };

  private initDeleteModalOptions = (): IEmotionProps => {
    const delData = this.deleteController.getModalRest();
    return {
      emotion: 'alert',
      title: i18n.chain.proMicroModules.timeTask.delConfirm(delData?.name),
      description: i18n.chain.proMicroModules.timeTask.delComfirmDesc,
    };
  };

  private loadHeaderOptions() {
    return {
      createBtnLabel: i18n.chain.proMicroModules.timeTask.addTiming,
      title: i18n.chain.proMicroModules.timeTask.timingJob,
      hideInput: true,
    };
  }

  private modifyDataToService = (value: IFormData, originalData?: ITableData) => {
    return originalData ? this.update(value, originalData) : this.create(value);
  };

  private async update(value: IFormData, originalData: ITableData): Promise<IBusinessResult<ITableData>> {
    const resp = await this.Model.updateTimedTask(value, originalData).toPromise();
    if (resp.success) {
      toastApi.success(i18n.chain.proMicroModules.timeTask.updateTimingSuccess);
    }
    return resp;
  }

  private async create(value: IFormData): Promise<IBusinessResult<ITableData>> {
    const resp = await this.Model.createTimedTask(value).toPromise();
    if (resp.success) {
      toastApi.success(i18n.chain.proMicroModules.timeTask.createTimingSuccess);
    }
    return resp;
  }

  // 初始请求
  private init() {
    this.tableController.listenFrontFilter();
    this.tableController.loadDataList();
  }
}

export { TableTimedTaskController };
