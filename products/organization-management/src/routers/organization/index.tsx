import { useController } from '@mdtBsComm/hooks/use-controller';
import { UserModel } from '@mdtProMicroModules/models/UserModel';
import { AppController } from '../../app/AppController';
import { Organization, OrganizationController, OrganizationModel } from '../../pages/organization';

const OrganizationView = () => {
  const [controller] = useController(() => {
    const ctrl = new OrganizationController(AppController.getInstance(), OrganizationModel, UserModel);
    return [ctrl, null];
  });

  return <Organization controller={controller} />;
};

export default OrganizationView;
