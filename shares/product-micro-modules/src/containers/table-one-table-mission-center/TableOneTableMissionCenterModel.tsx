import { Observable } from 'rxjs';
import { IRequestCancelToken } from '@mdtApis/interfaces';
import { IReportInfoAttrs } from '../../utils/oneTableUtil';

export interface ITableData extends IReportInfoAttrs {
  id: string;
  startTime?: string;
  endTime?: string;
  specId: string;
  status?: string;
  deliverDepart?: number;
  assignee?: number;
  rootWorkflowId?: string;
  [key: string]: any;
}

export interface ITableOneTableMissionCenterModel {
  queryNextPage: (params: any, cancelToken?: IRequestCancelToken) => Observable<ITableData[]>;
  queryFirstPage: (params: any, cancelToken?: IRequestCancelToken) => Observable<[number, ITableData[]]>;
}
