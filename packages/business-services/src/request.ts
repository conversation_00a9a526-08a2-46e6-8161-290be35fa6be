import _ from 'lodash';
import axios, { type AxiosRequestConfig, AxiosInstance } from 'axios';
import getDmRc from '@datlas/dm-rc';
import { initRequestConfig } from '@mdtApis/request';
import type {
  IRequestOptions as IConfigRequestOptions,
  IRequestRequestConfig,
} from '@mdtBsControllers/request-config-controller';
import { RequestConfigController } from '@mdtBsControllers/request-config-controller';
import { IRequestError, IRequestResponse, IServerResponse } from './interfaces';

type IRequestOptions = IConfigRequestOptions & {
  // 遇到status是401的情况，如果处理
  deal401?: Function;
  notifyErrorMsg?: Function;
};

export type IRequestConfig<P = any> = IRequestRequestConfig<Omit<AxiosRequestConfig, 'params'>, P>;
export type { IRequestOptions };

export class RequestClass extends RequestConfigController {
  private request: AxiosInstance;
  private deal401?: Function;
  private notifyErrorMsg?: Function;
  private removeUse?: () => void;

  public constructor(options: IRequestOptions) {
    super(options);
    const ins = this.initIns();
    this.request = ins;
    // 如何处理401的情况
    this.deal401 = options.deal401;
    // 怎么通知用户api请求错误
    this.notifyErrorMsg = options.notifyErrorMsg;
    // 构造请求
    initRequestConfig(ins, this.requestSuccess, this.requestError);
  }

  public getIns() {
    return this.request;
  }

  public getDeal401() {
    return this.deal401;
  }

  public getNotifyErrorMsg() {
    return this.notifyErrorMsg;
  }

  public destroy() {
    super.destroy();
    this.request = undefined!;
    this.deal401 = undefined!;
    this.notifyErrorMsg = undefined!;
    initRequestConfig(null!, null!, null!);
    this.removeUse?.();
    this.removeUse = undefined;
  }

  public updateRequestToken = (token: string, impersonateToken?: string) => {
    this.request.defaults.headers.common = this.getHeaderRequestToken(
      this.request.defaults.headers.common,
      token,
      impersonateToken,
    );
  };

  public removeRequestToken = () => {
    this.request.defaults.headers.common = this.removeHeaderRequestToken(this.request.defaults.headers.common);
  };

  private requestSuccess = (resp: IRequestResponse): IServerResponse => {
    const { data = {} } = resp;
    if ((resp.config as IRequestConfig).skipResponseInterceptor === true) {
      return data;
    }
    // 有部分api直接返回了结果，不带rc
    let result = data;
    if (data.rc === 0 && data.result) {
      if ((resp.config as IRequestConfig).needMetadata === true) {
        result = {
          ...(data.metadata || { page_num: 1, page_size: 0, total_count: 0 }),
          dataResult: data.result,
        };
      } else {
        result = data.result;
      }
    }
    return { success: true, data: result };
  };

  private requestError = (error: IRequestError): IServerResponse => {
    if (axios.isCancel(error)) {
      return { success: false, canceled: true };
    }

    const isQuiet = (error.config as IRequestConfig).quiet === true;

    if (!error.response) {
      const msg = getDmRc({ status: 0 });
      return this.notifyMsg(isQuiet, msg, 0, error);
    }

    const { status, data } = error.response;

    if (status >= 401) {
      const deal401 = this.getDeal401();
      status === 401 && deal401?.();
      const msg = getDmRc({ status });
      return this.notifyMsg(isQuiet, msg, status, error);
    }

    // status = 400
    const { rc, msg_args, msg, message } = data || {};
    let dMsg = message || msg || '';
    dMsg && (dMsg = `(${dMsg})`);
    const displayMsg = getDmRc({ rc, msg_args, status }) + dMsg;
    return this.notifyMsg(isQuiet, displayMsg, rc, error);
  };

  private notifyMsg = (quiet: boolean, msg: string, rc: number, error: IRequestError): IServerResponse => {
    const notifyErrorMsg = this.getNotifyErrorMsg();
    !quiet && notifyErrorMsg?.(msg, error);
    return { success: false, msg: msg, rc };
  };

  private initIns = () => {
    const ins = axios.create();
    const { request, response } = ins.interceptors;
    // 加密api请求
    const req1 = request.use(this.encodeRequest);
    // api请求方法转换
    const req2 = request.use(this.transformRequestMethod);
    // 请求返回拦截处理
    const resp1 = response.use(
      (response) => {
        const { status, data, config } = response;
        if (
          (status >= 200 && status < 300) ||
          _.get(data, 'rc') === 0 ||
          (config as IRequestConfig).skipResponseInterceptor === true
        ) {
          return Promise.resolve(response);
        }
        return Promise.reject(response);
      },
      (error) => {
        return Promise.reject(error);
      },
    );
    // 设置baseUrl
    ins.defaults.baseURL = this.getBaseUrl();
    this.removeUse = () => {
      request.eject(req1);
      request.eject(req2);
      response.eject(resp1);
    };
    return ins;
  };
}

export const getRequestCancelToken = () => {
  return axios.CancelToken.source();
};

export const getNewRequest = () => {
  return axios.create();
};
