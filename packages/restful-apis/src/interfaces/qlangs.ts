import { IPaginationQuery, IRowValue } from './comm';

export interface IQlangsQuery extends IPaginationQuery {
  with_detail: boolean;
}

export interface IQlang {
  id: string;
  app_id?: string;
  create_time?: number;
  datapkgs?: string;
  dataset?: string;
  format_qlang?: object;
  geometry_type?: object;
  mdt_parser_version?: string;
  name?: string;
  origin_qlang?: string;
  parsed_variables?: object;
  permissions?: string[];
  pyparsing_version?: string;
  qlang_type?: string;
  revision?: string;
  timeout?: number;
  update_time?: number;
  user_id?: number;
  variables?: object;
  version?: string;
}

export interface IQlangResultTable {
  columns: string[];
  types: string[];
  values: IRowValue[][];
}

export type IQlangResult = IQlangResultTable;

export interface IQlangTempExecutionPost {
  timeout?: number;
  result_filters?: string;
  flags?: any;
  resource_filters?: any;
  result_type?: 'scalar' | 'dict' | 'record' | 'table' | 'table_with_binary';
  variables?: Record<string, any>;
  qlang: string;
}
