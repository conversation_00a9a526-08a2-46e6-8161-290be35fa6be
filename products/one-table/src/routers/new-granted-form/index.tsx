import { useController } from '@mdtBsComm/hooks/use-controller';
import {
  OneTableNewGrantedForm,
  OneTableNewGrantedFormController,
  OneTableNewGrantedFormModel,
} from '@mdtProMicroModules/pages/one-table-new-granted-form';
import { useOneTableNewOpenWorkItemIdentifier } from '@mdtProMicroModules/shared/useOpenWorkItemIdentifier';

const NewGrantedFormView = () => {
  const [controller] = useController(() => {
    return [
      new OneTableNewGrantedFormController({
        Model: OneTableNewGrantedFormModel,
      }),
      null,
    ];
  }, []);
  useOneTableNewOpenWorkItemIdentifier(controller.openWorkItemIdentifier);
  return <OneTableNewGrantedForm controller={controller} />;
};

export default NewGrantedFormView;
