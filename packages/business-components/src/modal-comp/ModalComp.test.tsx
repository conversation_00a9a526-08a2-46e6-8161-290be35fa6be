import _ from 'lodash';
import { act } from 'react-dom/test-utils';
import { render, screen } from '@testing-library/react';
import { ModalComp } from './ModalComp';
import { ModalCompController } from './ModalCompController';

const controllers: ModalCompController[] = [];
let ModalComController: ModalCompController;

export enum ModalTypeEnum {
  DRAWER = 'drawer',
  DIALOG = 'dialog',
  EMOTION = 'emotion',
}

const InnerViewMock = () => {
  return <div>InnerViewMock</div>;
};

describe('FormCompTest', () => {
  let IBeforeOpenFunc = () => {
    return new Promise((resolve) => {
      resolve({});
    });
  };
  describe('默认样式', () => {
    beforeAll(() => {
      ModalComController = new ModalCompController({
        modalType: ModalTypeEnum.DIALOG,
        modalOptions: {},
        InnerView: InnerViewMock,
      });
      controllers.push(ModalComController);
    });
    test('会话框', () => {
      render(<ModalComp controller={ModalComController} />);
      act(() => ModalComController.openModal({}));
      expect(screen.queryByRole(/dialog/)).toBeVisible();
    });
  });

  describe('loading', () => {
    beforeEach(() => {
      ModalComController = new ModalCompController({
        modalType: ModalTypeEnum.DIALOG,
        modalOptions: {},
        beforeOpenFunc: IBeforeOpenFunc,
        InnerView: InnerViewMock,
      });
      controllers.push(ModalComController);
    });

    it('LoadingModalData变为true', () => {
      render(<ModalComp controller={ModalComController} />);
      act(() => {
        ModalComController.openModal({});
      });
      expect(ModalComController.getLoadingModalData$().value).toEqual(true);
    });
  });
});

afterAll(() => {
  _.forEach(controllers, (it) => {
    it.destroy();
  });
  controllers.length = 0;
  // mockData.length = 0;
});
