import _ from 'lodash';
import { from, Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { ITicketModel, TICKET_FULL_DATA } from '@mdtBsBffServices/services';
import { formateDate } from '@mdtBsComm/utils/dayUtil';
import { ApprovalTicketModel, IQueryParams } from '@mdtProComm/models/ApprovalTicketModel';
import { ITableData } from '@mdtProMicroModules/containers/table-approval-record/TableApprovalRecordModel';
import { ticketBffService } from '../../bff-service';
import i18n from '../../languages';

const RESOLUTION_LABEL_MAP: Record<string, [string, string]> = {
  approved: [i18n.chain.proMicroModules.approval.approved, 'green-700'],
  rejected: [i18n.chain.proMicroModules.approval.rejected, 'red-700'],
  cancelled: [i18n.chain.proMicroModules.approval.cancelled, 'yellow-700'],
};
const PERMISSION_LABEL_MAP: Record<string, string> = {
  read: i18n.chain.proMicroModules.approval.use,
  view_detail: i18n.chain.proMicroModules.approval.search,
  download: i18n.chain.proMicroModules.approval.download,
};

export class ApprovalRecordModelBff extends ApprovalTicketModel {
  // 数据转换
  public static transformToTableData(item: ITicketModel): ITableData {
    const eDate = item.params.expire_ts;
    const expireDate = eDate
      ? formateDate(_.toNumber(eDate) * 1000)
      : i18n.chain.proMicroModules.datapkg.share.followOrgDate;
    return {
      id: item.ticket_id,
      name: item.title,
      initiator: item.initiatorUser?.name || '',
      createTime: item.createTimeDisplay,
      pkgId: item.resource_id,
      appName: item.initiatorApp?.name || '',
      // permission: item.permissionDisplay,
      permission: PERMISSION_LABEL_MAP[item.params.permission || ''] || i18n.chain.proMicroModules.approval.other,
      resolver: item.resolverUser?.name || '',
      // resolution: item.resolutionLabels,
      resolution: RESOLUTION_LABEL_MAP[item.resolution || ''] || [],
      expireDate,
    };
  }

  // 查询下一页数据
  public static queryNextPageTickets(queryParams: IQueryParams) {
    return from(ticketBffService.queryTicketList({ params: queryParams, respData: TICKET_FULL_DATA }, false)).pipe(
      map((resp) => {
        if (!resp.success) return [];
        return _.map(resp.page_data, this.transformToTableData);
      }),
    );
  }

  // 查询第一页数据
  public static queryFirstPageTickets(queryParams: IQueryParams): Observable<[number, ITableData[]]> {
    return from(ticketBffService.queryTicketList({ params: queryParams, respData: TICKET_FULL_DATA }, true)).pipe(
      map((resp) => {
        if (!resp.success) return [0, []];
        const data = _.map(resp.page_data, this.transformToTableData);
        return [resp.total_count, data] as [number, ITableData[]];
      }),
    );
  }
}
