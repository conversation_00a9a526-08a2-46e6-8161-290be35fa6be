import _ from 'lodash';
import { skip } from 'rxjs/operators';
import { RequestController } from '@mdtBsControllers/request-controller';
import { OwnershipEnum } from '@mdtProComm/constants';
import { IDatapkgsQuery, IRequestCancelToken } from '@mdtProComm/interfaces';
import {
  IDatapkgItem,
  ILoadDataListRslt,
  IPaginationParams,
  SearchTypeEnum,
  TableCardDatapkgSearchController,
} from '@mdtProMicroModules/containers/table-card-datapkg-search';
import {
  DatapkgDetailPreviewController,
  DrawerDatapkgDetailPreviewController,
} from '@mdtProMicroModules/pages/datapkg-detail-preview';
import { DatapkgDetailPreviewModel } from '@mdtProMicroModules/pages/datapkg-detail-preview/DatapkgDetailPreviewModel';
import { DatapkgDetailPreviewModelBff } from '@mdtProMicroModules/pages/datapkg-detail-preview/DatapkgDetailPreviewModelBff';
import { getModel } from '../../_util/modelUtil';
import { AppController } from '../../app/AppController';
import { IAllPkgSearchModel } from './AllPkgSearchModel';

export interface IControllerOptions {
  title: string;
  Model: IAllPkgSearchModel;
}

interface IFilterParams {
  cancelToken: IRequestCancelToken;
  params: IDatapkgsQuery;
}

class AllPkgSearchController extends RequestController {
  private readonly tableCardController: TableCardDatapkgSearchController;
  private readonly drawerController: DrawerDatapkgDetailPreviewController;
  private datapkgPreviewCache: Record<string, DatapkgDetailPreviewController> = {};
  private app?: AppController;
  private Model?: IAllPkgSearchModel;
  private apps = '';
  private themeIds: string[] = [];

  public constructor(options: IControllerOptions) {
    super();
    this.Model = options.Model;
    this.app = AppController.getInstance() as AppController;
    this.tableCardController = new TableCardDatapkgSearchController({
      dataListControllerOptions: {
        loadDataListFunc: this.queryFirstPageData,
        loadNextPageDataListFunc: this.queryNextPageData,
        getBackendFilterParams: this.getBackendFilterParams,
      },
      headerTitle: options.title,
      clickDatapkgItemFunc: this.clickDatapkgItem,
      Model: options.Model,
      enabledSearchTypes: [SearchTypeEnum.NAME, SearchTypeEnum.TAG, SearchTypeEnum.COLUMN],
    });
    this.drawerController = new DrawerDatapkgDetailPreviewController(this.app, {
      getPreviewControllerFunc: this.getDatapkgController,
    });
    this.init();
  }

  public destroy() {
    super.destroy();
    this.tableCardController.destroy();
    this.drawerController.destroy();
    _.forEach(this.datapkgPreviewCache, (it) => it.destroy());
    this.datapkgPreviewCache = {};
    this.app = undefined;
    this.Model = undefined;
  }

  public getTableCardController() {
    return this.tableCardController;
  }

  public getDrawerController() {
    return this.drawerController;
  }

  private clickDatapkgItem = (item: IDatapkgItem) => {
    this.drawerController.openModal(item.id);
  };

  private getDatapkgController = (pkgId: string) => {
    let cache = this.datapkgPreviewCache[pkgId];
    if (!cache) {
      cache = new DatapkgDetailPreviewController({
        pkgId,
        Model: getModel(DatapkgDetailPreviewModel, DatapkgDetailPreviewModelBff),
      });
    }
    this.datapkgPreviewCache[pkgId] = cache;
    return cache;
  };

  // 加载首页列表
  private queryFirstPageData = (filterParams: IFilterParams): ILoadDataListRslt<IDatapkgItem> => {
    return this.Model!.queryFirstPagePkgs(filterParams.params, { apps: this.apps }, filterParams.cancelToken);
  };

  // 加载下一页列表
  private queryNextPageData = (params: IPaginationParams) => {
    const filterParams = this.getBackendFilterParams();
    return this.Model!.queryNextPageTickets(
      filterParams.params,
      { ...params, apps: this.apps },
      filterParams.cancelToken,
    );
  };

  private getBackendFilterParams = (): IFilterParams => {
    const tcc = this.tableCardController;
    const params = tcc.defaultGetSearchParams();
    params.need_permissions = true;
    params.need_tags = true;
    params.status = 'approved';
    params.ownership = OwnershipEnum.APP;
    const pas = this.app!.getAlbumController().getPrefAlbumIds();
    this.app!.getPreferencesController().getHasSetPkgPref() && (params.album_id = pas);
    const cancelToken = this.getCancelToken();
    return { cancelToken, params };
  };

  // 初始请求
  private async init() {
    const appId = this.app!.getAppId();
    const grantedApppIds = this.app!.getGrantedAppController()!.getPrefGrantedAppIds();
    grantedApppIds.push(appId);
    this.apps = _.join(grantedApppIds, ',');
    this.themeIds = this.app!.getAlbumController()!.getPrefAlbumIds();
    // 监听toolFilter变化
    const tcc = this.tableCardController;
    tcc.defaultComputeConditionList();
    tcc.listenBackendFilter(tcc.getToolInput$(), tcc.getToolFilter$(), tcc.getToolDateRange$());
    this.listenSearchTypeChange();
    tcc.loadDataList(this.getBackendFilterParams());
  }

  private listenSearchTypeChange() {
    this.tableCardController
      .getSearchType$()
      .pipe(skip(1))
      .subscribe(() => {
        const filter = this.getBackendFilterParams();
        const { name, fuzzy_tag, column } = filter.params;
        if (name || fuzzy_tag || column) {
          this.tableCardController.loadDataList(filter);
        }
      });
  }
}

export { AllPkgSearchController };
