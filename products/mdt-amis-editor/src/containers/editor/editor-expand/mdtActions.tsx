import { drawerApi } from '@metroDesign/drawer';
import { SettingsForm } from './api-editor-setting';

interface IData {
  type: string;
  payload: any;
}

// 目前先支持qlang,后续可以扩展支持内部api
const showApi = (payload: any) => {
  const key = Math.random();
  drawerApi.open({
    placement: 'right',
    width: '350px',
    maskClosable: false,
    destroyOnClose: true,
    closable: false,
    okButtonProps: { style: { display: 'none' } },
    operatorsTarget: 'extra',
    className: 'drawer-api-editor',
    push: false,
    keyboard: false,
    children: (onClose: () => void) => <SettingsForm key={key} payload={payload} onClose={onClose} />,
  });
};

export const mdtActions = ({ type, payload }: IData) => {
  if (type === 'api') {
    showApi(payload);
  }
};
