import _ from 'lodash';
import { ReactNode } from 'react';
import {
  DataListCompTableCurdController,
  IControllerOptions as IDataListCompTableCurdControllerOptions,
  IDynamicOptions,
} from '@mdtBsComponents/data-list-comp-table-curd';

export interface IHeaderOptions {
  createBtnLabel?: string;
  inputPlaceholder?: string;
  hideInput?: boolean;
  title?: ReactNode;
  renderExtendOthers?: () => ReactNode;
  hideHeader?: boolean;
}

export interface IControllerOptions<V> {
  dataListCompTableCurdControllerOptions: IDataListCompTableCurdControllerOptions<V>;
  headerOptions: IDynamicOptions<IHeaderOptions>;
}

class TableCurdWithSimpleSearchController<V> extends DataListCompTableCurdController<V> {
  private headerOptions: IDynamicOptions<IHeaderOptions>;

  public constructor(options: IControllerOptions<V>) {
    super(options.dataListCompTableCurdControllerOptions);
    this.headerOptions = options.headerOptions;
  }

  public destroy() {
    super.destroy();
    this.headerOptions = null!;
  }

  public getHeaderOptions(): IHeaderOptions {
    return (_.isFunction(this.headerOptions) ? this.headerOptions() : this.headerOptions) || {};
  }

  // 复写最上层父类的方法
  public changeSingleFilter(value: string) {
    this.getSingleFilter$().next(_.trimStart(value));
  }
}

export { TableCurdWithSimpleSearchController };
