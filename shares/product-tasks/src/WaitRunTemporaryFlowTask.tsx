import _ from 'lodash';
import { BehaviorSubject } from 'rxjs';
import { getFlowRunResultAsync } from '@mdtBsServices/flows';
import type { ISocketTaskResp } from '@mdtBsServices/interfaces';
import { Button } from '@mdtDesign/button';
import notification from '@mdtDesign/notification';
import Progress from '@mdtDesign/progress';
import Tooltip from '@mdtDesign/tooltip';
import { TaskStatusEnum } from '@mdtProComm/constants/enum';
import { BaseTask, ITaskOptions } from './BaseTask';
import i18n from './languages';
import './index.less';

class WaitRunTemporaryFlowTask extends BaseTask {
  public readonly socketResp$ = new BehaviorSubject<ISocketTaskResp | null>(null);
  private flowRunId: string;
  private flowName: string;
  private quietProcess: boolean;

  public constructor(taskId: string, flowRunId: string, flowName: string, options?: ITaskOptions) {
    super(taskId, _.assign({ loop: true }, options));
    this.flowRunId = flowRunId;
    this.flowName = flowName;
    this.quietProcess = options?.quietProcess ?? false;
  }

  public destroy() {
    super.destroy();
    this.socketResp$?.complete();
  }

  protected taskFailed(task: ISocketTaskResp) {
    notification.error({
      message: `【${this.flowName}】${i18n.chain.proTasks.runFailed}`,
      description: (
        <div>
          <p>flowRunId: 【{this.flowRunId}】</p>
          <p>taskId: 【{this.taskId}】</p>
          <Tooltip title={task.message}>
            <p>{i18n.chain.proTasks.jobRunFailed(task.message)}</p>
          </Tooltip>
        </div>
      ),
      duration: 0,
      key: this.taskId,
    });
  }

  protected taskSuccess(task: ISocketTaskResp) {
    notification.success({
      message: `【${this.flowName}】${i18n.chain.proTasks.runCompleted}`,
      description: `【${this.flowName}】${i18n.chain.proTasks.jobRunSuccess}!`,
      duration: 5,
      key: task.task_id,
    });
  }

  protected dealTaskResp(resp: ISocketTaskResp) {
    this.socketResp$.next(resp);
    const status = resp.status;
    if (!this.isFinished && status === TaskStatusEnum.FAILED) {
      this.taskResp$.next(resp);
    } else if (!this.isFinished && status === TaskStatusEnum.SUCCESSFUL) {
      this.getPrviewResult(this.flowRunId);
    }
  }

  protected notifyProgress = (progress: number) => {
    if (this.isFinished || this.quietProcess) return;
    const resp = this.socketResp$.value;
    const close = () => {
      this.quietProcess = true;
      notification.close(this.taskId);
    };
    const btn = (
      <Button size="compact" onClick={close}>
        {i18n.chain.proTasks.putBackground}
      </Button>
    );
    notification.info({
      key: this.taskId,
      message: i18n.chain.proTasks.processTitle,
      duration: 0,
      onClose: () => {
        notification.close(this.taskId);
        this.quietProcess = true;
      },
      footer: btn,
      description: (
        <>
          <p className="percentage">{progress}%</p>
          <Progress percent={progress} />
          <p>{resp?.message}</p>
        </>
      ),
    });
  };

  private async getPrviewResult(flowRunId: string) {
    const resp = await getFlowRunResultAsync(flowRunId);
    if (resp.success) {
      if (resp.data!.status === TaskStatusEnum.FAILED) {
        this.taskResp$.next({
          task_id: this.taskId,
          status: TaskStatusEnum.FAILED,
          message: resp.data!.error,
        });
      } else {
        this.taskResp$.next({
          task_id: this.taskId,
          status: TaskStatusEnum.SUCCESSFUL,
          result: resp.data!,
        });
      }
    } else {
      this.taskResp$.next({
        task_id: this.taskId,
        status: TaskStatusEnum.FAILED,
        message: resp.msg,
      });
    }
  }
}

export { WaitRunTemporaryFlowTask };
