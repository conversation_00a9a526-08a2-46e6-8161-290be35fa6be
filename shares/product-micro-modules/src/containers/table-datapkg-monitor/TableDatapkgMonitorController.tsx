import _ from 'lodash';
import { FC } from 'react';
import { BehaviorSubject } from 'rxjs';
import { map } from 'rxjs/operators';
import { DataListCompTableCurdController } from '@mdtBsComponents/data-list-comp-table-curd';
import { ModalWithBtnsCompDrawerController } from '@mdtBsComponents/modal-with-btns-comp-drawer';
import { IEmotionProps, ModalWithBtnsCompEmotionController } from '@mdtBsComponents/modal-with-btns-comp-emotion';
import Tag from '@mdtDesign/tag';
import toastApi from '@mdtDesign/toast';
import { TaskStatusEnum } from '@mdtProComm/constants';
import i18n from '../../languages';
import { DialogModifyFormMonitorController, IFormData } from '../dialog-modify-form-monitor';
import { IVirtualizedTableProps } from '../table-curd-with-simple-search';
import { TableTimedTask, TableTimedTaskController } from '../table-timed-task';
import { ILatestMonitor, ITableData, ITableDatapkgMonitorModel } from './TableDatapkgMonitorModel';

class TableDatapkgMonitorController extends DataListCompTableCurdController<ITableData> {
  private deleteController: ModalWithBtnsCompEmotionController<ITableData>;
  private drawerTimedTaskController: ModalWithBtnsCompDrawerController<void, void>;
  private timedTaskController?: TableTimedTaskController;
  private modifyController;
  private Model: ITableDatapkgMonitorModel;
  private checklog$ = new BehaviorSubject<ILatestMonitor>(['', 'success', '']);
  private isRunning$ = new BehaviorSubject(false);
  private readonly enableEdit: boolean;

  public constructor(enableEdit: boolean, Model: ITableDatapkgMonitorModel) {
    super({
      dataListControllerOptions: {
        loadDataListFunc: () => this.Model.queryMonitorList().pipe(map((data) => [0, data])),
      },
      curdOptions: {
        enableCreate: enableEdit,
        enableEdit: enableEdit,
        enableDelete: enableEdit,
      },
      tableOptions: () => this.getTableOptions(),
      clickCreateBtnFunc: () => this.modifyController.openModal(),
      clickEditBtnFunc: (item) => this.modifyController.openModal(item),
      clickDeleteBtnFunc: (item) => this.deleteController.openModal(item),
    });

    this.Model = Model;
    this.enableEdit = enableEdit;
    this.deleteController = new ModalWithBtnsCompEmotionController<ITableData>({
      clickOkBtnFunc: this.handleDeleteConfirm,
      modalCompOptions: { modalOptions: this.initDeleteModalOptions },
    });
    this.modifyController = new DialogModifyFormMonitorController<ITableData>({
      modifyDataFunc: this.modifyDataToService,
      loadUiDataFunc: this.loadModifyUiData,
    });
    this.drawerTimedTaskController = new ModalWithBtnsCompDrawerController({
      modalCompOptions: {
        modalOptions: {
          width: '700px',
          level: null,
          className: 'drawer-timed-task',
        },
        InnerView: TableTimedTask as FC,
        innerViewController: () => this.getTimedTaskController(),
      },
      uiOptions: { title: i18n.chain.proMicroModules.monitor.timingJob, customerHeader: null },
    });

    this.init();
  }

  public init() {
    this.loadDataList();
    this.Model.queryLatestMonitor().subscribe((result) => {
      result && this.checklog$.next(result);
    });
  }

  public getEnableEdit() {
    return this.enableEdit;
  }

  public getChecklog$() {
    return this.checklog$;
  }

  public getIsRunning$() {
    return this.isRunning$;
  }

  public getDeleteController() {
    return this.deleteController;
  }

  public getModifyController() {
    return this.modifyController;
  }

  public getDrawerTimedTaskController() {
    return this.drawerTimedTaskController;
  }

  public destroy() {
    this.deleteController.destroy();
    this.modifyController.destroy();
    this.drawerTimedTaskController.destroy();
    this.timedTaskController?.destroy();
    this.timedTaskController = undefined;
    this.checklog$.complete();
    this.isRunning$.complete();
    this.Model = null!;
  }

  // 运行质量监控
  public handleRun = async () => {
    this.isRunning$.next(true);
    const ids = _.map(this.getDataListValue(), ({ id }) => id);
    this.Model.runMonitor(ids).subscribe((status) => {
      TaskStatusEnum.SUCCESSFUL === status && this.init();
      (status === TaskStatusEnum.SUCCESSFUL || status === TaskStatusEnum.FAILED) && this.isRunning$.next(false);
    });
  };

  public handleTimedTask = () => {
    this.drawerTimedTaskController.openModal();
  };

  private getTableOptions = (): IVirtualizedTableProps => {
    return {
      columns: [
        { code: 'name', name: i18n.chain.proMicroModules.monitor.name, width: 200 },
        { code: 'monitorTypeDisplay', name: i18n.chain.proMicroModules.monitor.type, width: 120 },
        { code: 'conditionDisplay', name: i18n.chain.proMicroModules.monitor.condition },
        {
          code: 'result',
          name: i18n.chain.proMicroModules.monitor.result,
          width: 100,
          render: (val) => {
            return val ? <Tag tag={val[0]} color={val[1]} /> : '-';
          },
        },
      ],
      type: 'page-bg',
      primaryKey: 'id',

      withVerticalBorder: false,
    };
  };

  private handleDeleteConfirm = async (data?: ITableData) => {
    const resp = await this.Model.deleteMonitor(data!).toPromise();
    resp && toastApi.success(i18n.chain.proMicroModules.monitor.delMonitorSuccess);
    return { success: !!resp, result: data };
  };

  private initDeleteModalOptions = (): IEmotionProps => {
    const delData = this.deleteController.getModalRest();
    return {
      emotion: 'alert',
      title: i18n.chain.proMicroModules.monitor.delMonitorTitle(delData?.name),
      description: i18n.chain.proMicroModules.monitor.delMonitorDesc,
    };
  };

  private modifyDataToService = (values: IFormData, originalData?: ITableData) => {
    if (originalData) {
      return this.Model.updateMonitor(values, originalData).toPromise();
    }
    return this.Model.createMonitor(values).toPromise();
  };

  private loadModifyUiData = async () => {
    return await this.Model.queryModifyUiData().toPromise();
  };

  private getTimedTaskController(): any {
    if (!this.timedTaskController) {
      this.timedTaskController = new TableTimedTaskController(this.Model.getTableTimedTaskModel());
    }
    return this.timedTaskController;
  }
}

export { TableDatapkgMonitorController };
