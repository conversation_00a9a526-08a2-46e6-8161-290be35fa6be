# 产品身份认证中心

## 产品前端单点登录设计

> 请引入模块前，跟 `leader` 确认下是否有必要(处于轻量考虑)

![sso](.md/sso.png 'sso')

| 参数名称      | 参数值        | 参考示例                         | 说明                                                                              |
| ------------- | ------------- | -------------------------------- | --------------------------------------------------------------------------------- |
| `redirect`    | 非必须        | `redirect=https://www.datlas.cn` | 认证成功后跳转地址                                                                |
| `force`       | 非必须 'true' | 'false'(默认 false)              | 强制登录                                                                          |
| `config_path` | 非必须        | `config_path=config.js`          | 自定义配置项地址                                                                  |
| `type`        | 非必须        | `type=dialog`                    | 目前支持弹窗类型，后续可追加，登录状态以 possmessage 形式返回(key = dialog-login) |
| `sso_out`     | 非必须        | `sso_out=false`                  | 强制不对外跳转 sso，只使用本 sso 登录                                             |

```markdown
Q: 为什么需要 force 参数
A: 有可能产品退出时，未调用 logout API 或者 其他原因导致 logout API 调用失败，导致 SSO 的 token 不失效，又重复跳转至产品。强烈推荐退出跳转至 SSO 时带上 force

Q: config_path 参数何时使用
A: 当你的产品需要自定义 sso 的配置项的时候使用，在你的目标目录下增加一个 JSON 文件，把当前的文件地址通过 config_path 传递过来，即可使用您的自定义配置；需要注意的是，配置项需要和 sso 的配置项类型保持同步。(sso 配置项参考)[https://gitlab.idatatlas.com/new-datamap/frontend-config/-/blob/main/config/pri/sso/README.md]

Q: sso_out 参数何时使用
A: 当脉策全家桶用户对接私有化系统的时候，并没有私有化系统的账号，只能用全家桶 SSO 登录，可带入参数`sso_out=false`，如果想改变当前不跳转的行为，使用`sso_out=true`。
```

## 定制化登录

> 目前支持通过`url参数`定制及`config.js`来定制; 假如`url参数`与`config.js`相同功能的参数冲突，以`url参数`为准

### url 参数

```markdown
样例 https://www.xxx.com/?language=cn&tab=phone
```

| 参数名称   | 参数值 | 参考示例      | 说明                                                                                                   |
| ---------- | ------ | ------------- | ------------------------------------------------------------------------------------------------------ |
| `language` | 非必须 | `language=cn` | 可选值：`cn, en`                                                                                       |
| `theme`    | 非必须 | `theme=light` | 可选值：`light`                                                                                        |
| `tab`      | 非必须 | `tab=account` | 可选值（包含默认配置和自定义 tab）：`ConfigTabEnum`(`phone` `account` `wechat` `dingtalk`) \| `string` |

### config.js 来定制

> 后续可根据需求追加参数

```markdown
window.**DM_SSO_CFS.deployWindowTitle="脉策 SSO";
window.**DM_SSO_CFS.deployWindowDesc="脉策产品统一认证中心";
```

| 参数名称                                  | 参数值                                           | 参考示例                  | 说明                                                                                                                                   |
| ----------------------------------------- | ------------------------------------------------ | ------------------------- | -------------------------------------------------------------------------------------------------------------------------------------- |
| `__DM_SSO_CFS.deployWindowTitle`          | 非必须                                           | `脉策SSO`                 | 窗口的标题                                                                                                                             |
| `__DM_SSO_CFS.deployWindowTitle`          | 非必须                                           | `脉策SSO`                 | 窗口的标题                                                                                                                             |
| `__DM_SSO_CFS.deployWindowDesc`           | 非必须，默认`__DM_SSO_CFS.deployWindowTitle`的值 | `脉策产品统一认证中心`    | 网页的描述                                                                                                                             |
| `__DM_SSO_CFS.deployEnableHashRouter`     | 非必须                                           | `false`                   | 是否开启 hash 路由                                                                                                                     |
| `__DM_SSO_CFS.backendApiUrl`              | 非必须                                           | `https://www.xx.cn`       | url 接口请求域名                                                                                                                       |
| `__DM_SSO_CFS.backendRedirectUrl`         | 非必须                                           | `https://www.xx.cn`       | url 扫码回调重定向地址                                                                                                                 |
| `__DM_SSO_CFS.productLanguage`            | 非必须                                           | 参考`url参数`的`language` | 设置语言                                                                                                                               |
| `__DM_SSO_CFS.productTab`                 | 非必须                                           | 参考`url参数`的`tab`      | 设置默认登录项                                                                                                                         |
| `__DM_SSO_CFS.productTabs`                | 非必须，登录项配置                               | `['phone', 'wechat']`     | 最多设置 4 项`['phone', 'wechat', 'dingtalk', 'account']`                                                                              |
| `__DM_SSO_CFS.productLogo`                | 非必须，定制化登录页面 Logo                      | `https://www.xx.png`      | url 及 base64 都行                                                                                                                     |
| `__DM_SSO_CFS.productDingtalkId`          | 非必须                                           | `xxxxxxxxxxxx`            | 钉钉应用 id                                                                                                                            |
| `__DM_SSO_CFS.productWechatId`            | 非必须                                           | `xxxxxxxxxxxx`            | 微信应用 id                                                                                                                            |
| `__DM_SSO_CFS.productRedirect`            | 非必须                                           | `https://www.xx.cn`       | 配置项认证成功后跳转地址                                                                                                               |
| `__DM_SSO_CFS.productLogo`                | 非必须                                           | `https://xxxx.png`        | 产品 Logo                                                                                                                              |
| \*新增`__DM_SSO_CFS.productTitle`         | 非必须                                           | `登录`                    | 产品标题                                                                                                                               |
| \*新增`__DM_SSO_CFS.productDesc`          | 非必须                                           | `欢迎登录您的脉策产品`    | 产品副标题                                                                                                                             |
| \*新增 `__DM_SSO_CFS.productPrivacy`      | 非必须                                           | `true`                    | `false` 或者 { visible: `true`, check: `false`, privacys: [{name: '协议 1', link: 'https://baidu.com'}] } 是否显示隐私或者传入隐私配置 |
| \*新增 `__DM_SSO_CFS.productFooter`       | 非必须                                           | `true`                    | `false` 或者 { visible: `false`, text: '1', link: 'https://baidu.com' } 是否显示 footer 或者传入 footer 自定义                         |
| \*新增 `__DM_SSO_CFS.productVerification` | 非必须                                           | `true` \| `IVerifyProp`   | `false` 或者 `滑动安全认证的配置项`                                                                                                    |

### 通过 SDK 来定制化

> 上述 2 种定制化只支持有限的功能定制，通过 SDK 你可以定制任何你想要的功能

#### 安装 npm 包

```shell
yarn add mdt-login
或者
npm install mdt-login
```

#### 代码中引入 lib，并初始化

```tsx
import { initLoginConfig, LoginPage, VerifyPage } from 'mdt-login';
// 初始化配置
initLoginConfig(Options);
// 然后在需要的地方使用组件
```

#### Options 参考

```tsx
export interface ILabelValue {
  label: string;
  value: string;
  /** 自定义render */
  render?: (value: string) => any;
  /** 需要使用的类型，用于自定义tab的时候快速构造想要选择的类型 */
  type?: ConfigTabEnum;
  /** 对已经选择使用的type模块，属性覆盖 */
  overrideConfig?: (value: string, type?: ConfigTabEnum) => any;
}

export interface IBg {
  isVideo: boolean;
  color: string;
  url: string;
}

export interface IGridConfig {
  status?: 'on' | 'off';
  position?: 'left' | 'right';
  url?: string;
  color?: string;
  isVideo?: boolean;
}

export interface ILanguageState {
  language: string;
  visible: boolean;
  [key: string]: any;
}

export interface ILanguage {
  disable?: boolean;
  supportLanguages: ILabelValue[];
  initialState: () => ILanguageState;
  changeLanguage: Function;
}

export interface IFooter {
  renderFooter: Function;
}

export interface ITabs {
  defaultItem: string;
  items: ILabelValue[];
  renderExtra?: Function;
}

export interface IAccountState {
  name: string;
  nameError: string;
  password: string;
  passwordError: string;
  buttonLoading: boolean;
  buttonDisabled?: boolean;
  verification?:
    | boolean
    | (Omit<IVertifyProp, 'onSuccess' | 'onFail' | 'visible'> & {
        onSuccess?: (close: any) => void;
        onFail?: (close: any) => void;
      });
  [key: string]: any;
}

export interface IAccount {
  nameLabel: string;
  namePlaceholder: string;
  passwordLabel: string;
  passwordPlaceholder: string;
  buttonLabel: string;
  buttonLoadingLabel: string;
  showPasswordForget?: boolean;
  passwordForgetLabel?: string;
  passwordForgetClick?: Function;
  initialState: () => IAccountState;
  verifyName: Function;
  verifyPassword: Function;
  verifyAll: Function;
  loginIn: Function;
  renderExtraFormItem?: Function;
  renderExtraButton?: Function;
}

export interface IPhoneState {
  phone: string;
  phoneError: string;
  captcha: string;
  captchaError: string;
  selectISO: string;
  code: string;
  captchaDisabled: boolean;
  buttonLoading: boolean;
  buttonDisabled?: boolean;
  [key: string]: any;
}

export interface IPasswordForgetState {
  account?: string;
  accountError?: string;
  captcha?: string;
  captchaError?: string;
  captchaDisabled?: boolean;
  password?: string;
  passwordError?: string;
  passwordConfirm?: string;
  passwordConfirmError?: string;
  buttonLoading: boolean;
  buttonDisabled?: boolean;
  buttonLabel?: string;
  buttonLoadingLabel?: string;
  [key: string]: any;
}

export interface IPhone {
  phoneLabel: string;
  phonePlaceholder: string;
  captchaLabel: string;
  captchaPlaceholder: string;
  captchaInterval: number;
  captchaButtonLabel: string;
  captchaButtonSendedLabel: Function;
  buttonLabel: string;
  buttonLoadingLabel: string;
  supportCodes: { name: string; iso: string; code: string }[];
  initialState: () => IPhoneState;
  verifyPhone: Function;
  verifyCaptcha: Function;
  verifyAll: Function;
  sendCaptcha: Function;
  loginIn: Function;
  renderExtraFormItem?: Function;
  renderExtraButton?: Function;
  verification?:
    | boolean
    | (Omit<IVertifyProp, 'onSuccess' | 'onFail' | 'visible'> & {
        onSuccess?: (close: any) => void;
        onFail?: (close: any) => void;
      });
}

export interface IWechatState {
  state: string;
  [key: string]: any;
}

export interface IWechat {
  successContent: string;
  failContent: string;
  statusContent: string;
  tipColor: string;
  privacyDesc?: string;
  flashInterval: number;
  initialState: () => IWechatState;
  getState: Function;
  getIframeSrc: Function;
  renderExtra?: Function;
  selfRedirect?: boolean;
  afterFailed?: Function;
  afterSuccess?: Function;
}

export interface IDingtalkState {
  state: string;
  [key: string]: any;
}

export interface IDingtalk {
  flashInterval: number;
  initialState: () => IDingtalkState;
  privacyDesc?: string;
  getState: Function;
  splicingGoto: Function;
  renderExtra?: Function;
  selfRedirect?: boolean;
  afterFailed?: Function;
  afterSuccess?: Function;
}

export interface IVerify {
  renderPage: Function;
}

export interface IPasswordForget {
  title?: any;
  steps: 'check' | 'confirm';
  accountLabel: string;
  accountPlaceholder: string;
  captchaLabel: string;
  captchaPlaceholder: string;
  captchaInterval: number;
  captchaButtonLabel: string;
  captchaButtonSendedLabel: Function;
  passwordLabel: string;
  passwordPlaceholder: string;
  passwordConfirmLabel: string;
  passwordConfirmPlaceholder: string;
  initialState: () => IPasswordForgetState;
  verifyAccount: Function;
  verifyCaptcha: Function;
  verifyPassword: Function;
  verifyPasswordConfirm: Function;
  verifyAll: Function;
  sendCaptcha: Function;
  onSubmit: Function;
  renderExtraFormItem?: Function;
  gobackClick?: Function;
}

export interface IPrivacy {
  visible: boolean;
  disable?: boolean;
  check: boolean;
  onChange?: (check: boolean) => void;
  privacyText?: string;
  privacyslotText?: string;
  privacys?: { name: string; link: string }[];
}
export interface IConfig {
  title?: any;
  logo?: string;
  bg: IBg;
  gridConfig?: IGridConfig;
  privacy: IPrivacy;
  theme: Record<string, string>;
  language: ILanguage;
  footer: IFooter;
  tabs: ITabs;
  account?: IAccount;
  phone?: IPhone;
  wechat?: IWechat;
  dingtalk?: IDingtalk;
  verify?: IVerify;
  passwordForget?: IPasswordForget;
  willMount?: (loading: boolean, setLoading: Function) => Function;
}

export enum ConfigTabEnum {
  ACCOUNT = 'account',
  PHONE = 'phone',
  WECHAT = 'wechat',
  DINGTALK = 'dingtalk',
}

export enum ConfigKeyEnum {
  LOGO = 'logo',
  TITLE = 'title',
  PRIVACY = 'privacy',
  BG = 'bg',
  GRID_CONFIG = 'gridConfig',
  THEME = 'theme',
  LANGUAGE = 'language',
  FOOTER = 'footer',
  TABS = 'tabs',
  ACCOUNT = 'account',
  PHONE = 'phone',
  WECHAT = 'wechat',
  DINGTALK = 'dingtalk',
  VERIFY = 'verify',
  PASSWORD_FORGET = 'passwordForget',
  WILLMOUNT = 'willMount',
}

let loginConfig: IConfig | null;

export const initLoginConfig = (config: any) => {
  loginConfig = config;
};

type IKey = keyof IConfig;

const getConfigByKey = (key: IKey, forceReturn?: Boolean): IConfig[IKey] => {
  if (!loginConfig || (!forceReturn && !loginConfig[key])) {
    throw new Error('使用前，需要先初始化相应配置');
  }
  return loginConfig[key];
};

export const useConfig = (key: keyof IConfig, forceReturn?: Boolean): IConfig[IKey] => {
  return getConfigByKey(key, forceReturn);
};

export const useReleaseConfig = () => {
  loginConfig = null;
};
```

### 个性化定制主题

> 目前支持以下变量定制

```markdown
--mdt-login-text-color: #a3aac2; // 5
--mdt-login-text-dim-color: #727d9b; // 7
--mdt-login-text-highlight-color: #f4f6fc; // 2
--mdt-login-primary-color: #4285f4; // 5
--mdt-login-primary-dim-color: #2b4997; // 8
--mdt-login-primary-highlight-color: #3b6fd5; // 6
--mdt-login-error-color: #f85960; // 5
--mdt-login-success-color: #22cc89; // 5
--mdt-login-bg-color: #1e2130;
--mdt-login-border-color: #dcdee5;
--mdt-login-tab-text-color: var(--mdt-login-text-dim-color);
--mdt-login-tab-text-hover-color: var(--mdt-login-text-color);
--mdt-login-tab-text-actived-color: var(--mdt-login-primary-color);
--mdt-login-tab-border-color: var(--mdt-login-text-dim-color);
--mdt-login-tab-border-actived-color: var(--mdt-login-primary-color);
--mdt-login-form-label-color: var(--mdt-login-text-color);
--mdt-login-form-msg-color: var(--mdt-login-error-color);
--mdt-login-input-tip-color: var(--mdt-login-text-color);
--mdt-login-input-color: var(--mdt-login-text-highlight-color);
--mdt-login-input-bg-color: #32384e33;
--mdt-login-input-bg-hover-color: #32384e59;
--mdt-login-input-border-color: #333b52;
--mdt-login-input-focused-border-color: var(--mdt-login-primary-color);
--mdt-login-input-error-border-color: var(--mdt-login-error-color);
--mdt-login-btn-bg-color: var(--mdt-login-primary-highlight-color);
--mdt-login-btn-bg-hover-color: var(--mdt-login-primary-color);
--mdt-login-btn-bg-diasbled-color: #2b4997;
--mdt-login-btn-text-color: #fff;
--mdt-login-btn-text-disabled-color: #fff9;
--mdt-login-link-color: var(--mdt-login-btn-bg-color);
--mdt-login-link-hover-color: var(--mdt-login-btn-bg-hover-color);
--mdt-login-link-diasbled-color: var(--mdt-login-btn-bg-diasbled-color);
--mdt-login-overlay-border-color: #4a5472;
--mdt-login-overlay-bg-color: #343c54;
--mdt-login-overlay-text-color: #f4f6fc;
--mdt-login-overlay-hover-item-bg-color: #3f4863;
--mdt-login-overlay-hover-item-text-color: #f4f6fc;
--mdt-login-overlay-scrollbar-track-color: transparent;
--mdt-login-overlay-scrollbar-thumb-color: #3f4863;
--mdt-login-toast-shadow-color: 0 0 1px #00000051, 0 0 2px #0000007a, 0 4px 8px #00000051;
--mdt-login-toast-text-color: #fff;
--mdt-login-toast-text-warning-color: #141419;
--mdt-login-toast-bg-color: #335bb6;
--mdt-login-toast-bg-success-color: #008d64;
--mdt-login-toast-bg-error-color: #b33a35;
--mdt-login-toast-bg-warning-color: #ffbf00;
--mdt-login-dialog-mask-color: #000c;
--mdt-login-dialog-content-bg-color: #282d40;
--mdt-login-dialog-content-border-color: #141419;
--mdt-login-check-check-color: #5096ff;
--mdt-login-check-uncheck-inner-color: #39425b;
--mdt-login-check-uncheck-out-color: #4a5472;
--mdt-login-check-check-hover-color: #63a7ff;
--mdt-login-check-uncheck-inner-hover-color: #444e6a;
--mdt-login-check-uncheck-out-hover-color: #5a6585;
--mdt-login-check-check-disabled-color: #2b4997;
--mdt-login-check-uncheck-inner-disabled-color: #333b52;
--mdt-login-check-uncheck-out-disabled-color: #39425b;
--mdt-login-check-title-color: #d4d9eb;
--mdt-login-check-sub-title-color: #8b94ad;
--mdt-login-check-title-disabled-color: #727d9b;
--mdt-login-check-sub-title-disabled-color: #4c556e;
```
