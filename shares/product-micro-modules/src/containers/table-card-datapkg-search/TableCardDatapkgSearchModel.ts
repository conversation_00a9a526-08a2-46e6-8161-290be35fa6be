import _ from 'lodash';
import { joinKey } from '@mdtBsComm/utils/stringUtil';
import { MenuItemProps } from '@mdtDesign/dropdown';
import {
  getDatapkgGeometryTypeLabel,
  getDatapkgPermissionLabel,
  getDatapkgPermissionNormalKeys,
  getSingleGeometryType,
} from '@mdtProComm/utils/datapkgUtil';
import i18n from '../../languages';

export interface IDatapkgItem {
  id: string;
  name: string;
  geoTypeDisplay: string;
  permissonLabels: string[];
  geoTypeIcon: string;
  geoTypeColor: string;
  updateTimeDisplay: string;
  count: string;
  source: string;
  appName: string;
  [key: string]: any;
}
export enum ConditionGroupEnum {
  PERMISSION = 'permission',
  TYPE = 'type',
  TAG = 'tag',
}
export const MULTIPLE_KEY = 'multiple';

const filterGroupLabelMap: () => Record<string, string> = () => ({
  [ConditionGroupEnum.PERMISSION]: i18n.chain.proMicroModules.dataSearch.dataPermission,
  [ConditionGroupEnum.TYPE]: i18n.chain.proMicroModules.dataSearch.dataGeoType,
  [ConditionGroupEnum.TAG]: i18n.chain.proMicroModules.datapkgDetail.tags,
});

export const getDefaultMenus = (): MenuItemProps[] => {
  return [
    {
      type: 'group',
      key: ConditionGroupEnum.PERMISSION,
      title: filterGroupLabelMap()[ConditionGroupEnum.PERMISSION],
      children: _.map(getDatapkgPermissionNormalKeys(), (it) => ({
        title: getDatapkgPermissionLabel(it),
        key: joinKey(ConditionGroupEnum.PERMISSION, it),
      })),
      divider: true,
    },
    {
      type: 'group',
      key: ConditionGroupEnum.TYPE,
      title: filterGroupLabelMap()[ConditionGroupEnum.TYPE],
      children: [
        ..._.map(getSingleGeometryType(), (it) => ({
          title: getDatapkgGeometryTypeLabel(it),
          key: joinKey(ConditionGroupEnum.TYPE, it),
        })),
        {
          title: i18n.chain.proMicroModules.dataSearch.mutiGeoType,
          key: joinKey(ConditionGroupEnum.TYPE, MULTIPLE_KEY),
        },
      ],
    },
  ];
};

export class TableCardDatapkgSearchModel {
  public static getFilterGroupLabel(group: string) {
    return filterGroupLabelMap()[group];
  }
}

export type ITableCardDatapkgSearchModel = typeof TableCardDatapkgSearchModel;
