.low-code-etl-home {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  padding: 0 20px;

  .table-curd-search_create-btn {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;

    .dmc-btn-bg {
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
    }
  }

  .dm-flow-template_upload {
    padding: 0 6px;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;

    .dmc-btn-bg {
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }
  }

  .edit-icon {
    margin: 0 8px;
  }
}

.more-btn-drop-menu {
  display: flex;
  flex-direction: column;
  width: 160px;
  color: var(--dmc-menu-text-color);
  background-color: var(--dmc-menu-bg-color);
  border: 1px solid var(--dmc-menu-border-color);
  border-radius: 4px;
  box-shadow: var(--dmc-shadow-1);

  .drop-menu-item {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    width: 100%;
    height: 36px;
    padding-left: 16px;
    font-weight: 500;
    font-size: 14px;
    font-style: normal;
    line-height: 20px;
    cursor: pointer;
  }

  .drop-menu-item:hover {
    background-color: var(--dmc-menu-hover-color);
  }

  .split-line {
    width: 100%;
    height: 2px;
    background-color: var(--dmc-menu-border-color);
  }

  .icon {
    width: 24px;
    height: 24px;
    padding: 6px;
  }

  .item-warning {
    color: var(--dmc-menu-danger-color);
  }
}

.drawer-flow-view {
  .rc-menu-item {
    color: var(--dmc-menu-text-color);
  }

  .flow-view-cancel-btn {
    display: none;
  }

  // 编辑FLOW按钮
  .drawer-flow-view-edit-flow-btn {
    position: absolute;
    top: 10px;
    right: 90px;
  }

  .drawer-flow-view-inner {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;

    &-table {
      display: flex;
      flex-direction: column;
      width: 50%;
      height: 100%;
      background-color: var(--dmc-primary-panel-2);

      .table-menu {
        position: absolute;
        margin-top: 5px;
        margin-left: 20px;

        &-header {
          margin-top: 0;
          background-color: var(--dmc-primary-panel-2);
          border-bottom: none;

          .rc-menu-item {
            margin: auto 2px;
            padding-bottom: 10px;
            font-weight: 500;
            font-size: 13px;
            cursor: pointer;
          }

          .rc-menu-item:hover:not(.rc-menu-item-selected) {
            color: var(--dmc-blue-600-color);
            background-color: var(--dmc-primary-panel-2);
            border-color: transparent;
          }

          .rc-menu-item-selected {
            color: var(--dmc-blue-600-color);
            background-color: var(--dmc-primary-panel-2);
            border-color: var(--dmc-blue-600-color);
          }
        }
      }

      .modules_table-curd-with-simple-search {
        .table-curd-search_header {
          display: flex;
          justify-content: flex-end;
          height: 52px;
          padding: 0;
          border: 1px solid var(--dmc-split-page-color);
        }

        .table-curd-search_title {
          display: none;
        }

        .table-curd-search_create-btn {
          .dmc-btn-container {
            color: var(--dmc-text-8);
          }

          .dmc-btn-bg {
            background: transparent !important;
            border: none;
          }
        }
      }
    }

    .is-flowrun {
      .modules_table-curd-with-simple-search {
        .table-curd-search_title {
          display: none;
        }

        .table-curd-search_create-btn {
          display: none;
        }
      }
    }

    .xflow-app-workspace {
      width: 50% !important;
    }

    .x6-graph-background {
      background-color: transparent !important;
    }

    .xflow-toolbar {
      background: var(--dmc-primary-panel-2);
      border: 1px solid var(--dmc-split-color);
    }

    .xflow-toolbar-item {
      color: var(--dmc-text-color) !important;
    }

    .x6-toolbar-item:hover {
      background-color: transparent !important;
    }
  }
}

/* stylelint-disable-next-line no-descending-specificity */
.drawer-flow-view-inner-table {
  width: 56% !important;
}

.taskName {
  color: var(--dmc-blue-500-color);
  cursor: pointer;
}
