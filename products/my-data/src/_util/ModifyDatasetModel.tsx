import { from, Observable, of } from 'rxjs';
import { map } from 'rxjs/operators';
import { patchDatasetAsync, pingDatasetAsync, pingUnsaveDatasetAsync, postDatasetAsync } from '@mdtBsServices/datasets';
import { IBusinessResult, IDataset, IDatasetExtraSSH, IDatasetPost } from '@mdtProComm/interfaces';
import {
  IDialogModifyFormDatasetModel,
  IFormData,
  IUiData,
} from '@mdtProMicroModules/containers/dialog-modify-form-dataset';

export type ITableData = IFormData & { id: string };

export class ModifyDatasetModel implements IDialogModifyFormDatasetModel<ITableData> {
  private newDataset?: IDataset;

  // 查询ui需要的数据
  public queryUiData(): Observable<IUiData> {
    const dbOptions = [
      { value: 'mysql', label: 'MySQL' },
      { value: 'postgresql', label: 'Postgresql' },
      { value: 'oracle', label: 'Oracle' },
      { value: 'firebird', label: 'Firebird' },
      { value: 'mssql', label: 'Microsoft SQL Server' },
      { value: 'sqlite', label: 'SQLite' },
      { value: 'sybase', label: 'Sybase' },
      { value: 'hive', label: 'Hive' },
      { value: 'dm', label: 'Dameng' },
    ];
    return of({
      dbOptions,
    });
  }

  // 创建dataset
  public createDataset(values: IFormData): Observable<IBusinessResult<ITableData>> {
    const postData = this.transformToBackendData(values);
    const request = postDatasetAsync(postData);
    return from(request).pipe(
      map((resp) => {
        const { success, data } = resp;
        this.newDataset = success ? data : undefined;
        const result = success ? this.transformToTableData(data!) : undefined;
        return { success, result };
      }),
    );
  }

  // 修改dataset
  public updateDataset(values: IFormData, originalData: ITableData): Observable<IBusinessResult<ITableData>> {
    const postData = this.transformToBackendData(values);
    return from(patchDatasetAsync(originalData.id, postData)).pipe(
      // eslint-disable-next-line sonarjs/no-identical-functions
      map((resp) => {
        const { success, data } = resp;
        this.newDataset = success ? data : undefined;
        const result = success ? this.transformToTableData(data!) : undefined;
        return { success, result };
      }),
    );
  }

  // 测试数据库连接
  public pingDataset(values: IFormData, originalData?: ITableData): Observable<boolean> {
    const postData = this.transformToBackendData(values);
    delete (postData as Partial<IDatasetPost>).name;
    // 测试已保存的数据库连接时，如果host没有修改就不传（传的话会校验此数据库的host是否是内部的，如果是会报错，防止外部用户访问）
    if (originalData?.host === postData.host) {
      delete (postData as Partial<IDatasetPost>).host;
    }
    const request = originalData ? pingDatasetAsync(originalData.id, postData) : pingUnsaveDatasetAsync(postData);
    return from(request).pipe(
      map((resp) => {
        return resp.success;
      }),
    );
  }

  // 获取新创建或修改后的dataset
  public getNewDataset() {
    return this.newDataset;
  }

  // 转换为 前端 需要的结构
  public transformToTableData = (db: IDataset): ITableData => {
    const ssh = db.extra?.ssh;
    const val: ITableData = {
      id: db.id,
      type: db.type,
      name: db.name,
      host: db.host,
      port: db.port,
      db: db.db,
      username: db.username,
      sshConnect: !!ssh,
    };
    if (ssh) {
      val.sshConnect = true;
      val.sshHost = ssh.host;
      val.sshPort = ssh.port;
      val.sshUsername = ssh.username;
    }
    return val;
  };

  // 转换为 后端 需要的结构
  private transformToBackendData = (values: IFormData): IDatasetPost => {
    const val: IDatasetPost = {
      type: values.type,
      name: values.name,
      host: values.host,
      port: values.port,
      db: values.db,
      username: values.username,
    };
    values.password && (val.password = values.password);
    val.extra = {};
    if (values.sshConnect) {
      const ssh: IDatasetExtraSSH = {
        host: values.sshHost,
        port: values.sshPort,
        username: values.sshUsername,
      };
      values.sshPassword && (ssh.password = values.sshPassword);
      values.sshPrivateKey && (ssh.private_key_string = values.sshPrivateKey);
      values.sshPrivateKeyPassword && (ssh.private_key_password = values.sshPrivateKeyPassword);
      val.extra = { ssh };
    }
    return val;
  };
}
