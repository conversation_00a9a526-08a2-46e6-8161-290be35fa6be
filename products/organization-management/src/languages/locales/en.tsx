import { en as microEn } from '@mdtProMicroModules/languages';
import { Locale } from './cn';

const en: Locale = {
  ...microEn,
  orgAdmin: {
    menu: {
      userManage: 'User Manage',
      userAndOrg: 'Organization',
      roleManage: 'Role Manage',
      authManage: 'Permission Manage',
      preferenceSetting: 'Preferences',
      common: 'Common',
      customLogin: 'Custom Login',
      logo: 'Logo Setting',
      appStyle: 'App Setting',
      defaultPlay: 'Default Play',
      dataSetting: 'Visible Data Settings',
      grantedForm: 'Granted Form',
      opsMetrics: 'Ops Metrics',
      operationLog: 'Operation Log',
      expired: (date: string) => `${date} will expired`,
    },
    organization: {
      username: 'Username',
      orgs: 'Organizations',
      roles: 'Roles',
      availability: 'Availability',
      expireTime: 'Expiration Time',
      batchOperation: 'Batch Operation',
      importOrg: 'Import Organizations',
      importUser: 'Import Users',
      exportUser: 'Export Users',
      batchEditUser: 'Batch Edit Users',
      orgManage: 'Organization Management',
      createUser: 'Create User',
      addOrgUser: 'Add Organization User',
      searchUser: 'Search User',
      edit: 'Edit User',
      activeUser: 'Activate User',
      freezeUser: 'Disable User',
      forcePassword: 'Force Password Change',
      unlockUser: 'Unlock Account',
      delUser: 'Delete User',
      delConfirm: (user: string) => `Are you sure you want to delete user "${user}"?`,
      delConfirmDesc: 'After deletion, the user will no longer be able to log in to use this product',
      removeConfirm: (user: string) => `Are you sure you want to remove user "${user}" from this organization?`,
      removeConfirmDesc: 'After removal, the user will no longer belong to this organization',
      unlockConfirm: 'Are you sure you want to unlock account for user',
      addUser: 'Add User',
      removeOrg: 'Remove from Organization',
      allUser: 'All Users',
      orgUser: 'Organization Users',
      active: 'Activate',
      freeze: 'Disable',
      enable: 'Enable',
      confirm: 'Confirm',
      freezeUserDesc: 'Disabling an account will not delete the user. Please confirm whether to disable it',
      exportError: 'Please select the corresponding users before performing the "Export Users" operation',
      exportSuccess: 'Export users successfully!',
      exportFailed: (error: string) => `Failed to export users: ${error}`,
      batchError: 'Please select the corresponding users before performing the "Batch Edit" operation',
      forcePasswordSuccess: 'Force password change successful',
      batchSuccess: 'Batch create users successful',
      batchOrgSuccess: 'Batch create Organizations successful',
      editUserSuccess: 'Update user successful',
      batchEditUserSuccess: 'Batch update users successful',
      createUserSuccess: 'Create user successful',
      delUserSuccess: 'Delete user successful',
      unlockSuccess: 'Unlock account successful',
      editUserStatusSuccess: 'Change user enabled status successful',
      addUserSuccess: 'Add user successful',
      removeUserSuccess: 'Remove user successful',
    },
    auth: {
      user: 'User',
      org: 'Organization',
      role: 'Role',
      editAuthSuccess: 'Permission updated successfully',
      roleName: 'Role Name',
      orgName: 'Organization Name',
      orgCount: 'Numbers of person',
      roleDesc: 'Role Description',
      auth: 'Permission',
      resource: 'Resource',
      authSetting: 'Permission Management',
      authSettingDesc:
        'The permissions inherited from the organization/role cannot be changed by the user. Modify the organization/role to change them',
    },
    logo: {
      product: 'Product',
      changeLogo: 'Change Logo',
      reset: 'Reset',
      resetSuccess: 'Reset Logo Successfully',
      maxSize: (size: string) => `Max size limit ${size}kb`,
      editLogoSuccess: 'Change Logo Successfully',
      fileUploadFailed: (name: string) => `File [${name}] Upload Failed`,
    },
    appStyle: {
      logo: 'Logo',
      favicon: 'Favicon',
      other: 'Other',
      logoName: 'Header logo',
      faviconName: 'The small icon in the browser tab, 64 x 64 pixels',
      windowTitleName: 'Title',
      windowDescName: 'Desc',
      helpCenterName: 'Support',
      waterMark: 'WaterMark',
      open: 'Open',
      close: 'Close',
      resetAll: 'Reset All',
      idleTimeoutName: 'Idle Timeout',
      idleTimeoutDesc:
        'If there is no user operation within the specified time period, the system will automatically log out',
      pswSaftyName: 'Password Security',
      pswSaftyDesc: 'Prompt users to change their password after exceeding the specified time period',
      tenMinutes: 'Ten minutes',
      halfHour: 'Half an hour',
      oneHour: 'One hour',
      twoHours: 'Two hours',
      fourHours: 'Four hours',
      eightHours: 'Eight hours',
      sevenDays: 'Sveven days',
      fifteenDays: 'Fifteen days',
      threeMonth: 'Three months',
      halfYear: 'Half a year',
      nineMonth: 'Nine months',
      oneYear: 'One year',
      never: 'Never',
      resetAllTips: 'Are you sure you want to reset all organizational styles?',
      resetAllTipsDesc: 'After resetting, the original configurations will not be preserved',
      sizeError: (width: number, height: number) =>
        `The image size is incorrect. Please upload an icon file with dimensions of ${width}x${height} pixels.`,
      fileError: 'Unable to load image. Please ensure that you have uploaded a valid image file.',
      updateSuccess: 'Update Successful',
    },
    defaultPlay: {
      defaultPage: 'Default Play Page',
      desc: 'After setting the homepage for the user, the user will play this page by default when entering the product. If you need the user to return to the system, please check whether the user has the "Return to System" permission.',
      batchChange: 'Batch Change',
      resetSuccess: 'Default play reset successfully',
      setSuccess: 'Default play set successfully',
    },
    dataSetting: {
      username: 'Username',
      themes: 'Visible Theme Libraries',
      orgs: 'Visible Organizations',
      cannotsee: 'Invisible',
      desc: 'When the user is not in the table below, all data will be displayed by default',
      addShare: 'Add Distribution',
      searchPlaceholder: 'Search User',
      addTitle: 'Create Theme Library Distribution Table',
      editTitle: 'Edit Theme Library Distribution Table',
      delSuccess: 'Deletion successful',
      del: 'Delete',
      delDesc: 'Are you sure you want to delete the created distribution table?',
      editSuccess: 'Edit successful',
      emptyDesc:
        'By default, users in this organization can see all theme libraries. You can set "Visible Theme Libraries" for users, and they will only be able to see these theme libraries and the data inside them.',
    },
    operationLog: {
      search: 'Search',
      export: 'Export',
      operator: 'Operator',
      resource: 'Resource',
      user: 'User',
      date: 'Date',
      object: 'Object',
      exportTitle: "You're about to export current data",
      exportDescription: 'Note: Due to system restrictions, you can only export up to 5000 pieces of data at a time',
      exportCancel: 'Later',
      exportOk: 'Immediately export',
      emptyTitle: 'No data found',
      emptyDescription: 'Please change the condition and try again',
      operationTooltip: 'The operation can only be selected after selecting [Resource]',
    },
    customLogin: {
      active: 'Default',
      title: 'Custom Login',
      createTemplate: 'Create New Template',
      uploadTemplate: 'Upload Template',
      manual: 'Manual Config',
      setDefault: 'Set as Default',
      editTemplate: 'Edit Template',
      cancelDefault: 'Cancel Default',
      copyLink: 'Copy Link',
      deleteTemplate: 'Delete Template',
      fileUploadError: 'File upload failed, please try again',
      copy: 'Copied to pasteboard',
      removeSuccess: 'Delete template successful',
      createSuccess: 'Create template successful',
      editSuccess: 'Edit template successful',
    },
  },
};

export default en;
