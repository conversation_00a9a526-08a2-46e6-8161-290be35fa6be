const fs = require('fs');
const { resolve } = require('path');
const { getCompilerHooks } = require('webpack-manifest-plugin');

class HtmlEntrypointsPlugin {
  constructor(options) {
    this.userOptions = options || {};
  }
  apply(compiler) {
    const { beforeEmit } = getCompilerHooks(compiler);
    beforeEmit.tap('HtmlEntrypointsPlugin', (manifest) => {
      if (manifest.entrypoints) {
        const js = [];
        const css = [];
        manifest.entrypoints.forEach((it) => {
          it.endsWith('.js') && js.push(it);
          it.endsWith('.css') && css.push(it);
        });
        const { templateContent, filePath, fileOpt } = this.userOptions;
        let context = templateContent.replace('{{jsArr}}', JSON.stringify(js));
        context = context.replace('{{cssArr}}', JSON.stringify(css));
        fs.writeFileSync(filePath, context, fileOpt);
      }
      return manifest;
    });
  }
}

module.exports = HtmlEntrypointsPlugin;
