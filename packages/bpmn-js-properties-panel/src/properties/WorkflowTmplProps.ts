import { isTextAreaEntryEdited, TextAreaEntry } from '@bpmn-io/properties-panel';
import { useService } from 'bpmn-js-properties-panel';
import { PREFIX } from '../constants';
import { ButtonEntry } from '../entries/ButtonEntry';
import { getRelevantBusinessObject, isBpmnProcess } from '../utils/ElementUtil';
import { addExtensionElements, getExtensionElement, removeExtensionElements } from '../utils/ExtensionElementsUtil';

const DESC_CUSTOM_ELEMENT_KEY = `${PREFIX}:WorkflowDescription`;
const NAME_CUSTOM_ELEMENT_KEY = `${PREFIX}:WorkflowName`;
const DETAIL_CUSTOM_ELEMENT_KEY = `${PREFIX}:WorkflowDetail`;

export function WorkflowTmplProps(props: any) {
  const { element } = props;

  if (!isBpmnProcess(element)) {
    return [];
  }

  return [
    { id: 'workflowName', component: WorkflowName, isEdited: isTextAreaEntryEdited },
    { id: 'workflowDesc', component: WorkflowDesc, isEdited: isTextAreaEntryEdited },
    { id: 'workflowDetail', component: WorkflowDetail, isEdited: isTextAreaEntryEdited },
  ];
}

function WorkflowDetail(props: any) {
  const { element, id } = props;

  const bpmnFactory = useService('bpmnFactory');
  const commandStack = useService('commandStack');
  const translate = useService('translate');
  const debounce = useService('debounceInput');
  const businessObject = getRelevantBusinessObject(element);
  const mdtExternal = useService('mdtExternalProvider');

  const getValue = () => {
    const extensionElement = getExtensionElement(businessObject, DETAIL_CUSTOM_ELEMENT_KEY);
    return extensionElement?.get('body') || '';
  };

  const setValue = (value: string) => {
    let extensionElement = getExtensionElement(businessObject, DETAIL_CUSTOM_ELEMENT_KEY);
    extensionElement && removeExtensionElements(element, businessObject, extensionElement, commandStack);
    extensionElement = bpmnFactory.create(DETAIL_CUSTOM_ELEMENT_KEY, { body: value });
    addExtensionElements(element, businessObject, extensionElement, bpmnFactory, commandStack);
  };

  const onClick = (e: any) => {
    e.stopPropagation();
    e.preventDefault();
    mdtExternal.handleConfigWorkflowDetail(getValue(), setValue);
  };

  const label = ButtonEntry({
    children: translate('Workflow detail'),
    onClick,
  });

  return TextAreaEntry({ element, id, label, getValue, setValue, debounce, disabled: true });
}

function WorkflowDesc(props: any) {
  const { element, id } = props;

  const bpmnFactory = useService('bpmnFactory');
  const commandStack = useService('commandStack');
  const translate = useService('translate');
  const debounce = useService('debounceInput');
  const businessObject = getRelevantBusinessObject(element);
  const mdtExternal = useService('mdtExternalProvider');

  const getValue = () => {
    const extensionElement = getExtensionElement(businessObject, DESC_CUSTOM_ELEMENT_KEY);
    return extensionElement?.get('body') || '';
  };

  const setValue = (value: any) => {
    let extensionElement = getExtensionElement(businessObject, DESC_CUSTOM_ELEMENT_KEY);
    extensionElement && removeExtensionElements(element, businessObject, extensionElement, commandStack);
    extensionElement = bpmnFactory.create(DESC_CUSTOM_ELEMENT_KEY, { body: value });
    addExtensionElements(element, businessObject, extensionElement, bpmnFactory, commandStack);
  };

  const onClick = (e: any) => {
    e.stopPropagation();
    e.preventDefault();
    mdtExternal.handleConfigWorkflowTmpl(translate('WorkflowDesc'), getValue(), setValue);
  };

  const label = ButtonEntry({
    children: translate('WorkflowDesc'),
    onClick,
  });

  return TextAreaEntry({ element, id, label, getValue, setValue, debounce, disabled: true });
}

function WorkflowName(props: any) {
  const { element, id } = props;

  const bpmnFactory = useService('bpmnFactory');
  const commandStack = useService('commandStack');
  const translate = useService('translate');
  const debounce = useService('debounceInput');
  const businessObject = getRelevantBusinessObject(element);
  const mdtExternal = useService('mdtExternalProvider');

  const getValue = () => {
    const extensionElement = getExtensionElement(businessObject, NAME_CUSTOM_ELEMENT_KEY);
    return extensionElement?.get('body') || '';
  };

  const setValue = (value: any) => {
    let extensionElement = getExtensionElement(businessObject, NAME_CUSTOM_ELEMENT_KEY);
    extensionElement && removeExtensionElements(element, businessObject, extensionElement, commandStack);
    extensionElement = bpmnFactory.create(NAME_CUSTOM_ELEMENT_KEY, { body: value });
    addExtensionElements(element, businessObject, extensionElement, bpmnFactory, commandStack);
  };

  const onClick = (e: any) => {
    e.stopPropagation();
    e.preventDefault();
    mdtExternal.handleConfigWorkflowTmpl(translate('WorkflowName'), getValue(), setValue);
  };

  const label = ButtonEntry({
    children: translate('WorkflowName'),
    onClick,
  });

  return TextAreaEntry({ element, id, label, getValue, setValue, debounce, disabled: true });
}
