import { DatlasAppController } from '@mdtProMicroModules/datlas/app/DatlasAppController';
import {
  OneTableReportCreate,
  OneTableReportCreateController,
  OneTableReportCreateModelBff,
} from '@mdtProMicroModules/pages/one-table-report-create';

const TestForm = () => {
  const ctrl = new OneTableReportCreateController({
    Model: OneTableReportCreateModelBff,
    app: DatlasAppController.getInstance(),
    isCreate: true,
    isCreateFromTemplate: false,
    reportInfo: {
      name: 'test',
    },
  });
  return (
    <div className="drawer-report-create">
      <OneTableReportCreate controller={ctrl} />
    </div>
  );
};

export default TestForm;
