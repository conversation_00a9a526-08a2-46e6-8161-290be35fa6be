import _ from 'lodash';
import { from } from 'rxjs';
import { map, takeWhile } from 'rxjs/operators';
import { queryDatapkgsAsync, queryDatapkgsPaginationTotalAsync } from '@mdtBsServices/datapkgs';
import { IDatapkg, IDatapkgsQuery, IRequestCancelToken, IServerResponse } from '@mdtProComm/interfaces';
import { IDatapkgItem } from '@mdtProMicroModules/containers/table-card-datapkg-search';
import { DatapkgSearchModel } from '../../_util/DatapkgSearchModel';

interface IRespData {
  total?: number;
  list: IDatapkg[];
  taggroupIdNameMap: Record<string, string>;
}

export class AppPkgSearchModel extends DatapkgSearchModel {
  // 查询下一页数据
  // eslint-disable-next-line max-params
  public static queryNextPageTickets(
    data: IDatapkgsQuery,
    params: any,
    cancelToken: IRequestCancelToken,
    appName: string,
  ) {
    // 获取数据包数据
    return from(
      new Promise<IServerResponse<IRespData>>((resolve) => {
        const request = async () => {
          const resp = await queryDatapkgsAsync(data, { cancelToken, params });
          // 如果成功，则在查询标签的名称
          let list: IDatapkg[] = [];
          let taggroupIdNameMap: Record<string, string> = {};
          if (resp.success) {
            list = resp.data!;
          }
          resolve({
            success: resp.success,
            canceled: resp.canceled,
            data: { list, taggroupIdNameMap },
          });
        };
        request();
      }),
    ).pipe(
      takeWhile((v) => !v.canceled),
      map((resp) => {
        const { list, taggroupIdNameMap } = resp.data!;
        return _.map(list, (it) => this.transformToDatapkgItem(it, appName, taggroupIdNameMap));
      }),
    );
  }

  // 查询第一页数据
  // eslint-disable-next-line max-params
  public static queryFirstPagePkgs(
    data: IDatapkgsQuery,
    params: any,
    cancelToken: IRequestCancelToken,
    appName: string,
  ) {
    return from(
      new Promise<IServerResponse<IRespData>>((resolve) => {
        const request = async () => {
          const resp = await queryDatapkgsPaginationTotalAsync(data, {
            cancelToken,
            params,
          });

          let total = 0;
          let list: IDatapkg[] = [];
          let taggroupIdNameMap: Record<string, string> = {};
          if (resp.success) {
            const { total_count, dataResult } = resp.data!;
            list = dataResult;
            total = total_count;
          }
          resolve({
            success: resp.success,
            canceled: resp.canceled,
            data: { total, list, taggroupIdNameMap },
          });
        };
        request();
      }),
    ).pipe(
      takeWhile((v) => !v.canceled),
      map((resp) => {
        const { total, list, taggroupIdNameMap } = resp.data!;
        const data = _.map(list, (it) => this.transformToDatapkgItem(it, appName, taggroupIdNameMap));
        return [total!, data] as [number, IDatapkgItem[]];
      }),
    );
  }
}

export type IAppPkgSearchModel = typeof AppPkgSearchModel;
