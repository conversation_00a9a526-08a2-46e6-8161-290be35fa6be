import loadable from '@loadable/component';
import { useMicroPath } from '@mdtProComm/utils/useMicroPath';
import { WorkflowPath } from '../../_util/product-path';

// 新增路由模块请使用 loadable 加载页面组件
const WorkflowDetailDrawerView = loadable(() => import('./page-workflow-detail-drawer'));
const PageStartWorkflow = loadable(() => import('./page-start-workflow'));
const PageWorkflowDetail = loadable(() => import('./page-workflow-detail'));

export const useWorkflowSubModule = () => {
  const pathname = useMicroPath();

  // 处于性能考虑, 部分页面直接在主页面渲染，加速打开速度
  if (pathname.includes(WorkflowPath.WORKFLOW_APPLY)) {
    return <PageStartWorkflow />;
  }

  if (pathname.includes(WorkflowPath.WORKFLOW_DETAIL_DRAWER)) {
    return <WorkflowDetailDrawerView />;
  }

  if (pathname.includes(WorkflowPath.WORKFLOW_DETAIL)) {
    return <PageWorkflowDetail />;
  }

  return null;
};
