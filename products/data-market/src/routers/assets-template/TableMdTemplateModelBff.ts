import _ from 'lodash';
import { from, Observable, of } from 'rxjs';
import { map } from 'rxjs/operators';
import { ALBUM_PKG_LIST_DATA, ARTICLE_DATA, FOLDER_DATA } from '@mdtBsBffServices/services';
import { DATE_FORMATTER_2, formateDate } from '@mdtBsComm/utils/dayUtil';
import { joinKey, splitKey } from '@mdtBsComm/utils/stringUtil';
import { ICurdOptions } from '@mdtBsComponents/data-list-comp-table-curd';
import { ArticleBffService } from '@mdtProComm/bff-services';
import { OwnershipEnum } from '@mdtProComm/constants';
import {
  IArticle,
  IArticlePost,
  IArticlesQuery,
  IBusinessResult,
  IDatapkgsMetaPost,
  ILabelValue,
  IQueryFolders,
} from '@mdtProComm/interfaces';
import { DatasetsModel } from '@mdtProComm/models/DatasetsModel';
import { IAssignFormData, IUiData } from '@mdtProMicroModules/containers/dialog-form-template-to-datapkg';
import { type IFolders, FolderModel } from '@mdtProMicroModules/models/FolderModel';
import { ITableData, ITableMdTemplateModel } from '@mdtProMicroModules/pages/table-md-template';
import { IFormData } from '@mdtProTasks/create-template-from-upload-local-file-task/dialog-modify-form-md-template';
import { AppController } from '../../app/AppController';
import { albumBffService, articleBffService, datasetBffService, pkgBffService } from '../../bff-service';
import i18n from '../../languages';

enum SourceType {
  SOURCE = 'source',
  THEME = 'theme',
}

export class TableMdTemplateModeBff implements ITableMdTemplateModel {
  private readonly app: AppController;
  private assignUiOptions?: IUiData;
  private permissions;

  public constructor(app: AppController, permissions: ICurdOptions<ITableData>) {
    this.app = app;
    this.permissions = permissions;
  }
  // 查询权限
  public queryPermissions(): ICurdOptions<ITableData> {
    return this.permissions;
  }

  public queryMdTemplateFolderList(
    articleParams: IArticlesQuery,
    folderParams: IQueryFolders,
  ): Observable<ITableData[]> {
    return from(
      ArticleBffService.queryFoldersArticles(
        { respData: ARTICLE_DATA, params: articleParams },
        { params: folderParams, respData: FOLDER_DATA },
      ),
    ).pipe(
      map(([articleResp, folderResp]) => {
        if (!articleResp.success) return [];
        const { page_data } = articleResp;
        const data = _.map(page_data, (item) => this.transformTableData(item));
        const folderData = _.map(folderResp, FolderModel.transformToFolder);
        const folderTableData = _.map(folderData, this.transformFolderToTableData);
        return [...folderTableData, ...data] as ITableData[];
      }),
    );
  }

  // 查询模板列表
  // eslint-disable-next-line sonarjs/no-identical-functions
  public queryMdTemplateList(params?: IArticlesQuery): Observable<ITableData[]> {
    return from(articleBffService.queryArticleList({ respData: ARTICLE_DATA, params })).pipe(
      map((resp) => {
        if (!resp.success) return [];
        return _.map(resp.page_data, this.transformTableData);
      }),
    );
  }

  // 创建模板
  public createMdTemplate(createData: IFormData): Observable<IBusinessResult<ITableData>> {
    const postData: IArticlePost = {
      content: createData.content,
      title: createData.name,
      content_type: 'markdown',
      version: '1',
    };
    return from(articleBffService.createArticle({ data: postData, respData: ARTICLE_DATA })).pipe(
      map((resp) => ({
        success: resp.success,
        result: resp.success ? this.transformTableData(resp.page_data!) : undefined,
      })),
    );
  }

  // 修改模板
  public updateMdTemplate(
    updateData: IFormData,
    originalUpdateData: ITableData,
  ): Observable<IBusinessResult<ITableData>> {
    const postData: IArticlePost = {
      content: updateData.content,
      title: updateData.name,
      content_type: 'markdown',
      version: originalUpdateData.version,
    };

    return from(
      articleBffService.updateArticle({
        id: originalUpdateData.id,
        data: postData,
        revision: originalUpdateData.revision,
        respData: ARTICLE_DATA,
      }),
    ).pipe(
      map((resp) => ({
        success: resp.success,
        result: resp.success ? this.transformTableData(resp.page_data!) : undefined,
      })),
    );
  }

  // 删除模板
  public deleteMdTemplate(deleteData: ITableData): Observable<IBusinessResult<ITableData>> {
    return from(
      articleBffService.deleteArticle({
        id: deleteData.id,
        revision: deleteData.revision,
        respData: ARTICLE_DATA,
      }),
    ).pipe(
      map((resp) => ({
        success: resp.success,
        result: resp.success ? deleteData : undefined,
      })),
    );
  }

  // 加载分配弹窗ui数据
  public loadAssignUiData(): Observable<IUiData> {
    if (this.assignUiOptions) return of(this.assignUiOptions);
    // TODO 应该不区分主题库和数据源，直接让用户检索有编辑元数据权限的数据包privilege=''
    return from(DatasetsModel.loadData()).pipe(
      map(() => {
        const themeOptions = _.map(this.app.getAlbumController()?.getPrefAlbums(), (it) => {
          const key = joinKey(SourceType.THEME, it.id);
          return {
            key,
            value: key,
            label: it.name,
          };
        });
        const sourceOptions = _.map(DatasetsModel.getAllDs(), (it) => {
          const key = joinKey(SourceType.SOURCE, it.value);
          return {
            key,
            value: key,
            label: it.label,
          };
        });

        this.assignUiOptions = {
          sourceOptions: [
            {
              title: i18n.chain.proMicroModules.mdTmpl.source,
              key: SourceType.SOURCE,
              value: SourceType.SOURCE,
              selectable: false,
              children: sourceOptions,
            },
            {
              title: i18n.chain.proMicroModules.mdTmpl.theme,
              key: SourceType.THEME,
              value: SourceType.THEME,
              selectable: false,
              children: themeOptions,
            },
          ],
        };
        return this.assignUiOptions!;
      }),
    );
  }

  // 查询数据包
  public loadPkgOptionsFunc(sourceId: string, search?: string): Observable<ILabelValue[]> {
    const [type, id] = splitKey(sourceId);
    const isSource = type === SourceType.SOURCE;
    const params = { name: search || undefined, ownership: isSource ? OwnershipEnum.APP : undefined };
    return isSource
      ? from(
          datasetBffService.queryDatasetPkgs({
            datasetId: id,
            data: params,
          }),
        ).pipe(
          map((resp) => {
            if (!resp.success) return [];
            return _.map(resp.page_data.datapkgs, (item) => ({
              label: item.name,
              value: item.id,
            }));
          }),
        )
      : from(albumBffService.queryAlbumPkgsNextPage({ albumId: id, data: params, respData: ALBUM_PKG_LIST_DATA })).pipe(
          map((resp) => {
            return _.map(resp.success ? resp.page_data.datapkgs : [], (item) => ({
              label: item.name,
              value: item.id,
            }));
          }),
        );
  }

  // 模板分配数据包
  public assignTemplateToPkg(
    formData: IAssignFormData,
    tableData: ITableData,
  ): Observable<IBusinessResult<ITableData>> {
    const postData: IDatapkgsMetaPost = { datapkg_ids: formData.pkgId };
    postData[formData.attrName] = { article_id: tableData.id };
    return from(pkgBffService.updateDatapkgListMeta({ data: postData })).pipe(
      map((resp) => ({
        success: resp.success ? resp.page_data : false,
      })),
    );
  }

  public transformTableData(item: IArticle): ITableData {
    return {
      id: item.id,
      name: item.title,
      content: item.content,
      contentType: item.content_type,
      version: item.version,
      revision: item.revision,
      updateTime: item.update_time,
      updateTimeDisplay: formateDate(item.update_time * 1000, DATE_FORMATTER_2),
    };
  }

  public transformFolderToTableData(item: IFolders): ITableData {
    return {
      id: item.path,
      name: item.currentPath,
      content: '',
      contentType: '',
      version: '',
      revision: NaN,
      updateTime: item.createTime,
      updateTimeDisplay: item.createTimeDisplay,
      ...item,
    };
  }
}
