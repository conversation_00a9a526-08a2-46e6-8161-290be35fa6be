import { FC, memo } from 'react';
import { ModuleIdEnum } from '@mdtProComm/constants';
import { getMicroAppProps, getSubAppName } from '../../_util/util';
import { MICRO_RESOURCE_SHARE } from '../../config';

const MicroResourceShare: FC<{ product: string }> = memo(({ product }) => {
  const appName = getSubAppName(product, ModuleIdEnum.RESOURCE_SHARE);
  const props = getMicroAppProps(appName, MICRO_RESOURCE_SHARE);
  return <micro-app {...props} />;
});

export { MicroResourceShare };
