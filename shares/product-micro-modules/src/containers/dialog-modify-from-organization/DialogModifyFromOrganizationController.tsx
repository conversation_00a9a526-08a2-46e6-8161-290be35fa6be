import _ from 'lodash';
import { FC } from 'react';
import { from, Observable, of } from 'rxjs';
import { distinctUntilChanged, map } from 'rxjs/operators';
import { DataNode } from '@mdtBsComm/components/side-menu';
import { IBusinessResult } from '@mdtBsComm/interfaces';
import { FormCompController } from '@mdtBsComponents/form-comp';
import { ModalWithBtnsCompDialogController } from '@mdtBsComponents/modal-with-btns-comp-dialog';
import i18n from '../../languages';
import {
  DrawerModifyFormPermissionOnlyController,
  IModalData as IPermissionOnlyModalData,
  IPermission,
} from '../drawer-modify-form-permission-only';
import { DialogModifyFromOrganizationInner } from './DialogModifyFromOrganizationInner';

export enum FormDataAttrs {
  TITLE = 'title',
  PID = 'pid',
  RESOURCES = 'resources',
}
export interface IFormData {
  title: string;
  key: string;
  pid?: string;
  permission: IPermission;
}

export interface IModalData {
  treeData: DataNode[];
  currentKey: string | number;
}

export type IPermissionModalData = IPermissionOnlyModalData;

export type IModifyDataFunc<V> = (params: IFormData, originalData?: V) => Promise<IBusinessResult<V>>;

export interface IControllerOptions<U, V> {
  modifyDataFunc: IModifyDataFunc<V>;
  loadDrawerUiDataFunc: () => Observable<U>;
  loadDrawerPermissionUiDataFunc: () => Observable<IPermissionModalData>;
  openResourceFunc?: (items: V) => void;
  openPermissionFunc?: (items: V) => void;
}

class DialogModifyFromOrganizationController<
  V extends IFormData,
  U extends IModalData,
> extends ModalWithBtnsCompDialogController<V> {
  // 加载界面所需要的数据
  private loadDrawerUiDataFunc: () => Observable<U>;
  private formCompController: FormCompController<IFormData>;
  private modifyDataFunc: IModifyDataFunc<V>;
  private openResourceFunc?: (items: V) => void;
  private openPermissionFunc?: (items: V) => void;
  private modifyPermissionController: DrawerModifyFormPermissionOnlyController<IPermission, V>;
  public constructor(options: IControllerOptions<U, V>) {
    super({
      modalCompOptions: {
        modalOptions: () => this.initModalOptions(),
        beforeOpenFunc: () => this.initModalData(),
      },
      InnerView: DialogModifyFromOrganizationInner as FC,
      clickOkBtnFunc: (originalData) => this.onClickDrawerOkBtn(originalData),
    });
    this.formCompController = new FormCompController();
    this.modifyPermissionController = new DrawerModifyFormPermissionOnlyController<IPermission, V>({
      loadDrawerUiDataFunc: options.loadDrawerPermissionUiDataFunc,
      modifyDataFunc: this.modifyDataToService,
    });
    this.loadDrawerUiDataFunc = options.loadDrawerUiDataFunc;
    this.openResourceFunc = options.openResourceFunc;
    this.openPermissionFunc = options.openPermissionFunc;
    this.modifyDataFunc = options.modifyDataFunc;
  }

  public destroy() {
    super.destroy();
    this.formCompController.destroy();
    this.modifyPermissionController.destroy();
    this.loadDrawerUiDataFunc = this.defaultLoadDrawerUiDataFunc;
    this.modifyDataFunc = this.defaultModifyDataFunc;
    this.openPermissionFunc = undefined;
    this.openResourceFunc = undefined;
  }

  public openModal(rest?: V) {
    this.formCompController.changeFormData(rest);
    const formData$ = this.formCompController.getFormData$();
    // 根据输入决定确定按钮是否可用
    formData$
      .pipe(
        map((v) => {
          return !(v?.title && v?.pid);
        }),
        distinctUntilChanged(),
      )
      .subscribe((v) => {
        this.changeDisabledOkBtn(v);
      });
    return super.openModal(rest);
  }

  public getFormCompController() {
    return this.formCompController;
  }

  public getModifyPermissionController() {
    return this.modifyPermissionController;
  }

  public getOpenResourceFunc = () => {
    return this.openResourceFunc?.(this.getModalRest() as V);
  };

  public openPermission = () => {
    return this.openPermissionFunc?.(this.getModalRest() as V);
  };

  private filterDeep(arr: DataNode[], tar?: string) {
    return _.filter(arr, (item: DataNode) => {
      if (item.children) {
        item.children = this.filterDeep(item.children, tar);
      }
      return item.key !== tar;
    });
  }

  // 获取弹窗所需的数据
  private initModalData = async () => {
    const modalRest = this.getModalRest();
    let modalData: U = await from(this.loadDrawerUiDataFunc()).toPromise();
    if (modalRest?.key) {
      // 去除当前的机构
      const newTreeData = this.filterDeep(_.cloneDeep(modalData.treeData), modalRest?.key);
      modalData = { ...modalData, treeData: newTreeData };
    }
    return modalData;
  };

  // 点击弹窗OK按钮
  private onClickDrawerOkBtn = (originalData?: V) => {
    const formData: IFormData = this.formCompController.getFormDataValue()!;
    return this.modifyDataFunc(formData, originalData);
  };

  private initModalOptions() {
    const modalRest = this.getModalRest();
    return {
      width: '440px',
      level: null,
      title: modalRest?.key ? i18n.chain.proMicroModules.orgList.orgManage : i18n.chain.proMicroModules.orgList.addOrg,
    };
  }

  // 向服务端修改数据
  private modifyDataToService = (params: IPermission) => {
    return new Promise<IBusinessResult<V>>((resolve) => {
      resolve({ success: true, result: params as unknown as V });
    });
  };

  // 默认请求数据方法
  private defaultLoadDrawerUiDataFunc() {
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    return of({} as U);
  }

  // 默认的修改数据方法
  private async defaultModifyDataFunc() {
    return { success: false };
  }
}

export { DialogModifyFromOrganizationController };
