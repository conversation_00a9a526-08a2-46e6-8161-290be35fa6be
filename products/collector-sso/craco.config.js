const { loadProductConfig } = require('../../_template/craco');

const updateDevProxy = (env, definePluginValue) => {
  const envOriginMap = {
    dev: 'https://collectoradmin.maicedata-dev.com/api',
    staging: 'https://collectoradmin.maicedata-staging.com/api',
    debug: 'https://collectoradmin.maicedata.com/api',
  };
  const origin = envOriginMap[env];
  if (definePluginValue) {
    definePluginValue.__DEVELOP_PROXY_API_URL = JSON.stringify('/proxyapi');
    definePluginValue.__DEVELOP_ENV_ORIGIN = JSON.stringify(origin);
  }
  return {
    '/proxyapi': {
      target: origin,
      secure: false,
      changeOrigin: true,
      pathRewrite: { '^/proxyapi': '' },
    },
  };
};

module.exports = {
  plugins: [
    ...loadProductConfig({
      updateDevProxy,
      secdir: '/collector-sso/',
      debug: true
    }),
  ],
};
