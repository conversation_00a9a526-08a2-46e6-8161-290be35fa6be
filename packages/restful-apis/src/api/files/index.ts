import { FILES_URL } from '../../config';
import {
  IApiResponse,
  IFile,
  IFileAckPost,
  IFileDeleteQuery,
  IFileGetQuery,
  IFilePatch,
  IFilePost,
  IFilePostQuery,
  IFilesQuery,
  IFileUrlQuery,
  IId,
  IRequestPromise,
  IRequestRequestConfig,
} from '../../interfaces';
import request from '../../request';

// 查询文件元数据
export const queryFiles = (config?: IRequestRequestConfig<IFilesQuery>) => {
  return request({
    ...(config || {}),
    method: 'get',
    url: `${FILES_URL}`,
  }) as IRequestPromise<IApiResponse<IFile[]>>;
};

// 新建文件
export const postFile = (data: IFilePost, config?: IRequestRequestConfig<IFilePostQuery>) => {
  return request({
    ...(config || {}),
    method: 'post',
    url: `${FILES_URL}`,
    data,
  }) as IRequestPromise<IApiResponse<IFile>>;
};

// 获取文件元数据
export const getFile = (fileId: string, config?: IRequestRequestConfig<IFileGetQuery>) => {
  return request({
    ...(config || {}),
    method: 'get',
    url: `${FILES_URL}/${fileId}`,
  }) as IRequestPromise<IApiResponse<IFile>>;
};

// 修改文件元数据
export const patchFile = (fileId: string, data: IFilePatch, config?: IRequestRequestConfig) => {
  return request({
    ...(config || {}),
    method: 'patch',
    url: `${FILES_URL}/${fileId}`,
    data,
  }) as IRequestPromise<IApiResponse<IFile>>;
};

// 删除文件
export const deleteFile = (fileId: string, config?: IRequestRequestConfig<IFileDeleteQuery>) => {
  return request({
    ...(config || {}),
    method: 'delete',
    url: `${FILES_URL}/${fileId}`,
  }) as IRequestPromise<IApiResponse<IId>>;
};

// 确认文件上传成功
export const postAckFile = (fileId: string, data: IFileAckPost, config?: IRequestRequestConfig) => {
  return request({
    ...(config || {}),
    method: 'post',
    url: `${FILES_URL}/${fileId}/ack`,
    data,
  }) as IRequestPromise<IApiResponse<IFile>>;
};

// 获取文件下载地址
export const getFileUrl = (fileId: string, config?: IRequestRequestConfig<IFileUrlQuery>) => {
  return request({
    ...(config || {}),
    method: 'get',
    url: `${FILES_URL}/${fileId}/content`,
  }) as IRequestPromise<IApiResponse<IFile>>;
};

// 获取更新文件的url，后续手动上传到该url
export const putFileContent = (fileId: string, config?: IRequestRequestConfig) => {
  return request({
    ...(config || {}),
    method: 'put',
    url: `${FILES_URL}/${fileId}/content`,
  }) as IRequestPromise<IApiResponse<IFile>>;
};
