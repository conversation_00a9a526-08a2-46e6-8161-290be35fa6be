import loadable from '@loadable/component';
import AppGrid from '@mdtDesign/icons/app-grid';
import ApprovalIcon from '@mdtDesign/icons/fact-check';
import List from '@mdtDesign/icons/list';
import { PermissionEnum } from '@mdtProComm/constants';
import { OneTableRoutePathEnum } from '@mdtProComm/constants/route';
import i18n from '../languages';

const FormManagementView = loadable(() => import('../routers/form-management'));
const MissionCenterTaskView = loadable(() => import('../routers/mission-center-task'));
const MissionCenterFormView = loadable(() => import('../routers/mission-center-form'));
const MissionCenterReviewView = loadable(() => import('../routers/mission-center-review'));
const FormDatapkgView = loadable(() => import('../routers/form-datapkg'));

export const hzsjczlsjzt2 = {
  appName: '杭州市基层治理数据中台',
  menuConfig: {
    openKeys: [OneTableRoutePathEnum.MISSION_CENTER],
    items: [
      {
        key: OneTableRoutePathEnum.FORM_MANAGEMENT,
        title: (
          <>
            <List size={16} />
            {i18n.chain.proMicroModules.oneTable.menu.formCreateManagement}
          </>
        ),
        className: `top-level menu-divider`,
      },
      {
        key: OneTableRoutePathEnum.MISSION_CENTER,
        title: (
          <>
            <ApprovalIcon size={15} />
            {i18n.chain.proMicroModules.oneTable.menu.missionCenter}
          </>
        ),
        disabled: true,
        className: `top-level menu-divider`,
        children: [
          {
            key: OneTableRoutePathEnum.MISSION_CENTER_TASK,
            title: i18n.chain.proMicroModules.oneTable.menu.missionCenterTask,
          },
          {
            key: OneTableRoutePathEnum.MISSION_CENTER_FORM,
            title: i18n.chain.proMicroModules.oneTable.menu.missionCenterForm,
          },
          {
            key: OneTableRoutePathEnum.MISSION_CENTER_REVIEW,
            title: i18n.chain.proMicroModules.oneTable.menu.missionCenterReview,
          },
        ],
      },
      {
        key: OneTableRoutePathEnum.FORM_DATAPKG,
        title: (
          <>
            <List size={16} />
            {i18n.chain.proMicroModules.oneTable.menu.formDatapkg}
          </>
        ),
        className: `top-level menu-divider`,
      },
      {
        key: '/app-grid',
        title: (
          <>
            <AppGrid size={16} />
            页面集成报表填写
          </>
        ),
        className: `top-level menu-divider`,
      },
    ],
  },
  routerConfig: [
    {
      path: OneTableRoutePathEnum.FORM_MANAGEMENT,
      permissionKey: PermissionEnum.OT_TABLE_MANAGEMENT,
      View: FormManagementView,
    },
    {
      path: OneTableRoutePathEnum.MISSION_CENTER_TASK,
      permissionKey: PermissionEnum.PRODUCT_MENU_OT,
      View: MissionCenterTaskView,
    },
    {
      path: OneTableRoutePathEnum.MISSION_CENTER_FORM,
      permissionKey: PermissionEnum.PRODUCT_MENU_OT,
      View: MissionCenterFormView,
    },
    {
      path: OneTableRoutePathEnum.MISSION_CENTER_REVIEW,
      permissionKey: PermissionEnum.PRODUCT_MENU_OT,
      View: MissionCenterReviewView,
    },
    {
      path: OneTableRoutePathEnum.FORM_DATAPKG,
      permissionKey: PermissionEnum.PRODUCT_MENU_OT,
      View: FormDatapkgView,
    },
    {
      path: '/app-grid',
      permissionKey: PermissionEnum.PRODUCT_MENU_OT,
      url: '/dataapp/aside-preview/cda67a34-471e-4cdc-bdb9-135cdd819faa?feature=1&projectid=6c0326ac-f9de-4867-a36c-56d6b01042d8',
    },
  ],
};
