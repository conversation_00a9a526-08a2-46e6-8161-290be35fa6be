import { useController } from '@mdtBsComm/hooks/use-controller';
import { OneTableMissionCenterReviewModelBff } from '@mdtProMicroModules/pages/one-table-mission-center-review';
import {
  MissionCenterReview,
  MissionCenterReviewController,
} from '@mdtProMicroModules/pages-in-micro/one-table/pages/mission-center-review';

const PageMissionCenterReview = () => {
  const [controller] = useController(() => {
    const ctrl = new MissionCenterReviewController({
      Model: OneTableMissionCenterReviewModelBff,
    });
    return [ctrl, null];
  });

  return <MissionCenterReview controller={controller} />;
};

export default PageMissionCenterReview;
