.mdt-login-input-wrap {
  position: relative;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  height: 44px;
  padding: 1px;
  background: var(--mdt-login-input-bg-color);
  border-color: var(--mdt-login-input-border-color);
  border-style: solid;
  border-width: 1px;
  border-radius: 6px;

  &:hover:not(.focused) {
    background: var(--mdt-login-input-bg-hover-color);
  }

  &.focused {
    padding: 0;
    border-color: var(--mdt-login-input-focused-border-color);
    border-width: 2px;
  }

  &.error-state {
    padding: 0 !important;
    border-color: var(--mdt-login-input-error-border-color) !important;
    border-width: 2px !important;
  }
}

.mdt-login-input {
  flex-grow: 1;
  height: 100%;
  padding: 8px;
  color: var(--mdt-login-input-color);
  font-weight: 400;
  font-size: 14px;
  background: transparent;
  border: none;
  border-radius: 6px;
  outline: none !important;
  box-shadow: none !important;

  // stylelint-disable-next-line
  &::-moz-placeholder {
    color: var(--mdt-login-input-tip-color);
    font-size: 13px;
    opacity: 1;
  }

  // stylelint-disable-next-line
  &::-ms-input-placeholder {
    color: var(--mdt-login-input-tip-color);
    font-size: 13px;
  }

  // stylelint-disable-next-line
  &::-webkit-input-placeholder {
    color: var(--mdt-login-input-tip-color);
    font-size: 13px;
  }
}

.mdt-login-phone-input {
  .mdt-login-input {
    padding-left: 66px;
  }

  &::after {
    position: absolute;
    left: 58px;
    width: 1px;
    height: 100%;
    background: var(--mdt-login-input-border-color);
    content: '';
  }

  .mdt-login-overlay-wrap {
    position: absolute;
    top: 33px;
    left: -11px;
  }

  .mdt-login-flag-wrap {
    position: absolute;
    left: 10px;
    z-index: 2;
    cursor: pointer;

    .mdt-login-flag {
      display: flex;
      align-items: center;

      &::after {
        width: 0;
        height: 0;
        margin-top: 6px;
        margin-left: 6px;
        border-color: var(--mdt-login-input-tip-color) transparent transparent transparent;
        border-style: solid;
        border-width: 6px;
        content: '';
      }
    }
  }

  &.focused,
  &.error-state {
    &::after {
      left: 57px;
    }

    .mdt-login-flag-wrap {
      left: 9px;
    }
  }
}

.mdt-login-captcha-input {
  .mdt-login-link {
    position: absolute;
    right: 8px;
    z-index: 2;
  }

  .mdt-login-input {
    padding-right: 40%;
  }

  &.focused,
  &.error-state {
    .mdt-login-link {
      right: 7px;
    }
  }
}
