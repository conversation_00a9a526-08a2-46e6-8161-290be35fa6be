import { putTemplate, queryTemplate } from '@mdtApis/api/collector';
import {
  IPaginationTemplate,
  IRequestRequestConfig,
  IServerResponse,
  ITemplatePut,
  ITemplatePutData,
  ITemplateQuery,
} from './interfaces';

// 查询模板列表
export const queryTemplateAsync = async (config?: IRequestRequestConfig<ITemplateQuery>) => {
  return queryTemplate(config) as unknown as IServerResponse<IPaginationTemplate>;
};

// 查询模板列表
export const putTemplateAsync = async (
  template_uuid: string,
  data: ITemplatePutData,
  config?: IRequestRequestConfig<ITemplatePut>,
) => {
  return putTemplate(template_uuid, data, config) as unknown as IServerResponse<ITemplatePut>;
};
