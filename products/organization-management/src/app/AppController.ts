import { History } from 'history';
import { ModuleIdEnum } from '@mdtProComm/constants';
import { DatlasAppController } from '@mdtProMicroModules/datlas/app/DatlasAppController';
import { RouterController } from '../_util/RouterController';
import { UserPermissionController } from '../_util/UserPermissionController';
import i18n from '../languages';
import { AppSideMenuController } from './AppSideMenuController';

/**
 * 使用单例模式
 */
class AppController extends DatlasAppController<RouterController, UserPermissionController, AppSideMenuController> {
  private constructor(history: History) {
    super({ i18n });
    this.routerController = new RouterController(history, this);
  }

  /** 产品跳转(脚本注释) */

  protected async afterAuthSuccess() {
    await super.afterAuthSuccess();
    this.initAppReleation();
  }

  // 构造
  private initAppReleation() {
    // 初始化完必备信息后，构建用户的权限
    const upc = new UserPermissionController(this.getUserPermission()!, this.getPermissionController());
    this.userPermissionController = upc;
    this.appHeaderController = this.initAppHeader({
      defaultProduct: ModuleIdEnum.ORGANIZATION_MANAGEMENT,
      defaultModule: ModuleIdEnum.HOME,
      dynamicOpt: { enableMenuOm: false },
    });
    // 初始化路由
    this.routerController!.initRoutes();
    // 初始化左侧菜单
    this.appSideMenuController = new AppSideMenuController(this);
    this.appSideMenuController.initMenus();
  }
}

export { AppController };
