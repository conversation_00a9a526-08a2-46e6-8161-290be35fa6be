# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [1.48.23](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.48.22...@mdt/product-micro-modules@1.48.23) (2025-05-19)

**Note:** Version bump only for package @mdt/product-micro-modules

## [1.48.22](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.48.21...@mdt/product-micro-modules@1.48.22) (2025-05-14)

### Features

- ✨ 填报页支持左右拖动 ([89da371](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/89da3716211199fcdeb227458e30adbd73ef9a18))

## [1.48.21](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.48.20...@mdt/product-micro-modules@1.48.21) (2025-05-14)

### Bug Fixes

- 🐛 理由不能为空格 ([bf4eb70](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/bf4eb70180d1ce3f5b2280287fe633ae9f53a078))
- 🐛 数据包选择点击防止多次误触 ([d0df2a8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/d0df2a838f1a3944533e582013420b650dc96fff))
- 🐛 修复输入确认弹窗必填理由时,不填写按钮依旧转圈的问题 ([5b5b7d9](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/5b5b7d9f1d10f41bb0dd0f0e83dc0699e97139ab))

### Features

- ✨ 结束周期流程 ([df4f3cb](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/df4f3cb4653e85c9673c40cc8d7e97eb5d27733d))
- ✨ 增加内联脚本的内置变量 ([dc99ab1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/dc99ab1c1665b809bf25480479a83afc68e97b02))
- ✨ 增加移动端取消报表配置的操作 ([a30ced1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/a30ced12a4839df6e1186e8d8aac04e557cc3f07))

## [1.48.20](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.48.19...@mdt/product-micro-modules@1.48.20) (2025-04-27)

### Bug Fixes

- 🐛 重新整理退回和撤销展示列表的状态 ([c921811](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/c9218116568827bae1b8dff9ee905285dc771268))

## [1.48.19](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.48.18...@mdt/product-micro-modules@1.48.19) (2025-04-27)

### Bug Fixes

- 🐛 创建模板增加 internal_request 前缀 ([7125ef4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/7125ef4b9a3135120b9447a3f003bf481d36ba6f))

### Features

- ✨ 下发列表增加开关:控制取消和撤销任务的展示与否 ([2c27e37](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/2c27e37574da313747ce962826e0a7735eeb951b))
- ✨ 增加一表通批量上传列数据超出限制的警告处理 ([da2405d](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/da2405dc79a598d7f50493b6bfeefed1e2d7911c))

## [1.48.18](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.48.17...@mdt/product-micro-modules@1.48.18) (2025-04-23)

### Bug Fixes

- 🐛 身份组件 destroy 不对 app 做注销 ([c727ab4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/c727ab4640ffa414365788d08286187d2f761e91))

## [1.48.17](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.48.16...@mdt/product-micro-modules@1.48.17) (2025-04-22)

### Bug Fixes

- 🐛 统计修改 ([e8faef0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/e8faef0c61b8398c6f5355be326cdeecda1f4f70))

## [1.48.16](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.48.15...@mdt/product-micro-modules@1.48.16) (2025-04-21)

### Bug Fixes

- 🐛 更新表单不需要 process_type ([c0d85ca](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/c0d85cafe4109ea2198ad5f68daa29f553a6f10a))
- 🐛 文案错误 ([3badbae](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/3badbae2c4ce32ca2e77263519e530631f47ac26))
- 🐛 修复表单提交的状态判断问题和其他选项可能出现的 display 判定错误 ([77da861](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/77da86139cc659f3fe1014cfef69672b3ba39242))
- 🐛 再次优化 serviceTask ([685c81a](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/685c81a811be84544c2de4f3cfb17847828a5130))

### Features

- ✨ 增加流程和服务任务的请求配置 ([dbd1791](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/dbd17911ff61301400592cec38d81b6d5a370258))
- ✨ 增加数据分发统计 ([ad7416d](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/ad7416d0c5df250dfd5b333264f23330bc798a23))

## [1.48.15](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.48.14...@mdt/product-micro-modules@1.48.15) (2025-04-02)

### Bug Fixes

- 🐛 修复一表通若干问题 ([acfad8b](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/acfad8b6f9e6fabc9c633b6effd2fe79f37a4f14))
- 类型错误 ([06a08b1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/06a08b1d6cf23cfdb20bab8fd5b24558dbab2530))
- dm open error ([fd305bb](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/fd305bb25c5a0763bb2e49cd0c74d5865662caa4))

### Features

- ✨ 报表授权 ([b8b9952](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/b8b9952c41d53aabaaba9e70123d55579e50cf57))
- ✨ 部门选人组件支持无部门用户的搜索 ([bb91582](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/bb915825e7890d9d0a96906bf472d34a6cba8d26))
- ✨ 增加一表通在流程引擎里面的过滤配置 ([0431c17](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/0431c17d4a47f1ff1ef1f9e05dbc1bc0bdbbdd65))

## [1.48.14](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.48.13...@mdt/product-micro-modules@1.48.14) (2025-04-01)

### Bug Fixes

- 🐛 评论若干问题修复 ([a0f72d4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/a0f72d4357ebaa79828288b3448ec337885edec5))

## [1.48.13](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.48.12...@mdt/product-micro-modules@1.48.13) (2025-04-01)

### Bug Fixes

- 🐛 取消下发修改数据失败后不执行后续操作 ([7157091](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/7157091a4178f007eff0a88369cd81be7f2a67df))
- 🐛 下载数据不带筛选条件会把所有数据全部下载的问题 ([d5c9a9a](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/d5c9a9a14e2990b7ea81d547545bc89a51e307b5))
- 🐛 修复批量填报无法更新数据的问题 ([4db8ec2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/4db8ec2caf5862ab7b582d4dcf2dbadd3522f4f2))

### Features

- ✨ 数据包列表默认排序保证唯一性 ([1fe4ca7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/1fe4ca71054ac49423fd566a7f7836827265d5bc))
- ✨ 增加周期最小时间限制 ([41db669](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/41db669575599b2bd74eb6dd0ba32a1dfd9cabe4))

## [1.48.12](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.48.11...@mdt/product-micro-modules@1.48.12) (2025-03-31)

**Note:** Version bump only for package @mdt/product-micro-modules

## [1.48.11](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.48.10...@mdt/product-micro-modules@1.48.11) (2025-03-31)

### Bug Fixes

- 🐛 保存后增加计算中间等待状态 ([1193219](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/1193219d17c1c01cd2d18e6e885cf5ee2ca3efaf))

### Features

- ✨ 增加评论模块 ([8646ef8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/8646ef89f99bb680b6627c87280820546d2ecf46))

## [1.48.10](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.48.9...@mdt/product-micro-modules@1.48.10) (2025-03-27)

### Bug Fixes

- 🐛 顶栏身份获取用户信息通过 app 传递 ([e17cd61](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/e17cd618ff9820cd4184b1b782f14ded81f78ced))
- 更新数据时不改变 status 状态 ([2bcf40f](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/2bcf40fbda93603f94a6dffab21e1d0a67eaad58))

## [1.48.9](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.48.8...@mdt/product-micro-modules@1.48.9) (2025-03-27)

### Features

- ✨ 前端主导跳转默认身份 ([9883a57](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/9883a575446b9089511ec4a78477563062fb4689))

## [1.48.8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.48.7...@mdt/product-micro-modules@1.48.8) (2025-03-27)

### Features

- ✨ 扩展 column 展示位置的配置项 ([2d8f96b](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/2d8f96b6aeef9dd9a08098f5d74b5174962a23cc))
- ✨ 压缩图片 link 转为本地 static ([f68d0df](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/f68d0dfef6a7d639f1b7ca4b77ced923718340f2))
- ✨ 增加对于 rc 的处理 ([e36db68](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/e36db68165693f2205f7a3b29829b28d23cf7e26))
- ✨ 增加身份指定跳转,增加身份默认偏好,增加默认身份标识 ([6fd5f9f](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/6fd5f9f5759256aab83e8dbe1e98e8e452f080af))

## [1.48.7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.48.6...@mdt/product-micro-modules@1.48.7) (2025-03-19)

### Bug Fixes

- 拓展可删除 url 上冗余值 ([c0bbb2c](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/c0bbb2cc8ab04c2f3d38d6c3c537677595ef6acc))

## [1.48.6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.48.5...@mdt/product-micro-modules@1.48.6) (2025-03-18)

### Bug Fixes

- 🐛 修复详情 h5 打开报错 ([cf5a46e](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/cf5a46ee4230ecea6d7df2f60c9e51e40d09c615))

## [1.48.5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.48.4...@mdt/product-micro-modules@1.48.5) (2025-03-13)

### Features

- ✨ 标准一表通增加报表归属部门 ([5d4a54a](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/5d4a54a8ac811791a9c49292c204c473f05ac9e2))

## [1.48.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.48.3...@mdt/product-micro-modules@1.48.4) (2025-03-10)

### Bug Fixes

- 🐛 build error ([e6a7959](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/e6a79598117b3e74179505a1302f1058a0081126))
- 遗漏列类型 string ([e751a3b](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/e751a3b792a91e8c627028c74f2ad8ae0044f050))

### Features

- ✨ 流程引擎服务任务函数参数追加 ioMapping 低码配置 ([caf8f1e](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/caf8f1e624c91f08d04a87877ffcfd19ab50791c))
- ✨ 一表通 h5 增加适配配置项 ([676889e](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/676889ee401c9e23b84b56c81381e847c98ffa03))
- ✨ 增加查看包含下级全部数据配置 ([07ddc04](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/07ddc04fa27ab0e95917af93f65c2c64a60aee68))
- ✨ 支持流程详情撤销功能 ([b5b9ddb](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/b5b9ddb164f7de7eb4fb9636b6c50b8097fe43ce))

## [1.48.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.48.2...@mdt/product-micro-modules@1.48.3) (2025-02-27)

### Bug Fixes

- 🐛 修复 h5 样式引发的问题 ([9bd6f06](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/9bd6f06f39358d403c9961d162f56f0b62a439fc))
- 🐛 formily 响应器变量增加联想, 修复响应器只能引用部分字段问题, 优化字段依赖响应 ([168c6d7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/168c6d764aca11e97ca0775562431cf636e7e743))

### Features

- ✨ 发起人信息支持 nickname 展示 ([8c0cf24](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/8c0cf243fc41330309f0f5bda6a1ecf1b134d86b))
- 添加执行上下文 ([1c38157](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/1c3815730286045981e44ab870b09b48b2742a2a))

## [1.48.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.48.1...@mdt/product-micro-modules@1.48.2) (2025-02-26)

### Bug Fixes

- 🐛 fix bugs ([e7fb704](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/e7fb704003308a8794cc56fad9857497033a7e58))
- workflow custom varible ([ddf262c](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/ddf262c6478a66aa522ea4099c5c6e9f65c688b6))

## [1.48.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.48.0...@mdt/product-micro-modules@1.48.1) (2025-02-24)

### Bug Fixes

- one-table error ([be8c402](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/be8c40221a2facd6e07b7784729f58fd3d35d925))

# [1.48.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.47.6...@mdt/product-micro-modules@1.48.0) (2025-02-24)

### Bug Fixes

- 🐛 机构管理部门信息更新修复 ([759f2ef](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/759f2ef4a3565d2d18a32285c5e9cb728dfdbec9))
- 修正 schema 类型 bool->boolean ([200583d](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/200583dd40ad17b9c1b36cb2c33e28f2fb49f97b))

### Features

- 支持指定 wfRootForm ([d93fca7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/d93fca78b5aa2331516e0062811343b542f26f61))
- ✨ NodeData 校验 DataKey 不通过 ,给当前 nodeData 赋值空对象 ([2aedf93](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/2aedf93d961fd5c6c03182de15925686c20b2c77))
- ✨ 响应器规则添加 ([194c80e](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/194c80ead2f752e278dd10b36feedc311268bbde))
- ✨ 增加 map-api-url ([2bfb97d](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/2bfb97d5867b7d22d4d498a3ffa50e79189241bf))
- ✨ 流程详情增加昵称的显示替换 ([449c9d8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/449c9d8121ed17aeeac5515563e80b2f9a0f152f))
- bff suport params encode ([d9648b2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/d9648b2cf9aa44b7c4f1a49c82bb5fe772cfc354))

## [1.47.6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.47.5...@mdt/product-micro-modules@1.47.6) (2025-02-17)

### Bug Fixes

- 🐛 FlowInfo 和 workflowDetail 增加 ToggleSectionList 高度为 200 ([800a4b8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/800a4b819d232530fa4dcae80d1ea9c1608cf020))
- 🐛 一表通移动端统计修复 ([379fbb9](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/379fbb9cb9bb7bc0121edc7a96b4abf7858bd95c))
- 🐛 修复内部 api 请求部门时,默认选择全部部门预览无数据的情况 ([39063b8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/39063b87098fdd2412ea784a60d10050a5270c87))
- bug ([d42d967](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/d42d9678e1799573b2d6cc39aaabae26ce55df30))

### Features

- ✨ 一表通的预览编辑数据要跟随外部的筛选条件和排序规则 ([a32f113](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/a32f113bdd16e34e90b4408d0120d3cd7194d01c))
- ✨ 个人数据权限共享 ([6bd7811](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/6bd78117bffb5be142533603322d6fcb1a09de91))
- ✨ 依赖设置增加代理配置 ([ed65093](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/ed6509374ebad0fe2177ea00a25cd608e038283f))
- ✨ 增加指定分页加载功能 ([4cbbfcb](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/4cbbfcbb13e1e3170dc9d4c143ca5ba6fa4fd6d2))
- ✨ 树形结构增加设置默认展开与否属性 ([ed31e96](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/ed31e96168d297fb8e3423b054af53dddf24f5d2))
- looptask support more params ([29aeb5c](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/29aeb5c8bd7e655e2779e9256415ab614a46b8fe))

## [1.47.5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.47.4...@mdt/product-micro-modules@1.47.5) (2025-01-23)

### Bug Fixes

- 🐛 修复只输入用户时查询带上组织的问题 ([cccd2e2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/cccd2e223b029edbd631405f6bef50fa57e967dd))
- 🐛 修复需要数据源选择值的部分组件缺失问题 ([5d4fb67](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/5d4fb67bfa21b2cd981b11642a737fd29e7fd083))
- 🐛 选人功能左宽右窄 ([0fcd51f](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/0fcd51f14eff1b39ade4396fe576d6418caa2609))

### Features

- ✨ 修改导出 pdf 支持 maptalks 的 canvas ([920419a](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/920419a29f8604fef42c190e9bfd3e19d787cccb))
- ✨ 取消,退回,转交时数据转移 ([95f376d](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/95f376d2a485c60c93f78d216ff67042a0405126))
- ✨ 增加外部请求设置服务器代理请求和 cookie 携带 ([46bfc98](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/46bfc98a344086c015ff163d3d3a6d43c6a5ec2a))
- ✨ 表单提交校验增加错误 toast 提示 ([b5ce7e3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/b5ce7e3b4ef1d87ac4c0f3ca4313b228f8e2d05c))
- ✨ 选人样式优化, 修复切换部门导致右侧部门展示丢失问题, 部门增加层级展示 ([665b064](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/665b064e88857298a958268248347035b40a15a4))

## [1.47.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.47.3...@mdt/product-micro-modules@1.47.4) (2025-01-16)

### Bug Fixes

- 修复数据包列类型修改失败 ([ebcab49](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/ebcab491cb9a5f1b7eff36e8aed0c5b2921b0d99))
- 🐛 toastContainer 修复 ([835b94b](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/835b94bfce599b04a1f5df7668a2daf0af0016d7))
- 🐛 修复函数组件包裹需要额外加 span 标签的问题 ([f62c3fb](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/f62c3fbdb15b4ac2495e40b3a268619b5b368e27))
- 🐛 修改 displayType 到属性值 ,删除从 field 到 props 的逻辑处理 ([65926b0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/65926b0839bc2c3e35e6c71682289e99b5f59383))
- 🐛 数据包预览时不显示排序 ([8c73274](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/8c732740312e034b643d328b72974d59aef3f9cd))

### Features

- ✨ ScriptTask 引入脚本编辑器 ([b0efea8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/b0efea87a5ef7dda8b772576a207585134094edd))
- ✨ 优化无数据源提示信息 ([6dd19c7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/6dd19c7aedb75f87cb14df425e18ab2aa64d6903))
- ✨ 地理列增加值模式,支持 wkb,坐标,地址, 支持预览模式地图配置 ([8e9f3aa](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/8e9f3aaf8daa0e38cb5bec9b62a0dd1d83c6336b))
- ✨ 支持不同的预览界面的展现形式 ([a25c50f](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/a25c50f7dbfb1a5f347028259629e1eff86e32d6))
- ✨ 点数据点位变化支持地址跟随变化, 组件阅读态支持解析地址展示 ([7e4b775](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/7e4b7756f8b8b836d36d09b2ed0566dc4dfb7835))
- ✨ 选人增加部门模糊搜索 ([88c6e1b](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/88c6e1b2814bb788dfcdeb437ed2319797845688))

## [1.47.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.47.2...@mdt/product-micro-modules@1.47.3) (2025-01-09)

### Bug Fixes

- 🐛 修复遗漏资源释放 ([e9dae82](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/e9dae822c19ffc8160794a4b54edaff9f3dd44a3))
- 🐛 修改 excel 表格颜色 ([9af5051](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/9af50510bea0fff36f8029b0504a8e2918a60710))
- 🐛 暗色下的详情标题颜色修复 ([f460ae2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/f460ae2c8faa95f551c2049fdbdcb061bca06b48))

### Features

- ✨ sso 页脚年份更新 ([6c767a9](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/6c767a996992d4a0fe2ca3634d52515029a871d2))
- ✨ 一表通批量上传 excel 模板,丰富描述内容和支持动态解析 field ([fe94e77](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/fe94e77fe890abb5f641559e775ac874c3f35921))
- ✨ 导出 excel 头根据必填和类型做颜色区分 ([3348159](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/3348159ad957036eb44021c858819551c00e6db5))
- ✨ 流程引擎选择框可筛选 ([ee4b414](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/ee4b414ab2d1fea3d98ee13f2ae030297b87a499))

## [1.47.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.47.1...@mdt/product-micro-modules@1.47.2) (2025-01-07)

### Bug Fixes

- 🐛 数据预览 ([3349e1a](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/3349e1a8b61fb33abc1c29d8d8ddcc080195e619))

## [1.47.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.47.0...@mdt/product-micro-modules@1.47.1) (2025-01-06)

### Bug Fixes

- 🐛 fix issues ([55032b3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/55032b30bada0e0608f6c24a4fb444a129834d71))
- 🐛 修复展示格式输入后又删除导致接口报错的问题 ([4071246](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/4071246111415fdfbe389d4f010e29c35d83ad35))
- 🐛 修复批量填报市民组件无法正确识别的问题 ([a1ff44e](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/a1ff44ee7d8a0224062faea396aa42701a77c7cc))
- 🐛 修复组件问题 ([219f465](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/219f4655e8ef7e3e81a91c2a033ae1017bc91862))
- 🐛 修复课件数据设置选中机构没有出现在已选列表内的问题 ([c3386eb](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/c3386eb4dfbe6ee17f4d714234b4e40cada51253))
- 🐛 数据包编辑带有 format 字段时清除 format 传入 null ([012affc](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/012affcdbedc6fbc017ed66451a79d415d8dc673))
- 🐛 数据源跟随权限 ([c324bc9](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/c324bc9969c8fe7ee2478fd2ed2edbc7407fc808))
- 🐛 系统联系人和字段联系人组件区分开 ([2dfa624](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/2dfa624e91bf1631138f83cea8ff16306e20f34a))
- 🐛 联想填空国际化,修改错误提示的显示 ([eb4762b](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/eb4762ba115d411563860c7ea46758d166854e71))
- 🐛 联系人 contain 只提取第一个值 ([4e7c0cd](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/4e7c0cd6da369de070bdb845e7e96f6efeaab5da))
- 🐛 联系人处理多种数据,修复组件崩溃,增加操作 ([90b6250](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/90b62500e10e5428e787905a00c6fc312b7b2b95))
- 🐛 表单设计预览模式不允许设置表单 ([3ae500c](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/3ae500cf0d27e7a15f0b72aeea7199f62ca9972d))
- 🐛 还原 array_str 组件 ([d389dbe](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/d389dbe21c9fb8d4de2638fa8064bf4f51c35004))
- 🐛 还原 column_action 的的 transform 处理,增加 json 类型的过滤 ([54d3572](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/54d3572882c9742b89e65fb2fbbed7c8e57a1efa))

### Features

- ✨ input 增加数据源和数据源输入限制,增加错误提示 ([ef9ffbd](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/ef9ffbd605a651dce53ea93b61a578b76c039eb8))
- ✨ workflow detail 支持参数修改文案 ([5671a1b](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/5671a1bbf3d30f6c7ff5aba1f998f2361f1b60b0))
- ✨ 一表通文件上传校验数据 ([1696f17](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/1696f174f6b53727a2c58272f0ca8d3bd7fe608f))
- ✨ 不同的联系人类型对应了不同的操作(后端限制) ([d9a7ed5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/d9a7ed5f7aab866fd7011ebda669ba48a97bb273))
- ✨ 合并数据市场的 rich_description 和 description ([a51469b](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/a51469b48c11cbc17e6ffe1c38ed7964f8020c61))
- ✨ 提交按钮增加显示条件处理 ([a6ea52d](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/a6ea52d0f19b1a8fc67338331def30700d9a7f50))
- ✨ 联想填空增加数据限制和错误提示 ([126b5c8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/126b5c87cb2feef16c4c548a4b8c62bebcbd13cd))
- ✨ 联系人显隐组件和外部 props 保持同步 ([32b20b2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/32b20b24943e141a432850ae808c24612cf88510))

# [1.47.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.46.10...@mdt/product-micro-modules@1.47.0) (2024-12-23)

### Bug Fixes

- 🐛 pdf 导出拉伸修复 ([41661db](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/41661dbc0bc49c1017f95739b51dda6e00b1d6f8))
- 🐛 修复周期设置创建的校验问题 ([4d0b885](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/4d0b885921a6b79734761aeada77da1bd9581dab))
- 🐛 修复在详情以报表新建关闭时,造成卡片无法点击到崩溃的问题 ([c58dbcf](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/c58dbcf5b796c394323283bb019cacaa6289bbde))
- 🐛 修复用户依赖部门的时候 传递多余值的问题 ([05ed91e](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/05ed91e50ad027b4f679ad7a22a11ed0c56bc319))
- 🐛 升级 rc-tree 类型错误修复 ([e65e864](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/e65e864f9acdc0ba34dd3315d176db45c4ca4a4a))
- 🐛 处理 ilike 的值 ([0d7b58b](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/0d7b58bd36b187efe2ceb89e3c8f5d6620c757d6))
- 🐛 数据包筛选位置调整 ([1ca727f](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/1ca727f9046d9705c33ea57fe741dc0a05556529))
- 🐛 高级筛选转换方法 onetable 自己实现 ([da40cba](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/da40cbabb8844212b704457b6b97021c14101768))
- props -> selectProps ([0de949c](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/0de949c441d3d6957c01316634ba92c18972e2ba))
- 数据源权限跟随 ([1d59560](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/1d595603faf718fc28d485a36ffd93177ee46b18))

### Features

- ✨ pdf 导出增加参数配置 ([654170e](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/654170e9259f25e4ae6679de03bc41dae0456d4d))
- ✨ 我的数据基本信息增加描述 ([df75654](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/df75654851cac8b924e92fc90dfbb6f7fca92c88))
- ✨ 数据市场增加描述 ([c26a43b](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/c26a43bf18f7f3601e72815fe1820cf69865b772))
- ✨ 数据市场增加时间筛选 ([39e7d1f](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/39e7d1f036bd0b2bd4ad4070e4bc150de2ffea1e))
- ✨ 数据源加载 loading 态 ([4cac800](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/4cac80086a06d6660f1ee27a050e62ef71cdba4b))
- 数据包 DDL 设置 ([543c22e](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/543c22e19976e5ca690e1d581de808ac74d23a13))

## [1.46.10](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.46.9...@mdt/product-micro-modules@1.46.10) (2024-12-16)

### Bug Fixes

- 🐛 修复预览单行填报快捷键退回导致崩溃的问题 ([a6b3239](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/a6b3239f529e72dca61700a332fd11c2db372657))
- 🐛 一表通筛选器增加为空操作 ([d506e03](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/d506e03e823db77a7ed4160742b0b26ca996f722))
- 🐛 状态列去掉 is 操作 ([a272cda](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/a272cda27da32f38fd700d636825e3dfd704d3cc))

## [1.46.9](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.46.8...@mdt/product-micro-modules@1.46.9) (2024-12-16)

### Features

- ✨ 升级 amis 到 6.10.0 ([f6ab128](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/f6ab128682e3fb93f1ce41d31fe9d9fd710d2c39))

## [1.46.8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.46.7...@mdt/product-micro-modules@1.46.8) (2024-12-16)

### Bug Fixes

- 🐛 不实时显示的问题 ([4711e44](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/4711e44250397bca509bcac9067e793241106db8))
- 🐛 草稿箱列表排序修复 ([9888abe](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/9888abe2b2816c694258beede2556df6211b3983))
- 🐛 任务待处理修复红点不常亮问题,总数抖动问题 ([63d8202](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/63d82020b8860104fbdd9a4bca9a56720ba08d0b))
- 🐛 新建用户空角色报错问题 ([5f4b085](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/5f4b0852605d727657cff06507294c2945b33396))
- 🐛 修复点击填完了页面无法更新 loading 状态的 bug ([90dbea6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/90dbea6065f9eb5473fa4dfeea6a8bd7524a642a))
- 🐛 修复如果是中间或低层部门无法展示成树结构的问题 ([608c561](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/608c56119649f68e23075968d67aa2ff9d7cd19b))
- 🐛 修复下发新数据不展示高级筛选的问题 ([b2c5081](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/b2c5081e2127106fe0ed52d7a28115394e658469))
- 🐛 修复自增表格在一表通内无法创建正确表单的问题 ([8002e00](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/8002e007f14022cc4c11cb0b51c4cbeaa2575f30))
- 🐛 修复 pdf 图片自适应造成的拉伸情况 ([958e268](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/958e268c8e6f7afcfb66fee422e9b5697b9bb705))
- 🐛 引用路径 tslint 报错问题 ([113f638](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/113f638e20ebc11aec2c4a9e178e39483f5f592c))

### Features

- ✨ 单条预览支持导出 pdf ([0e4694c](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/0e4694c59db893b1ab5445b581ecfbd61b2441e3))
- ✨ 加强任务中心待处理的提示 ([d88b770](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/d88b770e4e854f2514ac8e13d26ce373d3e82eb9))
- ✨ 内部 api 增加本部门 本部门以下 本部门以下所有 ([7ea3073](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/7ea3073a8119443e4d9a1522b595f28abbc67729))
- ✨ 显隐设置增加表达式设置,同时兼容之前 condition 模式 ([c726e8c](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/c726e8ccf2fb42d9b78af534158702ab9c16b542))
- ✨ 移动端一表通增加报表管理的权限校验 ([9f18b59](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/9f18b592ac37bbebd4556533553cf2e2de97d592))

## [1.46.7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.46.6...@mdt/product-micro-modules@1.46.7) (2024-12-03)

### Bug Fixes

- 🐛 解决数字为 0 存成空值的情况 ([85afd79](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/85afd795f4d9da905a17253649f667b0106f0883))
- 🐛 小数 limit 拼接 schema 报错修复 ([7cf1da1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/7cf1da1ff160467561dd9df8783bc68d5a0c925f))
- 🐛 增加列类型标识,用户后端筛选查找 ([f664107](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/f66410799afb03148a2af515bcc65d3143d7fccc))
- 🐛 fix user select ([02ba655](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/02ba655d5e3443334ff4d283493bccf0ed29b543))

## [1.46.6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.46.5...@mdt/product-micro-modules@1.46.6) (2024-12-02)

### Bug Fixes

- 🐛 修复多种筛选在一起冲突的问题 ([db0634d](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/db0634d5c2a8220ec014a570758ff13b83427fcf))

## [1.46.5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.46.4...@mdt/product-micro-modules@1.46.5) (2024-12-02)

### Features

- ✨ 增加 datetime 筛选 ([604d558](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/604d558fea018e7a92e1e7aecd87373be1da786a))

## [1.46.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.46.1...@mdt/product-micro-modules@1.46.4) (2024-12-02)

### Bug Fixes

- 🐛 密码不能复制粘贴 ([ccd7ba4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/ccd7ba4f014f0a2ae37d7461ac6a42baba406e2c))
- 🐛 删除测试代码 ([8002c3d](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/8002c3dfe426db08a670aafad72c00d37bf7d87b))
- 🐛 细分未提交 ([5c60e6b](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/5c60e6bfe811aa745113b8c902ba5dbdc3b2ca01))
- 🐛 下发图谱降序排列 ([57b06e9](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/57b06e9ba1e1dc44cdcac5bc2d7e02b2066eec65))
- 🐛 修复滚动条演示 ([34ba12f](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/34ba12f4c7b7e78829f69559469fb0b44ada2629))
- 🐛 修复 this 错误 ([2c191cf](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/2c191cf039a9f9a9a874ed79c0ecf20ac05d8f3b))
- 🐛 选人样式修复 ([8a62341](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/8a623411192411b505964d9befdaea1b245645e5))
- 🐛 依赖配置增加依赖空保留当前值的配置项 ([16bd96d](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/16bd96dacf012cf653638b4951cf24d8584306b1))

### Features

- ✨ 报表筛选功能 ([4e04917](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/4e0491788f7dd7097acdcf8d9597c4514aa197b0))
- ✨ 外部跳转增加表单变量注入 ([f2cff78](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/f2cff78cf8e4d72e30c98ad9d16f617676dd2d01))
- ✨ 增加报表的数量展示 ([52229a7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/52229a7bb80fe6d4b5ec4b22983e877d00d8ea4d))
- ✨ 周期流程配置增加自动继承上一周期数据 ([c841304](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/c8413042f8bdcb1bffdcc7318008531bf3d60207))
- ✨ 追加选择部门人的快捷方式 ([eebf5b1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/eebf5b149d858a3c2949f3b42e2a24d961c49bb1))

## [1.46.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.46.2...@mdt/product-micro-modules@1.46.3) (2024-12-02)

### Bug Fixes

- 🐛 密码不能复制粘贴 ([f6d8d13](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/f6d8d13f9b3be39c20cff766a7dd829a7d1f04eb))
- 🐛 细分未提交 ([7bcaec0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/7bcaec062d3bcace189f371adeb5c8b1d2635ce3))
- 🐛 修复滚动条演示 ([36cde39](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/36cde39b832a30d81b7eb43726f07e068d787554))

### Features

- ✨ 报表筛选功能 ([8919ca7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/8919ca79f49c505f1ded7211ad517a6ad8ee6025))
- ✨ 追加选择部门人的快捷方式 ([c05be22](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/c05be221fe335b6d62a99cb6eb024e78c41c245e))

## [1.46.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.46.1...@mdt/product-micro-modules@1.46.2) (2024-12-02)

### Bug Fixes

- 🐛 下发图谱降序排列 ([bd6b40a](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/bd6b40a51d1ea8bdcdb8cb4645be1373ad5af2aa))
- 🐛 修复 this 错误 ([2c191cf](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/2c191cf039a9f9a9a874ed79c0ecf20ac05d8f3b))
- 🐛 选人样式修复 ([f6a1680](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/f6a16804f7ec63c94ce7d5c3ceb508716465fd10))
- 🐛 依赖配置增加依赖空保留当前值的配置项 ([d661cd8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/d661cd85708eb8c49c9540137df0e542fd7286b0))

### Features

- ✨ 外部跳转增加表单变量注入 ([e2b30ff](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/e2b30ff54cb0997b054fb45701c6e6d7f5ab3b53))
- ✨ 周期流程配置增加自动继承上一周期数据 ([cc8ecf1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/cc8ecf19319c346ed43c242757e3304bb4108a52))

## [1.46.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.46.0...@mdt/product-micro-modules@1.46.1) (2024-11-26)

**Note:** Version bump only for package @mdt/product-micro-modules

# [1.46.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.45.5...@mdt/product-micro-modules@1.46.0) (2024-11-26)

### Bug Fixes

- 🐛 优化数据包定时器 ([75a246a](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/75a246a42c439daf7906d3bc349d5ca7e0f06de0))
- 🐛 修改 bug ([24e60b0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/24e60b08e2d42df5e7df924e9275a219ff2af5d8))
- 🐛 机构管理可查看禁用用户，机构样式工具统一改为方法调用, 修复新建部门没有刷新下拉部门组件的问题 ([54b5879](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/54b5879217a23e9ac3d035d09cd22fc8e0f6daa6))
- 🐛 调整实例排序 ([4d27a38](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/4d27a385c5bfd2772886872278c1bbb96a8e4e2f))
- 🐛 转交 approval ([84ae348](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/84ae3489a6aa7ec732a9f00d8afcb7c8cd43a1ec))
- 🐛 驳回意见改为必填 ([9f6486b](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/9f6486b6c833150ac2434ed5588e0349f7fb7449))

### Features

- ✨ h5 适配 ([4675022](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/46750223a044f02f5c3e93cb70ec59f14480197f))
- ✨ 优化数据包定时设置 ([8b2ecff](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/8b2ecff7e2f06c775b4d804425527101eb7196b0))
- ✨ 数据包创建表单时间类型修正 ([d089c77](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/d089c7774b1a3cd755b115ee1bcefabb7c3a7cb3))
- ✨ 时间类型存储切换 ok ([5a2e4b0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/5a2e4b04b10212f948f488c61e8138e6cc8e71e0))
- ✨ 流程引擎提交后跳转增加 h5 跳转 ([e6af8b1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/e6af8b175f2e11fece9e360fba5f0cabc0f7d94b))
- ✨ 说明文字支持更多类型 ([86d5c94](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/86d5c94217edabe5444cccdaa83a4a88d62936b4))
- 下发图谱表格展示 ([3acb549](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/3acb549ab6fe0df02c06cd13839a586eb9b976cb))
- 下发图谱表格展示优化 ([7df720b](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/7df720b6bc68ac7de230e293351cc8c999f9c027))
- 下发图谱表格展示优化 ([9d78389](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/9d783893ac8180503b5454925e9254d3d76316bd))

## [1.45.5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.45.4...@mdt/product-micro-modules@1.45.5) (2024-11-19)

### Bug Fixes

- 🐛 下发管理任务状态在非审批时增加退回的展示 ([9e17fd6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/9e17fd6e3580193d0103ef3f3a19598662034999))

## [1.45.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.45.3...@mdt/product-micro-modules@1.45.4) (2024-11-15)

### Bug Fixes

- 🐛 修复用例错误 ([86ddceb](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/86ddceb68cd8e1e6de2672961b7cdc53d3e31996))
- 🐛 兼容很老的 amis ([1522866](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/1522866de67a9289f31eec223ac614b3ea02bb7b))
- 🐛 预览 pc 宽度固定 ([c6e6588](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/c6e6588b68b171df00cf04bad71d0b2f78c4d561))

## [1.45.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.45.2...@mdt/product-micro-modules@1.45.3) (2024-11-14)

### Bug Fixes

- 🐛 amis request error ([1cf5294](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/1cf529497cd0681279c443fdb2fc045f0045f529))

## [1.45.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.45.1...@mdt/product-micro-modules@1.45.2) (2024-11-14)

### Bug Fixes

- 🐛 bpmn 分配用户 id undefined 问题 ([8f49e0c](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/8f49e0c468fb3d24dd7fe0c8a47d59c42a7389c5))
- 🐛 h5 单行删除增加二次确认 ([84ba2a1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/84ba2a1ef6ff89cb35d8138ac55f50ba9f8975b9))
- 🐛 h5 周期展示样式 ([1a6face](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/1a6face04a49346f5ae376b3512ca4c389d301a5))
- 🐛 修复从结果页填报下发报错的问题 ([ab122cd](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/ab122cd4cdb7800525656a67e43249a9b14b1602))
- 🐛 修复逻辑 ([a99430d](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/a99430df1b3736d3f0c2a274b6c6548f623cc312))
- 🐛 修复预览切换编辑没有定位到当前数据的 bug ([b331e8e](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/b331e8ea1146819771ed31856f8aa91bd3c92642))
- 🐛 发起界面的依赖关系需要根据 settingValues 计算 ([870e9cf](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/870e9cf5136db99945e89b043d0884069e0b6780))
- 🐛 移动端流程引擎统计数值跟随配置项 spec ([d1186d2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/d1186d2135dc93ca77e9b3ffbb318ae421a7cf5d))
- 🐛 调整发起预览表单的展示逻辑 ([f1d745e](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/f1d745e4caf481f7a5ade3d4a8975e481be8025f))
- 🐛 配置命名调整 ([48546ca](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/48546ca6962c8408c3e16aa0c7d963180e9f6464))
- 状态计算错误 ([d346db3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/d346db39b1c010c1a5321a467f7e83f9a753af5b))

### Features

- 周期 ([1765325](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/176532579affae5615001e5c079d5a9f383a8b57))
- ✨ 下载可配置及周期 ([362e304](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/362e3047145f6ca4316f4d520f289fcd6aa38ace))
- ✨ 周期 Ok ([ea4c848](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/ea4c8481c325b3d618ce7b5d9216dff1f370c335))
- ✨ 周期改版 ([901090d](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/901090db719c8b5efe07849b13e8e46057ea029b))
- ✨ 增加单条数据的预览 ([029b97a](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/029b97a539a52afb1d49a3fb5b116038c0f11c41))
- ✨ 增加填报数据的筛选器和下载功能的配置 ([fad8e7f](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/fad8e7f13262e5dea9977528d5209fcc1c78e108))
- ✨ 用户信息增加身份切换,h5 增加身份切换 ([8755bd3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/8755bd3c7aa73b640063ccac067741428e459bec))
- ✨ 用户列表增加部门依赖配置 ([d849e9a](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/d849e9abc33d3db6fe24cda15e35ad1a9ee743a6))
- 下载可配置 ([70a9474](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/70a94742bca08880fb3f1d442285d4cdbccdb392))

## [1.45.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.45.0...@mdt/product-micro-modules@1.45.1) (2024-11-07)

### Bug Fixes

- 🐛 修复选自己的时候写死 id 为 userId 的问题 ([cffca3e](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/cffca3e21187e8d9baae2165c54a5464fb631ef3))
- 🐛 h5 头部缩进为两行 不影响整体高度 ([6be4701](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/6be4701d3b0986ccacf0fd02c1ee111289006eb9))

### Features

- ✨ 国际化补充 ,sso 文案图片调整 ([d83df95](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/d83df95f19b841b509eb0f9ddb55d6c0de63030a))
- ✨ 移动端增加点击编辑和删除单条数据 ([3266c17](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/3266c17bb0284b5618d8c0ea8a746c46ac97c31c))
- ✨ 增加昵称列展示 ([07d0d72](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/07d0d72503b0eafc7f7ee51f7af701ce8c3ddd11))

# [1.45.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.44.12...@mdt/product-micro-modules@1.45.0) (2024-11-05)

### Bug Fixes

- css 优化 ([61c49ac](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/61c49acefa301a6cdc26c559bf00e3859b6cb402))
- 图谱优化 ([deb46a4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/deb46a4e54b0cdad11647e04e0af63fc88d1f6d6))
- 🐛 修复若干问题 ([1d05853](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/1d05853d286b40f816364540d27abc694b6254cb))

### Features

- 流程引擎可配置 ([7a6eef4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/7a6eef49849859e81ebad9a9c21bc897f6176241))

## [1.44.12](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.44.11...@mdt/product-micro-modules@1.44.12) (2024-11-04)

### Bug Fixes

- 兼容之前 url ([24a8207](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/24a8207815eabcacbcf7730494567191f7e56c7c))
- 🐛 一表通首页卡片图片增加主题切换 ([1d74ba9](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/1d74ba94899fc04b840aef1f04367409c0f60358))
- 🐛 修复详情单独打开循环引用的问题 ([60f4d38](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/60f4d387fe59a652a732e7375a61f37a145f605b))
- 🐛 修改催办的链接到 h5 ([d881ed7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/d881ed78e29e0a06c7a6edf756c23c9464ab8832))
- 🐛 数据市场的反馈调整 ([732d857](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/732d8577bf8cc2f2874e142430897113e995c582))
- 修复 h5 传参丢失 ([ad483a1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/ad483a16476e569fab75dbf34a8b24d01ff39910))

### Features

- ✨ 移动端 ([b20dbf7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/b20dbf73e5681d398686f1f84ae049eabacbfc42))
- 下发图谱 ([eca75b0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/eca75b07551be3cc55b1c3327fe278ef6949bd53))

## [1.44.11](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.44.10...@mdt/product-micro-modules@1.44.11) (2024-10-31)

### Features

- ✨ 详情页接收明文 id,增加去掉 microheader 的子路由 ([62ee068](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/62ee0685981418512159832bedc1a5d51aa5bc3d))
- 优化 ([c28deec](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/c28deecbadfb822e3a2a471a52cf59cd9d2bc59d))
- 优化 ([7ba104f](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/7ba104f99449c27186afb7d9b00206becbb31681))

## [1.44.10](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.44.9...@mdt/product-micro-modules@1.44.10) (2024-10-29)

### Bug Fixes

- 🐛 下载数据多列选择过长问题修复 ([7b7a779](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/7b7a7798f8620d4709f2a2a2f27f6ed6eac1d19e))
- 优化 ([ae156fa](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/ae156fa47faad1bf69731eab9b2488e4e0498fa1))

### Features

- ✨ 增加筛选器单列 validate ([06efbc6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/06efbc63fb5b3ce879a1af00e6da362eaf0ef7ca))

## [1.44.9](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.44.8...@mdt/product-micro-modules@1.44.9) (2024-10-28)

### Bug Fixes

- 🐛 带数据下发优化 ([1798427](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/179842745b86a5e3fad8472f78c1f6df2d0986a1))

## [1.44.8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.44.7...@mdt/product-micro-modules@1.44.8) (2024-10-27)

### Bug Fixes

- 优化 ([d0ed42d](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/d0ed42dc94daa397b9bf790a0920fbc690db59eb))
- 优化 ([a483603](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/a4836035d5284c6819d7404e70418afdc085661d))

## [1.44.7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.44.6...@mdt/product-micro-modules@1.44.7) (2024-10-25)

### Bug Fixes

- 优化 ([3035d8a](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/3035d8aea28cfb43e82a08f0b38a3b8dd5c64329))

## [1.44.6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.44.5...@mdt/product-micro-modules@1.44.6) (2024-10-25)

### Bug Fixes

- issue not close ([268e850](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/268e85074555b0ef6a3f563a10f6fc4862d44bb8))

## [1.44.5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.44.4...@mdt/product-micro-modules@1.44.5) (2024-10-25)

### Bug Fixes

- 🐛 Upload 增加只读模式 ([d901fc8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/d901fc8afd643251501e0fb102c379fc4676b8ad))

## [1.44.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.44.3...@mdt/product-micro-modules@1.44.4) (2024-10-25)

### Features

- ✨ 增加选择联系人单选模式 ([4176ca3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/4176ca31a9b08d74df4124e3aa30374cc658afa7))

## [1.44.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.44.2...@mdt/product-micro-modules@1.44.3) (2024-10-25)

### Bug Fixes

- 🐛 审核和提交的状态颜色修改 ([5c31cf7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/5c31cf797442b8d7372b87c585e18f43429393d4))
- 🐛 权限可以设置为空 & 一表通展示多种原因 ([d340a5b](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/d340a5b78f92333746d063621fdf46478cccc8fc))

## [1.44.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.44.1...@mdt/product-micro-modules@1.44.2) (2024-10-23)

### Bug Fixes

- 显示错误 ([ef9d8f8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/ef9d8f8fa0580581142b12df807dc5c09f6db3c2))
- 🐛 卡片样式修复 ([05a6c11](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/05a6c11bd10f111f0a68cc31b03d3c320bf89274))
- 🐛 身份信息修改样式和逻辑 ([d1872f1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/d1872f190de93b7218405104d776e268825f1cfe))
- 🐛 用户列表分情况展示不可用的用户 ([97117dc](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/97117dcdc4c54a7874a9c7cb0310adb925e98a1d))

## [1.44.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.44.0...@mdt/product-micro-modules@1.44.1) (2024-10-23)

### Features

- 优化 ([f300d43](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/f300d43d6a3dd3f149b338cad341c4db30527a8b))

# [1.44.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.43.10...@mdt/product-micro-modules@1.44.0) (2024-10-23)

### Bug Fixes

- 🐛 下发详情 icon 保持同步 ([93cb8d8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/93cb8d8c5974e102e8314f8b395893c4b5d138c2))
- 🐛 修改催办的判断 ([ac60801](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/ac60801c1887d5796b105c697ac5503fe4bccdae))

### Features

- update ([6dcadca](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/6dcadca4a8a114a802ec04ad5e180647cd92971b))

## [1.43.10](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.43.9...@mdt/product-micro-modules@1.43.10) (2024-10-23)

### Bug Fixes

- 刷新 ([23b4150](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/23b41506dd30b24a4822d3615f5c4c487ba349a8))
- 🐛 优化催办的写法 ([9a90add](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/9a90add1c438a9d5f434e2e402f44ca267935fcb))
- 🐛 修复筛选条件和文案问题 ([3649f7b](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/3649f7b13ba6fe002ec004706de638d86faeb7e1))
- 🐛 修复筛选的位置问题 ([ac9f748](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/ac9f748c6f7da32d0c4f04253b18243e5c5d642a))
- 🐛 筛选文案上的修改 ([b793e06](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/b793e06b143f73863e7d6434374f2ea0c8922fe2))
- 优化 ([e7cb31c](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/e7cb31c1190a767fd8a25b1cb8d77f63c99cfe22))

### Features

- 下发数据 ([37a1954](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/37a19544a0138e8822ae4106e50cc72cf8d69237))
- ✨ 增加详情单独链接, 增加催办功能 ([e19c2d6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/e19c2d686c03f76b77ec59ee4f2c4e8b7b468a06))
- 优化 ([ee27ec1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/ee27ec12cd57917aa1a7395fcdb4f06818585ad1))
- 优化 ([1aed774](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/1aed774234b5ed84f43d6ca5c5ecb1fe33817b4f))

## [1.43.9](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.43.8...@mdt/product-micro-modules@1.43.9) (2024-10-22)

### Bug Fixes

- 🐛 身份选择的样式问题 ([a7ae24f](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/a7ae24f0e793015b9dd9672829be19a32769d551))
- build error ([305f99b](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/305f99be8bc6f2e3b443d1e6c17681e0b5b857d9))
- error ([be94f18](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/be94f18c3f7d3a859a60a6284bc3403a039248f3))

### Features

- ✨ 报表管理详情页操作内外保持同步 ([048bbab](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/048bbabc22227d489b32c5479f413ebd9d92c3ce))
- ✨ 详情页增加协同标识 ([0f4c964](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/0f4c964321e19fc808de2bc383de2398e6999fd0))
- onetable2.2 ([23f031b](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/23f031bda6e64530b89df8eff55c5317f3a656d2))
- 优化 ([7353abd](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/7353abd272d38aa652a25bf259d58875987adab1))

## [1.43.8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.43.7...@mdt/product-micro-modules@1.43.8) (2024-10-21)

### Bug Fixes

- fix init error ([27d1bba](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/27d1bbac98d8280fd2b060bccbff32339a4840ba))

## [1.43.7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.43.6...@mdt/product-micro-modules@1.43.7) (2024-10-15)

### Bug Fixes

- 🐛 嵌套抽屉导致的问题 ([59dcc03](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/59dcc0386209d2875c355f00c91c5b82190e4ded))
- 🐛 智能搜索暗色调优 ([f6ac194](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/f6ac194dbd417dc7799e2c6834645f804b4e4ee0))

### Features

- onetable2.0 ([47e9fde](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/47e9fde7078b19b5806cd82b86bd764de6d20514))
- 数据工厂 Qe 支持手写和低码编辑器 2 种模式 ([beb878b](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/beb878b414b462933999c588ef642ef7736e9ef3))

## [1.43.6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.43.5...@mdt/product-micro-modules@1.43.6) (2024-09-12)

### Bug Fixes

- 下级数据查询 ([1a856e7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/1a856e737fc36c0c7702a3bc60469fa851a1c564))
- 🐛 编辑未发起报表时删除无用数据包不再进行失败阻塞 ([bf654eb](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/bf654eb742d6adc255016617bb1fc5e07844ec9b))
- 子流程已用自己时,子流程接受变量没有下拉框提示 ([474f65b](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/474f65b6a96196f607fc10b4631f08f8d5964b5e))
- 表单预览优化 ([daaaf23](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/daaaf23fc8d6d7c1bc398e606ebaa01f915e53be))

### Features

- 下载列可配置 ([b03ca60](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/b03ca6084abda019e3432fae03cdce8a412d613f))
- ✨ add form resource ([85bbcb9](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/85bbcb90bc0d2e1d8995bda981b026e391065989))
- ✨ 表单字段对应数据包字段映射增加多对一 ([afa1fb2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/afa1fb2caacd3dbfee5c4ca779f92382cb228347))
- 批量上传不做联动 ([89b1e65](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/89b1e65dd082ddada18a1b341cb0ec17f97d4536))
- 放开数据包下载权 ([9da34e6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/9da34e627fcfd44b79c3396a68ca99cf14f54f28))
- 放开数据包下载权及不展示预览数据 ([cc74aa5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/cc74aa5024df4df10a681ece29b6f8b036861c0a))
- 流程实例界面支持 url ([744660f](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/744660f4dcaf5319690d0cf8ae03bf7f77593755))

## [1.43.5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.43.4...@mdt/product-micro-modules@1.43.5) (2024-08-29)

### Bug Fixes

- 🐛 修复展示 ([4ea683c](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/4ea683c044cdc0953f2979703cc05009787fe3aa))
- 🐛 修改问题 ([2331c16](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/2331c16fe9b6f8c60ed28d4bf0fc18c52491f8d4))
- 🐛 填报操作直接进入数据页 ([b4dc32e](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/b4dc32e42c1b8c097912fc11dabef055c88436ed))
- 🐛 样式调整 ([e7dd153](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/e7dd153667136fdd7a950f8ba97d895b503de1d9))

### Features

- ✨ 任务根据状态展示操作 ([53614df](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/53614df85398531958b5a45d5450c3c777b372f1))
- ✨ 操作优化 ([f39dce2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/f39dce23c9690b2fe0780f3616c5eec07c9e999f))
- ✨ 详情快速响应一个操作 ([76a0b73](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/76a0b73e34041aa277874a93f487dfd2110e4182))

## [1.43.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.43.3...@mdt/product-micro-modules@1.43.4) (2024-08-27)

### Bug Fixes

- 🐛 callActivity 子流程接受不能有系统变量, 变量输入支持, 加入搜索 ([39dab54](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/39dab54b3524e0b6354ce49e80fb8415ac5426e0))
- 🐛 FlowInfo 获取 firstHandling 变量通过流程实例来 ([1aaeb4d](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/1aaeb4d3188de86a9aacffc2bce4d4a527b7624a))
- 🐛 headerLabel 在浙政钉 pc 端不换行的问题 ([67e70d5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/67e70d551ce39bcda9ce0e03d7b62cc7e8a01774))
- 🐛 remove console ([67e61d0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/67e61d041d5df319687958da2b626ac5bd8f51bd))
- 🐛 下级不换行 ([420401d](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/420401d6823efc5c29829ebe3a88d0c1338598c3))
- 🐛 修复数组类型数据源依赖无法赋值的问题 ([45198e9](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/45198e93ac9235b5b0375ecfd1b3ad4a3c19588e))
- 🐛 修复跳转偶现异常 ([784c2ea](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/784c2ea5417e0815448da8ccafe87a9e0d926306))
- 🐛 修复高度问题 ([bd972e9](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/bd972e95807be8a73166e4708d2af8b2340e2152))
- 🐛 审核页部分页面打开报错 ([847acaf](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/847acafd8edd6404327b9f28a194e87adb9ba5d4))
- 🐛 数据权限部门默认填充不展示名字的问题 ([63585e7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/63585e72604dd090bc0195efd78ceb7775156678))
- 🐛 根据 role key filter 和 join ([f068cf2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/f068cf2e454ff28c44596bc98405d68bfdac88f2))
- 🐛 移动端宽度调整 ([d52e062](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/d52e062fd9be692e28c9c801395bef026b7e4331))
- 🐛 移动端选人黑屏问题 ([96d3b5d](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/96d3b5de4338f946baee35ef5bbea4e3b475be68))
- 🐛 过滤角色的选中 ([9af53b5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/9af53b51554e9e3519655d57c894800629fd32f2))
- 🐛 选人逻辑修改和优化 ([190d126](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/190d1269773e1c79d306cf5db790effd4e2ecbba))
- 🐛 防止下级按钮被压缩 ([4a3eb7e](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/4a3eb7ec4e30f8b7763958f17c8b0771efcca297))
- 修复状态 ([b5034f0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/b5034f039c21c7fbe7448b736082fe77494d9a11))

### Features

- 优化操作 ([40b7c18](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/40b7c1828d0043581fac53efd02a5ec008b06723))
- 表格列可拖拽 ([aa29a52](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/aa29a5211fd50d60ca3d43bd1e96d0778ffb678d))
- ✨ 依赖配置增加数据权限跟随选项 ([4932c05](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/4932c058a3230e00ff89df59a84aeacc9f5093e4))
- ✨ 增加联想填空题型 ([ee005ea](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/ee005eaf25c0af597dacc2df1e166ec4eb699d5f))
- 任务合一 ([bb3b6e8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/bb3b6e80c16fb3204dbaf742a433cb66cf64882e))
- 日期类型时间戳+默认值填写优化 ([4533be2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/4533be20d00bdee4788a189649641a5d342cbb0c))
- 时间戳类型处理 ([8742efd](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/8742efd3dc09a9d297f0c72c332f0511dad6f2ae))
- 选人添加搜索 ([694dd03](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/694dd0334df402f9a4ecc70484e78a5713afa497))

## [1.43.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.43.2...@mdt/product-micro-modules@1.43.3) (2024-08-19)

### Bug Fixes

- 优化 ([57b1c92](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/57b1c92afd0d3d14032a65a869f319481a800ed1))
- 优化 ([aaf69c2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/aaf69c2532b871fc690c274b88549955d58bd5cb))
- 🐛 depth 外层控制 ([5285ba2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/5285ba2a6808229bd80c3d8b645bde6d69d691c7))
- 🐛 formOrg 只要当前 depth=0 的部门 ([3369ee8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/3369ee83c6e50edec1c4a19d35ff74779f2519c5))
- 🐛 name error ([8d1266c](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/8d1266c35e496111db4a12c3697af1cf6b784961))
- 🐛 ownership 级联数据源展示 ([cc09570](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/cc095700b521453662e04a6a94f1c03bccb7f46f))
- 🐛 user select display ([9370bba](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/9370bbab9a10eeaec4c409b142d055e93dff2ed6))
- 🐛 优先展示 updateUserName ([69ffd28](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/69ffd28da0d5c6d210592087f5f403199be5ad30))
- 🐛 修复审核任务状态展示异常的问题 ([ff0f519](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/ff0f519b5ea9aa40c8e0d7d54ee91fc538ed9681))
- 🐛 修改 redeliver_object 类型 ([786fdcd](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/786fdcd1c17470b10e8600c7e84be6b8846de877))
- 🐛 修改催办的传参 ([153908d](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/153908d5a059049da1a5058c3a8b93cc47485206))
- 🐛 修改移动端文案 ,修改 allsettings 包裹异常 ([aa395f7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/aa395f74bf1119c951f57e5d31ab3818558b94e3))
- 🐛 催办增加内容更新 ([c008a4c](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/c008a4cb5d27fb7f2b2f1bb9e80a293b482bdf26))
- 🐛 只隐藏报表创建管理的 headerLabel ([3ecde96](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/3ecde961affc620e6e63dc0cbfcb8f784f795c63))
- 🐛 增加 onetableInfo ownershipPkgId 的配置项 ([c1cc483](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/c1cc483444ca6f45afe5d060091e33348b12d667))
- 🐛 报表管理已结束操作栏展示异常 ([f11f81e](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/f11f81e22a240e02371cff49965a8f81432193de))
- 🐛 流转管理移除详情, 改为部门名称下钻 ([2c47d0f](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/2c47d0f23bd39a60ee4b71cf0940b0b8b15f4843))
- 🐛 问题修改 ([2c67b18](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/2c67b1881d76d2bcab48dce6d0d9db548af6c697))
- 🐛 页面调整 ([daed48f](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/daed48f8e3094a1fb2ac5b83bb5fca6bbbde2169))
- 🐛 页面调整 ([5a76ccf](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/5a76ccf9b1392c6cca824903db768265e2967463))
- 🐛 驳回增加 redeliver_object 传值 ([df931a5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/df931a5b2879f77e44dc3927a59fa9d2e68955d2))
- 人员展示 ([8d91a31](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/8d91a31c3a51c0e2e63f5a0ea2d49e9fc1824398))

### Features

- 新增派发不允许重复派发相同的用户 ([2877b4a](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/2877b4aaa6463589f147e91f9b8de0f920a2f693))
- 用户展示去掉 id ([9e52b12](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/9e52b12140a6d796b6d5b23eab5ba5510b8b37e8))
- 选人追加角色 ([0ac02a2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/0ac02a27ade6de66e76ba9b3287e6ff4e586ff02))
- ✨ 选人 ([9d77e48](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/9d77e4889ae897aaa654d7a821aba4854f44719c))
- ✨ 增加流程 3 的表单字段 ([c8efff6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/c8efff63621e214338b8e716e5ca786adff212e2))
- ✨ 增加跳转后展开详情页 ([3339382](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/333938219b7c73a588255ebdc0ea6112e8e07088))
- 下派及转派也不能重复 ([2054d66](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/2054d662a71504ecceb83f1dcbfa15a5da2baddc))
- 显示部门名称 ([5c2adcd](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/5c2adcd4dbc66b4ddf18be6c3bbfe876b73fa045))
- 题型切换 ([a736bf1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/a736bf1bfa5414a71a27460026cfe0dd0dbdd67d))

## [1.43.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.43.1...@mdt/product-micro-modules@1.43.2) (2024-08-13)

### Bug Fixes

- 🐛 修改 isPhone 正则 ([f22fa82](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/f22fa82f3a80cb1983752c73efdc16b8813e5931))
- 🐛 去掉详情 名称进入 ([d8741d9](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/d8741d9d5e0309d3ae89e87a366ac2b256949836))

### Features

- ✨ 菜单布局 ([cae1522](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/cae1522ef2ad0d5a7919ee2fe6c848653c42c5a7))

## [1.43.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.43.0...@mdt/product-micro-modules@1.43.1) (2024-08-12)

### Bug Fixes

- column sort unenable ([fbe777f](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/fbe777f2c4d23aad21bf19a353107556e999be6f))

# [1.43.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.42.2...@mdt/product-micro-modules@1.43.0) (2024-08-09)

### Bug Fixes

- 数据预览白屏 ([67ba9fc](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/67ba9fc84efb5227bab63cf19ed3298fed36c572))
- 🐛 style lint fix ([72a6ae1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/72a6ae12a7cbe0d7caab832d558bc6d0ecf4b0ca))
- 🐛 修复部门归属选择展示 id 的问题 ([3ff85da](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/3ff85da1f0357229cbee665652c685455c140ac9))
- 🐛 题型设置其他选项 bug fix ([b7fe60a](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/b7fe60a911ca85edbc0ad92a55494484c5e04535))

## [1.42.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.42.1...@mdt/product-micro-modules@1.42.2) (2024-08-07)

### Bug Fixes

- 🐛 flowinfo spec 只展示流程 2 ([0234970](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/0234970eef0b5c02422d2e85888de5e2206186e5))
- 🐛 兼容浙政钉的样式处理 ([f4ad979](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/f4ad97937d4e7d44553734a715b53dc49781df2d))
- 🐛 操作栏优化展示 ([47c100c](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/47c100c829084d173cd5bbbfec6005e04090f08e))
- 🐛 补全权限和缺失国际化 ([745914b](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/745914b5107e06ad00ef33c40c7a9f2f9d9b16d4))
- 🐛 跟路由访问还是访问 dataapp ([607623e](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/607623eb8e6c3368861afe5b3c15c5834769dc7b))

### Features

- ✨ 追加手动分页 ([38f7c2f](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/38f7c2f6740e3b5abf99f92c5d80b78b06a4866b))
- ✨ ai search ([a9520af](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/a9520af339d42fd34bbe9900061607874ba210b2))
- ✨ treeselect 题型属性配置 ([3ded1f9](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/3ded1f9d36c09478d8a447858df6ac2087925464))
- ✨ 无匹配路由默认跳转增加配置项 ([2a55642](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/2a556426323e3aaeea4bc4b2130d932ab4921cde))

## [1.42.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.42.0...@mdt/product-micro-modules@1.42.1) (2024-08-05)

### Bug Fixes

- 🐛 修复一表通提出的展示问题 ([f10975c](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/f10975c68ede999fb783fcc3fd2f7d1e3595cb5b))

### Features

- ✨ 新增题目说明设置 ([0eb3436](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/0eb34364448d842a6d5fc7141d2b23e4d29270e3))

# [1.42.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.41.5...@mdt/product-micro-modules@1.42.0) (2024-08-02)

**Note:** Version bump only for package @mdt/product-micro-modules

## [1.41.5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.41.4...@mdt/product-micro-modules@1.41.5) (2024-08-02)

### Bug Fixes

- remove btn ([d124bfa](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/d124bfae982469b68b997c62040d0769fa142235))
- 🐛 优化移动端图片预览 ([087e9b3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/087e9b34d2082f266444a83139d0024b205706b9))
- 🐛 修复选择有其他属性时的正确展示, 新增一行和批量编辑走一套增删改 ([ff65f9e](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/ff65f9edd920cab22a81e837405041dce4c5b585))
- 🐛 修改未接单的跳转链接 ([acfbdb6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/acfbdb65dac88b197138e0a290f998115ada1ee3))
- stopPropagation on mobail ([c060df2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/c060df20080fb3c2e85c63f33ad773ef596ce6e8))
- update css ([752ff07](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/752ff075d372e257eaf1c761252c4f93a90beed2))

### Features

- 编辑表格-新增一行数据交互优化 ([2995870](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/29958707b70d5d8f168cdbb5335fb43002cd9128))
- ✨ usertask 指定组织负责人 ([72472d9](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/72472d95d9c871233868bef78f993d8bef58b21a))
- ✨ add new question ([6fb9a82](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/6fb9a823593847e70adba43ddc80f83650f6f88c))
- ✨ PC 端数据填报时，新增视图 ([b44b807](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/b44b807246b5a1948f5fe427d9372a0d188b6362))
- 已完成的接单任务的切页显示逻辑调整 ([c9a216d](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/c9a216d318eb406d30286a2d48a4373a6132d825))

### Performance Improvements

- ⚡️ 机构管理大数据渲染性能优化 ([48937f9](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/48937f94f922d4c6cc5a124f8c1eafb63e2c9340))

## [1.41.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.41.3...@mdt/product-micro-modules@1.41.4) (2024-07-29)

### Bug Fixes

- 🐛 minio url kkfileview error ([2637217](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/26372170411ae102ff3498c7e1f78e51a371403c))
- 🐛 调整归属部门为必填字段 ([2ceaf21](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/2ceaf21443e963623f86f25f21e971f0803c0562))

### Features

- h5 append userOrgid ([74e8c30](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/74e8c30d71dc9ef3b1787dbfe27e275b891e32cb))
- ✨ datapkh support id to name display ([c417beb](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/c417beba00c2f70b646b752080742cbdcd568933))
- ✨ orgid from frontend ([08e8b14](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/08e8b14d6ee450076d266bccd618841875ace939))
- ✨ 填报增加 userOrgId ([36df1f0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/36df1f0be171578955f77660846b029a456dfff0))

### Performance Improvements

- ⚡️ 大数据渲染 innerApi 部门的算法优化 ([60db11e](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/60db11e80f14446f87a474369ffab2e48ab54cfb))

## [1.41.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.41.2...@mdt/product-micro-modules@1.41.3) (2024-07-25)

### Features

- config header menu ([146d0dc](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/146d0dc31e740ab2f82b940dc85d155c7629c8d4))

## [1.41.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.41.1...@mdt/product-micro-modules@1.41.2) (2024-07-25)

### Features

- ✨ add debug mode ([cb107a7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/cb107a775a003fb7fd0dee9231d60bfdcd5ceff5))

## [1.41.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.41.0...@mdt/product-micro-modules@1.41.1) (2024-07-24)

### Bug Fixes

- 🐛 build error ([593c866](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/593c8664e3598b94605c6217d8f410f9079d049f))
- 🐛 use config to controller ([dc5d68c](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/dc5d68c5cda5ff38c2c0bce7dc3af679c2dc36e2))
- value and datasource depence confium ([2566d4b](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/2566d4be90d4615db4e79a2599bafe0d7b915a49))

### Features

- add url param ([69c5d0f](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/69c5d0fd633aefe742e57883dd133e45ab8002a6))
- ✨ 增加 dashboard 和移动端页面的控制权限 ([4fdd823](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/4fdd8235d4ee4d4448bd47d96d006437527bbde0))
- ✨ 移除 tag 的添加和筛选, 增加流程 1 变量 和流程 3 变量 ([2cdfc9f](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/2cdfc9ff3aa2d36da090755e40f19d508e95545d))
- one table suport more app ([528bb78](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/528bb7846983496f44acf09aabb17c78f3879dcb))

# [1.41.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.40.4...@mdt/product-micro-modules@1.41.0) (2024-07-22)

### Bug Fixes

- change file position ([75f1bfd](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/75f1bfde4d4bfbf8f8d1204adc98ca728433d2be))
- 🐛 修复没有 modifyTableColumns 的情况下,赋值 columns 的情况 ([724e8dd](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/724e8dd47e3e1585fef3572c34decced08da7b6a))
- 🐛 修改查询条件的默认条件 ([29ff3fd](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/29ff3fd775569fb2af1d746ad6703d3af4424fe2))
- 🐛 文案调整 ([4a2ef37](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/4a2ef37dc98d7947d4be72c6f0977e781b39fac6))
- interfaces error ([04d86cd](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/04d86cd89a693290687804d1574d84a71a41b7aa))

### Features

- add report data page ([4669ed4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/4669ed4b8f8ae17632eb8848d756026502365ad5))
- ✨ 补充填报信息,增加筛选类型 ([6cc2b45](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/6cc2b45ddeb161b94f02a30c9c79a17f749cf3f7))
- add datapkg cron ([6bee9d7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/6bee9d730567ef3fae91c18d518a631c03c130ba))

### Performance Improvements

- ⚡️ 性能优化 ([1ad9dfe](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/1ad9dfeba0255f71d5f2e3a71a7cdb5b59bd6c8d))

## [1.40.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.40.3...@mdt/product-micro-modules@1.40.4) (2024-07-17)

### Bug Fixes

- 🐛 ascii 及 ustf8 无需解码 ([cad7638](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/cad763829067ef5347a4a2b91d4d37527204fb5c))
- 🐛 文案修改 ([66fe274](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/66fe274e4f2e618d1173da96634fcac36285ffd6))

### Features

- ✨ one table custom all ([08b8fc8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/08b8fc8a6182d9be21b1f01a42fa9b71505eb013))
- ✨ 增加预览的填报人列和最近修改时间列 ([891d22f](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/891d22fc6c842c3ff2fae8028802acd40b430c8f))
- ✨ 拓展 api 快捷设置的更新和删除参数配置,支持动态获取唯一索引 ([a429afb](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/a429afbc5e0e4e3a45a1eb441fb896d0b106b068))
- ✨ 筛选器增加 alias 映射关系 ([2cc63c8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/2cc63c8df3d33b183ca7c3a2d3fc86ee8f624676))
- support crontab ([1b1a750](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/1b1a75004f6e1b8e1fc4ac280691fed59c7a4983))

## [1.40.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.40.2...@mdt/product-micro-modules@1.40.3) (2024-07-10)

### Bug Fixes

- 🐛 Cascader 处理 array 包裹 string 的情况 ([1676711](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/16767113acf843eb0561b108272ff37821a8d110))
- 🐛 一表通反馈问题修复 ([6ba7036](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/6ba7036000cb142a646491d6ac1d3d3e072730a6))
- 🐛 催办的文案调整 ([712037b](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/712037b1fb316500acb2ec1050d0780a03d77e09))
- 🐛 补充 en 国际化 ([c13a285](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/c13a28520d9bc8060a893cd648d1f82428e9b06d))
- 🐛 规范模板信息 ([9b9429b](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/9b9429ba44a29374d3ffb0120930aa7ee3de8e14))

### Features

- ✨ 完成催办功能 ([c77be50](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/c77be5032fed4c1aeea4d2496a3913e5e49917da))
- user task detail page custom ([2e5670f](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/2e5670f81f52ec0d3b7d4969678881ab708bf69a))

## [1.40.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.40.1...@mdt/product-micro-modules@1.40.2) (2024-07-03)

### Bug Fixes

- css error ([42ffa66](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/42ffa6665caa476c4ac899ad4fe8cf2e2766225c))

## [1.40.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.40.0...@mdt/product-micro-modules@1.40.1) (2024-07-02)

### Bug Fixes

- 🐛 i18n miss ([bc62387](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/bc623878038e01b90114df626555349187489be2))

# [1.40.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.39.1...@mdt/product-micro-modules@1.40.0) (2024-07-02)

### Bug Fixes

- i18n error ([3651f06](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/3651f06d12d9245879eedea293f7b2bfffa2765c))
- 预览界面无需联动 ([146706c](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/146706c26836eeaad4fbebf3b4c1fdda2b876ffa))

### Features

- ✨ add punch mode ([8c91899](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/8c918992364bf536c1f8f335bdd90054ae0d687d))
- ✨ Modify all i18n ([80ff7fa](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/80ff7faf39a476757bf7285711f3508c03ecb85b))
- ✨ onetable 移动端优化 ([7f9b522](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/7f9b5220d5f454ead6efd1d92149d2d99e334e55))
- ✨ 升级 sql 编辑器 ([8168661](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/81686616768bec61a70356f55d384c5301f88fdd))
- ✨ 替换色系 ([8d365af](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/8d365afc49ebf3a611edb80cc76585d88a0e8536))
- refactor geometry input ([2831af0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/2831af087be6231367a3dfc291e115d84917e362))

## [1.39.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.39.0...@mdt/product-micro-modules@1.39.1) (2024-06-24)

### Features

- ✨ 一表通移动端 列表页 ([ed09ffd](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/ed09ffd2624846c0f0341ff023a58a81667d9d41))
- ✨ 一表通移动端 详情页 ([db002b6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/db002b667a7485a766e079cc4bdebea6c4f9d7d4))
- ✨ 一表通移动端 首页 ([de108da](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/de108da0b3973d88d79133f070316ca7f8505205))
- ✨ 依赖配置添加强制覆盖选项 ([1956403](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/1956403b3b0e984f27e36454f8b3af0fd0aacc2a))

# [1.39.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.38.2...@mdt/product-micro-modules@1.39.0) (2024-06-17)

### Bug Fixes

- data search ([80ff452](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/80ff45296c503d7f7174dffcc96811740759ee8d))
- 🐛 build error ([bae3993](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/bae3993aeaa3846c57af78220b6f8396e3d28605))
- 🐛 下载样式优化 ([faad809](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/faad80956a1a299e1617e17f2205bd39057f2d5b))
- 🐛 修复修改部门信息 用户列表里没有更新的问题 ([d8362e3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/d8362e3327bdfe84e5cb5f754e4751297321d475))
- 🐛 去掉 max_depth ([7df9d47](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/7df9d474a9c2fd7edc3bdc078e729023dbb94dd9))
- 🐛 未处理任务 total 异常 ([088956b](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/088956b532a6baf703036c40ececcf067af0a82b))
- 🐛 机构管理高度滚动 ([3101400](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/310140046be5d4ac48b7a8bbedc9c5496cf239f4))
- 🐛 没有自定义数据源权限时不显示入口 ([b6af904](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/b6af90421ebd64a99bbcb5302b923af46dc9c4d0))
- formily array table row id change error ([6cddb25](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/6cddb256b9f86f14c6b56ff4fca2ea68ccb850df))
- import error ([9711b35](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/9711b35133ca7d974c1d449b66275060991722f6))

### Features

- ✨ form editor 支持自定义题目 ([8ccf5c0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/8ccf5c098cba8c474bc4e3e91dee41f7e73437ea))
- ✨ 一表通优化 ([9b72a10](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/9b72a1012cd80c91ea62a6f4ae07386ef57cae65))
- ✨ 一表通概览 ([5dca300](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/5dca30097fb32cd920b4211191b935c9d1d4b20e))
- ✨ 任务流程 ([6bd85fc](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/6bd85fc67e5a5636096208110cce2be517b792ed))
- ✨ 增加 onetable 的权限展示和校验 ([3442117](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/34421172ce0a438ea262eeaebd793a5b496d5325))
- ✨ 增加 Upload 表单内组件 ([a09032c](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/a09032c480f9ba4cc038faa7ea1db51b301d41dd))
- ✨ 增加数据源低码配置 ([a641270](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/a641270ea26593f653be5cee95f3a02cf65aa112))
- ✨ 增加组件国际化的全局配置 ([7b6cfbe](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/7b6cfbe1badc7261a5c455549683621c532480a9))
- ✨ 子流程详情（待联调） ([7d3ea77](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/7d3ea777fd4fb4aa45cd4e4928fb342f50f6b167))
- ✨ 子流程通过功能 ([e1279a6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/e1279a6970e713688de581e4eca7951710d5b62e))
- ✨ 流程 3 调整 ([2724ae7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/2724ae78ddaa25bce9c59dcc23c736269b95379c))
- ✨ 流程优化 ([8835f2c](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/8835f2c827c46356ccf4efd71ee3d5afe7ceb5c1))
- ✨ 流程优化 ([ca01bb0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/ca01bb0afb8809ad26f0a72be7893c795c3d3ede))
- ✨ 流程优化 ([55649c8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/55649c8ab9f71159585e68888ed6bb0e3be4201e))
- ✨ 添加低码 Qlang 的请求类型 ([e4a78af](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/e4a78af0585c5e149b7b3ef8f67c135273c38648))
- ✨ 市民信息组件& 子流程数据查询调整 ([ac52dd2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/ac52dd28fcde51da30f8e3b2db36ddde1df8a5e6))
- adapt api chang ([1f5f272](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/1f5f272b86b49d5ede6b437e7fcaf94ac9e1dc39))
- collector api ([f36620f](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/f36620fa063a8c9e4e8bf1c1d6c7302284633f94))
- some codition hidden btn ([5da97de](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/5da97deb784e96faa0b10d09f6f73bf68b8eecb3))
- update ([c5a816a](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/c5a816a6e98fad68583a24928e771a8b2bacb35d))
- 追加部门过滤 ([4ad0f1a](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/4ad0f1aaf5387c611658fd91feb70211a0b84451))

## [1.38.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.38.1...@mdt/product-micro-modules@1.38.2) (2024-06-03)

### Bug Fixes

- 🐛 根据 name 创建数据包 ([8abf8c4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/8abf8c4e58218405ed6c531c5e82b420d1cb3f51))
- error ([689f57e](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/689f57eb357cbc57cb39802be661e92c60e4f5e4))
- import error ([cef0568](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/cef05687cf3805ef203e4e42b1643b2290483686))

### Features

- change v ([7bd7d88](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/7bd7d88fe756b5c02fd5750df5352d81835a9ce0))
- 国际化文案修改 ([b87655b](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/b87655bf55973106ecbfc5ffda41b8070309df0d))
- ✨ add workflow genealogy ([5ca1565](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/5ca1565023c9c8ad0206a2ef9c9c8ffb7e68128e))
- ✨ callactivity 子流程支持变量 ([4a65dc8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/4a65dc8b3da6e6abaff7774b98e316340f5a664d))
- ✨ 列表操作 ([6e2a4ff](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/6e2a4fffc9c47dc0ccac9b3ed541661c4853239c))
- ✨ 动态依赖字段 id 匹配所选字段 ([c0bd769](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/c0bd769e8df9d9a945b11e227af5219869d4e91a))
- ✨ 填写、审核任务列表接口对接 ([ee66383](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/ee66383fccfcb90a1d33e83fa8bc9c14eed79345))
- ✨ 增加文件下载,批量下载功能 ([fcda032](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/fcda03209573f424044fbf6e0f7028c3604bd796))
- ✨ 审批操作 ([c946590](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/c946590cb288cf9f5d4d50f96fc1085f9f9f1bee))
- ✨ 报表创建 ([0efd5e5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/0efd5e562304214607a5d381cf0c71ffcf6ad202))
- ✨ 报表管理列表接入接口 ([679d214](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/679d214f6da2d576df6d52af9b4444bd5be0ed50))
- ✨ 接单任务接口对接 ([06a72c6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/06a72c68dbfb144e216bef839f27bf9584a2c84a))
- ✨ 数据包行操作接口改为 bff ([cefa950](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/cefa950a3b4ffc57ef0a92a04bdb5485d4bdc873))
- ✨ 流程优化 ([faa11a0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/faa11a0e23597ad08eeb5d211a295008eb88c448))
- api 切换 ([342b330](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/342b33073ca5b005644b6f5cc06c26d39def1c50))
- 优化表格列宽度 ([a62bbc3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/a62bbc36f2fd2edca817cfe41528bbc44f406d07))
- 修改数据完成 ([99d63b0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/99d63b085bb92a7701c8c7c410b3ff1009d95d7a))
- 微信公众号获取经纬度 ([b95f82e](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/b95f82ef548232a881da83cddf082a76f67ecdf2))
- 报表详情 ([0622dc7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/0622dc7f5abc514c61d7d2faddd026c8d9b1cfc3))
- 报表详情 ([5faab1f](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/5faab1fa34e011b0f16c0db85dabdf4210defbec))
- 流转管理 ([0109ca5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/0109ca555c9197ff497e417bc9f3b80722d623ca))
- 通过 excel 批量更新 ([d3d02a1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/d3d02a1a5d383e47c43a0097a2d6df9c339a3394))

## [1.38.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.38.0...@mdt/product-micro-modules@1.38.1) (2024-05-20)

### Bug Fixes

- too short time to get location ([9166f38](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/9166f3870c92e54fc51118c4786f7fe56732d8d7))

### Features

- ✨ bpmn 支持 callActivity ([0f3583d](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/0f3583dfd960f4ae86251b5f928a8fae9b1d9d49))
- ✨ limitSize 可配置 ([26d0e3f](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/26d0e3f4293958f204dbdd3c3dc10e741b8fd21e))
- ✨ standard llop ([eb4482a](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/eb4482afef66710cae5148bbb0d06ae63ee7b9d9))
- ✨ userAssignment 支持变量 ([3179588](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/31795885e36d46e5cbdd5def88d30227eaf91c8e))
- ✨ 优化及 bug fix ([988c2eb](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/988c2eb38ce87beb8f325558415c0b0ee5da742f))
- ✨ 增加上传的图片压缩功能 ([7c5cd06](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/7c5cd066c01a32b83d35823b9b30497dfa6c850b))
- ✨ 重构添加水印的方法 ([c07304f](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/c07304f73a408e67eab60beffe370dbdfb6c38df))
- add one table ([a539840](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/a539840d70eaeec0df8ced01a1f9a377e3bc4d95))
- refacto wf visualization ([5693f8e](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/5693f8e8279c503bfaf4fcf7e099adacef2e6b23))

# [1.38.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.37.6...@mdt/product-micro-modules@1.38.0) (2024-05-13)

### Bug Fixes

- 🐛 修改滑动认证的变量名, 修改覆盖方式 ([10cafc9](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/10cafc99735a32517a4128f625dca406015b2b0e))
- 🐛 其他选项，填写时报错 ([22d0171](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/22d017183a1263bb8540c1525ee9e9c585d22ee5))
- 🐛 图表批量分享时支持下载权 ([7663e4d](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/7663e4dd4bbd7f1565ed4910ab5edc1217866d69))
- 🐛 数字输入框不能清空 ([dc52f94](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/dc52f94f10ea4b675c4717ca087ccdf2ce246980))

### Features

- import from clipboard ([0ce0d6c](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/0ce0d6c3f559240c979513bae5e6a0a7ec854c93))
- ✨ bpmn 任务支持多实例 ([6dfdfa5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/6dfdfa5832883b5f6dbb3ee29c17b43fbf3516ba))
- ✨ excel 模版 ([d57485d](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/d57485d2749ee5743d1942b68e7f0d5cd4aed178))
- ✨ 自增表格、卡片支持内部依赖 ([8f981a9](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/8f981a9c2bc08d4a4caaff2aaee069c8ab56ddbe))
- ✨ 自定义登录 ([ee27c83](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/ee27c83a5b349fd8292dfcbe0e910caf8bc570dd))
- ✨ 规范全局变量默认值设置&表单提交时添加 loading ([dd19c7b](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/dd19c7bd3d4bfe47cd7da5c4c94769b144bc7513))

## [1.37.6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.37.0...@mdt/product-micro-modules@1.37.6) (2024-04-29)

### Bug Fixes

- 🐛 设置小数位数时报错 ([b7c83b0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/b7c83b0b9375b39c0397d5d51f788606da6429a6))

### Features

- add amis editor ([e11f1e9](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/e11f1e9eac9d81f2763cab3e19c694cd11fac8dd))

# [1.37.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.36.7...@mdt/product-micro-modules@1.37.0) (2024-04-24)

### Bug Fixes

- 🐛 表单提交时类型转换 ([1278133](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/1278133f059bd6f55a08d221e53cd7f785f0639a))

## [1.36.7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.36.6...@mdt/product-micro-modules@1.36.7) (2024-04-24)

### Bug Fixes

- 🐛 表单有默认值时处理 sql 依赖 ([bad6726](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/bad67267245ef8f04aaa59a23cb12f869ec381a1))

## [1.36.6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.36.5...@mdt/product-micro-modules@1.36.6) (2024-04-24)

### Bug Fixes

- 🐛 formily error ([172d9e5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/172d9e599639663f0e5782a4be20d2872783817c))
- 🐛 数值类型使用 numberpicker 组件 ([7e61551](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/7e615511d85c6e599dc3417816af4796e447ad29))
- dev 忽略 ([7e91de6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/7e91de6a1bfdd2b7e998d94d336c628bf0e5832e))

## [1.36.5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.36.4...@mdt/product-micro-modules@1.36.5) (2024-04-23)

### Bug Fixes

- 🐛 全局变量类型显示 ([94c87e6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/94c87e66062711dcc7e634fb9b7bd54997970083))

## [1.36.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.36.3...@mdt/product-micro-modules@1.36.4) (2024-04-23)

### Features

- ✨ 填报后支持显示流程详情页 ([369cf14](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/369cf14217778d259007ad1045c119cf154e5162))

## [1.36.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.36.2...@mdt/product-micro-modules@1.36.3) (2024-04-23)

### Bug Fixes

- 🐛 修复 bpmn 无法使用默认值的问题 ([48d0943](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/48d0943ae8be3c4f4cb425e64ffb4edfeb0b55e8))
- 🐛 部门模块的代码优化 ([b7e80f2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/b7e80f2d3e19e8a477070beae268cf3ab2773db5))

### Features

- ✨ 数据源支持当前机构部门列表 & qlang 依赖项有默认值时没有执行 sql ([c608935](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/c608935ba7667ba1ac9a9eed4cff661db314199e))

## [1.36.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.36.1...@mdt/product-micro-modules@1.36.2) (2024-04-16)

### Bug Fixes

- 🐛 兼容外部数据格式为对象数组的组件 ([e00f832](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/e00f832295e8e85f813eee2c221a6683507ffd01))

## [1.36.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.36.0...@mdt/product-micro-modules@1.36.1) (2024-04-15)

### Features

- ✨ 水平模式、暗色系优化 ([0d7611b](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/0d7611b9b588feb57a5c8b73bb3feea3abbecc62))
- ✨ 选择题支持其它选项 ([768d239](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/768d2397cbdfc199bd6a86225ceccbea38638221))

# [1.36.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.35.3...@mdt/product-micro-modules@1.36.0) (2024-04-08)

**Note:** Version bump only for package @mdt/product-micro-modules

## [1.35.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.35.2...@mdt/product-micro-modules@1.35.3) (2024-04-01)

### Bug Fixes

- 🐛 api path error ([64a38dd](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/64a38ddd463f21ec6877eb2bdd9f8c67de5162a9))

## [1.35.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.35.1...@mdt/product-micro-modules@1.35.2) (2024-04-01)

### Features

- ✨ 切换 app 增加通知,消除双导航 ([58f8c71](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/58f8c719163ed873e9e2c8e3c9609d9534207256))
- ✨ 添加监听主项目主题变化的 init 方法 ([127260b](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/127260b24a97011af6385f01534b0c5a880480cf))
- ✨ 跳转增加 specId ([9ffa019](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/9ffa019d775edf5f769c3bbecdcacf16de0a814a))

## [1.35.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.35.0...@mdt/product-micro-modules@1.35.1) (2024-03-26)

### Bug Fixes

- 🐛 修复默认播放设置的问题和样式 ([4c54993](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/4c549938cd6ec8094a74f2b43067df445d01f29a))

# [1.35.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.34.1...@mdt/product-micro-modules@1.35.0) (2024-03-25)

### Bug Fixes

- 🐛 IframeChannelController 调整 ([51862ce](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/51862ce0c9ba572052398f18e9a7ee32e8e4df12))
- 🐛 修复循环引用问题 ([464589a](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/464589a78a4a6764cf9f11b60198c797b0286dbc))
- 🐛 参数问题 ([bbb8ffd](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/bbb8ffd61dbbe51949258eac89dc51f7fdb4ea07))
- 🐛 我的数据创建失效的问题 ([1643f6b](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/1643f6bbb2329562635a88c1b44b7e1ac1886b8a))

### Features

- ✨ 成员部门和角色增加弹窗形式展开 ([9af2580](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/9af25809e8b6a2a83ff958619442dcdcccd6247e))
- ✨ 模板增加文件夹 ([b2c0f53](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/b2c0f53e91e4e77f989ee9c8f51a2818de177f7e))
- ✨ 添加 iframe 通信类, 增加 iframe 发送退出的消息给主项目 ([5e2a7d5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/5e2a7d563859018c8e4a7e52af864d61a7276cff))
- ✨ 默认播放设置增加筛选 ([c8e182e](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/c8e182e750df0811470e99c31326f4737f192bae))
- ✨ 默认播放设置缩略图懒加载 ([5e6e9bc](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/5e6e9bcf6ea3b161aa622820821d579a520fa0d8))

## [1.34.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.34.0...@mdt/product-micro-modules@1.34.1) (2024-03-11)

**Note:** Version bump only for package @mdt/product-micro-modules

# [1.34.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.33.2...@mdt/product-micro-modules@1.34.0) (2024-03-11)

### Bug Fixes

- 🐛 micro 双反斜杠问题 ([c1abcee](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/c1abcee554119178fea06e19c1bd095dd07dbac1))

### Features

- ✨ 个人数据包文件夹+bff ([dee155f](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/dee155f48d360d2c77f97f945b1a7e27fcc775c9))
- ✨ 文件夹功能 ([9d6680e](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/9d6680e70da17e00d743efcf602480e9e62f0f40))
- ✨ 智能搜索&数据源支持权限及用户列表 ([f717ead](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/f717ead66bfb87cacc9be624dcb6ce4dc0e00f34))

### Performance Improvements

- ⚡️ build fast and smaller ([55b555f](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/55b555f51ce6f1fa8c3ae7387224c52e1636ec27))

## [1.33.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.33.1...@mdt/product-micro-modules@1.33.2) (2024-03-07)

### Bug Fixes

- 🐛 Select 抽离通用模糊搜索 ([22427db](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/22427db98ae72bb805f4ca87381986af14ff3177))
- 🐛 文档模板管理增加更新时间,降序排序 ([f0199c5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/f0199c55bc8815cf0a2ed1cb1a785774eacf12e7))

## [1.33.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.33.0...@mdt/product-micro-modules@1.33.1) (2024-03-01)

### Bug Fixes

- 🐛 修复路由拼接问题 ([e71d703](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/e71d7033faa690c4fa54ce20bf5998e3be04f54c))

# [1.33.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.32.2...@mdt/product-micro-modules@1.33.0) (2024-03-01)

**Note:** Version bump only for package @mdt/product-micro-modules

## [1.32.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.32.1...@mdt/product-micro-modules@1.32.2) (2024-02-28)

**Note:** Version bump only for package @mdt/product-micro-modules

## [1.32.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.32.0...@mdt/product-micro-modules@1.32.1) (2024-02-23)

### Bug Fixes

- 🐛 默认值是 null 时不赋值 ([c408220](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/c4082200d11596c13c3f318ad057fd1d9809ebcc))

# [1.32.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.31.0...@mdt/product-micro-modules@1.32.0) (2024-02-23)

### Features

- ✨ bpmn 变量管理&审批调整 ([c7bf101](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/c7bf101bf05471e43a1cd926094d2d80c0e35754))

# [1.31.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.30.1...@mdt/product-micro-modules@1.31.0) (2024-01-29)

**Note:** Version bump only for package @mdt/product-micro-modules

## [1.30.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.30.0...@mdt/product-micro-modules@1.30.1) (2024-01-23)

### Bug Fixes

- 🐛 remove cache ([b4b37a0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/b4b37a0e8c45b7ec481f64b417f8aa481893d440))
- 🐛 xml decode ([3c1b16a](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/3c1b16a55d959437a58c8df7bc49fd7873a904a5))
- 🐛 审批添加带菜单路由 ([18f133e](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/18f133eda115017add004c1002080b5c8044fc80))
- 🐛 审批表单被隐藏 ([54f96ee](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/54f96ee69eb0c78df8242cb6758cfb68bf5cc0e5))

# [1.30.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.29.0...@mdt/product-micro-modules@1.30.0) (2024-01-15)

### Bug Fixes

- 🐛 无权限时跳转异常(:id) ([c7254a8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/c7254a864f2c7e3c6c820879a2cb47750614ce8b))

### Features

- ✨ bpmn 全局变量相关优化 ([e7b033c](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/e7b033c3641e97300198aa6b84d87bf7bbf6c2f6))
- ✨ 模拟登录增加当前用户快速选择 ([a03388c](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/a03388c2185baba2d19ed0356df399fba8097d10))

# [1.29.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.28.2...@mdt/product-micro-modules@1.29.0) (2024-01-08)

### Bug Fixes

- 🐛 中转逻辑修改 问题修复, 站内消息跳转 ([f4af7c2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/f4af7c25b441a36ce14c27c721f94179fd4ce6cb))

## [1.28.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.28.1...@mdt/product-micro-modules@1.28.2) (2024-01-02)

### Bug Fixes

- 🐛 change the write ([175163a](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/175163a8527ade6f752eb11c7e7a11d5fa8ac73e))

## [1.28.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.28.0...@mdt/product-micro-modules@1.28.1) (2024-01-02)

### Bug Fixes

- 🐛 maybe error ([12469b3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/12469b339bd69f9d2702547a63fad9ab70f576ec))

# [1.28.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.27.3...@mdt/product-micro-modules@1.28.0) (2024-01-02)

### Bug Fixes

- 🐛 memory leak ([d082f51](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/d082f51d1afca676686c260ed50f1e3c5810120d))
- 🐛 中转页逻辑修改 ([58aa238](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/58aa238064b2b3d96ee5cae10a154877b184b5f5))
- 🐛 修复模拟登录在 app 展示的高度异常 ([cc424e3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/cc424e3b494ab98145b634bc9fa7b47d88c7de26))
- 🐛 底部导航栏增加控制 ([66bee6b](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/66bee6b7286e8cfa146132b2e1da55a61f86ff22))

### Features

- ✨ 接入移动端级联&定制表单覆盖初始表单字段值&timer 定制表单 ([e94a877](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/e94a8776bbe3e63f96515202eddddc3cb8b3d632))
- ✨ 消息组件升级 ([1b3957c](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/1b3957c36f941c63dad0c185bfd9284a28af1f7d))

## [1.27.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.27.2...@mdt/product-micro-modules@1.27.3) (2023-12-25)

### Features

- ✨ fill project require ([b171d80](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/b171d80bfc04407875e0e70a480adec227559f63))

## [1.27.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.27.1...@mdt/product-micro-modules@1.27.2) (2023-12-25)

**Note:** Version bump only for package @mdt/product-micro-modules

## [1.27.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.27.0...@mdt/product-micro-modules@1.27.1) (2023-12-25)

**Note:** Version bump only for package @mdt/product-micro-modules

# [1.27.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.26.3...@mdt/product-micro-modules@1.27.0) (2023-12-25)

### Bug Fixes

- 🐛 定时任务日期问题 ([16d59b7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/16d59b784b318027a7a8f13d96846ae4f4904e14))

### Features

- ✨ sso 增加微信和企业微信授权分发流程, 增加分发路由中转页 ([6d341f8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/6d341f8b2136c94d40610edb5f7ce71a1017cc0f))
- ✨ 增加服务导航页 ([296c20a](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/296c20a9eb42f02f8ddbc7c57882cb5970fa0670))
- ✨ 模拟登录样式优化,增加排序 ([910b91b](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/910b91bb9f035eb32e71ccc7b7aa1ec5925e36ad))
- ✨ 表单添加创建者参数& 上传数据包填报模版时支持重选数据包;文件组件添加最大最小数量限制 ([4ebb430](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/4ebb43037bf9d7753c44ad3f89d60b5809adb0e1))

## [1.26.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.26.2...@mdt/product-micro-modules@1.26.3) (2023-12-19)

### Features

- ✨ 数据包填报导出后支持上传 ([337dfc2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/337dfc290d87970bd590137b729aff854aed9c3d))

## [1.26.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.26.1...@mdt/product-micro-modules@1.26.2) (2023-12-18)

### Bug Fixes

- 🐛 memory leak ([d583292](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/d583292b8d7e8b0d4fdd6b1d6df86082af1370db))

### Features

- ✨ bpmn 全局变量设置 & 数据市场审批加 loading ([d505788](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/d50578812eb45b408f596b34a343142240ffefee))
- ✨ 增加移动端消息通知页面 ([65b5b0b](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/65b5b0b1c8bef89d28013611afc1f367a741a1d6))

## [1.26.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.26.0...@mdt/product-micro-modules@1.26.1) (2023-12-11)

**Note:** Version bump only for package @mdt/product-micro-modules

# [1.26.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.25.3...@mdt/product-micro-modules@1.26.0) (2023-12-08)

### Bug Fixes

- 🐛 内存泄露 ([78c1845](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/78c1845ffe65b5c9f2fcdd23f6ba68db5f86932d))
- 🐛 切换 app 后不会跳转到二级域名下,但是不做权限校验 ([70ff746](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/70ff746ef46f06f3ffa11bdb9c1f665ce84f3654))
- 🐛 去掉冗余的方法 ([a86ed8f](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/a86ed8f615315defdaf5dd059faaddc39baf84cf))
- 🐛 水印包裹的写法修改 ([b977848](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/b977848b8f866126c7e240b9a0160d5f64f9f8fd))
- 🐛 水印控制器修改默认值 ([9ec830f](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/9ec830fc8e9c8b73749cfb5c89fe2cd6cee9143d))
- 🐛 水印控制器入口修改 ([af78a7b](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/af78a7b113a2a68cbf853eeb499a55241a8c3f7c))
- 🐛 调整 baseRedirectUrl 的方法 ([97f6984](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/97f69847f9c4ff0cfc5a8d04ef69137e2e636ce7))

### Features

- ✨ 发送消息到钉钉 ([c4c08d0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/c4c08d017b73a55171da60e6679ec002a55d391d))

## [1.25.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.25.2...@mdt/product-micro-modules@1.25.3) (2023-12-04)

### Bug Fixes

- 🐛 build error ([8c2bc99](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/8c2bc9994aeaefd71fcbab822d74364a84eaa5ca))

## [1.25.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.25.1...@mdt/product-micro-modules@1.25.2) (2023-12-04)

### Features

- ✨ 级联选择&下载数据包时支持指定列 ([8a97c2e](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/8a97c2ea17d665b6cf3105bd455f5073d1b56e68))

## [1.25.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.25.0...@mdt/product-micro-modules@1.25.1) (2023-12-01)

**Note:** Version bump only for package @mdt/product-micro-modules

# [1.25.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.24.0...@mdt/product-micro-modules@1.25.0) (2023-12-01)

### Bug Fixes

- 🐛 import wrong ([50b8ff0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/50b8ff00141c6530979d18bac731e6641897cc51))

### Features

- ✨ api cache refactor ([859ab7f](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/859ab7f71706057fb8e08b61a2e82a05d364c45e))
- ✨ 机构偏好 ([92a66b9](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/92a66b963cdd7c2907a76802152bfbb131c51a5d))
- ✨ 自增表格&资源分享支持数据源 ([27820ab](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/27820abbd075bea5471885660183f851e76241b5))

# [1.24.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.23.1...@mdt/product-micro-modules@1.24.0) (2023-11-20)

### Bug Fixes

- 🐛 修改用户过期时间的提示 ([a662ea2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/a662ea271492c4d3ae7eab7d65bea580f86b2478))

### Features

- ✨ pwa ([80985fb](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/80985fbd6ca7f56d6b386959368e3ad44f02905b))
- ✨ 自增卡片 ([7dc9f0b](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/7dc9f0bec8926c123d8583b578e0c0b2bfdd041b))

## [1.23.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.23.0...@mdt/product-micro-modules@1.23.1) (2023-11-13)

### Bug Fixes

- 🐛 冗余引用 ([5f761b5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/5f761b548ba1562d94f6cd1e9bf011c3bf202aad))

# [1.23.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.22.5...@mdt/product-micro-modules@1.23.0) (2023-11-13)

### Bug Fixes

- 🐛 add variable value empty ([93fd932](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/93fd932c1977a3c70235294d2b3792bc514aea3e))

### Features

- ✨ 数据源动态依赖题目 ([7bdfd06](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/7bdfd06bc786ab6dd6c3f044e23b25433e75bad4))
- ✨ 消息通知增加类别,和换行处理 ([931867a](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/931867ab43c5534736b7aee273edd924c99d4d0f))

## [1.22.5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.22.4...@mdt/product-micro-modules@1.22.5) (2023-11-06)

### Bug Fixes

- 🐛 数据包订阅非本机构排除数据质量监控 ([37b7cd3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/37b7cd3945ab653fa9dc9752f047beb7f4b614b5))

## [1.22.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.22.3...@mdt/product-micro-modules@1.22.4) (2023-11-03)

### Features

- ✨ 自定义登录 ([a3d1f99](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/a3d1f99c74c2e73e2e1488cd658f2522c06498d6))

## [1.22.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.22.2...@mdt/product-micro-modules@1.22.3) (2023-11-01)

### Bug Fixes

- 🐛 修复打包报错的问题 ([2785eb5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/2785eb5d713bd478d056a12bdd4ebc19e911fd85))

## [1.22.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.22.1...@mdt/product-micro-modules@1.22.2) (2023-10-31)

**Note:** Version bump only for package @mdt/product-micro-modules

## [1.22.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.22.0...@mdt/product-micro-modules@1.22.1) (2023-10-31)

### Performance Improvements

- ⚡️ html load faster ([e7f2d23](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/e7f2d233d799c6081d3cae94f1ca0663ebe20e28))

# [1.22.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.21.1...@mdt/product-micro-modules@1.22.0) (2023-10-30)

### Bug Fixes

- 🐛 修复通知字词打开, 未读消息和已读消息没有更新的问题 ([a553238](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/a553238b742cefef01e6d186713daec6c0a04874))

### Features

- ✨ 增加全局默认值 ([385fae8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/385fae8753bf74ec8acfd07273a8d80ef87f1b04))

### Performance Improvements

- ⚡️ build faster ([1c14c93](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/1c14c9346709282613bed42addcdfcf7d359575f))

## [1.21.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.21.0...@mdt/product-micro-modules@1.21.1) (2023-10-26)

### Bug Fixes

- 🐛 标题在移动端显示问题 ([39c6224](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/39c62244f12bd43688273ff2e0f8e06cc3be14ce))

# [1.21.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.20.3...@mdt/product-micro-modules@1.21.0) (2023-10-25)

**Note:** Version bump only for package @mdt/product-micro-modules

## [1.20.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.20.2...@mdt/product-micro-modules@1.20.3) (2023-10-23)

### Features

- ✨ 数据包订阅功能 ([f6f43ff](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/f6f43ffe94d07cbdd9d08c63304ebd1dec40030a))

## [1.20.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.20.1...@mdt/product-micro-modules@1.20.2) (2023-10-16)

### Bug Fixes

- 🐛 上传不了文件 ([6821253](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/6821253f00997f48d32a23f6298ba083bba19a31))

## [1.20.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.20.0...@mdt/product-micro-modules@1.20.1) (2023-10-16)

**Note:** Version bump only for package @mdt/product-micro-modules

# [1.20.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.19.0...@mdt/product-micro-modules@1.20.0) (2023-10-10)

### Features

- add ai assiast ([18399e0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/18399e0c30afbc12e9a1ce8647e212b614423341))

# [1.19.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.18.3...@mdt/product-micro-modules@1.19.0) (2023-09-18)

### Features

- ✨ 流程引擎支持发起数据包填报 ([498bc62](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/498bc62727e3a2dffc46e303fc5a3187629f100c))

## [1.18.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.18.2...@mdt/product-micro-modules@1.18.3) (2023-09-04)

### Features

- ✨ operation-log ([5cbb88b](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/5cbb88b7eff44dcfe5f3bb6cad27927347a6ed51))

## [1.18.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.18.1...@mdt/product-micro-modules@1.18.2) (2023-08-22)

### Bug Fixes

- 🐛 virtual-table hover color ([da1f10b](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/da1f10bad7d1002ffeb92556a5f8e5e32ec3b213))
- 🐛 资源分享参数传递 ([08af6e9](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/08af6e9b3c516e96a011a94721f9965a6ba383e7))

## [1.18.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.18.0...@mdt/product-micro-modules@1.18.1) (2023-08-14)

### Bug Fixes

- 🐛 修复执行警告 Cannot update a component ([afad54e](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/afad54ec7fab2c85426a2ed1a4978320ed3b9750))
- 🐛 修改密码点击变为 loading,防止多次调用 ([5aaeb82](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/5aaeb82c63b92ac7fabbcaa60393035170908ad9))

# [1.18.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.17.1...@mdt/product-micro-modules@1.18.0) (2023-07-31)

### Bug Fixes

- 🐛 增加全局 toast 的 close ([cb49d27](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/cb49d27c498061317d0c883fd115f068abf4b5d7))

### Features

- ✨ add totalAsync ([b3bdb28](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/b3bdb287780d82f85c9b1149a688ddc2c4fe0d41))
- ✨ Notices center ([bc24bed](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/bc24bedbe38b612f8f0d507db8951ce2ac3689fa))

## [1.17.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.17.0...@mdt/product-micro-modules@1.17.1) (2023-07-26)

### Bug Fixes

- 🐛 跨机构缓存问题 ([3d017c9](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/3d017c955a16ea98f9d37427a18872a0fbfdba62))

# [1.17.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.16.7...@mdt/product-micro-modules@1.17.0) (2023-07-24)

### Performance Improvements

- ⚡️ 性能优化(秒开) ([7142db5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/7142db546bb7e1ea57651c700d2745e1f57a3a60))

## [1.16.7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.16.6...@mdt/product-micro-modules@1.16.7) (2023-07-03)

### Bug Fixes

- 🐛 default-play add preview params ([9b2033f](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/9b2033f9f5fe2a93878286246cc3bc690631dce1))

## [1.16.6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.16.5...@mdt/product-micro-modules@1.16.6) (2023-07-03)

### Bug Fixes

- 🐛 用户选择器组件 禁用角色时不能选择用户 ([f6c5a03](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/f6c5a03428548a19244a1271daa90176befb0a7b))
- 🐛 默认播放设置修改偏好参数,修改国际化 ([58b2910](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/58b2910f84e748d1e5f9fb93d411e6dde2690a93))

### Features

- ✨ 通过数据包详情页发起流程填报 ([5c2d77b](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/5c2d77b6910e07dc9bf9a1e02dd0162c85ebb671))

## [1.16.5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.16.4...@mdt/product-micro-modules@1.16.5) (2023-06-20)

**Note:** Version bump only for package @mdt/product-micro-modules

## [1.16.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.16.3...@mdt/product-micro-modules@1.16.4) (2023-06-13)

### Bug Fixes

- 🐛 @i18n-chain/react add dependencies ([5fc7e47](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/5fc7e47c557a03343e4885594bbcd24dbaec912c))

## [1.16.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.16.2...@mdt/product-micro-modules@1.16.3) (2023-06-12)

### Bug Fixes

- 🐛 sso verify issue handle ([e00aec1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/e00aec103ad155a3e028687455fcf0f76f5e34f0))

## [1.16.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.16.1...@mdt/product-micro-modules@1.16.2) (2023-06-12)

### Bug Fixes

- 🐛 i18n handle ([eee0959](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/eee09596c3e713c85ea50a6ab28f9f096664a741))

### Features

- ✨ 定时支持分钟 ([24d3c96](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/24d3c9630b72ced7911253bb1ba11338f98fbed1))

## [1.16.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.16.0...@mdt/product-micro-modules@1.16.1) (2023-06-06)

### Bug Fixes

- 🐛 预览地理数据退出登录 ([908a9d1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/908a9d1971d30635d5de149d021a61c51c8788b6))

# [1.16.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.15.8...@mdt/product-micro-modules@1.16.0) (2023-06-05)

### Bug Fixes

- 🐛 fixed cancel sub bug ([1f5469e](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/1f5469eeeb581c0876da6b6e89b4fcd2495b0bc9))
- 🐛 i18n fixed ([4bcf571](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/4bcf5713b1808157ae75c18213f165e065246c06))

### Features

- ✨ all i18n finished ([449c38e](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/449c38eb7a0e33455ab4c2abd846079158a7ba9d))

## [1.15.8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.15.7...@mdt/product-micro-modules@1.15.8) (2023-05-22)

### Bug Fixes

- 🐛 fixed build error ([62adfaa](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/62adfaa8f37326024e005b40717b97643dd0faf1))

### Features

- ✨ delete v1 api, update impersonate sub perferences to v2 ([2029340](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/2029340fed2368448fb723d99f8ab9f02ce2e850))

## [1.15.7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.15.6...@mdt/product-micro-modules@1.15.7) (2023-05-15)

**Note:** Version bump only for package @mdt/product-micro-modules

## [1.15.6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.15.5...@mdt/product-micro-modules@1.15.6) (2023-04-27)

**Note:** Version bump only for package @mdt/product-micro-modules

## [1.15.5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.15.4...@mdt/product-micro-modules@1.15.5) (2023-04-24)

### Bug Fixes

- 🐛 切换语言偏好上传 ([87d0b60](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/87d0b60af2e3e202c3179a7553caa7c082ffab40))
- 🐛 增加对 redirect 的解码处理,建议编码传入 ([a86e129](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/a86e129f4171e03d298ab08e612142175d5b0778))

## [1.15.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.15.3...@mdt/product-micro-modules@1.15.4) (2023-04-17)

### Features

- ✨ 默认 app 过期时,可由用户选择其他 app ([239a93f](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/239a93f73239092d0f78266cf74c6a30ef44b767))

## [1.15.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.15.2...@mdt/product-micro-modules@1.15.3) (2023-04-11)

### Bug Fixes

- 🐛 资源分享时包含素材 ([1f0c8a1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/1f0c8a187699d5ce552ab96e3ec470d623fea958))

## [1.15.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.15.1...@mdt/product-micro-modules@1.15.2) (2023-04-10)

### Bug Fixes

- 🐛 流程表单提升层级;资源分享支持脚本 ([6261924](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/6261924f22f7f2722f98c49959b055bbf8c24b75))

## [1.15.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.15.0...@mdt/product-micro-modules@1.15.1) (2023-04-07)

**Note:** Version bump only for package @mdt/product-micro-modules

# [1.15.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.14.3...@mdt/product-micro-modules@1.15.0) (2023-04-06)

### Features

- ✨ 帮助中心 ([06aa72d](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/06aa72d79cb740aff2542456c8ae68cf37f4bc46))
- ✨ 部门新增用户的所有功能 ([6243bba](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/6243bbac2ad421e5551dffce383b6a0343bd9814))

## [1.14.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.14.2...@mdt/product-micro-modules@1.14.3) (2023-03-24)

### Bug Fixes

- 🐛 修复新建用户没有手机号的 bug ([90791f1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/90791f11be7f6270c236511b10e359bf9c4c1c95))

## [1.14.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.14.1...@mdt/product-micro-modules@1.14.2) (2023-03-20)

### Bug Fixes

- 🐛 权限去掉产品过滤 ([397413e](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/397413e14312780605505bf76e5ca1f90c2c150d))
- 🐛 通用顶栏增加回调 func ([666f02c](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/666f02c3c066ec27d7fd142510e3e4144b172809))

## [1.14.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.14.0...@mdt/product-micro-modules@1.14.1) (2023-03-14)

### Bug Fixes

- 🐛 个人数据可以编辑名称 ([55eec80](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/55eec80b2651ddb025e4fcb87f662795476d347c))

# [1.14.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.13.5...@mdt/product-micro-modules@1.14.0) (2023-03-13)

### Bug Fixes

- 🐛 隐藏资源共享 tip ([1e6770f](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/1e6770f9c077017bc4c3110b392e5af6e38b19df))

### Features

- ✨ add workflow ([646e475](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/646e4758659a102c60ff745761e112f3c5d10958))
- ✨ 为抽象类和偏好类增加获取和修改属性的方法 ([6cb0ac1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/6cb0ac1d892afb42f1198f8e1f310113a00b66cf))
- ✨ 权限列表增加流程引擎产品的权限面板 ([35c85af](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/35c85afd6e41dabe8a0bfa72394fda9c1d5fb9d9))

## [1.13.5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.13.2...@mdt/product-micro-modules@1.13.5) (2023-02-20)

### Features

- ✨ add workflow ([85b5104](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/85b5104b8c48d8b4b11a9a4269a6f5067ac87488))
- ✨ 增加用户批量修改信息功能 ([d4e1521](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/d4e15216d524145f7268f3fcad45ea1338fb431f))
- ✨ 增加解除锁定用户功能 ([0a91cd7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/0a91cd70777d9b28062beada8e144bc25f73a3d9))

## [1.13.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.13.1...@mdt/product-micro-modules@1.13.2) (2023-02-13)

### Features

- ✨ app-header 同步增加配置 ([3ff609a](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/3ff609aaa13c1f4b82fcdb4db3dfe2a9a187c27a))

## [1.13.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.13.0...@mdt/product-micro-modules@1.13.1) (2023-02-08)

### Bug Fixes

- 🐛 [app-header]: 去掉挂载主题的方法 ([b9a7098](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/b9a7098ff157ecc77ea4062b4f47038c7ad393ac))
- 🐛 [appHeader]: 接收的默认主题参数可以修改全局主题 ([2c57b6e](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/2c57b6ed2648d856913c42420fb6190697d25a05))

### Performance Improvements

- ⚡️ [模拟登录]: 性能优化，排序参数修改 ([78ff0cb](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/78ff0cbbf6ddac1e38b20cb03150b1e6dc0f4e29))

# [1.13.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.12.4...@mdt/product-micro-modules@1.13.0) (2023-02-06)

### Bug Fixes

- 🐛 [模拟登录]: 优化 ([cf2d205](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/cf2d2051758d2e44d74526bf4799d689552570dd))

### Features

- 🎸[个人设置]: 增加修改手机号和邮箱号的功能 ([e9f681a](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/e9f681a35fb9d72c94c534ae7b2799b21145f696))

## [1.12.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.12.3...@mdt/product-micro-modules@1.12.4) (2023-01-30)

### Bug Fixes

- 🐛 [授权管理]: 修复拥有部门的用户,其权限还能修改的 bug ([5bbe074](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/5bbe074388a082c92581392a1eaebceb86f42ec1))
- 🐛 显示 display_geometry;数据包数据展示不全 ([f99af61](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/f99af61838dd229a529fc844b9ec1ad97069d3df))

### Features

- ✨ [顶栏]: 增加 collector 跳转 ([0202323](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/0202323e075ae62dc01b5c4f48b632335b5cbed5))
- ✨ [顶栏]: 增加跳转方式的配置, 产品根据配置项决定跳转方式 ([2c3975f](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/2c3975fba0e5ce8bff6e8f325e6731a8f3258661))

## [1.12.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.12.2...@mdt/product-micro-modules@1.12.3) (2023-01-06)

### Bug Fixes

- 🐛 个人数据不做权限校验 ([93d7826](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/93d7826c1e6b00884414d1001987ad966a5c7e8f))

### Features

- ✨[我的数据]双地理处理;[数据市场]排行榜新增详情入口；[资源共享]分享页面时连带 flow ([5af6057](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/5af6057359309185041192f9f10b5049a47c0a06))

## [1.12.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.12.1...@mdt/product-micro-modules@1.12.2) (2023-01-03)

### Bug Fixes

- 🐛 [权限表]: 修复一个权限可能会多次点击无效的 bug ([e34c4b5](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/e34c4b5dfee8fa9de39489245ba1bf4b737a38ee))

### Features

- ✨ [部门用户]: 增加模板批量导入导出 ([ffdb07c](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/ffdb07cc938bd2ab3d8552f3865cb4912f64ca70))
- ✨ 新增帮助中心 ([bca156e](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/bca156ed842a05bd7613185a893524ed64b0c3ed))
- ✨ 资源分享支持项目、flow ([23268aa](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/23268aaf4467655f911d25749304059012960dff))

## [1.12.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.12.0...@mdt/product-micro-modules@1.12.1) (2022-12-20)

**Note:** Version bump only for package @mdt/product-micro-modules

# [1.12.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.11.0...@mdt/product-micro-modules@1.12.0) (2022-12-20)

**Note:** Version bump only for package @mdt/product-micro-modules

# [1.11.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.10.4...@mdt/product-micro-modules@1.11.0) (2022-12-13)

### Bug Fixes

- 🐛 fix supper use error ([54c6839](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/54c6839e5a01e76d83c07fa2a7cc21c091a8278a))

## [1.10.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.10.3...@mdt/product-micro-modules@1.10.4) (2022-12-12)

**Note:** Version bump only for package @mdt/product-micro-modules

## [1.10.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.10.2...@mdt/product-micro-modules@1.10.3) (2022-12-12)

### Bug Fixes

- 🐛 [过期时间提示]: 存储到偏好,改为用户过期时间提醒 ([4f5daec](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/4f5daec9f3f6a4979455b59dccfa146a87243f54))

### Features

- ✨ [Table 通用]: 可选列表增加全选功能 ([e3e368a](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/e3e368ae2c5e3224e26aea0e0c47ed580ee4ee14))

## [1.10.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.10.1...@mdt/product-micro-modules@1.10.2) (2022-12-06)

**Note:** Version bump only for package @mdt/product-micro-modules

## [1.10.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.10.0...@mdt/product-micro-modules@1.10.1) (2022-12-05)

### Features

- ✨ [我的数据] 新版协作编辑 ([66df912](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/66df9124ee1ac035a1d51d699f78514c2c1b2052))
- ✨ dm bff ([6ac4ea6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/6ac4ea6bcd73077633e67745abe694a62fda6536))

# [1.10.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.9.4...@mdt/product-micro-modules@1.10.0) (2022-11-30)

### Features

- ✨ [资源共享] 支持分享个人数据包、机构数据包 ([3d66dab](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/3d66dab5e48a864d6a6fb1f0cb7f2d700c1d6f55))

## [1.9.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.9.3...@mdt/product-micro-modules@1.9.4) (2022-11-29)

### Bug Fixes

- 🐛 打开 mapEditor 支持多窗口 ([1c99a57](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/1c99a5724eb2d875100523a9ca4b029d65ad27a1))

## [1.9.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.9.2...@mdt/product-micro-modules@1.9.3) (2022-11-28)

### Bug Fixes

- 🐛 [个人设置]: 修复主题切换下次进来没有改变的问题 ([49d6685](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/49d6685a6f5b2e1f41220afbfad677af0683fc55))
- 🐛 [个人资料]: 修复点击确定还会再发一次验证码的问题 ([e814fb4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/e814fb4591a4ab53286db550553cacadd2efb4ab))

### Features

- ✨ [偏好设置]: Logo 样式模块增加,通用顶栏适配增加 ([894b2be](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/894b2beab961f3e427e3b8c9a178845f76ba5e4d))

## [1.9.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.9.1...@mdt/product-micro-modules@1.9.2) (2022-11-21)

### Bug Fixes

- 🐛 fix ([0b11490](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/0b114902369505586123747dbfbfc6e68674a8c0))
- 🐛[我的数据] 预览地理数据时携带 token，创建 sql 数据包 ownership 设默认值;[数据市场]跨机构加偏好 ([3cc0415](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/3cc0415f87ce227476c32c44b5c54c483727bcdf))

### Features

- ✨ [通用顶栏]: 加进入后台功能 ([79255f9](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/79255f97117626a91679c26ae4290e0c70514486))

## [1.9.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.9.0...@mdt/product-micro-modules@1.9.1) (2022-11-15)

**Note:** Version bump only for package @mdt/product-micro-modules

# [1.9.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.8.1...@mdt/product-micro-modules@1.9.0) (2022-11-14)

### Bug Fixes

- 🐛 [通用顶栏]: 修复 app 禁止切换的逻辑和空页面样式修复 ([bb19399](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/bb19399cefc7bcce9b844c3fb96e0be3033aa188))

### Features

- ✨ [全家桶]: 主题切换, 深色适配 ([549d854](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/549d8543f36ad6b7fa16403cc5bc8d5053341395))
- ✨ [我的数据]新增跳转 mode ([dfccdf6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/dfccdf6f52318ff46fce452c6e8bf05c91df2081))
- ✨ [用户管理]: 大模块重构 ([f3fa668](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/f3fa668174590ea5020a9bb910b22bd37d4de067))
- ✨ [通用顶栏]: 更新 ([3db1330](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/3db1330e027bca4e5480e15416af52bcce3ff2e7))
- ✨ 数据市场、我的数据优化 ([02957c7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/02957c727b6a298f5b021f378f2adf0a7f4e0855))
- ✨ 数据申请简化 ([74a9fc8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/74a9fc800171e9d78ca1b0cdaa9cf0a7f83b3ac7))
- ✨ 转地理数据前新增默认地理列 ([405a286](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/405a2867fdbb1c4b626f98a753b60bba39761842))
- ✨ 预览地理数据;主题库加搜索框;表格预览排序 ([77680b6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/77680b64fa85358356ac554d6c92460098c6a520))

## [1.8.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.8.0...@mdt/product-micro-modules@1.8.1) (2022-11-01)

**Note:** Version bump only for package @mdt/product-micro-modules

# [1.8.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.7.4...@mdt/product-micro-modules@1.8.0) (2022-10-31)

### Features

- ✨ 数据市场、我的数据 优化 ([40ac4c6](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/40ac4c67afebdfc586da4221e2cb934c6bea51db))

## [1.7.4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.7.3...@mdt/product-micro-modules@1.7.4) (2022-10-25)

### Bug Fixes

- 🐛 自定义数据源中 SQL 数据包会生成为个人数据 ([92b6d8f](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/92b6d8f1a8ffc9406339ecc417c951be009575b6))

### Features

- ✨ 数据看板 ([9a85fb8](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/9a85fb81fd7086a856c298f659795deef0f69721))
- ✨ 新节点-同比/环比计算 ([0aaf000](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/0aaf000a94f6bad75815d60f096a3bc839214387))

## [1.7.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.7.2...@mdt/product-micro-modules@1.7.3) (2022-10-21)

**Note:** Version bump only for package @mdt/product-micro-modules

## [1.7.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.7.1...@mdt/product-micro-modules@1.7.2) (2022-10-18)

**Note:** Version bump only for package @mdt/product-micro-modules

## [1.7.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.7.0...@mdt/product-micro-modules@1.7.1) (2022-10-10)

**Note:** Version bump only for package @mdt/product-micro-modules

# [1.7.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.6.0...@mdt/product-micro-modules@1.7.0) (2022-09-30)

### Features

- ✨ 可见数据设置 ([c540a22](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/c540a22a0c8493d9c462318b8e1d9bb256d80dce))
- ✨ 数据市场 依赖偏好获取主题库、app ([4209802](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/42098024fda0f9457b6daca013b5c922e6c99ea1))

# [1.6.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.5.3...@mdt/product-micro-modules@1.6.0) (2022-09-26)

### Bug Fixes

- 🐛 定时任务实时更新下次运行时间&起止日期字段禁用小于当前时间的选择 ([dade027](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/dade027eff753d87b7d45608671bbee60f3fa17d))

### Features

- ✨ [通用头部]: 展示 app 名称 ([0023743](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/0023743c13165b1eb4e61a432346d866ea259ca2))
- ✨ 定时任务增加下次运行时间 ([628e919](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/628e919be01438930f8626c4b57a8509e2d9a69a))

## [1.5.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.5.2...@mdt/product-micro-modules@1.5.3) (2022-09-19)

**Note:** Version bump only for package @mdt/product-micro-modules

## [1.5.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.5.1...@mdt/product-micro-modules@1.5.2) (2022-09-19)

**Note:** Version bump only for package @mdt/product-micro-modules

## [1.5.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.5.0...@mdt/product-micro-modules@1.5.1) (2022-09-15)

### Bug Fixes

- 🐛 兼容深色模式+跨机构搜索无效 ([c6e05b1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/c6e05b1d970bcc0af133d34ca147309924cf72d9))

# [1.5.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.4.1...@mdt/product-micro-modules@1.5.0) (2022-09-14)

### Features

- ✨ 放开以下功能入口：个人数据发布、审批；创建空数据包；数据更新新增追加 ([a626e0d](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/a626e0d726c96cdd9fab375b5129d208a6f1d197))

## [1.4.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.4.0...@mdt/product-micro-modules@1.4.1) (2022-09-13)

### Bug Fixes

- 🐛 隐藏个人数据包发布相关功能 ([8f9ef6b](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/8f9ef6bf088ecb6c46e2be3cb5e2894e63dbaa2b))

# [1.4.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.3.2...@mdt/product-micro-modules@1.4.0) (2022-09-13)

### Features

- ✨ [个人设置]: 手机邮箱和第三方的绑定验证 ([c489c50](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/c489c50978adea72fd6bfa871fcaa5e59d6d1eda))
- ✨ sql 节点增加 SQL 编辑器 ([ab24b04](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/ab24b04eb260eda6f3d291f90e1334987f3f6220))
- ✨ 数据包创建、详情+血缘图优化+个人数据发布审批 ([35a345c](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/35a345cc293c4f4c2adb0186ae7fac41c0ede6ad))

## [1.3.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.3.1...@mdt/product-micro-modules@1.3.2) (2022-09-05)

**Note:** Version bump only for package @mdt/product-micro-modules

## [1.3.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.3.0...@mdt/product-micro-modules@1.3.1) (2022-08-30)

**Note:** Version bump only for package @mdt/product-micro-modules

# [1.3.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.2.1...@mdt/product-micro-modules@1.3.0) (2022-08-29)

### Bug Fixes

- 🐛 取值默认 appId 地址变更 ([49e899b](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/49e899bdd7cb8480647e969f1aadcb4f405c7a90))

## [1.2.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/product-micro-modules@1.2.0...@mdt/product-micro-modules@1.2.1) (2022-08-12)

### Bug Fixes

- 🐛 [模拟登录]: 无法正常使用的诸多问题修复 ([13f36dd](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/13f36dd7a8af3b1ad0aeb065276ef7f6da923f3c))

# 1.2.0 (2022-08-12)

### Bug Fixes

- 🐛 fix issues ([8789043](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/8789043ceb6cf02c1355c3e01b6e0c2e5a0fbca9))
- 🐛 markdown 相关问题 ([b66e416](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/b66e4162ae9d7f2a59ad5a691dca71aa5bed07e2))
- 🐛 主题库扁平化 ([66f6407](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/66f6407e62d0fe939e2345d629a6144aa80d7035))
- 🐛 修复引用报错 ([626ec2c](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/626ec2c95c555aa755fe618c9bc373d651746521))
- 🐛 修改新数据工厂的导航栏 ([53cb069](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/53cb069ac6ea490fb2df555de01f723fb207c5c1))
- 🐛 切换 app 时跳 sso 更新 token ([297c72c](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/297c72c70aacd83d327824e6cc4a135dca7054e1))
- 🐛 循环应用 ([8443062](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/8443062465cced469833d6e45bd08e085448f33a))
- 🐛 样式污染 ([77282b2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/77282b20211338b0a2a5f13724b5220b6fc1ad35))
- 🐛 样式覆盖 ([bbc3676](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/bbc36768433d4947ede9785be6a885e7f24ed778))

### Features

- ✨ [通用 header]: 功能扩展，样式优化 ([d843e94](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/d843e9438b766313f08cfbcba5734f88d852ccfb))
- ✨ xflow canvas 渲染函数&modelServices 类型调整 ([423df49](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/423df4961658dd013ef2abb6bf9a706f22bb216e))
- ✨ 增加允许产品跳转的配置项,修改菜单栏的样式 ([c4b803f](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/c4b803fb802e3d1dcef64a7a44ab37aa95e31992))
- ✨ 机构管理 ([b79db5b](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/b79db5b545ba159b25a910f39b34cd851e322020))
- ✨ 机构管理初版配套修改 ([a7311bf](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/a7311bfeeb5c5dcc3b2509fb21ece8baefb10216))
- ✨ 模板管理 ([9da7e90](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/9da7e904cacec51c3c25df6ce6602da53b46eb37))
- ✨ 画布侧边拖拽组件&XFlow Canvas 画布组件 ([e429970](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/e429970a4fa22f0414bf16a83210eb88a38948c7))
- ✨ 血缘图 ([d9e9402](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/d9e94027679dc46458a0f50022805c64652c81a8))
- ✨ 血缘图 ([dbe2c30](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/dbe2c308386df9286f60026925486879c7aab965))

### Performance Improvements

- ⚡️ [模拟登录]: 左侧列表继承 curd ([40eb062](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/40eb0621cf2481dd48f33e739f05e36d856e76db))

# 1.1.0 (2022-06-30)

### Features

- ✨ 通用分离 ([8a90e4b](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/8a90e4b5ee9bf1cdd6dd15d86dfa54b989f35dfc))
