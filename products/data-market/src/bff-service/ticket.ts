import {
  BaseBffService,
  batchcreateTicket,
  createTicket,
  IBatchcreateTicketOptions,
  ICreateTicketOptions,
  IQueryTicketListOptions,
  IUpdateTicketResolutionOptions,
  queryTicketFirstPage,
  queryTicketNextPage,
  updateTicketResolution,
} from '@mdtBsBffServices/services';

export class DMTicketBffService extends BaseBffService {
  public queryTicketList(options: IQueryTicketListOptions, isFirstPage = true) {
    return isFirstPage
      ? queryTicketFirstPage(this.productName, options)
      : queryTicketNextPage(this.productName, options);
  }

  public createTicket(options: ICreateTicketOptions) {
    return createTicket(this.productName, options);
  }

  public batchcreateTicket(options: IBatchcreateTicketOptions) {
    return batchcreateTicket(this.productName, options);
  }

  public updateTicketResolution(options: IUpdateTicketResolutionOptions) {
    return updateTicketResolution(this.productName, options);
  }
}
