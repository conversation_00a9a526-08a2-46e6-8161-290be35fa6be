.node-form-config {
  position: absolute;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  width: 380px;
  background: var(--dmc-page-100-color);
  border: 1px solid var(--dmc-split-page-color);

  form {
    width: 100%;
  }
}

.node-form-config-item {
  width: 100%;
  padding: 12px;
  border-bottom: 1px solid var(--dmc-split-page-color);

  .dmc-select {
    width: 100%;
  }
}

.node-form-config-title {
  display: flex;
  gap: 12px;
  align-items: center;
  height: 50px;
  padding-left: 12px 0 12px 2px;
  color: var(--dmc-text-8);
  font-weight: 500;
  font-size: 13px;
  font-style: normal;
  line-height: 26px;
  transition: all 0.3s ease 0s;

  .title {
    cursor: pointer;
  }

  .titleIcon {
    margin-left: 2px;
    color: var(--dmc-blue-500-color);
    cursor: pointer;
  }
}

.node-form-config-title:hover {
  background-color: var(--dmc-page-600-color);
}

.nodeWrap {
  width: 100%;
  height: 100%;
  padding: 12px;
  overflow-x: hidden;
  overflow-y: scroll;
  color: var(--dmc-text-color);

  .ant-formily-item-label {
    color: var(--dmc-text-color);
  }

  .anticon-question-circle {
    color: var(--dmc-text-color);
  }

  .ant-formily-item-extra {
    color: var(--dmc-text-color);
  }

  .dmc-input-textarea {
    width: 100%;

    textarea {
      width: 100%;
    }
  }

  .dmc-select-multiple {
    cursor: pointer;
  }

  .node-form-formGrid {
    .mdt-formily-item {
      margin-bottom: 5px !important;
    }
  }

  .dmc-date-picker {
    width: 100%;
  }

  .dmc-date-picker-suffix,
  .dmc-date-picker-clear-icon {
    line-height: 1;
  }
}

.mdt-formily-collapse-item {
  border: none !important;
}
