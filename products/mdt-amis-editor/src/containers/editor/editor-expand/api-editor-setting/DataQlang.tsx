import _ from 'lodash';
import { CSSProperties, FC, useRef, useState } from 'react';
import { Field, ObjectField, observer } from '@formily/react';
import { VisibilityOn } from '@metro/icons';
import { Button } from '@metroDesign/button';
import { Drawer } from '@metroDesign/drawer';
import { formatQlang } from '@sql-generator/utils/dist/sqlparse';
import { RequestTypeEnum } from '@mdtProMicroModules/amis-expand/constants';
import { IEditor, IMonaco, MonacoEditor } from '@mdtProMicroModules/pages/form-editor/formily-externals/monaco-editor';
import i18n from '../../../../languages';
import { SqlEditor } from './SqlEditor';
import { VariableSelect } from './VariableSelect';

const BTN_STYLE: CSSProperties = {
  position: 'absolute',
  top: 10,
  right: 13,
  padding: 0,
  fontSize: 12,
};
const SqlEditorComp: FC = observer((props: any) => {
  const { value, onChange, ...resetProps } = props;
  const editorRef = useRef<IEditor>();
  const [open, setOpen] = useState(false);
  const changeRef = useRef(onChange);
  const valueRef = useRef(value);
  changeRef.current = onChange;
  valueRef.current = value || {};

  const finishedEdit = (editor: IEditor, monaco: IMonaco) => {
    const model = editor.getModel()!;
    const newValue = _.trim(editor.getValue());
    const markers = monaco.editor.getModelMarkers({ resource: model.uri });
    // 如果有错误，则不保存
    if (markers.length) return;
    const fc = formatQlang(newValue);
    if (valueRef.current.sql === fc) return;
    editor.setValue(fc);
    changeRef.current({ ...valueRef.current, sql: fc });
  };

  const openDrawer = () => {
    setOpen(true);
  };

  const onMount = (editor: IEditor) => {
    editorRef.current = editor;
  };

  const onClose = () => {
    setOpen(false);
  };

  const onConfirm = (payload: any) => {
    const fc = formatQlang(payload.sql);
    editorRef.current!.setValue(fc);
    changeRef.current!(payload);
    setOpen(false);
  };

  return (
    <>
      <MonacoEditor
        {...resetProps}
        height={400}
        language="sql"
        value={formatQlang(valueRef.current.sql)}
        fullScreenTitle={i18n.chain.fullEdit}
        finishedEdit={finishedEdit}
        onMount={onMount}
      />
      <Button type="primary" size="small" ghost style={BTN_STYLE} icon={<VisibilityOn />} onClick={openDrawer}>
        {i18n.chain.previewEdit}
      </Button>
      <Drawer bodyStyle={{ padding: 0 }} footer={null} open={open} closable={false} width="80%" push={false}>
        {open ? <SqlEditor onClose={onClose} data={valueRef.current.editor} onConfirm={onConfirm} /> : null}
      </Drawer>
    </>
  );
});

export const DataQlang: FC<{ parentName: string }> = observer(({ parentName }) => {
  return (
    <ObjectField
      name="qlang"
      reactions={(field) => {
        const type = field.query(`..${parentName}`).get('value');
        field.setState({ display: type === RequestTypeEnum.QLANG ? 'visible' : 'hidden' });
      }}
    >
      <Field name="variable" title={i18n.chain.sqlDep} decorator={['FormItem']} component={[VariableSelect]} />
      <Field required name="editor" title="Sql" decorator={['FormItem']} component={[SqlEditorComp]} />
    </ObjectField>
  );
});
