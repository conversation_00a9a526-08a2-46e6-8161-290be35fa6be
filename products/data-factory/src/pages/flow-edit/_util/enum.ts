// 节点分类
export enum NodeRenderKeyEnum {
  EXTRACTOR_NODE = 'extractor_node',
  TRANSFORMER_NODE = 'transformer_node',
  LOADER_NODE = 'loader_node',
  EXECUTE_NODE = 'execute_node',
  CREATE_NODE = 'create_node',
}

export enum NodeTypeEnum {
  // extractor
  DATAPKG = 'from_datapkg',
  FILE = 'from_file_id',
  SQL = 'from_sql',
  FROM_QLANG_RAW = 'from_qlang_raw',
  TABLE = 'from_db_table',
  REMOTE_FILE = 'from_file',
  FS_SHEET = 'from_feishu_spreadsheet',
  FS_TABLE = 'from_feishu_bitable',
  DK_SHEET = 'from_dingtalk_sheet',
  DK_PROCESS_INSTANCES = 'from_dingtalk_process_instances',
  WECHAT_WORK_APPROVAL_DATA = 'from_3rd_wechat_approval_data',

  // transformer
  APPEND = 'append',
  REPLACE = 'replace',
  RENAME = 'rename',
  INSERT_CONST_COLUMN = 'insert_const_column',
  INSERT_SEQUENCE_COLUMN = 'insert_sequence_column',
  ONLY_COLUMNS = 'only_columns',
  COPY_COLUMN = 'copy_column',
  JOIN = 'join',
  SJOIN = 'sjoin',
  PCT_CHANGE = 'pct_change',
  PIPE = 'pipe',
  UPSERT_DATAFRAME = 'upsert',
  STRIP = 'strip',
  TYPECAST = 'typecast',
  NULL_IF = 'null_if',
  VALUE_MAPPING = 'value_mapping',
  GROUPBY = 'groupby',
  // TODO 新增节点
  DROP_DUPLICATES = 'drop_duplicates',
  DROP_COLUMMNS = 'drop_columns',
  SORT = 'sort',
  FILTER = 'filter',
  GEOCODE = 'geocode',
  LNGLAT_TO_GEOMETRY = 'lnglat_to_geometry',
  CONVERT_GEOMETRY = 'convert_geometry',

  // execute
  EXECUTE_SQL = 'execute_sql',
  RUN_FLOW = 'run_flow',

  // create
  CREATE_TABLE = 'create_table',

  // loader
  MARKET = 'to_market',
  UPSERT_DB_TABLE = 'to_upsert_db_table',
  UPDATE_DATAPKG = 'to_update_datapkg',
}

export enum ExecuteNodeTypeEnum {
  EXECUTE_SQL = 'execute_sql',
  RUN_FLOW = 'run_flow',
}

export enum CreateNodeTypeEnum {
  CREATE_TABLE = 'create_table',
}

export enum NodeStatusEnum {
  SUCCESS = 'success',
  ERROR = 'error',
  INFO = 'info',
  WARNING = 'warning',
  PROCESSING = 'processing',
  NORMAL = 'normal',
  LOCATION = 'location',
  // NO_COMPLETE = 'no_complete',
  // COMPLETE = 'complete',
}

export enum NodeGroupEnum {
  EXTRACTOR = 'extractor',
  TRANSFORMER = 'transformer',
  LOADER = 'loader',
  CREATE = 'create',
  EXECUTE = 'execute',
}

export enum GraphPortGroupEnum {
  IN = 'in',
  OUT = 'out',
}

export enum JunctionNameEnum {
  // 默认
  DEFAULT_JUNCTION_NAME = '_default_',
  // join sjoin
  JOIN_JUNCTION_NAME = 'right',
  // pipe append
  APPEND_JUNCTION_NAME = 'others',
  // upsert
  UPSERT_JUNCTION_NAME = 'new',
}

export enum FlowParamsKeyEnum {
  NODE_PARAMS = 'nodeParams',
  EDGE_RELATION = 'edgeRelation',
  DATA_SOURCE = 'dataSourceMap',
  CHANGE_OPERATION = 'columnsChangeOperationMap',
}

export enum RunNodeActionEnum {
  UPDATA_LABEL = 'update_label',
}

export enum GraphEventEnum {
  NODE_ADDED = 'node:added',
  NODE_CLICK = 'node:click',
  NODE_DBLCLICK = 'node:dblclick',
  BLANK_CLICK = 'blank:click',
  EDGE_CONNECTED = 'edge:connected',
  EDGE_CLICK = 'edge:click',
  EDGE_REMOVED = 'edge:removed',
}
