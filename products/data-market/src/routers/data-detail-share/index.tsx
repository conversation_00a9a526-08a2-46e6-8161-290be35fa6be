import { useRef } from 'react';
import { useParams } from 'react-router-dom';
import { useCreation } from 'ahooks';
import { DatapkgDetailPreview, DatapkgDetailPreviewController } from '@mdtProMicroModules/pages/datapkg-detail-preview';
import { DatapkgDetailPreviewModel } from '@mdtProMicroModules/pages/datapkg-detail-preview/DatapkgDetailPreviewModel';
import { DatapkgDetailPreviewModelBff } from '@mdtProMicroModules/pages/datapkg-detail-preview/DatapkgDetailPreviewModelBff';
import { getModel } from '../../_util/modelUtil';

const DataDetailShare = () => {
  const ref = useRef<DatapkgDetailPreviewController>();
  const { id } = useParams<Record<string, string>>();

  return useCreation(() => {
    ref.current?.destroy();
    const controller = new DatapkgDetailPreviewController({
      pkgId: id,
      Model: getModel(DatapkgDetailPreviewModel, DatapkgDetailPreviewModelBff),
    });
    ref.current = controller;
    return <DatapkgDetailPreview key={id} controller={controller} />;
  }, [id]);
};

export default DataDetailShare;
