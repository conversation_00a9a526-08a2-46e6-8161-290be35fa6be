import _ from 'lodash';
import { createDesigner, Engine, ITreeNode, KeyCode, Shortcut, TreeNode } from '@designable/core';
import { IFormilySchema, transformToTreeNode } from '@designable/formily-transformer';
import { transformToSchema } from '@designable/formily-transformer';
import type { ISchema } from '@formily/json-schema';
import { BehaviorSubject } from 'rxjs';
import { debounceTime, skip } from 'rxjs/operators';
import type { ILabelValue } from '@mdtBsComm/interfaces';
import { DebounceTimeEnum } from '@mdtProComm/constants';
import { isFormHiddenKey } from '@mdtProComm/utils/questionUtil';
import { MetroSettingProp } from '@mdtProFormEditor/metro-form-design/shared';
import i18n from '../../languages';
import { modifyResidentInfoDefaultSetting } from '../../utils/oneTableUtil';
import type { IOnPageCancelCb, IOnPageSuccessCb } from '../drawer-within-page';
import { DataValue } from './service/data-value';
import { defaultConfig, formDefaultConfig } from './default-config';
import { ICustomerDefaultSettingFunc } from './interface';
import { DataSource, saveSchema } from './service';
import { generateNewQuestionSchema } from './transformer';
import {
  ArrayBaseCompEnum,
  arrayBaseComps,
  ensureSameStyle,
  FORM_NODE_ID,
  hasDataSource,
  isArrayBaseComp,
  mergeNodePropsWithSetting,
  modifySavedFormilySchemaAndSentting,
  NODE_COMP,
  NODE_COMP_PROPS,
  NODE_DECO_PROPS,
  NODE_PROPS_ID,
  transformAllSettingValuesToSave,
  transformFormilySchemaToSave,
  transformSavedAllSettingValues,
  transformSavedFormilySchema,
} from './util';

export interface IFormEditorControllerProps {
  theme?: string;
  editorSchema?: IFormEditorSchema;
  optionsMap?: Record<string, ILabelValue[]>;
  validate?: (values: IFormEditorSchema) => string | undefined;
  defaultSettingFunc?: ICustomerDefaultSettingFunc;
  onSuccessCb?: IOnPageSuccessCb;
  onCancelCb?: IOnPageCancelCb;
}

export interface IFormEditorSchema {
  formilySchema: IFormilySchema;
  allSettingValues: Record<string, ISchema>;
  dataSource?: DataSource;
}

// 默认值管理器接口
interface IDefaultValueManager {
  getDefaultValue: (componentName: string, context?: IDefaultValueContext) => any;
  mergeWithExisting: (defaultValue: any, existingValue: any) => any;
  processSpecialCases: (node: ITreeNode, targetNode: TreeNode, defaultValue: any) => any;
}

interface IDefaultValueContext {
  node?: ITreeNode;
  targetNode?: TreeNode;
  isClone?: boolean;
  isReplaceType?: boolean;
  preserveProps?: string[];
}

// 定制组件处理器接口
interface ICustomComponentProcessor {
  componentName: string;
  processToSave: (properties: Record<string, any>, engine: Engine) => Record<string, any>;
  processFromSave?: (properties: Record<string, any>) => Record<string, any>;
  getDefaultValue?: (context?: IDefaultValueContext) => any;
}

// 定制组件管理器
class CustomComponentManager {
  private processors = new Map<string, ICustomComponentProcessor>();

  public registerProcessor(processor: ICustomComponentProcessor) {
    this.processors.set(processor.componentName, processor);
  }

  public processToSave(properties: Record<string, any>, engine: Engine): Record<string, any> {
    let result = { properties, engine };

    // @ts-ignore
    for (const processor of this.processors.values()) {
      result = {
        properties: processor.processToSave(result.properties, result.engine),
        engine: result.engine,
      };
    }

    return result.properties;
  }

  public processFromSave(properties: Record<string, any>): Record<string, any> {
    let result = properties;

    // @ts-ignore
    for (const processor of this.processors.values()) {
      if (processor.processFromSave) {
        result = processor.processFromSave(result);
      }
    }

    return result;
  }

  public getDefaultValue(componentName: string, context?: IDefaultValueContext): any {
    const processor = this.processors.get(componentName);
    return processor?.getDefaultValue?.(context);
  }
}

// ResidentInfo 定制组件处理器
class ResidentInfoProcessor implements ICustomComponentProcessor {
  public componentName = 'ResidentInfo';

  public processToSave(properties: Record<string, any>, engine: Engine): Record<string, any> {
    // 使用原有的 processArrayBaseCompToSave 逻辑
    const hasArrayBaseComp = !!_.filter(_.values(properties), (it: any) => it['x-component'] === this.componentName)
      .length;
    if (!hasArrayBaseComp) return properties;

    const schema = transformToSchema(engine.getCurrentTree());
    const list = _.values(schema.schema?.properties);
    const arrayBaseProperties = _.filter(list, (it: any) => it['x-component'] === this.componentName);
    const toRemoveKeys: string[] = [];

    _.forEach(arrayBaseProperties, (it: any) => {
      const key = it.name || it[NODE_PROPS_ID];
      // 获取所有子属性的 key
      const getAllKeys = (props: Record<string, any>) => {
        const keys: string[] = [];
        _.forEach(props, (p) => {
          keys.push(p[NODE_PROPS_ID]);
          p.properties && keys.push(...getAllKeys(p.properties));
          _.isObject(p.items) && keys.push(p.items[NODE_PROPS_ID]);
        });
        return keys;
      };

      toRemoveKeys.push(...getAllKeys(it.items?.properties || {}));
      toRemoveKeys.push(...getAllKeys(it.properties || {}));

      // 转换属性 - 简化处理，避免循环依赖
      const transformProperty = (prop: Record<string, any>) => {
        // 对于 ResidentInfo，我们只需要保持其结构，不需要递归转换
        return prop;
      };

      properties[key] = transformProperty(it);
    });

    _.forEach(toRemoveKeys, (key) => {
      delete properties[key];
    });
    return properties;
  }

  public getDefaultValue(context?: IDefaultValueContext): any {
    if (context?.node) {
      return modifyResidentInfoDefaultSetting(context.node);
    }
    return undefined;
  }
}

// 默认值管理器实现
class DefaultValueManager implements IDefaultValueManager {
  public constructor(
    private defaultConfig: Record<string, any>,
    private customerDefaultSettingFunc?: ICustomerDefaultSettingFunc,
    private controller?: FormEditorController,
    private customComponentManager?: CustomComponentManager,
  ) {}

  public getDefaultValue(componentName: string, context?: IDefaultValueContext): any {
    // 先检查是否有定制组件处理器
    if (this.customComponentManager) {
      const customDefault = this.customComponentManager.getDefaultValue(componentName, context);
      if (customDefault) {
        return customDefault;
      }
    }

    const defaultValue = this.defaultConfig[componentName] || {};

    // 如果是 Form 组件，需要特殊处理
    if (componentName === 'Form') {
      const formDefault = _.cloneDeep(defaultValue);
      if (this.controller) {
        formDefault['x-metro-default-value'].defaultConfig.map.value = this.controller.getDefaultValueFiledMap();
      }
      return formDefault;
    }

    // 如果有自定义默认值函数且有上下文，调用它
    if (context?.node && context?.targetNode && this.customerDefaultSettingFunc) {
      const customDefault = this.customerDefaultSettingFunc(context.node, context.targetNode);
      if (customDefault) {
        return { ...defaultValue, ...customDefault };
      }
    }

    return defaultValue;
  }

  public mergeWithExisting(defaultValue: any, existingValue: any): any {
    return { ...defaultValue, ...existingValue };
  }

  public processSpecialCases(node: ITreeNode, targetNode: TreeNode, defaultValue: any): any {
    let result = { ...defaultValue };

    // 处理表格列标题
    const noNeedTitle = _.get(targetNode, 'parent.props.x-component') === 'ArrayTable.Column';
    if (noNeedTitle) {
      result.title = '';
    }

    // 处理子节点布局
    if (targetNode.depth > 1 && this.controller) {
      result[NODE_DECO_PROPS] = {
        layout: _.get(this.controller.getAllSettingValues(), 'form-node-id.layout'),
        colon: _.get(this.controller.getAllSettingValues(), 'form-node-id.colon'),
      };
    }

    return result;
  }
}

export class FormEditorController {
  public currentNodeData$: BehaviorSubject<any> = new BehaviorSubject(null);
  public onSuccessCb?: IOnPageSuccessCb;
  public onCancelCb?: IOnPageCancelCb;
  private _engine: Engine;
  private _theme: string;
  private _initSchemaUpdate$ = new BehaviorSubject('N');
  private _optionsMap?: Record<string, ILabelValue[]>;
  // 移除手动维护的 allSettingValues 和 formilySchema，改为从 engine 实时获取
  // private allSettingValues: Record<string, ISchema> = {};
  // private formilySchema: IFormilySchema = {};
  // 字段节点实例
  private nodeFiledMap: Record<string, any> = {};
  // 默认值管理器
  private defaultValueManager: IDefaultValueManager;
  // 定制组件管理器
  private customComponentManager: CustomComponentManager;
  private nodeSetting$ = new BehaviorSubject<any>(null);
  private dataSource: DataSource;
  private dataValue: DataValue;
  private customerValidate: (values: IFormEditorSchema) => string | undefined;
  private customerDefaultSettingFunc?: ICustomerDefaultSettingFunc;

  public constructor(props?: IFormEditorControllerProps, onSuccessCb?: IOnPageSuccessCb, onCancelCb?: IOnPageCancelCb) {
    const {
      theme = 'light',
      editorSchema,
      optionsMap,
      validate = this.defaultCustomerValidate,
      defaultSettingFunc,
      onSuccessCb: fbOnSuccessCb,
      onCancelCb: fbOnCancelCb,
    } = props || {};
    this.onSuccessCb = onSuccessCb || fbOnSuccessCb;
    this.onCancelCb = onCancelCb || fbOnCancelCb;
    this.customerValidate = validate;
    this.customerDefaultSettingFunc = defaultSettingFunc;
    defaultConfig['Form'] = _.cloneDeep(formDefaultConfig);
    this._theme = theme;
    this._engine = createDesigner({
      shortcuts: [
        new Shortcut({
          codes: [
            [KeyCode.Meta, KeyCode.S],
            [KeyCode.Control, KeyCode.S],
          ],
          handler(ctx) {
            saveSchema(ctx.engine);
          },
        }),
      ],
      rootComponentName: 'Form',
    });
    this.dataSource = new DataSource(this._engine);
    this.dataValue = new DataValue(this._engine);

    // 初始化定制组件管理器
    this.customComponentManager = new CustomComponentManager();
    // 注册 ResidentInfo 定制组件处理器
    this.customComponentManager.registerProcessor(new ResidentInfoProcessor());

    // 初始化默认值管理器
    this.defaultValueManager = new DefaultValueManager(
      defaultConfig,
      this.customerDefaultSettingFunc,
      this,
      this.customComponentManager,
    );
    // 监听节点拖入事件，初始化节点默认值
    this._engine.subscribeTo(FromNodeEvent, (event) => {
      this.saveNodeFiledMap(event.data.target);
      this.initializeNodeDefaultValues(event.data.source, event.data.target);
    });
    // 监听节点删除事件，清理节点映射
    this._engine.subscribeTo(RemoveNodeEvent, (event) => {
      this.saveNodeFiledMap(event.data.target, true);
    });
    // 保留设置变更监听，用于处理特殊逻辑
    this.listenNodeSettingChange();
    if (editorSchema) {
      requestAnimationFrame(() => {
        this.initSchema(editorSchema, optionsMap);
      });
    }
  }

  public destroy() {
    this.currentNodeData$.complete();
    this.currentNodeData$.next(null);
    this.onSuccessCb = undefined;
    this.onCancelCb = undefined;
    this._engine = null!;
    this._initSchemaUpdate$.complete();
    this._optionsMap = undefined;
    // allSettingValues 和 formilySchema 已移除，不再手动维护
    this.nodeFiledMap = {};
    this.nodeSetting$.complete();
    this.nodeSetting$.next(null);
    this.dataSource.destroy();
    this.dataValue.destroy();
    this.customerValidate = this.defaultCustomerValidate;
  }

  public getDataSource() {
    return this.dataSource;
  }

  public getDataValue() {
    return this.dataValue;
  }

  public get engine() {
    return this._engine;
  }

  public get theme() {
    return this._theme;
  }

  public get initSchemaUpdate$() {
    return this._initSchemaUpdate$;
  }

  public get optionsMap() {
    return this._optionsMap;
  }

  // 为 DefaultValueManager 提供访问 allSettingValues 的方法
  public getAllSettingValues() {
    return this.getCurrentAllSettingValues();
  }

  // 初始化editor schema
  public initSchema(editorSchema?: IFormEditorSchema, optionsMap?: Record<string, ILabelValue[]>) {
    this._optionsMap = optionsMap;
    let val = editorSchema || { formilySchema: {}, allSettingValues: {} };
    val = modifySavedFormilySchemaAndSentting(val);

    // 转换保存的数据格式为运行时格式
    const transformedFormilySchema = transformSavedFormilySchema(val.formilySchema);
    const transformedAllSettingValues = transformSavedAllSettingValues(val.allSettingValues);

    // 将设置值合并到 node.props 中，然后设置到 engine
    this.mergeSettingValuesIntoSchema(transformedFormilySchema, transformedAllSettingValues);

    // 直接设置到 engine，不再手动维护 formilySchema 和 allSettingValues
    this._engine.setCurrentTree(transformToTreeNode(transformedFormilySchema));
    this.initSchemaUpdate$.next('Y');
  }

  // 预览时无需触发外部校验
  public getEditorSchemaToSave(disableCustomerValidate = false): IFormEditorSchema | string {
    const errMsg = this.validate();
    if (errMsg) return errMsg;

    // 从 engine 实时获取 formilySchema 和 allSettingValues
    const currentFormilySchema = this.getCurrentFormilySchema();
    const currentAllSettingValues = this.getCurrentAllSettingValues();

    const data = {
      formilySchema: this.transformFormilySchemaToSaveWithCustomComponents(currentFormilySchema, this._engine),
      allSettingValues: transformAllSettingValuesToSave(currentAllSettingValues, currentFormilySchema),
    };

    ensureSameStyle(data);

    const customerErrorMsg = !disableCustomerValidate ? this.customerValidate(data) : '';
    if (customerErrorMsg) return customerErrorMsg;

    return data;
  }

  // 属性修改后刷新node tree
  public refreshNodeTree(node: TreeNode, setting: any) {
    this.nodeSetting$.next({ node, setting });
  }

  // 修改题型后替换node
  public replaceNodeType(node: TreeNode, newComponentName: string) {
    const currentProps = node.props || {};

    // 使用默认值管理器获取默认值
    const context: IDefaultValueContext = {
      node: node as ITreeNode,
      targetNode: node,
      isReplaceType: true,
      preserveProps: ['x-index', 'title', 'name', 'dataSource', NODE_PROPS_ID, NODE_DECO_PROPS],
    };
    const defaultSettingValue = this.defaultValueManager.getDefaultValue(newComponentName, context);

    // 生成新的 node props
    const newProps = {
      ...generateNewQuestionSchema(newComponentName, defaultSettingValue),
      ..._.pick(currentProps, ['x-index', 'title', 'name', 'dataSource', NODE_PROPS_ID, NODE_DECO_PROPS]),
    };

    // 更新 node.props，让 formily engine 自动更新 schema
    node.props = newProps;

    // 如果有数据源设置，则创建数据源对象
    if (hasDataSource(newComponentName, _.get(newProps, MetroSettingProp.datasource))) {
      this.dataSource.addNodeDataSource(node.id, newProps);
    }
  }

  public initNodeForm(nodeId: string, values: any) {
    const ds = this.dataSource.getData(nodeId);
    const hasDataSource = ds && values?.[MetroSettingProp.datasource];
    const nodeData = hasDataSource ? this.dataSource.addNodeDataSource(nodeId, values, true) : null;
    this.currentNodeData$.next(nodeData);
  }

  public getDefaultValueFiledMap() {
    const defaultValueList: any[] = [];
    const editorSchema = this.getEditorSchemaToSave();
    if (typeof editorSchema !== 'string') {
      const filterKeys = ['form_submitter__BPMN_ROOT_NODE_KEY'];
      _.forEach(_.values(editorSchema.formilySchema.schema?.properties), (item: any) => {
        if (_.includes(filterKeys, item.name)) return;
        defaultValueList.push({ label: item.name, value: item.name, select: true });
      });
    }
    return defaultValueList;
  }

  // 属性修改后或添加新的node后更新editorSchema
  // 直接更新 node.props，让 formily engine 自动处理 schema 构建
  private updateNodeProps(node: ITreeNode, values: any) {
    const isForm = node.componentName === 'Form';

    if (isForm) {
      // Form 节点的处理
      let horizontalStyle = { labelCol: 24 };
      if (values.layout === 'horizontal') {
        horizontalStyle = { labelCol: 6 };
      }
      node.props = { ...node.props, ...values, ...horizontalStyle };
    } else {
      // 普通字段节点的处理
      node.props = mergeNodePropsWithSetting(node, values);
    }
  }

  // removeEditorNode 方法已不需要，因为不再手动维护 allSettingValues

  // 从 engine 实时获取 formilySchema，替代手动维护的 formilySchema
  private getCurrentFormilySchema(): IFormilySchema {
    return transformToSchema(this._engine.getCurrentTree());
  }

  // 从 formily engine 实时获取所有设置值，并补充默认值
  private getCurrentAllSettingValues(): Record<string, ISchema> {
    const currentTree = this._engine.getCurrentTree();
    const allSettingValues: Record<string, ISchema> = {};

    // 遍历所有节点，提取设置值
    const extractSettingValues = (node: TreeNode) => {
      if (!node.id) return;

      const componentName = node.componentName;
      const nodeProps = node.props || {};

      // 从 node.props 中提取设置值
      let settingValue: any = {};

      if (componentName === 'Form') {
        // Form 节点的设置值
        settingValue = {
          layout: nodeProps.layout || 'vertical',
          colon: nodeProps.colon,
          labelCol: nodeProps.labelCol,
          ...nodeProps,
        };
      } else {
        // 普通字段节点的设置值
        settingValue = {
          title: nodeProps.title,
          name: nodeProps.name,
          required: nodeProps.required,
          'x-decorator-props': nodeProps[NODE_DECO_PROPS],
          'x-component-props': nodeProps[NODE_COMP_PROPS],
          ...this.extractCustomSettingValues(nodeProps, componentName),
        };
      }

      // 补充默认值
      const context: IDefaultValueContext = {
        node: node as ITreeNode,
        targetNode: node,
      };
      const defaultValue = this.defaultValueManager.getDefaultValue(componentName, context);

      // 合并默认值和当前值
      allSettingValues[node.id] = {
        ...defaultValue,
        ...settingValue,
      };

      // 递归处理子节点
      node.children?.forEach((child) => extractSettingValues(child));
    };

    if (currentTree) {
      extractSettingValues(currentTree);
    }

    return allSettingValues;
  }

  // 从 node.props 中提取定制的设置值
  private extractCustomSettingValues(nodeProps: any, componentName: string): any {
    const customValues: any = {};

    // 根据组件类型提取特定的设置值
    switch (componentName) {
      case 'Input':
        if (nodeProps[NODE_COMP_PROPS]?.placeholder) {
          customValues.placeholder = nodeProps[NODE_COMP_PROPS].placeholder;
        }
        break;
      case 'Select':
      case 'Radio.Group':
      case 'Checkbox.Group':
        if (nodeProps[NODE_COMP_PROPS]?.options) {
          customValues.options = nodeProps[NODE_COMP_PROPS].options;
        }
        break;
      case 'ResidentInfo':
        // ResidentInfo 的特殊处理
        if (nodeProps[NODE_COMP_PROPS]) {
          customValues.residentFieldList = nodeProps[NODE_COMP_PROPS].residentFieldList;
        }
        break;
      // 可以继续添加其他组件的特殊处理
    }

    return customValues;
  }

  // 将设置值合并到 schema 的 node.props 中
  // eslint-disable-next-line sonarjs/cognitive-complexity
  private mergeSettingValuesIntoSchema(formilySchema: IFormilySchema, allSettingValues: Record<string, ISchema>) {
    const properties = formilySchema.schema?.properties;
    if (!properties) return;

    // 处理 Form 节点
    const formSettings = allSettingValues[FORM_NODE_ID];
    if (formSettings && formilySchema.form) {
      formilySchema.form = {
        ...formilySchema.form,
        ...formSettings,
      };
    }

    // 处理字段节点
    const mergeNodeSettings = (props: Record<string, any>) => {
      _.forEach(props, (property) => {
        const nodeId = property[NODE_PROPS_ID];
        const settings = allSettingValues[nodeId];

        if (settings) {
          // 将设置值合并到 property 中
          property.title = settings.title || property.title;
          property.name = settings.name || property.name;
          property.required = settings.required || property.required;

          if (settings[NODE_DECO_PROPS]) {
            property[NODE_DECO_PROPS] = {
              ...property[NODE_DECO_PROPS],
              ...settings[NODE_DECO_PROPS],
            };
          }

          if (settings[NODE_COMP_PROPS]) {
            property[NODE_COMP_PROPS] = {
              ...property[NODE_COMP_PROPS],
              ...settings[NODE_COMP_PROPS],
            };
          }

          // 合并其他设置值
          _.forEach(settings, (value, settingKey) => {
            if (!['title', 'name', 'required', NODE_DECO_PROPS, NODE_COMP_PROPS].includes(settingKey)) {
              property[settingKey] = value;
            }
          });
        }

        // 递归处理子属性
        if (property.properties) {
          mergeNodeSettings(property.properties);
        }
        if (property.items?.properties) {
          mergeNodeSettings(property.items.properties);
        }
      });
    };

    mergeNodeSettings(properties);
  }

  // 使用定制组件管理器转换 formilySchema 为保存格式
  private transformFormilySchemaToSaveWithCustomComponents(
    formilySchema: IFormilySchema,
    engine: Engine,
  ): IFormilySchema {
    const copiedSchema = _.cloneDeep(formilySchema);
    const properties = copiedSchema.schema?.properties;
    if (!properties) return copiedSchema;

    // 使用定制组件管理器处理定制组件
    const processedProperties = this.customComponentManager.processToSave(properties, engine);

    // 使用原有的 transformFormilySchemaToSave 处理其他组件，但跳过 ResidentInfo
    const tempSchema = { ...copiedSchema, schema: { ...copiedSchema.schema, properties: processedProperties } };
    return transformFormilySchemaToSave(tempSchema, engine);
  }

  // fillDefaultValue 相关方法已不需要，因为不再手动维护状态
  // 默认值将在 getCurrentAllSettingValues 中自动补充

  // getSettingValue 方法已不需要，因为不再手动维护 allSettingValues

  // getChildrenNodeLayout 方法已移动到 DefaultValueManager.processSpecialCases 中

  // 存储字段节点实例, 方便后续操作
  private saveNodeFiledMap(node: TreeNode | TreeNode[], remove?: boolean) {
    const nodes = Array.isArray(node) ? node : [node];
    if (remove) {
      for (const d of nodes) {
        delete this.nodeFiledMap[d.id];
      }
    } else {
      for (const d of nodes) {
        this.nodeFiledMap[d.id] = node;
      }
    }
  }

  private listenNodeSettingChange() {
    this.nodeSetting$.pipe(skip(1), debounceTime(DebounceTimeEnum.NORMAL)).subscribe((val) => {
      if (!val) return;
      const { node, setting } = val;
      this.updateNodeProps(node, setting);

      // 处理 Form 布局变化对子节点的影响
      const isForm = node.componentName === 'Form';
      if (isForm) {
        const newLayout = setting.layout;
        node.eachChildren((subNode: TreeNode) => {
          const decoProps = _.get(subNode, 'props.x-decorator-props');
          if (!decoProps) return;
          if (decoProps.layout === newLayout) return false;
          decoProps.layout = newLayout;
        });
      }
    });
  }

  private validate() {
    const baseErrMsg = this.baseValidate();
    if (baseErrMsg) {
      console.log('base validate error', baseErrMsg, this.getCurrentFormilySchema(), this.getCurrentAllSettingValues());
      return baseErrMsg;
    }

    const arrayBaseErrMsg = this.validateArrayBaseComps();
    if (arrayBaseErrMsg) {
      console.log(
        'array base validate error',
        arrayBaseErrMsg,
        this.getCurrentFormilySchema(),
        this.getCurrentAllSettingValues(),
      );
      return arrayBaseErrMsg;
    }
  }

  // 字段校验
  private baseValidate() {
    const prefixOfNoNeedNamesComs = _.map(arrayBaseComps, (comp) => `${comp}.`);
    const currentSchema = this.getCurrentFormilySchema();
    const obj = _.values(currentSchema.schema?.properties);
    const properties = _.filter(
      obj,
      (it: any) => !_.some(prefixOfNoNeedNamesComs, (prefix) => _.startsWith(it[NODE_COMP], prefix)),
    );
    const validFields: string[] = [];
    const repeatedNames: string[] = [];
    const noNameFields: string[] = [];
    const existNameMap: Record<string, true> = {};
    _.forEach(properties, (it: any) => {
      const compName = it[NODE_COMP];
      const name = it.name;
      const title = it.title;
      const nodeId = it[NODE_PROPS_ID];
      if (!compName || isFormHiddenKey(nodeId)) return;
      if (name) {
        validFields.push(name);
        if (existNameMap[name]) {
          repeatedNames.push(title || 'name');
        } else {
          existNameMap[name] = true;
        }
      } else {
        noNameFields.push(title);
      }
    });
    if (!_.isEmpty(noNameFields))
      return `${i18n.chain.proMicroModules.fillDesign.columnFieldEmptyTip} ${_.join(noNameFields, ', ')}`;
    if (!_.isEmpty(repeatedNames))
      return `${i18n.chain.proMicroModules.fillDesign.columnFieldRepeatTip} ${_.join(repeatedNames, ', ')}`;
    if (_.isEmpty(validFields)) return i18n.chain.proMicroModules.fillDesign.columnEmptyTip;
  }

  private validateArrayBaseComps() {
    let result = '';
    const schema = transformToSchema(this.engine.getCurrentTree());
    const list = _.values(schema.schema?.properties);
    const arrayBaseProperties = _.filter(list, isArrayBaseComp);
    _.forEach(arrayBaseProperties, (abItem: any) => {
      const comp = abItem[NODE_COMP];
      const children = _.get(abItem, 'items.properties');
      let hasChildren = true;
      if (comp === ArrayBaseCompEnum.ARRAY_CARDS) {
        hasChildren = _.some(children, (child) => !_.startsWith(child[NODE_COMP], ArrayBaseCompEnum.ARRAY_CARDS));
      } else if (comp === ArrayBaseCompEnum.ARRAY_TABLE) {
        hasChildren = _.some(children, (col) => !!_.size(col.properties));
      }
      if (!hasChildren) {
        result = i18n.chain.proMicroModules.fillDesign.dragFieldToForm(abItem.title || abItem[NODE_PROPS_ID]);
        return false;
      }
    });
    return result;
  }

  private defaultCustomerValidate() {
    return undefined;
  }
}
