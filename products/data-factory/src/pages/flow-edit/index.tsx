import { FC } from 'react';
import { useXFlowApp } from '@antv/xflow';
import { useObservableState } from '@mdtBsComm/hooks/use-observable-state';
import { ModalWithBtnsComp } from '@mdtBsComponents/modal-with-btns-comp';
import { ModalWithBtnsCompDrawer } from '@mdtBsComponents/modal-with-btns-comp-drawer';
import Spin from '@mdtDesign/spin';
import { NodeCollapsePanel } from '@mdtProMicroModules/components/canvas-collapse-panel';
import { FlowCanvas as XFlowCanvas } from '@mdtProMicroModules/containers/xflow-canvas';
import { DrawerSqlDataTabelPreview } from '@mdtProMicroModules/pages/sql-data-tabel-preview';
import DataView from './custom-component/data-view';
import NodeFormConfig from './custom-component/node-form-config';
import { useFlowEditContext, useFlowEditProvider } from './flowEditContext';
import FlowEditController from './FlowEditController';
import './index.less';

/* 自定义节点侧边栏 */
const NodeSideMenu: FC = () => {
  const { flowEditController } = useFlowEditContext();
  const flowEditConfigController = flowEditController.getFlowEditConfigController();

  return (
    <NodeCollapsePanel
      searchService={flowEditConfigController.getSearchService}
      onNodeDrop={flowEditConfigController.onNodeDrop}
      nodeDataService={flowEditConfigController.getNodeDataService}
      position={{ top: 0, bottom: 0, left: 0, width: 210 }}
    />
  );
};

// canvas 内置组件
const CanvasWidget: FC = () => {
  // const { flowEditController } = useFlowEditContext();
  const app = useXFlowApp();

  return (
    <>
      {app && <NodeFormConfig app={app} position={{ top: 0, bottom: 0, right: 0, width: 380 }} />}

      <DataView position={{ bottom: 2, left: 0 }} />
    </>
  );
};

interface IProps {
  controller: FlowEditController;
}

export const DrawerFlowEdit: FC<IProps> = ({ controller }) => {
  const Provider = useFlowEditProvider();
  const val = { flowEditController: controller };

  return (
    <Provider value={val}>
      <ModalWithBtnsComp controller={controller} />
    </Provider>
  );
};

const FlowEdit: FC<IProps> = ({ controller }) => {
  const Provider = useFlowEditProvider();
  const val = { flowEditController: controller };
  const pageLoading = useObservableState(() => controller.getPageLoading());
  const flowEditConfigController = controller.getFlowEditConfigController();

  const keyBindings = { config: flowEditConfigController.getKeybindingConfig() };

  const canvasComponent = pageLoading ? (
    <Spin />
  ) : (
    <>
      <XFlowCanvas
        controller={controller.getFlowConfigController()}
        xflowChildren={<NodeSideMenu />}
        xflowCanvasChildren={<CanvasWidget />}
        xFlowProps={{ commandConfig: flowEditConfigController.getCmdConfig() }}
        xFlowCanvasProps={{
          position: { top: 0, bottom: 0, left: 210, right: 0 },
        }}
        xFlowCanvasWidgetProps={{ keyBindings }}
      />
      <DrawerSqlDataTabelPreview controller={controller.getDrawerSqlDataTabelController()} />
      <ModalWithBtnsCompDrawer controller={controller.getdrawerGlobalController()} />
    </>
  );

  return (
    <Provider value={val}>
      <div className="low-code-etl-edit-container">{canvasComponent}</div>
    </Provider>
  );
};

export default FlowEdit;
