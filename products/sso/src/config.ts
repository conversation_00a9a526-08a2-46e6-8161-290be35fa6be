import _ from 'lodash';
import { decompressFromEncodedURIComponent } from 'lz-string';
import { initCommConfig } from '@mdtProSso/config/ssoCommConfig';
import {
  initDingtalkConfig,
  initWechatConfig,
  initWechatOAuthConfig,
  initWechatWorkOAuthConfig,
  initZzDingConfig,
  initZzDingOAuthConfig,
} from '@mdtProSso/config/third-party-config';
import { handleConfigWithProjectFile, handleConfigWithUrlFile } from '@mdtProSso/utils/fileHandle';
import {
  getConfigFromUrl,
  getConfigNamesapceFromUrl,
  getConfigPathFromUrl,
  getRedirectFromUrl,
  isDialog,
} from './_util/util';

const CUSTOM_CONFIG_KEY = 'custom_config_key';
const orginConfig = window.__DM_SSO_CFS;

const namespaceUrl = getConfigNamesapceFromUrl();
const redirectUrl = getRedirectFromUrl();
const configPath = getConfigPathFromUrl();
const dialogType = isDialog();
const configUrl = getConfigFromUrl();
// 指定域名匹配对应配置项
const originConfigCustom = decompressFromEncodedURIComponent(
  (orginConfig.productOriginCustomMap || {})?.[window.location.origin],
);
const parseConfig = configUrl || originConfigCustom;

const customLoginConfig = JSON.parse(sessionStorage.getItem(CUSTOM_CONFIG_KEY) || '{}');
const configByCustom = handleConfigWithUrlFile(
  '__DM_SSO_CFS',
  parseConfig ? `${orginConfig.backendApiUrl}/public/datamap/v2/files/p/${parseConfig}` : '',
);
const confgByProject = handleConfigWithProjectFile(configPath, redirectUrl);

let cfs = orginConfig;
// 根据目录替换配置文件
!_.isEmpty(configByCustom) && (cfs = configByCustom);
// 根据fileid替换配置文件
!_.isEmpty(confgByProject) && (cfs = confgByProject);
// 根据本地存储（一般是通过通知做自定义展示）override配置文件
if (!_.isEmpty(customLoginConfig)) {
  cfs = { ...cfs, ...customLoginConfig };
  sessionStorage.removeItem(CUSTOM_CONFIG_KEY);
}
// 自定义配置不允许覆盖的参数
if (cfs !== orginConfig) {
  cfs.deployPublicPath = orginConfig.deployPublicPath;
  cfs.backendApiUrl = orginConfig.backendApiUrl;
  cfs.deployEnableHashRouter = orginConfig.deployEnableHashRouter;
  cfs.backendApiUrlBasePath = orginConfig.backendApiUrlBasePath;
}
// 根据配置的命名空间查找
const namespaceConfig = orginConfig?.productNamespaceCustomMap?.[namespaceUrl] || {};
if (namespaceUrl && !_.isEmpty(namespaceConfig)) {
  cfs = { ...cfs, ...namespaceConfig };
}

window.__DM_SSO_CFS = cfs;

const newCfs = initCommConfig(
  'sso',
  {
    isDevelop: __IS_DEVELOPMENT,
    developProxyApiUrl: __DEVELOP_PROXY_API_URL,
    developEnvOrigin: __DEVELOP_ENV_ORIGIN,
  },
  { isDialog: dialogType, redirect: redirectUrl },
);

initDingtalkConfig(newCfs.productDingtalkId || 'dingoavbvm1rkermenetpj');
initWechatConfig(newCfs.productWechatId || 'wx7a501afcc0a053ef');
initWechatOAuthConfig(newCfs.productWechatOAuthId || 'wx9db14b6130787f9c', cfs.productWechatOAuthOptions);
initWechatWorkOAuthConfig(newCfs.productWechatWorkOAuthId || 'ww3374f6e82f9a0a51', cfs.productWechatWorkOAuthOptions);
initZzDingConfig(
  newCfs.productZzdingId || 'yh_syrk_dingoa',
  newCfs.productZzdingRedirect || 'https://syrk.yhxxfw.cn/auth/login/bydingtalkauthcode',
);
initZzDingOAuthConfig(newCfs.productZzdingOAuthCorpId || '196729');

// 部署的环境
__webpack_public_path__ = newCfs.deployPublicPath;

// 配置提供的appId
const configAppId = parseConfig?.match(/(\d+)\//)?.[1];
export const CONFIG_APP_ID = configAppId ? _.parseInt(configAppId) : undefined;

export const PRODUCT_AUTH_MAP = cfs.productAuthMap || {
  '681301': 'dataapp',
  '681302': 'datamarket',
  '681309': 'datafactory',
};
export const NO_CHOOSE_APP = cfs.productNoChooseApp ?? false;
export const SHOW_PASSWORD_FORGET = cfs.productShowPasswordForget ?? true;

export * from '@mdtProSso/config/ssoCommConfig';
export * from '@mdtProSso/config/third-party-config';
