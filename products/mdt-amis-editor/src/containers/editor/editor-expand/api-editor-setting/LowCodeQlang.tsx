import { CSSProperties, FC, useRef, useState } from 'react';
import { Field, ObjectField, observer } from '@formily/react';
import { VisibilityOn } from '@metro/icons';
import { Button } from '@metroDesign/button';
import { Drawer } from '@metroDesign/drawer';
import { type BuilderJsonConfig, type BuilderRef } from '@sql-generator/sql-builder';
import { getQlang } from '@sql-generator/sql-builder-mdt';
import { formatQlang } from '@sql-generator/utils/dist/sqlparse';
import { RequestTypeEnum } from '@mdtProMicroModules/amis-expand/constants';
import { SqlBuilder } from '@mdtProMicroModules/components/sql-builder';
import { type IEditor, MonacoEditor } from '@mdtProMicroModules/pages/form-editor/formily-externals/monaco-editor';
import i18n from '../../../../languages';

const BTN_STYLE: CSSProperties = {
  position: 'absolute',
  top: 10,
  right: 13,
  padding: 0,
  fontSize: 12,
};

const EMPTY_SQL = '';
const SqlBuildComp: FC = observer((props: any) => {
  const { value, onChange, ...restProps } = props;
  const [open, setOpen] = useState(false);
  const editorRef = useRef<IEditor>();
  const builderRef = useRef<BuilderRef>(null);
  const changeRef = useRef(onChange);
  const valueRef = useRef(value);
  changeRef.current = onChange;

  const getConfig = () => {
    return builderRef.current?.getConfig();
  };

  const openDrawer = () => {
    setOpen(true);
  };

  const onClose = () => {
    setOpen(false);
  };

  const onMount = (editor: IEditor) => {
    editorRef.current = editor;
  };

  const getFormatQlang = (value?: BuilderJsonConfig) => {
    if (!value) {
      return EMPTY_SQL;
    }
    return formatQlang(getQlang(value));
  };

  const onConfirm = () => {
    const config = getConfig();
    const sql = config ? getQlang(config) : EMPTY_SQL;

    changeRef.current({ ...valueRef.current, config, sql });
    editorRef.current?.setValue(getFormatQlang(config));
    setOpen(false);
  };
  return (
    <>
      <MonacoEditor
        {...restProps}
        height={400}
        language="sql"
        fullScreenTitle={i18n.chain.full}
        ref={editorRef}
        value={getFormatQlang(value?.config)}
        readOnly
        finishedEdit={() => {}}
        onMount={onMount}
      />
      <Button type="primary" size="small" ghost style={BTN_STYLE} icon={<VisibilityOn />} onClick={openDrawer}>
        {i18n.chain.qeEditor}
      </Button>
      <Drawer
        bodyStyle={{ padding: 0 }}
        open={open}
        closable={false}
        width="100%"
        onClose={onClose}
        onConfirm={onConfirm}
        operatorsTarget="extra"
        title={i18n.chain.qeEditor}
        push={false}
      >
        {open ? <SqlBuilder config={value?.config} ref={builderRef} /> : null}
      </Drawer>
    </>
  );
});

export const LowCodeQlang: FC<{ parentName: string }> = observer(({ parentName }) => {
  return (
    <ObjectField
      name="lowCodeQlang"
      reactions={(field) => {
        const type = field.query(`..${parentName}`).get('value');
        field.setState({ display: type === RequestTypeEnum.LOW_CODE_QLANG ? 'visible' : 'hidden' });
      }}
    >
      <Field required name="editor" title={i18n.chain.lowcodeSql} decorator={['FormItem']} component={[SqlBuildComp]} />
    </ObjectField>
  );
});
