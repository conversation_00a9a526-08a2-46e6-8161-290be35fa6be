import { FC, useState } from 'react';
import { ConfigKeyEnum, IPrivacy, useConfig } from '../../config';
import { IContext, LoginContext } from './context';

const GlobalContext: FC = ({ children }) => {
  const { check, visible } = useConfig(ConfigKeyEnum.PRIVACY) as IPrivacy;
  const [privacyChecked, setPrivacyChecked] = useState<boolean>(visible ? check ?? true : true);
  const [isPasswordForget, setIsPasswordForget] = useState<boolean>(false);
  // eslint-disable-next-line react/jsx-no-constructed-context-values
  const val: IContext = {
    privacyState: [privacyChecked, setPrivacyChecked],
    isPasswordForgetState: [isPasswordForget, setIsPasswordForget],
  };

  return <LoginContext.Provider value={val}>{children}</LoginContext.Provider>;
};

GlobalContext.displayName = 'GlobalContext';
// @ts-ignore
export { GlobalContext };
