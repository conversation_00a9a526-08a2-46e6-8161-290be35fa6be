import { FC } from 'react';
import { Empty } from '@mdtBsComm/components/empty';
import { useObservableState } from '@mdtBsComm/hooks/use-observable-state';
import { ModalWithBtnsComp } from '@mdtBsComponents/modal-with-btns-comp';
import { LinkButton } from '@mdtDesign/button';
import Spin from '@mdtDesign/spin';
import MarkdownPreview from '@mdtProComm/components/markdown-preview';
import i18n from '../../languages';
import { MarkdownDatapkgDescriptionController } from './MarkdownDatapkgDescriptionController';
import './index.less';

interface IProps {
  controller: MarkdownDatapkgDescriptionController;
}

export const PkgDescriptionTool = ({ controller }: IProps) => {
  const enableEdit = controller.getEnableEdit();
  return (
    <>
      <LinkButton
        className="module_table-datapkg-monitor_tool-btn"
        leftIcon="edit"
        onClick={() => controller.handleEdit()}
        disabled={!enableEdit}
      >
        {i18n.chain.proMicroModules.markdownPreview.editDesc}
      </LinkButton>
      {enableEdit ? <ModalWithBtnsComp controller={controller.getModifyController()} /> : null}
    </>
  );
};

const Content: FC<IProps> = ({ controller }) => {
  const content = useObservableState(controller.getContent$());
  return content ? (
    <MarkdownPreview
      className="module_markdown-datapkg-description"
      md={content}
      onLoadFileFunc={controller.handleLoadFile}
    />
  ) : (
    <Empty />
  );
};

const MarkdownDatapkgDescription: FC<IProps> = ({ controller }) => {
  const loading = useObservableState(controller.getLoading$());
  return loading ? (
    <Spin size="large" tip={i18n.chain.comDataLoading} className="module_markdown-datapkg-description_loading" />
  ) : (
    <Content controller={controller} />
  );
};

export { MarkdownDatapkgDescription };
