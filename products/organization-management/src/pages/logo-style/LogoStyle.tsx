import { FC } from 'react';
import { TableCurdWithSimpleSearch } from '@mdtProMicroModules/containers/table-curd-with-simple-search';
import { LogoStyleController } from './LogoStyleController';
import './index.less';

interface IProps {
  controller: LogoStyleController;
}
const LogoStyle: FC<IProps> = ({ controller }) => {
  return (
    <div style={{ padding: '0 20px 20px 20px', height: '100%', width: '100%' }}>
      <TableCurdWithSimpleSearch controller={controller.getTableController()} />
    </div>
  );
};

export { LogoStyle };
