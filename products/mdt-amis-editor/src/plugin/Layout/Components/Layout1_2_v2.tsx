/* eslint-disable sonarjs/no-duplicate-string */
/* eslint-disable @typescript-eslint/explicit-member-accessibility */
import i18n from '../../../languages';
import { FlexPluginBase } from '../FlexPluginBase';

export default class Layout1_2_v2 extends FlexPluginBase {
  name = i18n.chain.jsonComp.layout1_2_v2;
  isBaseComponent = false;
  pluginIcon = 'layout-1with2-plugin';
  description = i18n.chain.jsonComp.layout1_2_v2Description;
  tags = [i18n.chain.jsonComp.commonLayout];
  order = 303;
  scaffold: any = {
    type: 'flex',
    className: 'p-1',
    items: [
      {
        type: 'wrapper',
        size: 'xs',
        body: [],
        style: {
          flex: '0 0 auto',
          flexBasis: '100px',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'flex-start',
          alignItems: 'stretch',
        },
      },
      {
        type: 'flex',
        items: [
          {
            type: 'wrapper',
            size: 'xs',
            body: [],
            style: {
              flex: '1 1 auto',
              flexBasis: 'auto',
              flexGrow: 1,
              display: 'block',
            },
          },
          {
            type: 'wrapper',
            size: 'xs',
            body: [],
            style: {
              flex: '1 1 auto',
              flexBasis: 'auto',
              flexGrow: 1,
              display: 'block',
            },
          },
        ],
        style: {
          flex: '1 1 auto',
          padding: 0,
        },
        alignItems: 'stretch',
      },
    ],
    style: {
      overflowX: 'auto',
      margin: '0',
      maxWidth: 'auto',
      height: '350px',
      overflowY: 'auto',
    },
    direction: 'column',
    justify: 'center',
    alignItems: 'stretch',
    isFixedHeight: true,
  };
}
