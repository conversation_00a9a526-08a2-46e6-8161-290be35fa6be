import i18n from '../../../languages';
import { GraphPortGroupEnum, NodeRenderKeyEnum, NodeTypeEnum } from './enum';

// 连接锚点的类型 IN | OUT
export const GRAPH_ATTR_PORT_GROUP_KEY = 'port-group';

// 链接桩连接属性常量
export const PORT_CONNECTED = 'connected';

export const NODE_RENAME_INFO_MAP: Record<NodeTypeEnum, { name?: string; tip?: string }> = {
  // 输入
  from_datapkg: { name: i18n.chain.dataFactory.flow.fromDatapkgName, tip: i18n.chain.dataFactory.flow.fromDatapkgTip },
  from_db_table: { name: i18n.chain.dataFactory.flow.fromDbTableName, tip: i18n.chain.dataFactory.flow.fromDbTableTip },
  from_sql: { name: i18n.chain.dataFactory.flow.fromSqlName, tip: i18n.chain.dataFactory.flow.fromSqlTip },
  from_qlang_raw: { name: i18n.chain.dataFactory.flow.fromQlangRaw, tip: i18n.chain.dataFactory.flow.fromQlangRawTip },
  from_file_id: { name: i18n.chain.dataFactory.flow.fromFileIdName, tip: i18n.chain.dataFactory.flow.fromFileIdTip },
  from_file: { name: i18n.chain.dataFactory.flow.fromFileName, tip: i18n.chain.dataFactory.flow.fromFileTip },
  from_feishu_spreadsheet: {
    name: i18n.chain.dataFactory.flow.fromFSSheet,
    tip: i18n.chain.dataFactory.flow.fromFSSheetTip,
  },
  from_feishu_bitable: {
    name: i18n.chain.dataFactory.flow.fromFSTable,
    tip: i18n.chain.dataFactory.flow.fromFSTableTip,
  },
  from_dingtalk_sheet: {
    name: i18n.chain.dataFactory.flow.fromDKSheet,
    tip: i18n.chain.dataFactory.flow.fromDKSheetTip,
  },
  from_dingtalk_process_instances: {
    name: i18n.chain.dataFactory.flow.fromDKProcess,
    tip: i18n.chain.dataFactory.flow.fromDKProcessTip,
  },
  from_3rd_wechat_approval_data: {
    name: i18n.chain.dataFactory.flow.wwApprovalData,
    tip: i18n.chain.dataFactory.flow.wwApprovalDataTip,
  },
  // 数据处理
  append: { name: i18n.chain.dataFactory.flow.appendName, tip: i18n.chain.dataFactory.flow.appendTip },
  copy_column: { name: i18n.chain.dataFactory.flow.copyColumnName, tip: i18n.chain.dataFactory.flow.copyColumnTip },
  pipe: { name: i18n.chain.dataFactory.flow.pipeName, tip: i18n.chain.dataFactory.flow.pipeTip },
  insert_sequence_column: {
    name: i18n.chain.dataFactory.flow.insertSequenceColumnName,
    tip: i18n.chain.dataFactory.flow.insertSequenceColumnTip,
  },
  insert_const_column: {
    name: i18n.chain.dataFactory.flow.insertConstColumnName,
    tip: i18n.chain.dataFactory.flow.insertConstColumnTip,
  },
  join: { name: i18n.chain.dataFactory.flow.joinName, tip: i18n.chain.dataFactory.flow.joinTip },
  sjoin: { name: i18n.chain.dataFactory.flow.sjoinName, tip: i18n.chain.dataFactory.flow.sjoinTip },
  pct_change: { name: i18n.chain.dataFactory.flow.pctChangeName, tip: i18n.chain.dataFactory.flow.pctChangeTip },
  upsert: { name: i18n.chain.dataFactory.flow.upsertName, tip: i18n.chain.dataFactory.flow.upsertTip },
  only_columns: { name: i18n.chain.dataFactory.flow.onlyColumnsName, tip: i18n.chain.dataFactory.flow.onlyColumnsTip },
  rename: { name: i18n.chain.dataFactory.flow.renameName, tip: i18n.chain.dataFactory.flow.renameTip },
  replace: { name: i18n.chain.dataFactory.flow.replaceName, tip: i18n.chain.dataFactory.flow.replaceTip },
  strip: { name: i18n.chain.dataFactory.flow.stripName, tip: i18n.chain.dataFactory.flow.stripTip },
  typecast: { name: i18n.chain.dataFactory.flow.typecastName, tip: i18n.chain.dataFactory.flow.typecastTip },
  null_if: { name: i18n.chain.dataFactory.flow.nullIfName, tip: i18n.chain.dataFactory.flow.nullIfTip },
  value_mapping: {
    name: i18n.chain.dataFactory.flow.valueMappingName,
    tip: i18n.chain.dataFactory.flow.valueMappingTip,
  },
  groupby: { name: i18n.chain.dataFactory.flow.groupbyName, tip: i18n.chain.dataFactory.flow.groupbyTip },
  drop_duplicates: {
    name: i18n.chain.dataFactory.flow.dropDuplicatesName,
    tip: i18n.chain.dataFactory.flow.dropDuplicatesTip,
  },
  drop_columns: { name: i18n.chain.dataFactory.flow.dropColumnsName, tip: i18n.chain.dataFactory.flow.dropColumnsTip },
  sort: { name: i18n.chain.dataFactory.flow.sortName, tip: i18n.chain.dataFactory.flow.sortTip },
  filter: { name: i18n.chain.dataFactory.flow.filterName, tip: i18n.chain.dataFactory.flow.filterTip },
  geocode: { name: i18n.chain.dataFactory.flow.geocodeName, tip: i18n.chain.dataFactory.flow.geocodeTip },
  lnglat_to_geometry: {
    name: i18n.chain.dataFactory.flow.lnglatToGeometryName,
    tip: i18n.chain.dataFactory.flow.lnglatToGeometryTip,
  },
  convert_geometry: {
    name: i18n.chain.dataFactory.flow.convertGeometryName,
    tip: i18n.chain.dataFactory.flow.convertGeometryTip,
  },

  // 执行
  execute_sql: { name: i18n.chain.dataFactory.flow.executeSqlName, tip: i18n.chain.dataFactory.flow.executeSqlTip },
  run_flow: { name: i18n.chain.dataFactory.flow.runFlowName, tip: i18n.chain.dataFactory.flow.runFlowTip },

  // 创建
  create_table: {
    name: i18n.chain.dataFactory.flow.createTableName,
    tip: i18n.chain.dataFactory.flow.createTableTip,
  },
  to_upsert_db_table: {
    name: i18n.chain.dataFactory.flow.toUpsertDbTableName,
    tip: i18n.chain.dataFactory.flow.toUpsertDbTableTip,
  },
  to_market: {
    name: i18n.chain.dataFactory.flow.toMarketName,
    tip: i18n.chain.dataFactory.flow.toMarketTip,
  },
  to_update_datapkg: {
    name: i18n.chain.dataFactory.flow.toUpdateDatapkgName,
    tip: i18n.chain.dataFactory.flow.toUpdateDatapkgTip,
  },
};

// 实线
export const EDGE_LINE_NORMAL = { strokeDasharray: '', stroke: '#808080', targetMarker: { fill: '#808080' } };
// 虚线
export const EDGE_LINE_IDEL = { strokeDasharray: '5,5', stroke: '#808080', targetMarker: { fill: '#808080' } };
// 绿色的虚线
export const EDGE_LINE_SUCCESS = { strokeDasharray: '5,5', stroke: '#04b776ff', targetMarker: { fill: '#04b776ff' } };

export const COMMON_GROUPS = {
  groups: {
    [GraphPortGroupEnum.IN]: {
      position: { name: 'top' },
      zIndex: 2,
    },
    [GraphPortGroupEnum.OUT]: {
      position: { name: 'bottom' },
      zIndex: 2,
    },
  },
};

export const COMMON_PORT_CONFIG = {
  attrs: {
    circle: {
      r: 6,
      magnet: true,
      stroke: '#3D7FE9',
      strokeWidth: 2,
      fill: '#fff',
    },
  },
};

// NodeConfig
export const NODE_COMMON_PROPS = {
  renderKey: NodeRenderKeyEnum.EXECUTE_NODE,
  width: 214,
  height: 40,
};

// EdgeConfig
export const EDGE_COMMON_PROPS = {
  attrs: {
    line: {
      targetMarker: {
        name: 'block',
        width: 4,
        height: 8,
      },
      strokeDasharray: '',
      stroke: '#A2B1C3',
      strokeWidth: 1,
    },
  },
};

export const NEED_FIELD_INFO_NODE: NodeTypeEnum[] = [
  NodeTypeEnum.COPY_COLUMN,
  NodeTypeEnum.CREATE_TABLE,
  NodeTypeEnum.JOIN,
  NodeTypeEnum.SJOIN,
  NodeTypeEnum.UPSERT_DATAFRAME,
  NodeTypeEnum.ONLY_COLUMNS,
  NodeTypeEnum.RENAME,
  NodeTypeEnum.REPLACE,
  NodeTypeEnum.STRIP,
  NodeTypeEnum.TYPECAST,
  NodeTypeEnum.NULL_IF,
  NodeTypeEnum.MARKET,
  NodeTypeEnum.UPDATE_DATAPKG,
  NodeTypeEnum.UPSERT_DB_TABLE,
  NodeTypeEnum.VALUE_MAPPING,
  NodeTypeEnum.GROUPBY,
  NodeTypeEnum.DROP_DUPLICATES,
  NodeTypeEnum.DROP_COLUMMNS,
  NodeTypeEnum.SORT,
  NodeTypeEnum.FILTER,
  NodeTypeEnum.GEOCODE,
  NodeTypeEnum.LNGLAT_TO_GEOMETRY,
  NodeTypeEnum.PCT_CHANGE,
  NodeTypeEnum.CONVERT_GEOMETRY,
];

export const OTHER_APP_DATAPKG = 'other_app_datapkg';
export const PRIVATE_DATAPKG = 'private_datapkg';

// export const AGG_FUNC_NAME = [
//   'mean',
//   'sum',
//   'size',
//   'count',
//   'std',
//   'var',
//   'sem',
//   'describe',
//   'first',
//   'last',
//   'nth',
//   'min',
//   'max',
//   'avg',
//   'distinct_count',
// ];

export const AGG_FUNC_NAME = [
  { label: i18n.chain.dataFactory.flow.aggFuncNameDistinctCount, value: 'distinct_count' },
  { label: i18n.chain.dataFactory.flow.aggFuncNameCount, value: 'count' },
  { label: i18n.chain.dataFactory.flow.aggFuncNameSum, value: 'sum' },
  { label: i18n.chain.dataFactory.flow.aggFuncNameMean, value: 'mean' },
  { label: i18n.chain.dataFactory.flow.aggFuncNameMin, value: 'min' },
  { label: i18n.chain.dataFactory.flow.aggFuncNameMax, value: 'max' },
  { label: i18n.chain.dataFactory.flow.aggFuncNameStd, value: 'std' },
  { label: i18n.chain.dataFactory.flow.aggFuncNameFirst, value: 'first' },
  { label: i18n.chain.dataFactory.flow.aggFuncNameLast, value: 'last' },
];

export const REMOTE_FILE_TYPE = [
  { label: i18n.chain.dataFactory.flow.remoteFileTypeOss, value: 'oss' },
  { label: i18n.chain.dataFactory.flow.remoteFileTypeMinio, value: 'minio' },
  { label: i18n.chain.dataFactory.flow.remoteFileTypeFtp, value: 'ftp' },
  { label: i18n.chain.dataFactory.flow.remoteFileTypeSftp, value: 'sftp' },
  { label: i18n.chain.dataFactory.flow.remoteFileTypeHttp, value: 'http' },
];

export const TABLE_CONNECT_TYPE = [
  { label: i18n.chain.dataFactory.flow.tableConnectTypeInner, value: 'inner' },
  { label: i18n.chain.dataFactory.flow.tableConnectTypeOuter, value: 'outer' },
  { label: i18n.chain.dataFactory.flow.tableConnectTypeLeft, value: 'left' },
  { label: i18n.chain.dataFactory.flow.tableConnectTypeRight, value: 'right' },
];

export const STRIP_DIR = [
  { label: i18n.chain.dataFactory.flow.stripDirLeft, value: 'left' },
  { label: i18n.chain.dataFactory.flow.stripDirRight, value: 'right' },
  { label: i18n.chain.dataFactory.flow.stripDirBoth, value: 'both' },
  { label: i18n.chain.dataFactory.flow.stripDirLeftAndRight, value: 'left_and_right' },
  { label: i18n.chain.dataFactory.flow.stripDirAll, value: 'all' },
  { label: i18n.chain.dataFactory.flow.stripDirMiddle, value: 'middle' },
];

export const DINGTALK_PROCESS_TYPE = [
  { label: i18n.chain.dataFactory.flow.dkProcessNew, value: 'NEW' },
  { label: i18n.chain.dataFactory.flow.dkProcessRunning, value: 'RUNNING' },
  { label: i18n.chain.dataFactory.flow.dkProcessTerminated, value: 'TERMINATED' },
  { label: i18n.chain.dataFactory.flow.dkProcessCompleted, value: 'COMPLETED' },
  { label: i18n.chain.dataFactory.flow.dkProcessCanceled, value: 'CANCELED' },
];

export const TABLE_ACTION_TYPE = [
  { label: i18n.chain.dataFactory.flow.tableActionTypeNew, value: 'new' },
  { label: i18n.chain.dataFactory.flow.tableActionTypeReplace, value: 'replace' },
  { label: i18n.chain.dataFactory.flow.tableActionTypeTruncate, value: 'truncate' },
];

export const columnTypeOperatorMap: Record<
  'common' | 'number' | 'text' | 'filterText' | 'filterNumber',
  { label: string; value: string }[]
> = {
  common: [
    { label: i18n.chain.dataFactory.flow.commonEq, value: 'eq' },
    { label: i18n.chain.dataFactory.flow.commonNe, value: 'ne' },
    { label: i18n.chain.dataFactory.flow.commonIn, value: 'in' },
    { label: i18n.chain.dataFactory.flow.commonIs, value: 'is' },
  ],
  number: [
    { label: i18n.chain.dataFactory.flow.numberGt, value: 'gt' },
    { label: i18n.chain.dataFactory.flow.numberGe, value: 'ge' },
    { label: i18n.chain.dataFactory.flow.numberLt, value: 'lt' },
    { label: i18n.chain.dataFactory.flow.numberLe, value: 'le' },
    { label: i18n.chain.dataFactory.flow.numberBetween, value: 'between' },
  ],
  text: [
    { label: i18n.chain.dataFactory.flow.textStartswith, value: 'startswith' },
    { label: i18n.chain.dataFactory.flow.textEndswith, value: 'endswith' },
    { label: i18n.chain.dataFactory.flow.textLike, value: 'like' },
    { label: i18n.chain.dataFactory.flow.textIlike, value: 'ilike' },
    { label: i18n.chain.dataFactory.flow.textContain, value: 'contain' },
    { label: i18n.chain.dataFactory.flow.textMatch, value: 'match' },
  ],
  filterText: [
    { label: i18n.chain.dataFactory.flow.filterTextIs, value: 'is' },
    { label: i18n.chain.dataFactory.flow.filterTextEq, value: 'eq' },
    { label: i18n.chain.dataFactory.flow.filterTextStartswith, value: 'startswith' },
    { label: i18n.chain.dataFactory.flow.filterTextEndswith, value: 'endswith' },
  ],
  filterNumber: [
    { label: i18n.chain.dataFactory.flow.filterNumberIs, value: 'is' },
    { label: i18n.chain.dataFactory.flow.filterNumberEq, value: 'eq' },
    { label: i18n.chain.dataFactory.flow.filterNumberGt, value: 'gt' },
    { label: i18n.chain.dataFactory.flow.filterNumberGe, value: 'ge' },
    { label: i18n.chain.dataFactory.flow.filterNumberLt, value: 'lt' },
    { label: i18n.chain.dataFactory.flow.filterNumberLe, value: 'le' },
    { label: i18n.chain.dataFactory.flow.filterNumberBetween, value: 'between' },
  ],
};

export const CONVERT_GEOMETRY_FORMAT = [
  { label: i18n.chain.dataFactory.flow.convertGeometryFormatWkb, value: 'wkb' },
  { label: i18n.chain.dataFactory.flow.convertGeometryFormatWkt, value: 'wkt' },
  { label: i18n.chain.dataFactory.flow.convertGeometryFormatGeojson, value: 'geojson' },
  { label: i18n.chain.dataFactory.flow.convertGeometryFormatShapely, value: 'shapely' },
];

export const DEFAULT_SQL = '-- select * from "schema"."table"\n-- For example select * from "etl"."test-table"';
