import { IPaginationParams } from '@mdtBsControllers/data-list-controller';
import { exportPDF } from '../../utils/export-pdf';
import {
  IControllerOptions as IProps,
  OneTableNewFillFormController,
} from '../one-table-new-fill-form/OneTableNewFillFormController';

export interface IControllerOptions extends Omit<IProps, 'submitConfirmOptionsFunc' | 'defaultValue'> {
  previewCallback?: (value: Record<string, any>, onClose?: () => void) => void;
  onClose?: () => void;
  showEdit?: boolean;
  defaultValue: Record<string, any>;
  paginationParams?: IPaginationParams;
  requestParams?: Record<string, any>;
}

export class OneTableNewPreviewFormController extends OneTableNewFillFormController {
  private showEdit?: boolean;
  private previewCallback?: (value: Record<string, any>, onClose?: () => void) => void;
  private onClose?: () => void;

  public constructor(options: IControllerOptions) {
    const { previewCallback, onClose, showEdit, defaultValue, itemData, ...restOptions } = options;
    super({
      ...restOptions,
      defaultValue,
      itemData,
      onlyForm: true,
      submitConfirmOptionsFunc: () => ({
        onlySaveCallBack: () => {},
        submitSuccessCallback: () => {},
      }),
    });
    this.showEdit = showEdit;
    this.previewCallback = previewCallback;
    this.onClose = onClose;
  }

  public destroy() {
    super.destroy();
  }

  public getEnableClose() {
    return !!this.onClose;
  }

  public getEnableEdit() {
    return !!this.showEdit;
  }

  public previewCallbackFunc() {
    const value = this.getCurrentEditData$().getValue();
    this.previewCallback?.(value);
  }

  public onCloseFunc() {
    this.onClose?.();
  }

  public exportToPDF = async () => {
    const { id: elementId } = this.getCurrentEditData$().getValue();
    if (!elementId) {
      return;
    }
    return await exportPDF(`form-${elementId}`, this.getItemData().formName, { scale: 0.68 });
  };
}
