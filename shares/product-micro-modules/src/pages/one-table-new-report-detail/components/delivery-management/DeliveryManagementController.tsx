import _ from 'lodash';
import { ReactNode } from 'react';
import { HelpOutlined } from '@metro/icons';
import { MoreHoriz } from '@metro/icons';
import { Button } from '@metroDesign/button';
import { Dropdown } from '@metroDesign/dropdown';
import { Popconfirm } from '@metroDesign/popconfirm';
import { Space } from '@metroDesign/space';
import { Tag } from '@metroDesign/tag';
import { Tooltip } from '@metroDesign/tooltip';
import { DataListCompTableCurdController, IVirtualizedTableProps } from '@mdtBsComponents/data-list-comp-table-curd';
import { OneTableNewDataStateEnum } from '@mdtProComm/constants';
import { TransformIdToName, TransformUserIdToOrgName } from '../../../../components/transform-id-to-name';
import i18n from '../../../../languages';
import { buildDataDeliveryTooltip } from '../../../../utils/oneTableNewUtil';
import { type IControllerOptions, DeliveryManagementBaseController } from './DeliveryManagementBaseController';
import { ITableData } from './DeliveryManagementModel';

class DeliveryManagementController extends DeliveryManagementBaseController {
  public constructor(options: Omit<IControllerOptions, 'listController'>) {
    super(options);
    this.listController = new DataListCompTableCurdController({
      dataListControllerOptions: {
        loadDataListFunc: () => this.getFirstPageData(),
      },
      tableOptions: () => this.getTableOptions(),
      curdOptions: () => this.initCurdOptions(),
    });
    this.listController.loadDataList();
  }

  public destroy() {
    super.destroy();
    this.listController.destroy();
  }

  public getListController() {
    return this.listController;
  }

  // 表格
  private getTableOptions = (): IVirtualizedTableProps => {
    const columns: any[] = [
      {
        code: 'primaryAssignee',
        width: 350,
        name: i18n.chain.proMicroModules.oneTable.tableColumns.departName,
        render: (primaryAssignee: number, row: ITableData) => {
          const showDetail = !row.isCancelledOrRefused;
          const orgDom = <TransformUserIdToOrgName placement="right" id={primaryAssignee} showFullName />;
          return showDetail ? (
            <Button.Link primary onClick={() => this.openDetailDrawer(row)}>
              {orgDom}
            </Button.Link>
          ) : (
            orgDom
          );
        },
      },
      {
        code: 'userIds',
        width: 200,
        name: i18n.chain.proMicroModules.oneTable.tableColumns.userName,
        render: (userIds: number[]) => {
          return _.map(userIds, (user, index) => (
            <Tag key={user} {...(!index && { color: 'primary', type: 'light', bordered: false })}>
              <TransformIdToName id={user} type="user_id" />
            </Tag>
          ));
        },
      },
      {
        code: 'dataDeliveryInfo',
        width: 180,
        name: i18n.chain.proMicroModules.oneTable.tableColumns.dataDeliveryInfo,
        render: (dataDeliveryInfo: ITableData['dataDeliveryInfo']) => {
          if (!dataDeliveryInfo) {
            return '-';
          }

          const tooltipContent = buildDataDeliveryTooltip(dataDeliveryInfo);

          const displayContent = (
            <Space direction="vertical" size={2}>
              <Space size={4}>
                <span>
                  {i18n.chain.proMicroModules.oneTable.tableColumns.deliveryDataCount(
                    dataDeliveryInfo?.stat_include_downstream?.total_assign_rows,
                    `${dataDeliveryInfo?.percent}%`,
                  )}
                </span>
                <HelpOutlined style={{ color: 'var(--metro-text-2)' }} />
              </Space>
            </Space>
          );

          return (
            <Popconfirm
              title={<pre style={{ fontSize: '12px' }}>{tooltipContent}</pre>}
              placement="left"
              showArrow
              showCancel={false}
              trigger={['hover']}
              okButtonProps={{ style: { display: 'none' } }}
            >
              {displayContent}
            </Popconfirm>
          );
        },
      },
      { code: 'dealTime', width: 180, name: i18n.chain.proMicroModules.oneTable.tableColumns.issuedTime },
      {
        code: 'dataStateDisplay',
        width: 100,
        name: i18n.chain.proMicroModules.oneTable.tableColumns.taskStatus,
        // 下发可以被取消及被下级驳回, 所以需要根据不同情况来显示
        render: (status: string[], row: ITableData) => {
          const reason = row.reason;
          const tag = (
            <Tag color={status[1]} type="light" bordered={false}>
              {status[0]}
              {reason ? <HelpOutlined /> : null}
            </Tag>
          );

          return reason ? (
            <Tooltip overlay={reason} placement="left">
              {tag}
            </Tooltip>
          ) : (
            tag
          );
        },
      },
    ];
    return { columns: columns, type: 'page-bg', primaryKey: 'id', withVerticalBorder: false };
  };

  // 更多按钮
  private getTableItemMoreBtn = (item: ITableData, needIssueData?: boolean): ReactNode => {
    const dropdownItems = this.getDropdownItems(item, needIssueData);
    return dropdownItems.length ? (
      <Dropdown key="more" menu={{ items: dropdownItems }}>
        <Button ghost icon={<MoreHoriz />} onlyIcon />
      </Dropdown>
    ) : null;
  };

  // 需要审批时按钮
  private getNeedApprovalTableItemBtns = (item: ITableData) => {
    const { dataState } = item;
    const approvalBtns = _.map(this.getNeedApprovalItemBtns(item), (btn) => (
      <Button.Link key={btn.key} primary onClick={btn.onClick}>
        {btn.text}
      </Button.Link>
    ));

    const isUnsubmitted = dataState === OneTableNewDataStateEnum.UNSUBMITTED;
    const isRejected = dataState === OneTableNewDataStateEnum.REJECTED;

    // 目前先未提交及驳回可以重新分配数据
    // 也可以其他状态分配数据, 但需要自动帮用户做驳回操作
    const moreBtn = this.getTableItemMoreBtn(item, isUnsubmitted || isRejected);
    const btns: ReactNode[] = [approvalBtns, moreBtn].filter(Boolean);
    return <Space style={{ justifyContent: 'flex-end', width: '100%' }}>{btns}</Space>;
  };

  // 不需要审批按钮
  private getNotNeedApprovalTableItemBtns = (item: ITableData) => {
    const notApprovalBtns = _.map(this.getNotNeedApprovalItemBtns(item), (btn) => (
      <Button.Link key={btn.key} primary onClick={btn.onClick}>
        {btn.text}
      </Button.Link>
    ));

    const moreBtn = this.getTableItemMoreBtn(item, true);
    const btns: ReactNode[] = [notApprovalBtns, moreBtn].filter(Boolean);
    return <Space style={{ justifyContent: 'flex-end', width: '100%' }}>{btns}</Space>;
  };

  private initCurdOptions = () => {
    const showExpediteBtn = this.getShowExpediteBtn$().getValue();
    let otherBtns: any = null;
    if (showExpediteBtn) {
      otherBtns = this.getIsNeedApproval() ? this.getNeedApprovalTableItemBtns : this.getNotNeedApprovalTableItemBtns;
    }
    return { otherBtns };
  };
}

export { DeliveryManagementController };
