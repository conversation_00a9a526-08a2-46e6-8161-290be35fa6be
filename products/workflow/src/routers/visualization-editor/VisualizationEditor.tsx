import { FC, useEffect, useRef } from 'react';
import './index.less';

interface IProps {
  controller: any;
}

const Editor: FC<IProps> = ({ controller }) => {
  const drawFlow = (e: any) => {
    const json = JSON.parse(e.target.value);
    controller.drawerEngineContext(json);
  };

  return <textarea style={{ flex: '1', flexShrink: 0 }} onBlur={drawFlow} />;
};

const ViewXML2: FC<IProps> = ({ controller }) => {
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    controller.initDom(ref.current!);
  }, [controller]);

  return <div ref={ref} style={{ flex: '1', flexShrink: 0 }} />;
};

const VisualizationEditor: FC<IProps> = ({ controller }) => {
  return (
    <div className="workflow-visualization-editor">
      <div className="leftBox">
        <h1>Engine_Context</h1>
        <Editor controller={controller} />
      </div>
      <div className="rightBox">
        <h1>Flow</h1>
        <ViewXML2 controller={controller} />
      </div>
    </div>
  );
};

export { VisualizationEditor };
