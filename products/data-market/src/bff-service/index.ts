import { ProductPrefixEnum } from '@mdtProComm/constants';
import { DMAlbumBffService } from './album';
import { DMAppBffService } from './app';
import { DMArticleBffService } from './article';
import { DMDatapkgBffService } from './datapkg';
import { DMDatasetBffService } from './dataset';
import { DMEntityBffService } from './entity';
import { DMStatisticBffService } from './statistic';
import { DMTaggroupBffService } from './taggroup';
import { DMTicketBffService } from './ticket';

export const albumBffService = new DMAlbumBffService(ProductPrefixEnum.DATA_MARKET);
export const pkgBffService = new DMDatapkgBffService(ProductPrefixEnum.DATA_MARKET);
export const appBffService = new DMAppBffService(ProductPrefixEnum.DATA_MARKET);
export const articleBffService = new DMArticleBffService(ProductPrefixEnum.DATA_MARKET);
export const datasetBffService = new DMDatasetBffService(ProductPrefixEnum.DATA_MARKET);
export const entityBffService = new DMEntityBffService(ProductPrefixEnum.DATA_MARKET);
export const statisticBffService = new DMStatisticBffService(ProductPrefixEnum.DATA_MARKET);
export const taggroupBffService = new DMTaggroupBffService(ProductPrefixEnum.DATA_MARKET);
export const ticketBffService = new DMTicketBffService(ProductPrefixEnum.DATA_MARKET);
